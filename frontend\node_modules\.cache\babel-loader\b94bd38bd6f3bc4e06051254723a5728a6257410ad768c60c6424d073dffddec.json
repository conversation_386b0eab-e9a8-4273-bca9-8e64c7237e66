{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\ApproveFinishedProduct.js\",\n  _s = $RefreshSig$();\n// src/pages/ApproveFinishedProduct.jsx\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport axios from 'axios';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Form, Button, Alert, Row, Col, Spinner, Image, Modal, ProgressBar, Badge, Tabs, Tab, Table, OverlayTrigger, Tooltip } from 'react-bootstrap';\nimport { FaCheck, FaUpload, FaImage, FaTags, FaInfoCircle, FaMoneyBillWave, FaArrowRight, FaPercentage, FaBoxOpen, FaClipboardList, FaTrash, FaUndo, FaExclamationTriangle, FaFilePdf, FaDownload } from 'react-icons/fa';\nimport { useDropzone } from 'react-dropzone';\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\nimport './ApproveFinishedProduct.css';\nimport { jsPDF } from 'jspdf';\n// No need to import uploadMultipleImages as we're using FormData directly\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ApproveFinishedProduct = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const fileInputRef = useRef(null);\n\n  // Basic form state\n  const [manufacturePrice, setManufacturePrice] = useState('');\n  const [sellingPrice, setSellingPrice] = useState('');\n  const [productNotes, setProductNotes] = useState('');\n  const [error, setError] = useState('');\n  const [successMsg, setSuccessMsg] = useState('');\n  const [isApproved, setIsApproved] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Image handling state\n  const [productImages, setProductImages] = useState([]);\n  const [imagePreviewUrls, setImagePreviewUrls] = useState([]);\n  const [existingImageUrls, setExistingImageUrls] = useState([]);\n  const [isDragging, setIsDragging] = useState(false);\n  const [activeImageIndex, setActiveImageIndex] = useState(0);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Product details state\n  const [productDetails, setProductDetails] = useState(null);\n  const [fabricDetails, setFabricDetails] = useState([]);\n  const [sizeQuantities, setSizeQuantities] = useState({\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0\n  });\n\n  // UI state\n  const [activeTab, setActiveTab] = useState('details');\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [showPdfModal, setShowPdfModal] = useState(false);\n  const [pdfLoading, setPdfLoading] = useState(false);\n  const [validationErrors, setValidationErrors] = useState({});\n  const [profitMargin, setProfitMargin] = useState(0);\n\n  // Product name editing state\n  const [productName, setProductName] = useState('');\n  const [isEditingName, setIsEditingName] = useState(false);\n  const [productNameError, setProductNameError] = useState('');\n\n  // Price editing state for approved products\n  const [isEditingPrices, setIsEditingPrices] = useState(false);\n  const [editManufacturePrice, setEditManufacturePrice] = useState('');\n  const [editSellingPrice, setEditSellingPrice] = useState('');\n  const [priceEditErrors, setPriceEditErrors] = useState({});\n  const [finishedProductId, setFinishedProductId] = useState(null);\n\n  // Calculate profit margin whenever prices change\n  useEffect(() => {\n    if (manufacturePrice && sellingPrice) {\n      const mPrice = parseFloat(manufacturePrice);\n      const sPrice = parseFloat(sellingPrice);\n      if (mPrice > 0 && sPrice > 0) {\n        const margin = (sPrice - mPrice) / sPrice * 100;\n        setProfitMargin(margin.toFixed(2));\n      }\n    }\n  }, [manufacturePrice, sellingPrice]);\n\n  // State for retry mechanism\n  const [retryCount, setRetryCount] = useState(0);\n  const maxRetries = 3;\n\n  // Function to fetch product data with retry mechanism\n  const fetchProductData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Fetch approval status\n      const approvalRes = await axios.get(`http://localhost:8000/api/finished_product/status/${id}/`);\n\n      // Fetch cutting record details\n      const cuttingRes = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\n      if (approvalRes.data && approvalRes.data.is_approved) {\n        setIsApproved(true);\n        setManufacturePrice(approvalRes.data.manufacture_price);\n        setSellingPrice(approvalRes.data.selling_price);\n        setFinishedProductId(approvalRes.data.finished_product_id);\n\n        // Set existing image URLs if available\n        if (approvalRes.data.product_images && Array.isArray(approvalRes.data.product_images)) {\n          setExistingImageUrls(approvalRes.data.product_images);\n        } else if (approvalRes.data.product_image) {\n          // For backward compatibility with single image\n          setExistingImageUrls([approvalRes.data.product_image]);\n        }\n        if (approvalRes.data.notes) {\n          setProductNotes(approvalRes.data.notes);\n        }\n      }\n      if (cuttingRes.data) {\n        setProductDetails(cuttingRes.data);\n\n        // Set product name from API response\n        if (cuttingRes.data.product_name) {\n          setProductName(cuttingRes.data.product_name);\n        } else if (cuttingRes.data.fabric_definition_data) {\n          // If no product name, use fabric name as default\n          setProductName(cuttingRes.data.fabric_definition_data.fabric_name);\n        }\n\n        // Extract fabric details\n        if (cuttingRes.data.details && cuttingRes.data.details.length > 0) {\n          // Debug: Log the fabric details to see what color data we're getting\n          console.log('Fabric details from API:', cuttingRes.data.details);\n          setFabricDetails(cuttingRes.data.details);\n\n          // Calculate size quantities\n          const sizes = {\n            xs: 0,\n            s: 0,\n            m: 0,\n            l: 0,\n            xl: 0\n          };\n          cuttingRes.data.details.forEach(detail => {\n            sizes.xs += detail.xs || 0;\n            sizes.s += detail.s || 0;\n            sizes.m += detail.m || 0;\n            sizes.l += detail.l || 0;\n            sizes.xl += detail.xl || 0;\n          });\n          setSizeQuantities(sizes);\n        }\n      }\n\n      // Reset retry count on success\n      setRetryCount(0);\n    } catch (err) {\n      console.error(\"Failed to fetch product data:\", err);\n\n      // Provide more detailed error message\n      const errorMessage = err.response ? `Error: ${err.response.status} - ${err.response.statusText}` : err.request ? \"No response received from server. Check if the backend is running.\" : \"Failed to make request. Check your network connection.\";\n      setError(`Unable to fetch product data. ${errorMessage}`);\n\n      // Implement retry mechanism\n      if (retryCount < maxRetries) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => {\n          fetchProductData();\n        }, 2000); // Wait 2 seconds before retrying\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [id, retryCount]);\n\n  // Fetch product details and approval status\n  useEffect(() => {\n    fetchProductData();\n  }, [fetchProductData]);\n\n  // Handle image selection from file input\n  const handleImageChange = e => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      processImageFiles(files);\n    }\n  };\n\n  // Process the selected image files\n  const processImageFiles = useCallback(files => {\n    // Check if adding these files would exceed the limit\n    if (productImages.length + files.length > 10) {\n      setError(`You can only upload up to 10 images. You already have ${productImages.length} images.`);\n      return;\n    }\n    const newImages = [];\n    const newPreviewUrls = [...imagePreviewUrls];\n    files.forEach(file => {\n      // Validate file type\n      if (!file.type.match('image.*')) {\n        setError('Please select image files only (JPEG, PNG, etc.)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('Each image size should be less than 5MB');\n        return;\n      }\n      newImages.push(file);\n\n      // Create a preview URL\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        newPreviewUrls.push(reader.result);\n        setImagePreviewUrls([...newPreviewUrls]);\n      };\n      reader.readAsDataURL(file);\n    });\n    setProductImages([...productImages, ...newImages]);\n  }, [productImages, imagePreviewUrls]);\n\n  // Trigger file input click\n  const triggerFileInput = () => {\n    fileInputRef.current.click();\n  };\n\n  // Handle drag and drop functionality\n  const onDrop = useCallback(acceptedFiles => {\n    if (acceptedFiles && acceptedFiles.length > 0) {\n      processImageFiles(acceptedFiles);\n    }\n  }, [processImageFiles]);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif']\n    },\n    maxFiles: 10\n  });\n\n  // Remove an uploaded image\n  const removeImage = index => {\n    const newImages = [...productImages];\n    const newPreviewUrls = [...imagePreviewUrls];\n    newImages.splice(index, 1);\n    newPreviewUrls.splice(index, 1);\n    setProductImages(newImages);\n    setImagePreviewUrls(newPreviewUrls);\n\n    // Adjust active index if needed\n    if (index === activeImageIndex) {\n      setActiveImageIndex(Math.max(0, index - 1));\n    } else if (index < activeImageIndex) {\n      setActiveImageIndex(activeImageIndex - 1);\n    }\n  };\n\n  // Set active image\n  const setActiveImage = index => {\n    setActiveImageIndex(index);\n  };\n\n  // Validate form inputs\n  const validateForm = () => {\n    const errors = {};\n\n    // Validate manufacture price\n    if (!manufacturePrice || manufacturePrice.trim() === '') {\n      errors.manufacturePrice = \"Manufacture price is required\";\n    } else if (parseFloat(manufacturePrice) <= 0) {\n      errors.manufacturePrice = \"Manufacture price must be greater than zero\";\n    } else if (isNaN(parseFloat(manufacturePrice))) {\n      errors.manufacturePrice = \"Manufacture price must be a valid number\";\n    }\n\n    // Validate selling price\n    if (!sellingPrice || sellingPrice.trim() === '') {\n      errors.sellingPrice = \"Selling price is required\";\n    } else if (parseFloat(sellingPrice) <= 0) {\n      errors.sellingPrice = \"Selling price must be greater than zero\";\n    } else if (isNaN(parseFloat(sellingPrice))) {\n      errors.sellingPrice = \"Selling price must be a valid number\";\n    } else if (parseFloat(sellingPrice) < parseFloat(manufacturePrice)) {\n      errors.sellingPrice = \"Selling price should be greater than or equal to manufacture price\";\n    }\n\n    // Validate product notes (optional)\n    if (productNotes && productNotes.length > 500) {\n      errors.productNotes = \"Notes should be less than 500 characters\";\n    }\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Show confirmation modal\n  const handleFormSubmit = e => {\n    e.preventDefault();\n\n    // Validate form\n    if (!validateForm()) {\n      return;\n    }\n\n    // Show confirmation modal\n    setShowConfirmModal(true);\n  };\n\n  // Handle actual submission\n  const handleSubmit = async () => {\n    setError('');\n    setSuccessMsg('');\n    setShowConfirmModal(false);\n    try {\n      // Show loading state\n      setLoading(true);\n      setIsUploading(true);\n\n      // Create FormData object for API request\n      const formData = new FormData();\n      formData.append('cutting_record', id);\n      formData.append('manufacture_price', parseFloat(manufacturePrice));\n      formData.append('selling_price', parseFloat(sellingPrice));\n      if (productNotes) {\n        formData.append('notes', productNotes);\n      }\n\n      // Add images directly to the FormData if there are any\n      if (productImages && productImages.length > 0) {\n        setUploadProgress(0);\n\n        // Append each image to the FormData\n        productImages.forEach(image => {\n          formData.append('product_images', image);\n        });\n      }\n\n      // Make the API request with progress tracking\n      const response = await axios.post('http://localhost:8000/api/finished_product/approve/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          setUploadProgress(percentCompleted);\n        }\n      });\n      setSuccessMsg(response.data.message || 'Product approved successfully!');\n      setIsUploading(false);\n\n      // Redirect after a delay\n      setTimeout(() => {\n        navigate('/approveproduct-list');\n      }, 2000);\n    } catch (err) {\n      console.error(\"Error approving finished product:\", err);\n      const errMsg = err.response && err.response.data ? typeof err.response.data === 'object' ? JSON.stringify(err.response.data) : err.response.data : \"Failed to approve finished product. Please try again.\";\n      setError(errMsg);\n      setIsUploading(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Cancel confirmation\n  const handleCancelConfirmation = () => {\n    setShowConfirmModal(false);\n  };\n\n  // Open PDF modal\n  const openPdfModal = () => {\n    setShowPdfModal(true);\n  };\n\n  // Close PDF modal\n  const closePdfModal = () => {\n    setShowPdfModal(false);\n  };\n\n  // Start editing product name\n  const startEditingName = () => {\n    setIsEditingName(true);\n  };\n\n  // Cancel editing product name\n  const cancelEditingName = () => {\n    setIsEditingName(false);\n    setProductNameError('');\n    // Reset to original name from product details\n    if (productDetails && productDetails.product_name) {\n      setProductName(productDetails.product_name);\n    } else if (productDetails && productDetails.fabric_definition_data) {\n      setProductName(productDetails.fabric_definition_data.fabric_name);\n    }\n  };\n\n  // Save updated product name\n  const saveProductName = async () => {\n    // Validate product name\n    if (!productName.trim()) {\n      setProductNameError('Product name cannot be empty');\n      return;\n    }\n    setProductNameError('');\n    setLoading(true);\n    try {\n      // First, get the current cutting record data\n      const currentRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\n\n      // Create a payload with all the required fields, including details\n      const payload = {\n        fabric_definition: currentRecord.data.fabric_definition,\n        cutting_date: currentRecord.data.cutting_date,\n        product_name: productName,\n        details: currentRecord.data.details // Include existing details without modification\n      };\n\n      // Update the product name in the backend\n      await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\n\n      // Update local state\n      setProductDetails({\n        ...productDetails,\n        product_name: productName\n      });\n      setIsEditingName(false);\n      setSuccessMsg('Product name updated successfully');\n\n      // Clear success message after 3 seconds\n      setTimeout(() => {\n        setSuccessMsg('');\n      }, 3000);\n    } catch (err) {\n      console.error('Error updating product name:', err);\n\n      // Check if the product name was actually updated despite the error\n      try {\n        const updatedRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\n        if (updatedRecord.data.product_name === productName) {\n          // If the name was updated successfully despite the error, show success message\n          setProductDetails({\n            ...productDetails,\n            product_name: productName\n          });\n          setIsEditingName(false);\n          setSuccessMsg('Product name updated successfully');\n\n          // Clear success message after 3 seconds\n          setTimeout(() => {\n            setSuccessMsg('');\n          }, 3000);\n          return;\n        }\n      } catch (checkErr) {\n        // If we can't check, just show the original error\n        console.error('Error checking product name update:', checkErr);\n      }\n\n      // Show error message if the name wasn't updated\n      setError('Failed to update product name. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Start editing prices\n  const startEditingPrices = () => {\n    setEditManufacturePrice(manufacturePrice);\n    setEditSellingPrice(sellingPrice);\n    setIsEditingPrices(true);\n    setPriceEditErrors({});\n  };\n\n  // Cancel editing prices\n  const cancelEditingPrices = () => {\n    setIsEditingPrices(false);\n    setEditManufacturePrice('');\n    setEditSellingPrice('');\n    setPriceEditErrors({});\n  };\n\n  // Validate price editing form\n  const validatePriceEdit = () => {\n    const errors = {};\n\n    // Validate manufacture price\n    if (!editManufacturePrice || editManufacturePrice.trim() === '') {\n      errors.editManufacturePrice = \"Manufacture price is required\";\n    } else if (parseFloat(editManufacturePrice) <= 0) {\n      errors.editManufacturePrice = \"Manufacture price must be greater than zero\";\n    } else if (isNaN(parseFloat(editManufacturePrice))) {\n      errors.editManufacturePrice = \"Manufacture price must be a valid number\";\n    }\n\n    // Validate selling price\n    if (!editSellingPrice || editSellingPrice.trim() === '') {\n      errors.editSellingPrice = \"Selling price is required\";\n    } else if (parseFloat(editSellingPrice) <= 0) {\n      errors.editSellingPrice = \"Selling price must be greater than zero\";\n    } else if (isNaN(parseFloat(editSellingPrice))) {\n      errors.editSellingPrice = \"Selling price must be a valid number\";\n    } else if (parseFloat(editSellingPrice) < parseFloat(editManufacturePrice)) {\n      errors.editSellingPrice = \"Selling price should be greater than or equal to manufacture price\";\n    }\n    setPriceEditErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Save updated prices\n  const savePriceChanges = async () => {\n    if (!validatePriceEdit()) {\n      return;\n    }\n    if (!finishedProductId) {\n      setError('Could not find finished product record');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      // Update the prices using the PATCH endpoint\n      const updateData = {\n        manufacture_price: parseFloat(editManufacturePrice),\n        selling_price: parseFloat(editSellingPrice)\n      };\n      await axios.patch(`http://localhost:8000/api/finished_product/update/${finishedProductId}/`, updateData);\n\n      // Update local state\n      setManufacturePrice(editManufacturePrice);\n      setSellingPrice(editSellingPrice);\n      setIsEditingPrices(false);\n      setSuccessMsg('Prices updated successfully');\n\n      // Clear success message after 3 seconds\n      setTimeout(() => {\n        setSuccessMsg('');\n      }, 3000);\n    } catch (err) {\n      console.error('Error updating prices:', err);\n      const errMsg = err.response && err.response.data ? typeof err.response.data === 'object' ? JSON.stringify(err.response.data) : err.response.data : \"Failed to update prices. Please try again.\";\n      setError(errMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Generate PDF report for the product\n  const generateProductReport = () => {\n    setPdfLoading(true);\n    try {\n      // Create PDF document\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes\n      const titleFontSize = 16;\n      const headingFontSize = 12;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add header\n      doc.setFontSize(titleFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Product Report', 105, 20, {\n        align: 'center'\n      });\n\n      // Add product name\n      const pdfProductName = productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`);\n      doc.setFontSize(headingFontSize);\n      doc.text(pdfProductName, 105, 30, {\n        align: 'center'\n      });\n\n      // Add approval date\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n      doc.text(`Approval Date: ${new Date().toLocaleDateString()}`, 105, 40, {\n        align: 'center'\n      });\n\n      // Add horizontal line\n      doc.setDrawColor(200, 200, 200);\n      doc.line(20, 45, 190, 45);\n\n      // Start Y position for content\n      let yPos = 55;\n\n      // Add pricing information\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Pricing Information', 20, yPos);\n      yPos += 10;\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n      doc.text(`Manufacture Price: LKR ${manufacturePrice}`, 25, yPos);\n      yPos += 7;\n      doc.text(`Selling Price: LKR ${sellingPrice}`, 25, yPos);\n      yPos += 7;\n      doc.text(`Profit Margin: ${profitMargin}%`, 25, yPos);\n      yPos += 15;\n\n      // Add size distribution\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Size Distribution', 20, yPos);\n      yPos += 10;\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n\n      // Create a table for size distribution\n      const sizes = Object.entries(sizeQuantities);\n      const sizeHeaders = ['Size', 'Quantity', 'Percentage'];\n      const totalQuantity = sizes.reduce((sum, [_, qty]) => sum + qty, 0);\n\n      // Draw table headers\n      doc.setFont('helvetica', 'bold');\n      doc.text(sizeHeaders[0], 25, yPos);\n      doc.text(sizeHeaders[1], 60, yPos);\n      doc.text(sizeHeaders[2], 95, yPos);\n      yPos += 7;\n\n      // Draw table rows\n      doc.setFont('helvetica', 'normal');\n      sizes.forEach(([size, quantity]) => {\n        const percentage = totalQuantity > 0 ? (quantity / totalQuantity * 100).toFixed(1) : '0.0';\n        doc.text(size.toUpperCase(), 25, yPos);\n        doc.text(quantity.toString(), 60, yPos);\n        doc.text(`${percentage}%`, 95, yPos);\n        yPos += 7;\n      });\n\n      // Add total row\n      doc.setFont('helvetica', 'bold');\n      doc.text('Total', 25, yPos);\n      doc.text(totalQuantity.toString(), 60, yPos);\n      doc.text('100.0%', 95, yPos);\n      yPos += 15;\n\n      // Add color information\n      if (fabricDetails.length > 0) {\n        doc.setFontSize(headingFontSize);\n        doc.setFont('helvetica', 'bold');\n        doc.text('Color Information', 20, yPos);\n        yPos += 10;\n        doc.setFontSize(normalFontSize);\n        doc.setFont('helvetica', 'normal');\n        fabricDetails.forEach((detail, index) => {\n          var _detail$fabric_varian;\n          const colorName = ((_detail$fabric_varian = detail.fabric_variant_data) === null || _detail$fabric_varian === void 0 ? void 0 : _detail$fabric_varian.color_name) || detail.color || 'N/A';\n          doc.text(`Color ${index + 1}: ${colorName}`, 25, yPos);\n          yPos += 7;\n        });\n        yPos += 8;\n      }\n\n      // Add product notes if available\n      if (productNotes) {\n        doc.setFontSize(headingFontSize);\n        doc.setFont('helvetica', 'bold');\n        doc.text('Product Notes', 20, yPos);\n        yPos += 10;\n        doc.setFontSize(normalFontSize);\n        doc.setFont('helvetica', 'normal');\n\n        // Split notes into multiple lines if needed\n        const splitNotes = doc.splitTextToSize(productNotes, 160);\n        doc.text(splitNotes, 25, yPos);\n        yPos += splitNotes.length * 7 + 8;\n      }\n\n      // Add image information\n      if (existingImageUrls && existingImageUrls.length > 0) {\n        doc.setFontSize(headingFontSize);\n        doc.setFont('helvetica', 'bold');\n        doc.text('Product Images', 20, yPos);\n        yPos += 10;\n        doc.setFontSize(normalFontSize);\n        doc.setFont('helvetica', 'normal');\n        doc.text(`Number of Images: ${existingImageUrls.length}`, 25, yPos);\n        yPos += 7;\n        doc.text('Note: Images can be viewed in the system', 25, yPos);\n        yPos += 15;\n      }\n\n      // Add footer\n      doc.setFontSize(smallFontSize);\n      doc.setFont('helvetica', 'italic');\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      doc.text('Pri Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\n      doc.save(`Product_Report_${cleanProductName}_${new Date().toISOString().slice(0, 10)}.pdf`);\n      setPdfLoading(false);\n      setShowPdfModal(false);\n    } catch (error) {\n      console.error(\"Error generating PDF:\", error);\n      setError(`Failed to generate PDF: ${error.message}`);\n      setPdfLoading(false);\n      setShowPdfModal(false);\n    }\n  };\n\n  // Loading spinner\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex justify-content-center align-items-center\",\n    style: {\n      height: \"100vh\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Spinner, {\n      animation: \"border\",\n      role: \"status\",\n      variant: \"primary\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"visually-hidden\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 743,\n    columnNumber: 5\n  }, this);\n\n  // Render color swatch\n  const renderColorSwatch = color => {\n    if (!color) return null;\n\n    // For debugging - log the color value we're receiving\n    console.log('Color value received:', color);\n\n    // SIMPLIFIED APPROACH: Directly use the color value if it looks like a hex code\n    let bgColor;\n\n    // If it starts with #, use it directly\n    if (color.startsWith('#')) {\n      bgColor = color;\n    }\n    // If it looks like a hex code without #, add the #\n    else if (/^[0-9A-Fa-f]{6}$/.test(color) || /^[0-9A-Fa-f]{3}$/.test(color)) {\n      bgColor = `#${color}`;\n    }\n    // For named colors like \"Black\", \"Red\", etc.\n    else {\n      // Common color names mapping\n      const colorMap = {\n        'red': '#dc3545',\n        'blue': '#0d6efd',\n        'green': '#198754',\n        'yellow': '#ffc107',\n        'black': '#212529',\n        'white': '#f8f9fa',\n        'purple': '#6f42c1',\n        'orange': '#fd7e14',\n        'pink': '#d63384',\n        'brown': '#8B4513',\n        'gray': '#6c757d'\n      };\n\n      // Try to get from color map or use the name directly\n      bgColor = colorMap[color.toLowerCase()] || color;\n    }\n    return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n      placement: \"top\",\n      overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n        className: \"custom-tooltip\",\n        children: color\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 18\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"color-swatch\",\n        style: {\n          backgroundColor: bgColor\n        },\n        \"data-color\": color\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 790,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render size quantity bars\n  const renderSizeQuantityBars = () => {\n    const sizes = Object.entries(sizeQuantities);\n    const maxQuantity = Math.max(...sizes.map(([_, qty]) => qty));\n    return sizes.map(([size, quantity]) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: size.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [quantity, \" pcs\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n        now: maxQuantity ? quantity / maxQuantity * 100 : 0,\n        variant: quantity > 0 ? \"info\" : \"light\",\n        className: \"size-quantity-bar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 814,\n        columnNumber: 9\n      }, this)]\n    }, size, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this));\n  };\n\n  // Confirmation Modal\n  const ConfirmationModal = () => /*#__PURE__*/_jsxDEV(Modal, {\n    show: showConfirmModal,\n    onHide: handleCancelConfirmation,\n    centered: true,\n    className: \"confirmation-modal\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: \"Confirm Product Approval\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 831,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Are you sure you want to approve this product with the following details?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        bordered: true,\n        hover: true,\n        size: \"sm\",\n        className: \"mt-3\",\n        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Manufacture Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [\"LKR \", manufacturePrice]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Selling Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [\"LKR \", sellingPrice]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Profit Margin:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [profitMargin, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 13\n          }, this), productNotes && /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Notes:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: productNotes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 9\n      }, this), imagePreviewUrls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Product Image:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Image, {\n          src: imagePreviewUrls[0],\n          alt: \"Product\",\n          thumbnail: true,\n          style: {\n            maxHeight: \"100px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 834,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: handleCancelConfirmation,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 877,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"success\",\n        onClick: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 11\n        }, this), \"Confirm Approval\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 876,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 825,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 10,\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow product-card slide-in\",\n            style: {\n              backgroundColor: \"#D9EDFB\",\n              borderRadius: \"10px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-center mb-3\",\n                children: \"Approve Finished Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: isEditingName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-0 me-2 flex-grow-1\",\n                    style: {\n                      maxWidth: '300px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      value: productName,\n                      onChange: e => setProductName(e.target.value),\n                      isInvalid: !!productNameError,\n                      placeholder: \"Enter product name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 903,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: productNameError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 910,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"success\",\n                    size: \"sm\",\n                    className: \"me-1\",\n                    onClick: saveProductName,\n                    children: /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"secondary\",\n                    size: \"sm\",\n                    onClick: cancelEditingName,\n                    children: /*#__PURE__*/_jsxDEV(FaUndo, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 927,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-center text-muted mb-0 me-2\",\n                    children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    onClick: startEditingName,\n                    children: \"Edit Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 17\n              }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"danger\",\n                className: \"mb-4 fade-in\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 952,\n                      columnNumber: 25\n                    }, this), error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 951,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: fetchProductData,\n                    children: \"Retry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this), successMsg && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"success\",\n                className: \"mb-4 fade-in\",\n                children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 21\n                }, this), successMsg]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 19\n              }, this), isApproved ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 bg-white rounded mb-3 slide-in\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-center mb-4 text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 23\n                  }, this), \"Product Already Approved\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: isEditingName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-center align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-0 me-2 flex-grow-1\",\n                      style: {\n                        maxWidth: '300px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        value: productName,\n                        onChange: e => setProductName(e.target.value),\n                        isInvalid: !!productNameError,\n                        placeholder: \"Enter product name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 984,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                        type: \"invalid\",\n                        children: productNameError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 983,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      onClick: saveProductName,\n                      children: /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1001,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"secondary\",\n                      size: \"sm\",\n                      onClick: cancelEditingName,\n                      children: /*#__PURE__*/_jsxDEV(FaUndo, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1008,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-center align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"mb-0 me-2\",\n                      children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: startEditingName,\n                      children: \"Edit Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4 bg-white rounded mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Row, {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-0\",\n                          children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1033,\n                            columnNumber: 52\n                          }, this), \"Pricing Information\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1033,\n                          columnNumber: 31\n                        }, this), !isEditingPrices && /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-primary\",\n                          size: \"sm\",\n                          onClick: startEditingPrices,\n                          children: \"Edit Prices\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1035,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1032,\n                        columnNumber: 29\n                      }, this), isEditingPrices ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-3 bg-light rounded\",\n                        children: /*#__PURE__*/_jsxDEV(Form, {\n                          children: [/*#__PURE__*/_jsxDEV(Row, {\n                            children: [/*#__PURE__*/_jsxDEV(Col, {\n                              md: 6,\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                className: \"mb-3\",\n                                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                    children: \"Manufacture Price (LKR):\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1051,\n                                    columnNumber: 53\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1051,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                                  type: \"number\",\n                                  step: \"0.01\",\n                                  min: \"0\",\n                                  value: editManufacturePrice,\n                                  onChange: e => setEditManufacturePrice(e.target.value),\n                                  isInvalid: !!priceEditErrors.editManufacturePrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1052,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                                  type: \"invalid\",\n                                  children: priceEditErrors.editManufacturePrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1060,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1050,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1049,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Col, {\n                              md: 6,\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                className: \"mb-3\",\n                                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                    children: \"Selling Price (LKR):\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1067,\n                                    columnNumber: 53\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1067,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                                  type: \"number\",\n                                  step: \"0.01\",\n                                  min: \"0\",\n                                  value: editSellingPrice,\n                                  onChange: e => setEditSellingPrice(e.target.value),\n                                  isInvalid: !!priceEditErrors.editSellingPrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1068,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                                  type: \"invalid\",\n                                  children: priceEditErrors.editSellingPrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1076,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1066,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1065,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1048,\n                            columnNumber: 35\n                          }, this), editManufacturePrice && editSellingPrice && parseFloat(editManufacturePrice) > 0 && parseFloat(editSellingPrice) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-3 p-2 bg-info bg-opacity-10 rounded\",\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"New Profit Margin: \"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1085,\n                              columnNumber: 39\n                            }, this), ((parseFloat(editSellingPrice) - parseFloat(editManufacturePrice)) / parseFloat(editSellingPrice) * 100).toFixed(2), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1084,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex gap-2\",\n                            children: [/*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"success\",\n                              size: \"sm\",\n                              onClick: savePriceChanges,\n                              disabled: loading,\n                              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1097,\n                                columnNumber: 39\n                              }, this), \"Save Changes\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1091,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"secondary\",\n                              size: \"sm\",\n                              onClick: cancelEditingPrices,\n                              children: [/*#__PURE__*/_jsxDEV(FaUndo, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1105,\n                                columnNumber: 39\n                              }, this), \"Cancel\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1100,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1090,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1047,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1046,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(Table, {\n                        bordered: true,\n                        hover: true,\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Manufacture Price:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1115,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1115,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [\"LKR \", manufacturePrice]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1116,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1114,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Selling Price:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1119,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1119,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [\"LKR \", sellingPrice]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1120,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1118,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Profit Margin:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1123,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1123,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [profitMargin, \"%\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1124,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1122,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1113,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1112,\n                        columnNumber: 31\n                      }, this), productNotes && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1132,\n                            columnNumber: 54\n                          }, this), \"Notes\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1132,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-3 bg-light rounded\",\n                          children: productNotes\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1133,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1131,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"mb-3 mt-4\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTags, {\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1139,\n                          columnNumber: 55\n                        }, this), \"Colors\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1139,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-4\",\n                        children: fabricDetails.length > 0 ? fabricDetails.map((detail, index) => {\n                          var _detail$fabric_varian2, _detail$fabric_varian3;\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-2\",\n                            children: [renderColorSwatch(((_detail$fabric_varian2 = detail.fabric_variant_data) === null || _detail$fabric_varian2 === void 0 ? void 0 : _detail$fabric_varian2.color) || detail.color || 'gray'), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"ms-2\",\n                              children: ((_detail$fabric_varian3 = detail.fabric_variant_data) === null || _detail$fabric_varian3 === void 0 ? void 0 : _detail$fabric_varian3.color_name) || detail.color\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1145,\n                              columnNumber: 37\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1143,\n                            columnNumber: 35\n                          }, this);\n                        }) : /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted\",\n                          children: \"No color information available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1149,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1140,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(FaBoxOpen, {\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1153,\n                          columnNumber: 50\n                        }, this), \"Size Distribution\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1153,\n                        columnNumber: 29\n                      }, this), renderSizeQuantityBars(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-primary\",\n                          className: \"w-100\",\n                          onClick: openPdfModal,\n                          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1162,\n                            columnNumber: 33\n                          }, this), \"Generate Product Report\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1157,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1156,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1169,\n                          columnNumber: 50\n                        }, this), \"Product Images\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1169,\n                        columnNumber: 29\n                      }, this), existingImageUrls && existingImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"product-images-container\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"product-images-grid\",\n                          children: existingImageUrls.map((imageUrl, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `product-image-item ${index === activeImageIndex ? 'active' : ''}`,\n                            onClick: () => setActiveImageIndex(index),\n                            children: [/*#__PURE__*/_jsxDEV(Image, {\n                              src: imageUrl,\n                              alt: `Product ${index + 1}`,\n                              thumbnail: true,\n                              className: \"image-preview\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1179,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"image-number\",\n                              children: index + 1\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1185,\n                              columnNumber: 39\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1174,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1172,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"main-image-container mt-3\",\n                          children: /*#__PURE__*/_jsxDEV(Image, {\n                            src: existingImageUrls[activeImageIndex],\n                            alt: \"Product\",\n                            thumbnail: true,\n                            className: \"main-image-preview\",\n                            style: {\n                              maxHeight: \"250px\"\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1190,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1189,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1171,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-5 bg-light rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                          size: 60,\n                          className: \"text-secondary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1201,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"mt-3 text-muted\",\n                          children: \"No images available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1202,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1200,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1168,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"slide-in\",\n                children: /*#__PURE__*/_jsxDEV(Tabs, {\n                  id: \"product-approval-tabs\",\n                  activeKey: activeTab,\n                  onSelect: k => setActiveTab(k),\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Tab, {\n                    eventKey: \"details\",\n                    title: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1217,\n                        columnNumber: 60\n                      }, this), \"Product Details\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1217,\n                      columnNumber: 54\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(Row, {\n                      className: \"mt-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1220,\n                            columnNumber: 50\n                          }, this), \"Product Name\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1220,\n                          columnNumber: 29\n                        }, this), isEditingName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-4\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                            children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                              type: \"text\",\n                              value: productName,\n                              onChange: e => setProductName(e.target.value),\n                              isInvalid: !!productNameError,\n                              placeholder: \"Enter product name\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1224,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: productNameError\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1231,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1223,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mt-2\",\n                            children: [/*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"success\",\n                              size: \"sm\",\n                              className: \"me-1\",\n                              onClick: saveProductName,\n                              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1242,\n                                columnNumber: 37\n                              }, this), \" Save\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1236,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"secondary\",\n                              size: \"sm\",\n                              onClick: cancelEditingName,\n                              children: [/*#__PURE__*/_jsxDEV(FaUndo, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1249,\n                                columnNumber: 37\n                              }, this), \" Cancel\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1244,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1235,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1222,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-3 bg-light rounded mb-4 d-flex justify-content-between align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1255,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outline-primary\",\n                            size: \"sm\",\n                            onClick: startEditingName,\n                            children: \"Edit Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1260,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1254,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTags, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1270,\n                            columnNumber: 50\n                          }, this), \"Colors\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1270,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-4\",\n                          children: fabricDetails.length > 0 ? fabricDetails.map((detail, index) => {\n                            var _detail$fabric_varian4, _detail$fabric_varian5;\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"mb-2\",\n                              children: [renderColorSwatch(((_detail$fabric_varian4 = detail.fabric_variant_data) === null || _detail$fabric_varian4 === void 0 ? void 0 : _detail$fabric_varian4.color) || detail.color || 'gray'), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-2\",\n                                children: ((_detail$fabric_varian5 = detail.fabric_variant_data) === null || _detail$fabric_varian5 === void 0 ? void 0 : _detail$fabric_varian5.color_name) || detail.color\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1276,\n                                columnNumber: 37\n                              }, this)]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1274,\n                              columnNumber: 35\n                            }, this);\n                          }) : /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-muted\",\n                            children: \"No color information available\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1280,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1271,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaBoxOpen, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1284,\n                            columnNumber: 50\n                          }, this), \"Size Distribution\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1284,\n                          columnNumber: 29\n                        }, this), renderSizeQuantityBars()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1219,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                              className: \"me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1290,\n                              columnNumber: 52\n                            }, this), \"Product Images\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1290,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-muted mb-3\",\n                            children: [\"Upload up to 10 images of the product (Current: \", productImages.length, \"/10)\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1291,\n                            columnNumber: 31\n                          }, this), imagePreviewUrls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"product-images-container mb-4\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"product-images-grid\",\n                              children: imagePreviewUrls.map((previewUrl, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `product-image-item ${index === activeImageIndex ? 'active' : ''}`,\n                                onClick: () => setActiveImageIndex(index),\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"image-actions\",\n                                  children: /*#__PURE__*/_jsxDEV(Button, {\n                                    variant: \"danger\",\n                                    size: \"sm\",\n                                    className: \"btn-remove-image\",\n                                    onClick: e => {\n                                      e.stopPropagation();\n                                      removeImage(index);\n                                    },\n                                    children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1313,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1304,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1303,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Image, {\n                                  src: previewUrl,\n                                  alt: `Preview ${index + 1}`,\n                                  thumbnail: true,\n                                  className: \"image-preview\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1316,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"image-number\",\n                                  children: index + 1\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1322,\n                                  columnNumber: 41\n                                }, this)]\n                              }, index, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1298,\n                                columnNumber: 39\n                              }, this))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1296,\n                              columnNumber: 35\n                            }, this), imagePreviewUrls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"main-image-container mt-3\",\n                              children: /*#__PURE__*/_jsxDEV(Image, {\n                                src: imagePreviewUrls[activeImageIndex],\n                                alt: \"Product Preview\",\n                                thumbnail: true,\n                                className: \"main-image-preview\",\n                                style: {\n                                  maxHeight: \"250px\"\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1329,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1328,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1295,\n                            columnNumber: 33\n                          }, this), isUploading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"p-4 bg-light rounded text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                              className: \"mb-3\",\n                              children: \"Uploading Images\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1344,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                              now: uploadProgress,\n                              label: `${Math.round(uploadProgress)}%`,\n                              variant: \"info\",\n                              animated: true,\n                              className: \"mb-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1345,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-muted\",\n                              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                                className: \"me-2 text-primary\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1353,\n                                columnNumber: 37\n                              }, this), \"Uploading \", productImages.length, \" images...\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1352,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1343,\n                            columnNumber: 33\n                          }, this) : productImages.length < 10 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            ...getRootProps(),\n                            className: `image-upload-container ${isDragActive ? 'active' : ''}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                              ...getInputProps(),\n                              multiple: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1360,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-center\",\n                              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                                size: 40,\n                                className: \"mb-3 text-primary\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1362,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                children: \"Drag & drop product images here, or click to select\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1363,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                className: \"text-muted small\",\n                                children: \"Supported formats: JPEG, PNG, GIF (Max: 5MB each)\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1364,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                className: \"text-muted small\",\n                                children: \"You can select multiple images at once (Max: 10)\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1365,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1361,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1359,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"file\",\n                            ref: fileInputRef,\n                            onChange: handleImageChange,\n                            accept: \"image/*\",\n                            multiple: true,\n                            style: {\n                              display: 'none'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1371,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1289,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1288,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1218,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1217,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                    eventKey: \"pricing\",\n                    title: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1384,\n                        columnNumber: 60\n                      }, this), \"Pricing\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1384,\n                      columnNumber: 54\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(Form, {\n                      onSubmit: handleFormSubmit,\n                      className: \"mt-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Row, {\n                        children: [/*#__PURE__*/_jsxDEV(Col, {\n                          md: 6,\n                          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Manufacture Price (LKR):\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1390,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                                placement: \"top\",\n                                overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                                  children: \"The cost to manufacture this product\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1393,\n                                  columnNumber: 46\n                                }, this),\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"ms-2 text-muted\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1395,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1391,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1389,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                              type: \"number\",\n                              step: \"0.01\",\n                              min: \"0\",\n                              value: manufacturePrice,\n                              onChange: e => setManufacturePrice(e.target.value),\n                              isInvalid: !!validationErrors.manufacturePrice,\n                              required: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1398,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: validationErrors.manufacturePrice\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1407,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1388,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Selling Price (LKR):\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1414,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                                placement: \"top\",\n                                overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                                  children: \"The price at which this product will be sold\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1417,\n                                  columnNumber: 46\n                                }, this),\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"ms-2 text-muted\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1419,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1415,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1413,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                              type: \"number\",\n                              step: \"0.01\",\n                              min: \"0\",\n                              value: sellingPrice,\n                              onChange: e => setSellingPrice(e.target.value),\n                              isInvalid: !!validationErrors.sellingPrice,\n                              required: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1422,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: validationErrors.sellingPrice\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1431,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1412,\n                            columnNumber: 31\n                          }, this), manufacturePrice && sellingPrice && parseFloat(manufacturePrice) > 0 && parseFloat(sellingPrice) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-4 p-3 bg-light rounded\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex justify-content-between align-items-center mb-2\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Profit Margin:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1439,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1439,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                                bg: profitMargin < 10 ? \"danger\" : profitMargin < 20 ? \"warning\" : \"success\",\n                                children: [/*#__PURE__*/_jsxDEV(FaPercentage, {\n                                  className: \"me-1\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1445,\n                                  columnNumber: 39\n                                }, this), profitMargin, \"%\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1440,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1438,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"profit-margin-indicator\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1449,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex justify-content-between mt-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                                children: \"Low\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1451,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                                children: \"High\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1452,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1450,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1437,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1387,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Col, {\n                          md: 6,\n                          children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Product Notes:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1461,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                                placement: \"top\",\n                                overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                                  children: \"Additional information about this product\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1464,\n                                  columnNumber: 46\n                                }, this),\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"ms-2 text-muted\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1466,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1462,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1460,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                              as: \"textarea\",\n                              rows: 5,\n                              value: productNotes,\n                              onChange: e => setProductNotes(e.target.value),\n                              isInvalid: !!validationErrors.productNotes,\n                              placeholder: \"Enter any additional notes about this product...\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1469,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: validationErrors.productNotes\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1477,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                              className: \"text-muted\",\n                              children: [productNotes ? 500 - productNotes.length : 500, \" characters remaining\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1480,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1459,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1458,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1386,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-grid gap-2 mt-4\",\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          type: \"submit\",\n                          className: \"btn-approve\",\n                          size: \"lg\",\n                          children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1489,\n                            columnNumber: 31\n                          }, this), \"Approve Product\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1488,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1487,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1384,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 892,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 891,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1505,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: closePdfModal,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n            className: \"text-danger me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1511,\n            columnNumber: 13\n          }, this), \"Generate Product Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1510,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Are you sure you want to generate a PDF report for this product?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-light p-3 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Product:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1518,\n              columnNumber: 33\n            }, this), \" \", productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Manufacture Price:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 33\n            }, this), \" LKR \", manufacturePrice]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Selling Price:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1522,\n              columnNumber: 33\n            }, this), \" LKR \", sellingPrice]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Profit Margin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1523,\n              columnNumber: 33\n            }, this), \" \", profitMargin, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1523,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1517,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: closePdfModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1527,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: generateProductReport,\n          disabled: pdfLoading,\n          children: pdfLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1537,\n              columnNumber: 17\n            }, this), \"Generating...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1549,\n              columnNumber: 17\n            }, this), \"Generate PDF\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1526,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1508,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ApproveFinishedProduct, \"5KckY46D3LkO17wQKFjKYOi3e9Y=\", false, function () {\n  return [useParams, useNavigate, useDropzone];\n});\n_c = ApproveFinishedProduct;\nexport default ApproveFinishedProduct;\nvar _c;\n$RefreshReg$(_c, \"ApproveFinishedProduct\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "axios", "useParams", "useNavigate", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Row", "Col", "Spinner", "Image", "Modal", "ProgressBar", "Badge", "Tabs", "Tab", "Table", "OverlayTrigger", "<PERSON><PERSON><PERSON>", "FaCheck", "FaUpload", "FaImage", "FaTags", "FaInfoCircle", "FaMoneyBillWave", "FaArrowRight", "FaPercentage", "FaBoxOpen", "FaClipboardList", "FaTrash", "FaUndo", "FaExclamationTriangle", "FaFilePdf", "FaDownload", "useDropzone", "RoleBasedNavBar", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ApproveFinishedProduct", "_s", "id", "navigate", "fileInputRef", "manufacturePrice", "setManufacturePrice", "sellingPrice", "setSellingPrice", "productNotes", "setProductNotes", "error", "setError", "successMsg", "setSuccessMsg", "isApproved", "setIsApproved", "loading", "setLoading", "productImages", "setProductImages", "imagePreviewUrls", "setImagePreviewUrls", "existingImageUrls", "setExistingImageUrls", "isDragging", "setIsDragging", "activeImageIndex", "setActiveImageIndex", "uploadProgress", "setUploadProgress", "isUploading", "setIsUploading", "productDetails", "setProductDetails", "fabricDetails", "setFabricDetails", "sizeQuantities", "setSizeQuantities", "xs", "s", "m", "l", "xl", "activeTab", "setActiveTab", "showConfirmModal", "setShowConfirmModal", "showPdfModal", "setShowPdfModal", "pdfLoading", "setPdfLoading", "validationErrors", "setValidationErrors", "profitMargin", "setProfitMargin", "productName", "setProductName", "isEditingName", "setIsEditingName", "productNameError", "setProductNameError", "isEditingPrices", "setIsEditingPrices", "editManufacturePrice", "setEditManufacturePrice", "editSellingPrice", "setEditSellingPrice", "priceEditErrors", "setPriceEditErrors", "finishedProductId", "setFinishedProductId", "mPrice", "parseFloat", "sPrice", "margin", "toFixed", "retryCount", "setRetryCount", "maxRetries", "fetchProductData", "approvalRes", "get", "cuttingRes", "data", "is_approved", "manufacture_price", "selling_price", "finished_product_id", "product_images", "Array", "isArray", "product_image", "notes", "product_name", "fabric_definition_data", "fabric_name", "details", "length", "console", "log", "sizes", "for<PERSON>ach", "detail", "err", "errorMessage", "response", "status", "statusText", "request", "prev", "setTimeout", "handleImageChange", "e", "files", "from", "target", "processImageFiles", "newImages", "newPreviewUrls", "file", "type", "match", "size", "push", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "triggerFileInput", "current", "click", "onDrop", "acceptedFiles", "getRootProps", "getInputProps", "isDragActive", "accept", "maxFiles", "removeImage", "index", "splice", "Math", "max", "setActiveImage", "validateForm", "errors", "trim", "isNaN", "Object", "keys", "handleFormSubmit", "preventDefault", "handleSubmit", "formData", "FormData", "append", "image", "post", "headers", "onUploadProgress", "progressEvent", "percentCompleted", "round", "loaded", "total", "message", "errMsg", "JSON", "stringify", "handleCancelConfirmation", "openPdfModal", "closePdfModal", "startEditingName", "cancelEditingName", "saveProductName", "currentRecord", "payload", "fabric_definition", "cutting_date", "put", "updatedRecord", "checkErr", "startEditingPrices", "cancelEditingPrices", "validatePriceEdit", "savePriceChanges", "updateData", "patch", "generateProductReport", "doc", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "pdfProductName", "Date", "toLocaleDateString", "setDrawColor", "line", "yPos", "entries", "sizeHeaders", "totalQuantity", "reduce", "sum", "_", "qty", "quantity", "percentage", "toUpperCase", "toString", "_detail$fabric_varian", "colorName", "fabric_variant_data", "color_name", "color", "splitNotes", "splitTextToSize", "toLocaleString", "cleanProductName", "replace", "save", "toISOString", "slice", "className", "style", "height", "children", "animation", "role", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderColorSwatch", "bgColor", "startsWith", "test", "colorMap", "toLowerCase", "placement", "overlay", "backgroundColor", "renderSizeQuantityBars", "maxQuantity", "map", "now", "ConfirmationModal", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "bordered", "hover", "src", "alt", "thumbnail", "maxHeight", "Footer", "onClick", "md", "lg", "borderRadius", "Group", "max<PERSON><PERSON><PERSON>", "Control", "value", "onChange", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "Label", "step", "min", "disabled", "_detail$fabric_varian2", "_detail$fabric_varian3", "imageUrl", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "_detail$fabric_varian4", "_detail$fabric_varian5", "previewUrl", "stopPropagation", "label", "animated", "multiple", "ref", "display", "onSubmit", "required", "bg", "as", "rows", "Text", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/ApproveFinishedProduct.js"], "sourcesContent": ["// src/pages/ApproveFinishedProduct.jsx\r\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { Card, Form, Button, Alert, Row, Col, Spinner, Image, Modal, ProgressBar, Badge, Tabs, Tab, Table, OverlayTrigger, Tooltip } from 'react-bootstrap';\r\nimport {\r\n  FaCheck, FaUpload, FaImage, FaTags, FaInfoCircle, FaMoneyBillWave,\r\n  FaArrowRight, FaPercentage, FaBoxOpen, FaClipboardList,\r\n  FaTrash, FaUndo, FaExclamationTriangle, FaFilePdf, FaDownload\r\n} from 'react-icons/fa';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport './ApproveFinishedProduct.css';\r\nimport { jsPDF } from 'jspdf';\r\n// No need to import uploadMultipleImages as we're using FormData directly\r\n\r\nconst ApproveFinishedProduct = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Basic form state\r\n  const [manufacturePrice, setManufacturePrice] = useState('');\r\n  const [sellingPrice, setSellingPrice] = useState('');\r\n  const [productNotes, setProductNotes] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [successMsg, setSuccessMsg] = useState('');\r\n  const [isApproved, setIsApproved] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Image handling state\r\n  const [productImages, setProductImages] = useState([]);\r\n  const [imagePreviewUrls, setImagePreviewUrls] = useState([]);\r\n  const [existingImageUrls, setExistingImageUrls] = useState([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [activeImageIndex, setActiveImageIndex] = useState(0);\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n\r\n  // Product details state\r\n  const [productDetails, setProductDetails] = useState(null);\r\n  const [fabricDetails, setFabricDetails] = useState([]);\r\n  const [sizeQuantities, setSizeQuantities] = useState({\r\n    xs: 0, s: 0, m: 0, l: 0, xl: 0\r\n  });\r\n\r\n  // UI state\r\n  const [activeTab, setActiveTab] = useState('details');\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [pdfLoading, setPdfLoading] = useState(false);\r\n  const [validationErrors, setValidationErrors] = useState({});\r\n  const [profitMargin, setProfitMargin] = useState(0);\r\n\r\n  // Product name editing state\r\n  const [productName, setProductName] = useState('');\r\n  const [isEditingName, setIsEditingName] = useState(false);\r\n  const [productNameError, setProductNameError] = useState('');\r\n\r\n  // Price editing state for approved products\r\n  const [isEditingPrices, setIsEditingPrices] = useState(false);\r\n  const [editManufacturePrice, setEditManufacturePrice] = useState('');\r\n  const [editSellingPrice, setEditSellingPrice] = useState('');\r\n  const [priceEditErrors, setPriceEditErrors] = useState({});\r\n  const [finishedProductId, setFinishedProductId] = useState(null);\r\n\r\n  // Calculate profit margin whenever prices change\r\n  useEffect(() => {\r\n    if (manufacturePrice && sellingPrice) {\r\n      const mPrice = parseFloat(manufacturePrice);\r\n      const sPrice = parseFloat(sellingPrice);\r\n\r\n      if (mPrice > 0 && sPrice > 0) {\r\n        const margin = ((sPrice - mPrice) / sPrice) * 100;\r\n        setProfitMargin(margin.toFixed(2));\r\n      }\r\n    }\r\n  }, [manufacturePrice, sellingPrice]);\r\n\r\n  // State for retry mechanism\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const maxRetries = 3;\r\n\r\n  // Function to fetch product data with retry mechanism\r\n  const fetchProductData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n\r\n      // Fetch approval status\r\n      const approvalRes = await axios.get(`http://localhost:8000/api/finished_product/status/${id}/`);\r\n\r\n      // Fetch cutting record details\r\n      const cuttingRes = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n\r\n      if (approvalRes.data && approvalRes.data.is_approved) {\r\n        setIsApproved(true);\r\n        setManufacturePrice(approvalRes.data.manufacture_price);\r\n        setSellingPrice(approvalRes.data.selling_price);\r\n        setFinishedProductId(approvalRes.data.finished_product_id);\r\n\r\n        // Set existing image URLs if available\r\n        if (approvalRes.data.product_images && Array.isArray(approvalRes.data.product_images)) {\r\n          setExistingImageUrls(approvalRes.data.product_images);\r\n        } else if (approvalRes.data.product_image) {\r\n          // For backward compatibility with single image\r\n          setExistingImageUrls([approvalRes.data.product_image]);\r\n        }\r\n\r\n        if (approvalRes.data.notes) {\r\n          setProductNotes(approvalRes.data.notes);\r\n        }\r\n      }\r\n\r\n      if (cuttingRes.data) {\r\n        setProductDetails(cuttingRes.data);\r\n\r\n        // Set product name from API response\r\n        if (cuttingRes.data.product_name) {\r\n          setProductName(cuttingRes.data.product_name);\r\n        } else if (cuttingRes.data.fabric_definition_data) {\r\n          // If no product name, use fabric name as default\r\n          setProductName(cuttingRes.data.fabric_definition_data.fabric_name);\r\n        }\r\n\r\n        // Extract fabric details\r\n        if (cuttingRes.data.details && cuttingRes.data.details.length > 0) {\r\n          // Debug: Log the fabric details to see what color data we're getting\r\n          console.log('Fabric details from API:', cuttingRes.data.details);\r\n\r\n          setFabricDetails(cuttingRes.data.details);\r\n\r\n          // Calculate size quantities\r\n          const sizes = {xs: 0, s: 0, m: 0, l: 0, xl: 0};\r\n          cuttingRes.data.details.forEach(detail => {\r\n            sizes.xs += detail.xs || 0;\r\n            sizes.s += detail.s || 0;\r\n            sizes.m += detail.m || 0;\r\n            sizes.l += detail.l || 0;\r\n            sizes.xl += detail.xl || 0;\r\n          });\r\n          setSizeQuantities(sizes);\r\n        }\r\n      }\r\n\r\n      // Reset retry count on success\r\n      setRetryCount(0);\r\n    } catch (err) {\r\n      console.error(\"Failed to fetch product data:\", err);\r\n\r\n      // Provide more detailed error message\r\n      const errorMessage = err.response\r\n        ? `Error: ${err.response.status} - ${err.response.statusText}`\r\n        : err.request\r\n          ? \"No response received from server. Check if the backend is running.\"\r\n          : \"Failed to make request. Check your network connection.\";\r\n\r\n      setError(`Unable to fetch product data. ${errorMessage}`);\r\n\r\n      // Implement retry mechanism\r\n      if (retryCount < maxRetries) {\r\n        setRetryCount(prev => prev + 1);\r\n        setTimeout(() => {\r\n          fetchProductData();\r\n        }, 2000); // Wait 2 seconds before retrying\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [id, retryCount]);\r\n\r\n  // Fetch product details and approval status\r\n  useEffect(() => {\r\n    fetchProductData();\r\n  }, [fetchProductData]);\r\n\r\n  // Handle image selection from file input\r\n  const handleImageChange = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    if (files.length > 0) {\r\n      processImageFiles(files);\r\n    }\r\n  };\r\n\r\n  // Process the selected image files\r\n  const processImageFiles = useCallback((files) => {\r\n    // Check if adding these files would exceed the limit\r\n    if (productImages.length + files.length > 10) {\r\n      setError(`You can only upload up to 10 images. You already have ${productImages.length} images.`);\r\n      return;\r\n    }\r\n\r\n    const newImages = [];\r\n    const newPreviewUrls = [...imagePreviewUrls];\r\n\r\n    files.forEach(file => {\r\n      // Validate file type\r\n      if (!file.type.match('image.*')) {\r\n        setError('Please select image files only (JPEG, PNG, etc.)');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        setError('Each image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      newImages.push(file);\r\n\r\n      // Create a preview URL\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        newPreviewUrls.push(reader.result);\r\n        setImagePreviewUrls([...newPreviewUrls]);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    });\r\n\r\n    setProductImages([...productImages, ...newImages]);\r\n  }, [productImages, imagePreviewUrls]);\r\n\r\n  // Trigger file input click\r\n  const triggerFileInput = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  // Handle drag and drop functionality\r\n  const onDrop = useCallback((acceptedFiles) => {\r\n    if (acceptedFiles && acceptedFiles.length > 0) {\r\n      processImageFiles(acceptedFiles);\r\n    }\r\n  }, [processImageFiles]);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif']\r\n    },\r\n    maxFiles: 10\r\n  });\r\n\r\n  // Remove an uploaded image\r\n  const removeImage = (index) => {\r\n    const newImages = [...productImages];\r\n    const newPreviewUrls = [...imagePreviewUrls];\r\n\r\n    newImages.splice(index, 1);\r\n    newPreviewUrls.splice(index, 1);\r\n\r\n    setProductImages(newImages);\r\n    setImagePreviewUrls(newPreviewUrls);\r\n\r\n    // Adjust active index if needed\r\n    if (index === activeImageIndex) {\r\n      setActiveImageIndex(Math.max(0, index - 1));\r\n    } else if (index < activeImageIndex) {\r\n      setActiveImageIndex(activeImageIndex - 1);\r\n    }\r\n  };\r\n\r\n  // Set active image\r\n  const setActiveImage = (index) => {\r\n    setActiveImageIndex(index);\r\n  };\r\n\r\n  // Validate form inputs\r\n  const validateForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate manufacture price\r\n    if (!manufacturePrice || manufacturePrice.trim() === '') {\r\n      errors.manufacturePrice = \"Manufacture price is required\";\r\n    } else if (parseFloat(manufacturePrice) <= 0) {\r\n      errors.manufacturePrice = \"Manufacture price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(manufacturePrice))) {\r\n      errors.manufacturePrice = \"Manufacture price must be a valid number\";\r\n    }\r\n\r\n    // Validate selling price\r\n    if (!sellingPrice || sellingPrice.trim() === '') {\r\n      errors.sellingPrice = \"Selling price is required\";\r\n    } else if (parseFloat(sellingPrice) <= 0) {\r\n      errors.sellingPrice = \"Selling price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(sellingPrice))) {\r\n      errors.sellingPrice = \"Selling price must be a valid number\";\r\n    } else if (parseFloat(sellingPrice) < parseFloat(manufacturePrice)) {\r\n      errors.sellingPrice = \"Selling price should be greater than or equal to manufacture price\";\r\n    }\r\n\r\n    // Validate product notes (optional)\r\n    if (productNotes && productNotes.length > 500) {\r\n      errors.productNotes = \"Notes should be less than 500 characters\";\r\n    }\r\n\r\n    setValidationErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Show confirmation modal\r\n  const handleFormSubmit = (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    // Show confirmation modal\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  // Handle actual submission\r\n  const handleSubmit = async () => {\r\n    setError('');\r\n    setSuccessMsg('');\r\n    setShowConfirmModal(false);\r\n\r\n    try {\r\n      // Show loading state\r\n      setLoading(true);\r\n      setIsUploading(true);\r\n\r\n      // Create FormData object for API request\r\n      const formData = new FormData();\r\n      formData.append('cutting_record', id);\r\n      formData.append('manufacture_price', parseFloat(manufacturePrice));\r\n      formData.append('selling_price', parseFloat(sellingPrice));\r\n\r\n      if (productNotes) {\r\n        formData.append('notes', productNotes);\r\n      }\r\n\r\n      // Add images directly to the FormData if there are any\r\n      if (productImages && productImages.length > 0) {\r\n        setUploadProgress(0);\r\n\r\n        // Append each image to the FormData\r\n        productImages.forEach(image => {\r\n          formData.append('product_images', image);\r\n        });\r\n      }\r\n\r\n      // Make the API request with progress tracking\r\n      const response = await axios.post(\r\n        'http://localhost:8000/api/finished_product/approve/',\r\n        formData,\r\n        {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n          onUploadProgress: (progressEvent) => {\r\n            const percentCompleted = Math.round(\r\n              (progressEvent.loaded * 100) / progressEvent.total\r\n            );\r\n            setUploadProgress(percentCompleted);\r\n          }\r\n        }\r\n      );\r\n\r\n      setSuccessMsg(response.data.message || 'Product approved successfully!');\r\n      setIsUploading(false);\r\n\r\n      // Redirect after a delay\r\n      setTimeout(() => {\r\n        navigate('/approveproduct-list');\r\n      }, 2000);\r\n    } catch (err) {\r\n      console.error(\"Error approving finished product:\", err);\r\n      const errMsg = err.response && err.response.data\r\n        ? typeof err.response.data === 'object'\r\n          ? JSON.stringify(err.response.data)\r\n          : err.response.data\r\n        : \"Failed to approve finished product. Please try again.\";\r\n      setError(errMsg);\r\n      setIsUploading(false);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Cancel confirmation\r\n  const handleCancelConfirmation = () => {\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  // Open PDF modal\r\n  const openPdfModal = () => {\r\n    setShowPdfModal(true);\r\n  };\r\n\r\n  // Close PDF modal\r\n  const closePdfModal = () => {\r\n    setShowPdfModal(false);\r\n  };\r\n\r\n  // Start editing product name\r\n  const startEditingName = () => {\r\n    setIsEditingName(true);\r\n  };\r\n\r\n  // Cancel editing product name\r\n  const cancelEditingName = () => {\r\n    setIsEditingName(false);\r\n    setProductNameError('');\r\n    // Reset to original name from product details\r\n    if (productDetails && productDetails.product_name) {\r\n      setProductName(productDetails.product_name);\r\n    } else if (productDetails && productDetails.fabric_definition_data) {\r\n      setProductName(productDetails.fabric_definition_data.fabric_name);\r\n    }\r\n  };\r\n\r\n  // Save updated product name\r\n  const saveProductName = async () => {\r\n    // Validate product name\r\n    if (!productName.trim()) {\r\n      setProductNameError('Product name cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setProductNameError('');\r\n    setLoading(true);\r\n\r\n    try {\r\n      // First, get the current cutting record data\r\n      const currentRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n\r\n      // Create a payload with all the required fields, including details\r\n      const payload = {\r\n        fabric_definition: currentRecord.data.fabric_definition,\r\n        cutting_date: currentRecord.data.cutting_date,\r\n        product_name: productName,\r\n        details: currentRecord.data.details  // Include existing details without modification\r\n      };\r\n\r\n      // Update the product name in the backend\r\n      await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\r\n\r\n      // Update local state\r\n      setProductDetails({\r\n        ...productDetails,\r\n        product_name: productName\r\n      });\r\n\r\n      setIsEditingName(false);\r\n      setSuccessMsg('Product name updated successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMsg('');\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Error updating product name:', err);\r\n\r\n      // Check if the product name was actually updated despite the error\r\n      try {\r\n        const updatedRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n        if (updatedRecord.data.product_name === productName) {\r\n          // If the name was updated successfully despite the error, show success message\r\n          setProductDetails({\r\n            ...productDetails,\r\n            product_name: productName\r\n          });\r\n          setIsEditingName(false);\r\n          setSuccessMsg('Product name updated successfully');\r\n\r\n          // Clear success message after 3 seconds\r\n          setTimeout(() => {\r\n            setSuccessMsg('');\r\n          }, 3000);\r\n          return;\r\n        }\r\n      } catch (checkErr) {\r\n        // If we can't check, just show the original error\r\n        console.error('Error checking product name update:', checkErr);\r\n      }\r\n\r\n      // Show error message if the name wasn't updated\r\n      setError('Failed to update product name. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Start editing prices\r\n  const startEditingPrices = () => {\r\n    setEditManufacturePrice(manufacturePrice);\r\n    setEditSellingPrice(sellingPrice);\r\n    setIsEditingPrices(true);\r\n    setPriceEditErrors({});\r\n  };\r\n\r\n  // Cancel editing prices\r\n  const cancelEditingPrices = () => {\r\n    setIsEditingPrices(false);\r\n    setEditManufacturePrice('');\r\n    setEditSellingPrice('');\r\n    setPriceEditErrors({});\r\n  };\r\n\r\n  // Validate price editing form\r\n  const validatePriceEdit = () => {\r\n    const errors = {};\r\n\r\n    // Validate manufacture price\r\n    if (!editManufacturePrice || editManufacturePrice.trim() === '') {\r\n      errors.editManufacturePrice = \"Manufacture price is required\";\r\n    } else if (parseFloat(editManufacturePrice) <= 0) {\r\n      errors.editManufacturePrice = \"Manufacture price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(editManufacturePrice))) {\r\n      errors.editManufacturePrice = \"Manufacture price must be a valid number\";\r\n    }\r\n\r\n    // Validate selling price\r\n    if (!editSellingPrice || editSellingPrice.trim() === '') {\r\n      errors.editSellingPrice = \"Selling price is required\";\r\n    } else if (parseFloat(editSellingPrice) <= 0) {\r\n      errors.editSellingPrice = \"Selling price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(editSellingPrice))) {\r\n      errors.editSellingPrice = \"Selling price must be a valid number\";\r\n    } else if (parseFloat(editSellingPrice) < parseFloat(editManufacturePrice)) {\r\n      errors.editSellingPrice = \"Selling price should be greater than or equal to manufacture price\";\r\n    }\r\n\r\n    setPriceEditErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Save updated prices\r\n  const savePriceChanges = async () => {\r\n    if (!validatePriceEdit()) {\r\n      return;\r\n    }\r\n\r\n    if (!finishedProductId) {\r\n      setError('Could not find finished product record');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      // Update the prices using the PATCH endpoint\r\n      const updateData = {\r\n        manufacture_price: parseFloat(editManufacturePrice),\r\n        selling_price: parseFloat(editSellingPrice)\r\n      };\r\n\r\n      await axios.patch(`http://localhost:8000/api/finished_product/update/${finishedProductId}/`, updateData);\r\n\r\n      // Update local state\r\n      setManufacturePrice(editManufacturePrice);\r\n      setSellingPrice(editSellingPrice);\r\n      setIsEditingPrices(false);\r\n      setSuccessMsg('Prices updated successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMsg('');\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Error updating prices:', err);\r\n      const errMsg = err.response && err.response.data\r\n        ? typeof err.response.data === 'object'\r\n          ? JSON.stringify(err.response.data)\r\n          : err.response.data\r\n        : \"Failed to update prices. Please try again.\";\r\n      setError(errMsg);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Generate PDF report for the product\r\n  const generateProductReport = () => {\r\n    setPdfLoading(true);\r\n\r\n    try {\r\n      // Create PDF document\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes\r\n      const titleFontSize = 16;\r\n      const headingFontSize = 12;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add header\r\n      doc.setFontSize(titleFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Product Report', 105, 20, { align: 'center' });\r\n\r\n      // Add product name\r\n      const pdfProductName = productName || (productDetails && productDetails.fabric_definition_data\r\n        ? productDetails.fabric_definition_data.fabric_name\r\n        : `Batch ID: ${id}`);\r\n      doc.setFontSize(headingFontSize);\r\n      doc.text(pdfProductName, 105, 30, { align: 'center' });\r\n\r\n      // Add approval date\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Approval Date: ${new Date().toLocaleDateString()}`, 105, 40, { align: 'center' });\r\n\r\n      // Add horizontal line\r\n      doc.setDrawColor(200, 200, 200);\r\n      doc.line(20, 45, 190, 45);\r\n\r\n      // Start Y position for content\r\n      let yPos = 55;\r\n\r\n      // Add pricing information\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Pricing Information', 20, yPos);\r\n      yPos += 10;\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Manufacture Price: LKR ${manufacturePrice}`, 25, yPos);\r\n      yPos += 7;\r\n      doc.text(`Selling Price: LKR ${sellingPrice}`, 25, yPos);\r\n      yPos += 7;\r\n      doc.text(`Profit Margin: ${profitMargin}%`, 25, yPos);\r\n      yPos += 15;\r\n\r\n      // Add size distribution\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Size Distribution', 20, yPos);\r\n      yPos += 10;\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n\r\n      // Create a table for size distribution\r\n      const sizes = Object.entries(sizeQuantities);\r\n      const sizeHeaders = ['Size', 'Quantity', 'Percentage'];\r\n      const totalQuantity = sizes.reduce((sum, [_, qty]) => sum + qty, 0);\r\n\r\n      // Draw table headers\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text(sizeHeaders[0], 25, yPos);\r\n      doc.text(sizeHeaders[1], 60, yPos);\r\n      doc.text(sizeHeaders[2], 95, yPos);\r\n      yPos += 7;\r\n\r\n      // Draw table rows\r\n      doc.setFont('helvetica', 'normal');\r\n      sizes.forEach(([size, quantity]) => {\r\n        const percentage = totalQuantity > 0 ? ((quantity / totalQuantity) * 100).toFixed(1) : '0.0';\r\n        doc.text(size.toUpperCase(), 25, yPos);\r\n        doc.text(quantity.toString(), 60, yPos);\r\n        doc.text(`${percentage}%`, 95, yPos);\r\n        yPos += 7;\r\n      });\r\n\r\n      // Add total row\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Total', 25, yPos);\r\n      doc.text(totalQuantity.toString(), 60, yPos);\r\n      doc.text('100.0%', 95, yPos);\r\n      yPos += 15;\r\n\r\n      // Add color information\r\n      if (fabricDetails.length > 0) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Color Information', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n\r\n        fabricDetails.forEach((detail, index) => {\r\n          const colorName = detail.fabric_variant_data?.color_name || detail.color || 'N/A';\r\n          doc.text(`Color ${index + 1}: ${colorName}`, 25, yPos);\r\n          yPos += 7;\r\n        });\r\n\r\n        yPos += 8;\r\n      }\r\n\r\n      // Add product notes if available\r\n      if (productNotes) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Product Notes', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n\r\n        // Split notes into multiple lines if needed\r\n        const splitNotes = doc.splitTextToSize(productNotes, 160);\r\n        doc.text(splitNotes, 25, yPos);\r\n        yPos += splitNotes.length * 7 + 8;\r\n      }\r\n\r\n      // Add image information\r\n      if (existingImageUrls && existingImageUrls.length > 0) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Product Images', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n        doc.text(`Number of Images: ${existingImageUrls.length}`, 25, yPos);\r\n        yPos += 7;\r\n        doc.text('Note: Images can be viewed in the system', 25, yPos);\r\n        yPos += 15;\r\n      }\r\n\r\n      // Add footer\r\n      doc.setFontSize(smallFontSize);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      doc.text('Pri Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Product_Report_${cleanProductName}_${new Date().toISOString().slice(0, 10)}.pdf`);\r\n\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      setError(`Failed to generate PDF: ${error.message}`);\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Loading spinner\r\n  if (loading) return (\r\n    <div className=\"d-flex justify-content-center align-items-center\" style={{ height: \"100vh\" }}>\r\n      <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n        <span className=\"visually-hidden\">Loading...</span>\r\n      </Spinner>\r\n    </div>\r\n  );\r\n\r\n  // Render color swatch\r\n  const renderColorSwatch = (color) => {\r\n    if (!color) return null;\r\n\r\n    // For debugging - log the color value we're receiving\r\n    console.log('Color value received:', color);\r\n\r\n    // SIMPLIFIED APPROACH: Directly use the color value if it looks like a hex code\r\n    let bgColor;\r\n\r\n    // If it starts with #, use it directly\r\n    if (color.startsWith('#')) {\r\n      bgColor = color;\r\n    }\r\n    // If it looks like a hex code without #, add the #\r\n    else if (/^[0-9A-Fa-f]{6}$/.test(color) || /^[0-9A-Fa-f]{3}$/.test(color)) {\r\n      bgColor = `#${color}`;\r\n    }\r\n    // For named colors like \"Black\", \"Red\", etc.\r\n    else {\r\n      // Common color names mapping\r\n      const colorMap = {\r\n        'red': '#dc3545',\r\n        'blue': '#0d6efd',\r\n        'green': '#198754',\r\n        'yellow': '#ffc107',\r\n        'black': '#212529',\r\n        'white': '#f8f9fa',\r\n        'purple': '#6f42c1',\r\n        'orange': '#fd7e14',\r\n        'pink': '#d63384',\r\n        'brown': '#8B4513',\r\n        'gray': '#6c757d',\r\n      };\r\n\r\n      // Try to get from color map or use the name directly\r\n      bgColor = colorMap[color.toLowerCase()] || color;\r\n    }\r\n\r\n    return (\r\n      <OverlayTrigger\r\n        placement=\"top\"\r\n        overlay={<Tooltip className=\"custom-tooltip\">{color}</Tooltip>}\r\n      >\r\n        <div\r\n          className=\"color-swatch\"\r\n          style={{ backgroundColor: bgColor }}\r\n          data-color={color}\r\n        />\r\n      </OverlayTrigger>\r\n    );\r\n  };\r\n\r\n  // Render size quantity bars\r\n  const renderSizeQuantityBars = () => {\r\n    const sizes = Object.entries(sizeQuantities);\r\n    const maxQuantity = Math.max(...sizes.map(([_, qty]) => qty));\r\n\r\n    return sizes.map(([size, quantity]) => (\r\n      <div key={size} className=\"mb-2\">\r\n        <div className=\"d-flex justify-content-between mb-1\">\r\n          <span><strong>{size.toUpperCase()}</strong></span>\r\n          <span>{quantity} pcs</span>\r\n        </div>\r\n        <ProgressBar\r\n          now={maxQuantity ? (quantity / maxQuantity) * 100 : 0}\r\n          variant={quantity > 0 ? \"info\" : \"light\"}\r\n          className=\"size-quantity-bar\"\r\n        />\r\n      </div>\r\n    ));\r\n  };\r\n\r\n  // Confirmation Modal\r\n  const ConfirmationModal = () => (\r\n    <Modal\r\n      show={showConfirmModal}\r\n      onHide={handleCancelConfirmation}\r\n      centered\r\n      className=\"confirmation-modal\"\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title>Confirm Product Approval</Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        <p>Are you sure you want to approve this product with the following details?</p>\r\n        <Table bordered hover size=\"sm\" className=\"mt-3\">\r\n          <tbody>\r\n            <tr>\r\n              <td><strong>Product Name:</strong></td>\r\n              <td>{productName || (productDetails && productDetails.fabric_definition_data ?\r\n                productDetails.fabric_definition_data.fabric_name :\r\n                `Batch ID: ${id}`)}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Manufacture Price:</strong></td>\r\n              <td>LKR {manufacturePrice}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Selling Price:</strong></td>\r\n              <td>LKR {sellingPrice}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Profit Margin:</strong></td>\r\n              <td>{profitMargin}%</td>\r\n            </tr>\r\n            {productNotes && (\r\n              <tr>\r\n                <td><strong>Notes:</strong></td>\r\n                <td>{productNotes}</td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </Table>\r\n        {imagePreviewUrls.length > 0 && (\r\n          <div className=\"text-center mt-3\">\r\n            <p><strong>Product Image:</strong></p>\r\n            <Image\r\n              src={imagePreviewUrls[0]}\r\n              alt=\"Product\"\r\n              thumbnail\r\n              style={{ maxHeight: \"100px\" }}\r\n            />\r\n          </div>\r\n        )}\r\n      </Modal.Body>\r\n      <Modal.Footer>\r\n        <Button variant=\"secondary\" onClick={handleCancelConfirmation}>\r\n          Cancel\r\n        </Button>\r\n        <Button variant=\"success\" onClick={handleSubmit}>\r\n          <FaCheck className=\"me-2\" />\r\n          Confirm Approval\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div className=\"main-content\">\r\n        <Row className=\"justify-content-center\">\r\n          <Col md={10} lg={8}>\r\n            <Card className=\"shadow product-card slide-in\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n              <Card.Body>\r\n                <h2 className=\"text-center mb-3\">Approve Finished Product</h2>\r\n\r\n                {/* Product Name with Edit Functionality */}\r\n                <div className=\"text-center mb-4\">\r\n                  {isEditingName ? (\r\n                    <div className=\"d-flex justify-content-center align-items-center\">\r\n                      <Form.Group className=\"mb-0 me-2 flex-grow-1\" style={{ maxWidth: '300px' }}>\r\n                        <Form.Control\r\n                          type=\"text\"\r\n                          value={productName}\r\n                          onChange={(e) => setProductName(e.target.value)}\r\n                          isInvalid={!!productNameError}\r\n                          placeholder=\"Enter product name\"\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {productNameError}\r\n                        </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                      <Button\r\n                        variant=\"success\"\r\n                        size=\"sm\"\r\n                        className=\"me-1\"\r\n                        onClick={saveProductName}\r\n                      >\r\n                        <FaCheck />\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"secondary\"\r\n                        size=\"sm\"\r\n                        onClick={cancelEditingName}\r\n                      >\r\n                        <FaUndo />\r\n                      </Button>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"d-flex justify-content-center align-items-center\">\r\n                      <p className=\"text-center text-muted mb-0 me-2\">\r\n                        {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                          productDetails.fabric_definition_data.fabric_name :\r\n                          `Batch ID: ${id}`)}\r\n                      </p>\r\n                      <Button\r\n                        variant=\"outline-primary\"\r\n                        size=\"sm\"\r\n                        onClick={startEditingName}\r\n                      >\r\n                        Edit Name\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {error && (\r\n                  <Alert variant=\"danger\" className=\"mb-4 fade-in\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div>\r\n                        <FaExclamationTriangle className=\"me-2\" />\r\n                        {error}\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={fetchProductData}\r\n                      >\r\n                        Retry\r\n                      </Button>\r\n                    </div>\r\n                  </Alert>\r\n                )}\r\n\r\n                {successMsg && (\r\n                  <Alert variant=\"success\" className=\"mb-4 fade-in\">\r\n                    <FaCheck className=\"me-2\" />\r\n                    {successMsg}\r\n                  </Alert>\r\n                )}\r\n\r\n                {isApproved ? (\r\n                  <div className=\"p-4 bg-white rounded mb-3 slide-in\">\r\n                    <h4 className=\"text-center mb-4 text-success\">\r\n                      <FaCheck className=\"me-2\" />\r\n                      Product Already Approved\r\n                    </h4>\r\n\r\n                    <div className=\"text-center mb-4\">\r\n                      {isEditingName ? (\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <Form.Group className=\"mb-0 me-2 flex-grow-1\" style={{ maxWidth: '300px' }}>\r\n                            <Form.Control\r\n                              type=\"text\"\r\n                              value={productName}\r\n                              onChange={(e) => setProductName(e.target.value)}\r\n                              isInvalid={!!productNameError}\r\n                              placeholder=\"Enter product name\"\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {productNameError}\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                          <Button\r\n                            variant=\"success\"\r\n                            size=\"sm\"\r\n                            className=\"me-1\"\r\n                            onClick={saveProductName}\r\n                          >\r\n                            <FaCheck />\r\n                          </Button>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"sm\"\r\n                            onClick={cancelEditingName}\r\n                          >\r\n                            <FaUndo />\r\n                          </Button>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <h5 className=\"mb-0 me-2\">\r\n                            {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                              productDetails.fabric_definition_data.fabric_name :\r\n                              `Batch ID: ${id}`)}\r\n                          </h5>\r\n                          <Button\r\n                            variant=\"outline-primary\"\r\n                            size=\"sm\"\r\n                            onClick={startEditingName}\r\n                          >\r\n                            Edit Name\r\n                          </Button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"p-4 bg-white rounded mb-3\">\r\n                        <Row className=\"mt-3\">\r\n                          <Col md={6}>\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                              <h5 className=\"mb-0\"><FaMoneyBillWave className=\"me-2\" />Pricing Information</h5>\r\n                              {!isEditingPrices && (\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={startEditingPrices}\r\n                                >\r\n                                  Edit Prices\r\n                                </Button>\r\n                              )}\r\n                            </div>\r\n\r\n                            {isEditingPrices ? (\r\n                              <div className=\"p-3 bg-light rounded\">\r\n                                <Form>\r\n                                  <Row>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Manufacture Price (LKR):</strong></Form.Label>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={editManufacturePrice}\r\n                                          onChange={(e) => setEditManufacturePrice(e.target.value)}\r\n                                          isInvalid={!!priceEditErrors.editManufacturePrice}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {priceEditErrors.editManufacturePrice}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Selling Price (LKR):</strong></Form.Label>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={editSellingPrice}\r\n                                          onChange={(e) => setEditSellingPrice(e.target.value)}\r\n                                          isInvalid={!!priceEditErrors.editSellingPrice}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {priceEditErrors.editSellingPrice}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n\r\n                                  {editManufacturePrice && editSellingPrice && parseFloat(editManufacturePrice) > 0 && parseFloat(editSellingPrice) > 0 && (\r\n                                    <div className=\"mb-3 p-2 bg-info bg-opacity-10 rounded\">\r\n                                      <strong>New Profit Margin: </strong>\r\n                                      {(((parseFloat(editSellingPrice) - parseFloat(editManufacturePrice)) / parseFloat(editSellingPrice)) * 100).toFixed(2)}%\r\n                                    </div>\r\n                                  )}\r\n\r\n                                  <div className=\"d-flex gap-2\">\r\n                                    <Button\r\n                                      variant=\"success\"\r\n                                      size=\"sm\"\r\n                                      onClick={savePriceChanges}\r\n                                      disabled={loading}\r\n                                    >\r\n                                      <FaCheck className=\"me-1\" />\r\n                                      Save Changes\r\n                                    </Button>\r\n                                    <Button\r\n                                      variant=\"secondary\"\r\n                                      size=\"sm\"\r\n                                      onClick={cancelEditingPrices}\r\n                                    >\r\n                                      <FaUndo className=\"me-1\" />\r\n                                      Cancel\r\n                                    </Button>\r\n                                  </div>\r\n                                </Form>\r\n                              </div>\r\n                            ) : (\r\n                              <Table bordered hover>\r\n                                <tbody>\r\n                                  <tr>\r\n                                    <td><strong>Manufacture Price:</strong></td>\r\n                                    <td>LKR {manufacturePrice}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <td><strong>Selling Price:</strong></td>\r\n                                    <td>LKR {sellingPrice}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <td><strong>Profit Margin:</strong></td>\r\n                                    <td>{profitMargin}%</td>\r\n                                  </tr>\r\n                                </tbody>\r\n                              </Table>\r\n                            )}\r\n\r\n                            {productNotes && (\r\n                              <div className=\"mt-4\">\r\n                                <h5 className=\"mb-3\"><FaClipboardList className=\"me-2\" />Notes</h5>\r\n                                <div className=\"p-3 bg-light rounded\">\r\n                                  {productNotes}\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n\r\n                            <h5 className=\"mb-3 mt-4\"><FaTags className=\"me-2\" />Colors</h5>\r\n                            <div className=\"mb-4\">\r\n                              {fabricDetails.length > 0 ? (\r\n                                fabricDetails.map((detail, index) => (\r\n                                  <div key={index} className=\"mb-2\">\r\n                                    {renderColorSwatch(detail.fabric_variant_data?.color || detail.color || 'gray')}\r\n                                    <span className=\"ms-2\">{detail.fabric_variant_data?.color_name || detail.color}</span>\r\n                                  </div>\r\n                                ))\r\n                              ) : (\r\n                                <p className=\"text-muted\">No color information available</p>\r\n                              )}\r\n                            </div>\r\n\r\n                            <h5 className=\"mb-3\"><FaBoxOpen className=\"me-2\" />Size Distribution</h5>\r\n                            {renderSizeQuantityBars()}\r\n\r\n                            <div className=\"mt-4\">\r\n                              <Button\r\n                                variant=\"outline-primary\"\r\n                                className=\"w-100\"\r\n                                onClick={openPdfModal}\r\n                              >\r\n                                <FaFilePdf className=\"me-2\" />\r\n                                Generate Product Report\r\n                              </Button>\r\n                            </div>\r\n                          </Col>\r\n\r\n                          <Col md={6} className=\"text-center\">\r\n                            <h5 className=\"mb-3\"><FaImage className=\"me-2\" />Product Images</h5>\r\n                            {existingImageUrls && existingImageUrls.length > 0 ? (\r\n                              <div className=\"product-images-container\">\r\n                                <div className=\"product-images-grid\">\r\n                                  {existingImageUrls.map((imageUrl, index) => (\r\n                                    <div\r\n                                      key={index}\r\n                                      className={`product-image-item ${index === activeImageIndex ? 'active' : ''}`}\r\n                                      onClick={() => setActiveImageIndex(index)}\r\n                                    >\r\n                                      <Image\r\n                                        src={imageUrl}\r\n                                        alt={`Product ${index + 1}`}\r\n                                        thumbnail\r\n                                        className=\"image-preview\"\r\n                                      />\r\n                                      <span className=\"image-number\">{index + 1}</span>\r\n                                    </div>\r\n                                  ))}\r\n                                </div>\r\n                                <div className=\"main-image-container mt-3\">\r\n                                  <Image\r\n                                    src={existingImageUrls[activeImageIndex]}\r\n                                    alt=\"Product\"\r\n                                    thumbnail\r\n                                    className=\"main-image-preview\"\r\n                                    style={{ maxHeight: \"250px\" }}\r\n                                  />\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"p-5 bg-light rounded\">\r\n                                <FaImage size={60} className=\"text-secondary\" />\r\n                                <p className=\"mt-3 text-muted\">No images available</p>\r\n                              </div>\r\n                            )}\r\n                          </Col>\r\n                        </Row>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"slide-in\">\r\n                    <Tabs\r\n                      id=\"product-approval-tabs\"\r\n                      activeKey={activeTab}\r\n                      onSelect={(k) => setActiveTab(k)}\r\n                      className=\"mb-4\"\r\n                    >\r\n                      <Tab eventKey=\"details\" title={<span><FaInfoCircle className=\"me-2\" />Product Details</span>}>\r\n                        <Row className=\"mt-3\">\r\n                          <Col md={6}>\r\n                            <h5 className=\"mb-3\"><FaInfoCircle className=\"me-2\" />Product Name</h5>\r\n                            {isEditingName ? (\r\n                              <div className=\"mb-4\">\r\n                                <Form.Group>\r\n                                  <Form.Control\r\n                                    type=\"text\"\r\n                                    value={productName}\r\n                                    onChange={(e) => setProductName(e.target.value)}\r\n                                    isInvalid={!!productNameError}\r\n                                    placeholder=\"Enter product name\"\r\n                                  />\r\n                                  <Form.Control.Feedback type=\"invalid\">\r\n                                    {productNameError}\r\n                                  </Form.Control.Feedback>\r\n                                </Form.Group>\r\n                                <div className=\"mt-2\">\r\n                                  <Button\r\n                                    variant=\"success\"\r\n                                    size=\"sm\"\r\n                                    className=\"me-1\"\r\n                                    onClick={saveProductName}\r\n                                  >\r\n                                    <FaCheck className=\"me-1\" /> Save\r\n                                  </Button>\r\n                                  <Button\r\n                                    variant=\"secondary\"\r\n                                    size=\"sm\"\r\n                                    onClick={cancelEditingName}\r\n                                  >\r\n                                    <FaUndo className=\"me-1\" /> Cancel\r\n                                  </Button>\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"p-3 bg-light rounded mb-4 d-flex justify-content-between align-items-center\">\r\n                                <strong>\r\n                                  {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                                    productDetails.fabric_definition_data.fabric_name :\r\n                                    `Batch ID: ${id}`)}\r\n                                </strong>\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={startEditingName}\r\n                                >\r\n                                  Edit Name\r\n                                </Button>\r\n                              </div>\r\n                            )}\r\n\r\n                            <h5 className=\"mb-3\"><FaTags className=\"me-2\" />Colors</h5>\r\n                            <div className=\"mb-4\">\r\n                              {fabricDetails.length > 0 ? (\r\n                                fabricDetails.map((detail, index) => (\r\n                                  <div key={index} className=\"mb-2\">\r\n                                    {renderColorSwatch(detail.fabric_variant_data?.color || detail.color || 'gray')}\r\n                                    <span className=\"ms-2\">{detail.fabric_variant_data?.color_name || detail.color}</span>\r\n                                  </div>\r\n                                ))\r\n                              ) : (\r\n                                <p className=\"text-muted\">No color information available</p>\r\n                              )}\r\n                            </div>\r\n\r\n                            <h5 className=\"mb-3\"><FaBoxOpen className=\"me-2\" />Size Distribution</h5>\r\n                            {renderSizeQuantityBars()}\r\n                          </Col>\r\n\r\n                          <Col md={6}>\r\n                            <div className=\"mb-3\">\r\n                              <h5 className=\"mb-3\"><FaImage className=\"me-2\" />Product Images</h5>\r\n                              <p className=\"text-muted mb-3\">Upload up to 10 images of the product (Current: {productImages.length}/10)</p>\r\n\r\n                              {/* Image preview grid */}\r\n                              {imagePreviewUrls.length > 0 && (\r\n                                <div className=\"product-images-container mb-4\">\r\n                                  <div className=\"product-images-grid\">\r\n                                    {imagePreviewUrls.map((previewUrl, index) => (\r\n                                      <div\r\n                                        key={index}\r\n                                        className={`product-image-item ${index === activeImageIndex ? 'active' : ''}`}\r\n                                        onClick={() => setActiveImageIndex(index)}\r\n                                      >\r\n                                        <div className=\"image-actions\">\r\n                                          <Button\r\n                                            variant=\"danger\"\r\n                                            size=\"sm\"\r\n                                            className=\"btn-remove-image\"\r\n                                            onClick={(e) => {\r\n                                              e.stopPropagation();\r\n                                              removeImage(index);\r\n                                            }}\r\n                                          >\r\n                                            <FaTrash />\r\n                                          </Button>\r\n                                        </div>\r\n                                        <Image\r\n                                          src={previewUrl}\r\n                                          alt={`Preview ${index + 1}`}\r\n                                          thumbnail\r\n                                          className=\"image-preview\"\r\n                                        />\r\n                                        <span className=\"image-number\">{index + 1}</span>\r\n                                      </div>\r\n                                    ))}\r\n                                  </div>\r\n\r\n                                  {imagePreviewUrls.length > 0 && (\r\n                                    <div className=\"main-image-container mt-3\">\r\n                                      <Image\r\n                                        src={imagePreviewUrls[activeImageIndex]}\r\n                                        alt=\"Product Preview\"\r\n                                        thumbnail\r\n                                        className=\"main-image-preview\"\r\n                                        style={{ maxHeight: \"250px\" }}\r\n                                      />\r\n                                    </div>\r\n                                  )}\r\n                                </div>\r\n                              )}\r\n\r\n                              {/* Upload area */}\r\n                              {isUploading ? (\r\n                                <div className=\"p-4 bg-light rounded text-center\">\r\n                                  <h5 className=\"mb-3\">Uploading Images</h5>\r\n                                  <ProgressBar\r\n                                    now={uploadProgress}\r\n                                    label={`${Math.round(uploadProgress)}%`}\r\n                                    variant=\"info\"\r\n                                    animated\r\n                                    className=\"mb-3\"\r\n                                  />\r\n                                  <p className=\"text-muted\">\r\n                                    <FaUpload className=\"me-2 text-primary\" />\r\n                                    Uploading {productImages.length} images...\r\n                                  </p>\r\n                                </div>\r\n                              ) : (\r\n                                productImages.length < 10 && (\r\n                                  <div {...getRootProps()} className={`image-upload-container ${isDragActive ? 'active' : ''}`}>\r\n                                    <input {...getInputProps()} multiple />\r\n                                    <div className=\"text-center\">\r\n                                      <FaUpload size={40} className=\"mb-3 text-primary\" />\r\n                                      <p>Drag & drop product images here, or click to select</p>\r\n                                      <p className=\"text-muted small\">Supported formats: JPEG, PNG, GIF (Max: 5MB each)</p>\r\n                                      <p className=\"text-muted small\">You can select multiple images at once (Max: 10)</p>\r\n                                    </div>\r\n                                  </div>\r\n                                )\r\n                              )}\r\n\r\n                              <Form.Control\r\n                                type=\"file\"\r\n                                ref={fileInputRef}\r\n                                onChange={handleImageChange}\r\n                                accept=\"image/*\"\r\n                                multiple\r\n                                style={{ display: 'none' }}\r\n                              />\r\n                            </div>\r\n                          </Col>\r\n                        </Row>\r\n                      </Tab>\r\n\r\n                      <Tab eventKey=\"pricing\" title={<span><FaMoneyBillWave className=\"me-2\" />Pricing</span>}>\r\n                        <Form onSubmit={handleFormSubmit} className=\"mt-3\">\r\n                          <Row>\r\n                            <Col md={6}>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Manufacture Price (LKR):</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>The cost to manufacture this product</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  step=\"0.01\"\r\n                                  min=\"0\"\r\n                                  value={manufacturePrice}\r\n                                  onChange={(e) => setManufacturePrice(e.target.value)}\r\n                                  isInvalid={!!validationErrors.manufacturePrice}\r\n                                  required\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.manufacturePrice}\r\n                                </Form.Control.Feedback>\r\n                              </Form.Group>\r\n\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Selling Price (LKR):</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>The price at which this product will be sold</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  step=\"0.01\"\r\n                                  min=\"0\"\r\n                                  value={sellingPrice}\r\n                                  onChange={(e) => setSellingPrice(e.target.value)}\r\n                                  isInvalid={!!validationErrors.sellingPrice}\r\n                                  required\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.sellingPrice}\r\n                                </Form.Control.Feedback>\r\n                              </Form.Group>\r\n\r\n                              {manufacturePrice && sellingPrice && parseFloat(manufacturePrice) > 0 && parseFloat(sellingPrice) > 0 && (\r\n                                <div className=\"mb-4 p-3 bg-light rounded\">\r\n                                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                    <span><strong>Profit Margin:</strong></span>\r\n                                    <Badge bg={\r\n                                      profitMargin < 10 ? \"danger\" :\r\n                                      profitMargin < 20 ? \"warning\" :\r\n                                      \"success\"\r\n                                    }>\r\n                                      <FaPercentage className=\"me-1\" />\r\n                                      {profitMargin}%\r\n                                    </Badge>\r\n                                  </div>\r\n                                  <div className=\"profit-margin-indicator\" />\r\n                                  <div className=\"d-flex justify-content-between mt-1\">\r\n                                    <small>Low</small>\r\n                                    <small>High</small>\r\n                                  </div>\r\n                                </div>\r\n                              )}\r\n                            </Col>\r\n\r\n                            <Col md={6}>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Product Notes:</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>Additional information about this product</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  as=\"textarea\"\r\n                                  rows={5}\r\n                                  value={productNotes}\r\n                                  onChange={(e) => setProductNotes(e.target.value)}\r\n                                  isInvalid={!!validationErrors.productNotes}\r\n                                  placeholder=\"Enter any additional notes about this product...\"\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.productNotes}\r\n                                </Form.Control.Feedback>\r\n                                <Form.Text className=\"text-muted\">\r\n                                  {productNotes ? 500 - productNotes.length : 500} characters remaining\r\n                                </Form.Text>\r\n                              </Form.Group>\r\n                            </Col>\r\n                          </Row>\r\n\r\n                          <div className=\"d-grid gap-2 mt-4\">\r\n                            <Button type=\"submit\" className=\"btn-approve\" size=\"lg\">\r\n                              <FaCheck className=\"me-2\" />\r\n                              Approve Product\r\n                            </Button>\r\n                          </div>\r\n                        </Form>\r\n                      </Tab>\r\n                    </Tabs>\r\n                  </div>\r\n                )}\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      </div>\r\n\r\n      {/* Confirmation Modal */}\r\n      <ConfirmationModal />\r\n\r\n      {/* PDF Report Modal */}\r\n      <Modal show={showPdfModal} onHide={closePdfModal} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"text-danger me-2\" />\r\n            Generate Product Report\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Are you sure you want to generate a PDF report for this product?</p>\r\n          <div className=\"bg-light p-3 rounded\">\r\n            <p className=\"mb-1\"><strong>Product:</strong> {productName || (productDetails && productDetails.fabric_definition_data ?\r\n              productDetails.fabric_definition_data.fabric_name :\r\n              `Batch ID: ${id}`)}</p>\r\n            <p className=\"mb-1\"><strong>Manufacture Price:</strong> LKR {manufacturePrice}</p>\r\n            <p className=\"mb-1\"><strong>Selling Price:</strong> LKR {sellingPrice}</p>\r\n            <p className=\"mb-0\"><strong>Profit Margin:</strong> {profitMargin}%</p>\r\n          </div>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={closePdfModal}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={generateProductReport}\r\n            disabled={pdfLoading}\r\n          >\r\n            {pdfLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Generating...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaDownload className=\"me-2\" />\r\n                Generate PDF\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ApproveFinishedProduct;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAEC,OAAO,QAAQ,iBAAiB;AAC3J,SACEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EACjEC,YAAY,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EACtDC,OAAO,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,UAAU,QACxD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAO,8BAA8B;AACrC,SAASC,KAAK,QAAQ,OAAO;AAC7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAG,CAAC,GAAG1C,SAAS,CAAC,CAAC;EAC1B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,YAAY,GAAG/C,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACA,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC;IACnDoF,EAAE,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE;EAC/B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6F,YAAY,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC2G,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC+G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpH,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACAC,SAAS,CAAC,MAAM;IACd,IAAIiD,gBAAgB,IAAIE,YAAY,EAAE;MACpC,MAAMiE,MAAM,GAAGC,UAAU,CAACpE,gBAAgB,CAAC;MAC3C,MAAMqE,MAAM,GAAGD,UAAU,CAAClE,YAAY,CAAC;MAEvC,IAAIiE,MAAM,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAMC,MAAM,GAAI,CAACD,MAAM,GAAGF,MAAM,IAAIE,MAAM,GAAI,GAAG;QACjDnB,eAAe,CAACoB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;MACpC;IACF;EACF,CAAC,EAAE,CAACvE,gBAAgB,EAAEE,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAG3H,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM4H,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAMC,gBAAgB,GAAG1H,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF4D,UAAU,CAAC,IAAI,CAAC;MAChBN,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMqE,WAAW,GAAG,MAAM1H,KAAK,CAAC2H,GAAG,CAAC,qDAAqDhF,EAAE,GAAG,CAAC;;MAE/F;MACA,MAAMiF,UAAU,GAAG,MAAM5H,KAAK,CAAC2H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC;MAEtF,IAAI+E,WAAW,CAACG,IAAI,IAAIH,WAAW,CAACG,IAAI,CAACC,WAAW,EAAE;QACpDrE,aAAa,CAAC,IAAI,CAAC;QACnBV,mBAAmB,CAAC2E,WAAW,CAACG,IAAI,CAACE,iBAAiB,CAAC;QACvD9E,eAAe,CAACyE,WAAW,CAACG,IAAI,CAACG,aAAa,CAAC;QAC/ChB,oBAAoB,CAACU,WAAW,CAACG,IAAI,CAACI,mBAAmB,CAAC;;QAE1D;QACA,IAAIP,WAAW,CAACG,IAAI,CAACK,cAAc,IAAIC,KAAK,CAACC,OAAO,CAACV,WAAW,CAACG,IAAI,CAACK,cAAc,CAAC,EAAE;UACrFjE,oBAAoB,CAACyD,WAAW,CAACG,IAAI,CAACK,cAAc,CAAC;QACvD,CAAC,MAAM,IAAIR,WAAW,CAACG,IAAI,CAACQ,aAAa,EAAE;UACzC;UACApE,oBAAoB,CAAC,CAACyD,WAAW,CAACG,IAAI,CAACQ,aAAa,CAAC,CAAC;QACxD;QAEA,IAAIX,WAAW,CAACG,IAAI,CAACS,KAAK,EAAE;UAC1BnF,eAAe,CAACuE,WAAW,CAACG,IAAI,CAACS,KAAK,CAAC;QACzC;MACF;MAEA,IAAIV,UAAU,CAACC,IAAI,EAAE;QACnBlD,iBAAiB,CAACiD,UAAU,CAACC,IAAI,CAAC;;QAElC;QACA,IAAID,UAAU,CAACC,IAAI,CAACU,YAAY,EAAE;UAChCrC,cAAc,CAAC0B,UAAU,CAACC,IAAI,CAACU,YAAY,CAAC;QAC9C,CAAC,MAAM,IAAIX,UAAU,CAACC,IAAI,CAACW,sBAAsB,EAAE;UACjD;UACAtC,cAAc,CAAC0B,UAAU,CAACC,IAAI,CAACW,sBAAsB,CAACC,WAAW,CAAC;QACpE;;QAEA;QACA,IAAIb,UAAU,CAACC,IAAI,CAACa,OAAO,IAAId,UAAU,CAACC,IAAI,CAACa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACjE;UACAC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEjB,UAAU,CAACC,IAAI,CAACa,OAAO,CAAC;UAEhE7D,gBAAgB,CAAC+C,UAAU,CAACC,IAAI,CAACa,OAAO,CAAC;;UAEzC;UACA,MAAMI,KAAK,GAAG;YAAC9D,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAC,CAAC;UAC9CwC,UAAU,CAACC,IAAI,CAACa,OAAO,CAACK,OAAO,CAACC,MAAM,IAAI;YACxCF,KAAK,CAAC9D,EAAE,IAAIgE,MAAM,CAAChE,EAAE,IAAI,CAAC;YAC1B8D,KAAK,CAAC7D,CAAC,IAAI+D,MAAM,CAAC/D,CAAC,IAAI,CAAC;YACxB6D,KAAK,CAAC5D,CAAC,IAAI8D,MAAM,CAAC9D,CAAC,IAAI,CAAC;YACxB4D,KAAK,CAAC3D,CAAC,IAAI6D,MAAM,CAAC7D,CAAC,IAAI,CAAC;YACxB2D,KAAK,CAAC1D,EAAE,IAAI4D,MAAM,CAAC5D,EAAE,IAAI,CAAC;UAC5B,CAAC,CAAC;UACFL,iBAAiB,CAAC+D,KAAK,CAAC;QAC1B;MACF;;MAEA;MACAvB,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,+BAA+B,EAAE6F,GAAG,CAAC;;MAEnD;MACA,MAAMC,YAAY,GAAGD,GAAG,CAACE,QAAQ,GAC7B,UAAUF,GAAG,CAACE,QAAQ,CAACC,MAAM,MAAMH,GAAG,CAACE,QAAQ,CAACE,UAAU,EAAE,GAC5DJ,GAAG,CAACK,OAAO,GACT,oEAAoE,GACpE,wDAAwD;MAE9DjG,QAAQ,CAAC,iCAAiC6F,YAAY,EAAE,CAAC;;MAEzD;MACA,IAAI5B,UAAU,GAAGE,UAAU,EAAE;QAC3BD,aAAa,CAACgC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAM;UACf/B,gBAAgB,CAAC,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC,SAAS;MACR9D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAChB,EAAE,EAAE2E,UAAU,CAAC,CAAC;;EAEpB;EACAzH,SAAS,CAAC,MAAM;IACd4H,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMgC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,KAAK,GAAGxB,KAAK,CAACyB,IAAI,CAACF,CAAC,CAACG,MAAM,CAACF,KAAK,CAAC;IACxC,IAAIA,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAE;MACpBmB,iBAAiB,CAACH,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAG/J,WAAW,CAAE4J,KAAK,IAAK;IAC/C;IACA,IAAI/F,aAAa,CAAC+E,MAAM,GAAGgB,KAAK,CAAChB,MAAM,GAAG,EAAE,EAAE;MAC5CtF,QAAQ,CAAC,yDAAyDO,aAAa,CAAC+E,MAAM,UAAU,CAAC;MACjG;IACF;IAEA,MAAMoB,SAAS,GAAG,EAAE;IACpB,MAAMC,cAAc,GAAG,CAAC,GAAGlG,gBAAgB,CAAC;IAE5C6F,KAAK,CAACZ,OAAO,CAACkB,IAAI,IAAI;MACpB;MACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,KAAK,CAAC,SAAS,CAAC,EAAE;QAC/B9G,QAAQ,CAAC,kDAAkD,CAAC;QAC5D;MACF;;MAEA;MACA,IAAI4G,IAAI,CAACG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B/G,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA0G,SAAS,CAACM,IAAI,CAACJ,IAAI,CAAC;;MAEpB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBR,cAAc,CAACK,IAAI,CAACC,MAAM,CAACG,MAAM,CAAC;QAClC1G,mBAAmB,CAAC,CAAC,GAAGiG,cAAc,CAAC,CAAC;MAC1C,CAAC;MACDM,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEFpG,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE,GAAGmG,SAAS,CAAC,CAAC;EACpD,CAAC,EAAE,CAACnG,aAAa,EAAEE,gBAAgB,CAAC,CAAC;;EAErC;EACA,MAAM6G,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9H,YAAY,CAAC+H,OAAO,CAACC,KAAK,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,MAAM,GAAG/K,WAAW,CAAEgL,aAAa,IAAK;IAC5C,IAAIA,aAAa,IAAIA,aAAa,CAACpC,MAAM,GAAG,CAAC,EAAE;MAC7CmB,iBAAiB,CAACiB,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACjB,iBAAiB,CAAC,CAAC;EAEvB,MAAM;IAAEkB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGhJ,WAAW,CAAC;IAChE4I,MAAM;IACNK,MAAM,EAAE;MACN,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC7C,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7B,MAAMvB,SAAS,GAAG,CAAC,GAAGnG,aAAa,CAAC;IACpC,MAAMoG,cAAc,GAAG,CAAC,GAAGlG,gBAAgB,CAAC;IAE5CiG,SAAS,CAACwB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC1BtB,cAAc,CAACuB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAE/BzH,gBAAgB,CAACkG,SAAS,CAAC;IAC3BhG,mBAAmB,CAACiG,cAAc,CAAC;;IAEnC;IACA,IAAIsB,KAAK,KAAKlH,gBAAgB,EAAE;MAC9BC,mBAAmB,CAACmH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIA,KAAK,GAAGlH,gBAAgB,EAAE;MACnCC,mBAAmB,CAACD,gBAAgB,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMsH,cAAc,GAAIJ,KAAK,IAAK;IAChCjH,mBAAmB,CAACiH,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC9I,gBAAgB,IAAIA,gBAAgB,CAAC+I,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvDD,MAAM,CAAC9I,gBAAgB,GAAG,+BAA+B;IAC3D,CAAC,MAAM,IAAIoE,UAAU,CAACpE,gBAAgB,CAAC,IAAI,CAAC,EAAE;MAC5C8I,MAAM,CAAC9I,gBAAgB,GAAG,6CAA6C;IACzE,CAAC,MAAM,IAAIgJ,KAAK,CAAC5E,UAAU,CAACpE,gBAAgB,CAAC,CAAC,EAAE;MAC9C8I,MAAM,CAAC9I,gBAAgB,GAAG,0CAA0C;IACtE;;IAEA;IACA,IAAI,CAACE,YAAY,IAAIA,YAAY,CAAC6I,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/CD,MAAM,CAAC5I,YAAY,GAAG,2BAA2B;IACnD,CAAC,MAAM,IAAIkE,UAAU,CAAClE,YAAY,CAAC,IAAI,CAAC,EAAE;MACxC4I,MAAM,CAAC5I,YAAY,GAAG,yCAAyC;IACjE,CAAC,MAAM,IAAI8I,KAAK,CAAC5E,UAAU,CAAClE,YAAY,CAAC,CAAC,EAAE;MAC1C4I,MAAM,CAAC5I,YAAY,GAAG,sCAAsC;IAC9D,CAAC,MAAM,IAAIkE,UAAU,CAAClE,YAAY,CAAC,GAAGkE,UAAU,CAACpE,gBAAgB,CAAC,EAAE;MAClE8I,MAAM,CAAC5I,YAAY,GAAG,oEAAoE;IAC5F;;IAEA;IACA,IAAIE,YAAY,IAAIA,YAAY,CAACyF,MAAM,GAAG,GAAG,EAAE;MAC7CiD,MAAM,CAAC1I,YAAY,GAAG,0CAA0C;IAClE;IAEA4C,mBAAmB,CAAC8F,MAAM,CAAC;IAC3B,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACjD,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAIvC,CAAC,IAAK;IAC9BA,CAAC,CAACwC,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACAnG,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM2G,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B9I,QAAQ,CAAC,EAAE,CAAC;IACZE,aAAa,CAAC,EAAE,CAAC;IACjBiC,mBAAmB,CAAC,KAAK,CAAC;IAE1B,IAAI;MACF;MACA7B,UAAU,CAAC,IAAI,CAAC;MAChBc,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM2H,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE3J,EAAE,CAAC;MACrCyJ,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEpF,UAAU,CAACpE,gBAAgB,CAAC,CAAC;MAClEsJ,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEpF,UAAU,CAAClE,YAAY,CAAC,CAAC;MAE1D,IAAIE,YAAY,EAAE;QAChBkJ,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEpJ,YAAY,CAAC;MACxC;;MAEA;MACA,IAAIU,aAAa,IAAIA,aAAa,CAAC+E,MAAM,GAAG,CAAC,EAAE;QAC7CpE,iBAAiB,CAAC,CAAC,CAAC;;QAEpB;QACAX,aAAa,CAACmF,OAAO,CAACwD,KAAK,IAAI;UAC7BH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEC,KAAK,CAAC;QAC1C,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMpD,QAAQ,GAAG,MAAMnJ,KAAK,CAACwM,IAAI,CAC/B,qDAAqD,EACrDJ,QAAQ,EACR;QACEK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,gBAAgB,EAAGC,aAAa,IAAK;UACnC,MAAMC,gBAAgB,GAAGpB,IAAI,CAACqB,KAAK,CAChCF,aAAa,CAACG,MAAM,GAAG,GAAG,GAAIH,aAAa,CAACI,KAC/C,CAAC;UACDxI,iBAAiB,CAACqI,gBAAgB,CAAC;QACrC;MACF,CACF,CAAC;MAEDrJ,aAAa,CAAC4F,QAAQ,CAACtB,IAAI,CAACmF,OAAO,IAAI,gCAAgC,CAAC;MACxEvI,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA+E,UAAU,CAAC,MAAM;QACf5G,QAAQ,CAAC,sBAAsB,CAAC;MAClC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOqG,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,mCAAmC,EAAE6F,GAAG,CAAC;MACvD,MAAMgE,MAAM,GAAGhE,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACtB,IAAI,GAC5C,OAAOoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,KAAK,QAAQ,GACnCqF,IAAI,CAACC,SAAS,CAAClE,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAAC,GACjCoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,GACnB,uDAAuD;MAC3DxE,QAAQ,CAAC4J,MAAM,CAAC;MAChBxI,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyJ,wBAAwB,GAAGA,CAAA,KAAM;IACrC5H,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM6H,YAAY,GAAGA,CAAA,KAAM;IACzB3H,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM4H,aAAa,GAAGA,CAAA,KAAM;IAC1B5H,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAM6H,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnH,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMoH,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpH,gBAAgB,CAAC,KAAK,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC;IACvB;IACA,IAAI5B,cAAc,IAAIA,cAAc,CAAC6D,YAAY,EAAE;MACjDrC,cAAc,CAACxB,cAAc,CAAC6D,YAAY,CAAC;IAC7C,CAAC,MAAM,IAAI7D,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,EAAE;MAClEtC,cAAc,CAACxB,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMgF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC;IACA,IAAI,CAACxH,WAAW,CAAC4F,IAAI,CAAC,CAAC,EAAE;MACvBvF,mBAAmB,CAAC,8BAA8B,CAAC;MACnD;IACF;IAEAA,mBAAmB,CAAC,EAAE,CAAC;IACvB3C,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM+J,aAAa,GAAG,MAAM1N,KAAK,CAAC2H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC;;MAEzF;MACA,MAAMgL,OAAO,GAAG;QACdC,iBAAiB,EAAEF,aAAa,CAAC7F,IAAI,CAAC+F,iBAAiB;QACvDC,YAAY,EAAEH,aAAa,CAAC7F,IAAI,CAACgG,YAAY;QAC7CtF,YAAY,EAAEtC,WAAW;QACzByC,OAAO,EAAEgF,aAAa,CAAC7F,IAAI,CAACa,OAAO,CAAE;MACvC,CAAC;;MAED;MACA,MAAM1I,KAAK,CAAC8N,GAAG,CAAC,qDAAqDnL,EAAE,GAAG,EAAEgL,OAAO,CAAC;;MAEpF;MACAhJ,iBAAiB,CAAC;QAChB,GAAGD,cAAc;QACjB6D,YAAY,EAAEtC;MAChB,CAAC,CAAC;MAEFG,gBAAgB,CAAC,KAAK,CAAC;MACvB7C,aAAa,CAAC,mCAAmC,CAAC;;MAElD;MACAiG,UAAU,CAAC,MAAM;QACfjG,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO0F,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,8BAA8B,EAAE6F,GAAG,CAAC;;MAElD;MACA,IAAI;QACF,MAAM8E,aAAa,GAAG,MAAM/N,KAAK,CAAC2H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC;QACzF,IAAIoL,aAAa,CAAClG,IAAI,CAACU,YAAY,KAAKtC,WAAW,EAAE;UACnD;UACAtB,iBAAiB,CAAC;YAChB,GAAGD,cAAc;YACjB6D,YAAY,EAAEtC;UAChB,CAAC,CAAC;UACFG,gBAAgB,CAAC,KAAK,CAAC;UACvB7C,aAAa,CAAC,mCAAmC,CAAC;;UAElD;UACAiG,UAAU,CAAC,MAAM;YACfjG,aAAa,CAAC,EAAE,CAAC;UACnB,CAAC,EAAE,IAAI,CAAC;UACR;QACF;MACF,CAAC,CAAC,OAAOyK,QAAQ,EAAE;QACjB;QACApF,OAAO,CAACxF,KAAK,CAAC,qCAAqC,EAAE4K,QAAQ,CAAC;MAChE;;MAEA;MACA3K,QAAQ,CAAC,kDAAkD,CAAC;IAC9D,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvH,uBAAuB,CAAC5D,gBAAgB,CAAC;IACzC8D,mBAAmB,CAAC5D,YAAY,CAAC;IACjCwD,kBAAkB,CAAC,IAAI,CAAC;IACxBM,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMoH,mBAAmB,GAAGA,CAAA,KAAM;IAChC1H,kBAAkB,CAAC,KAAK,CAAC;IACzBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMvC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACnF,oBAAoB,IAAIA,oBAAoB,CAACoF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/DD,MAAM,CAACnF,oBAAoB,GAAG,+BAA+B;IAC/D,CAAC,MAAM,IAAIS,UAAU,CAACT,oBAAoB,CAAC,IAAI,CAAC,EAAE;MAChDmF,MAAM,CAACnF,oBAAoB,GAAG,6CAA6C;IAC7E,CAAC,MAAM,IAAIqF,KAAK,CAAC5E,UAAU,CAACT,oBAAoB,CAAC,CAAC,EAAE;MAClDmF,MAAM,CAACnF,oBAAoB,GAAG,0CAA0C;IAC1E;;IAEA;IACA,IAAI,CAACE,gBAAgB,IAAIA,gBAAgB,CAACkF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvDD,MAAM,CAACjF,gBAAgB,GAAG,2BAA2B;IACvD,CAAC,MAAM,IAAIO,UAAU,CAACP,gBAAgB,CAAC,IAAI,CAAC,EAAE;MAC5CiF,MAAM,CAACjF,gBAAgB,GAAG,yCAAyC;IACrE,CAAC,MAAM,IAAImF,KAAK,CAAC5E,UAAU,CAACP,gBAAgB,CAAC,CAAC,EAAE;MAC9CiF,MAAM,CAACjF,gBAAgB,GAAG,sCAAsC;IAClE,CAAC,MAAM,IAAIO,UAAU,CAACP,gBAAgB,CAAC,GAAGO,UAAU,CAACT,oBAAoB,CAAC,EAAE;MAC1EmF,MAAM,CAACjF,gBAAgB,GAAG,oEAAoE;IAChG;IAEAG,kBAAkB,CAAC8E,MAAM,CAAC;IAC1B,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACjD,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMyF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACD,iBAAiB,CAAC,CAAC,EAAE;MACxB;IACF;IAEA,IAAI,CAACpH,iBAAiB,EAAE;MACtB1D,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;IAEAM,UAAU,CAAC,IAAI,CAAC;IAChBN,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMgL,UAAU,GAAG;QACjBtG,iBAAiB,EAAEb,UAAU,CAACT,oBAAoB,CAAC;QACnDuB,aAAa,EAAEd,UAAU,CAACP,gBAAgB;MAC5C,CAAC;MAED,MAAM3G,KAAK,CAACsO,KAAK,CAAC,qDAAqDvH,iBAAiB,GAAG,EAAEsH,UAAU,CAAC;;MAExG;MACAtL,mBAAmB,CAAC0D,oBAAoB,CAAC;MACzCxD,eAAe,CAAC0D,gBAAgB,CAAC;MACjCH,kBAAkB,CAAC,KAAK,CAAC;MACzBjD,aAAa,CAAC,6BAA6B,CAAC;;MAE5C;MACAiG,UAAU,CAAC,MAAM;QACfjG,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO0F,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,wBAAwB,EAAE6F,GAAG,CAAC;MAC5C,MAAMgE,MAAM,GAAGhE,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACtB,IAAI,GAC5C,OAAOoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,KAAK,QAAQ,GACnCqF,IAAI,CAACC,SAAS,CAAClE,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAAC,GACjCoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,GACnB,4CAA4C;MAChDxE,QAAQ,CAAC4J,MAAM,CAAC;IAClB,CAAC,SAAS;MACRtJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4K,qBAAqB,GAAGA,CAAA,KAAM;IAClC3I,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF;MACA,MAAM4I,GAAG,GAAG,IAAIpM,KAAK,CAAC;QACpBqM,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC;MAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAExD;MACA,MAAMC,cAAc,GAAGnJ,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GAC1F9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE,CAAC;MACtB6L,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACU,IAAI,CAACE,cAAc,EAAE,GAAG,EAAE,EAAE,EAAE;QAAED,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEtD;MACAX,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,kBAAkB,IAAIG,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEH,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE3F;MACAX,GAAG,CAACe,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/Bf,GAAG,CAACgB,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;;MAEzB;MACA,IAAIC,IAAI,GAAG,EAAE;;MAEb;MACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAEO,IAAI,CAAC;MACzCA,IAAI,IAAI,EAAE;MAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,0BAA0BpM,gBAAgB,EAAE,EAAE,EAAE,EAAE2M,IAAI,CAAC;MAChEA,IAAI,IAAI,CAAC;MACTjB,GAAG,CAACU,IAAI,CAAC,sBAAsBlM,YAAY,EAAE,EAAE,EAAE,EAAEyM,IAAI,CAAC;MACxDA,IAAI,IAAI,CAAC;MACTjB,GAAG,CAACU,IAAI,CAAC,kBAAkBnJ,YAAY,GAAG,EAAE,EAAE,EAAE0J,IAAI,CAAC;MACrDA,IAAI,IAAI,EAAE;;MAEV;MACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAEO,IAAI,CAAC;MACvCA,IAAI,IAAI,EAAE;MAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACA,MAAMnG,KAAK,GAAGiD,MAAM,CAAC2D,OAAO,CAAC5K,cAAc,CAAC;MAC5C,MAAM6K,WAAW,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC;MACtD,MAAMC,aAAa,GAAG9G,KAAK,CAAC+G,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,CAAC,EAAEC,GAAG,CAAC,KAAKF,GAAG,GAAGE,GAAG,EAAE,CAAC,CAAC;;MAEnE;MACAxB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,IAAI,CAAC;MAClCjB,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,IAAI,CAAC;MAClCjB,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,IAAI,CAAC;MAClCA,IAAI,IAAI,CAAC;;MAET;MACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCnG,KAAK,CAACC,OAAO,CAAC,CAAC,CAACqB,IAAI,EAAE6F,QAAQ,CAAC,KAAK;QAClC,MAAMC,UAAU,GAAGN,aAAa,GAAG,CAAC,GAAG,CAAEK,QAAQ,GAAGL,aAAa,GAAI,GAAG,EAAEvI,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;QAC5FmH,GAAG,CAACU,IAAI,CAAC9E,IAAI,CAAC+F,WAAW,CAAC,CAAC,EAAE,EAAE,EAAEV,IAAI,CAAC;QACtCjB,GAAG,CAACU,IAAI,CAACe,QAAQ,CAACG,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAEX,IAAI,CAAC;QACvCjB,GAAG,CAACU,IAAI,CAAC,GAAGgB,UAAU,GAAG,EAAE,EAAE,EAAET,IAAI,CAAC;QACpCA,IAAI,IAAI,CAAC;MACX,CAAC,CAAC;;MAEF;MACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,EAAE,EAAE,EAAEO,IAAI,CAAC;MAC3BjB,GAAG,CAACU,IAAI,CAACU,aAAa,CAACQ,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAEX,IAAI,CAAC;MAC5CjB,GAAG,CAACU,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAEO,IAAI,CAAC;MAC5BA,IAAI,IAAI,EAAE;;MAEV;MACA,IAAI7K,aAAa,CAAC+D,MAAM,GAAG,CAAC,EAAE;QAC5B6F,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;QAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAEO,IAAI,CAAC;QACvCA,IAAI,IAAI,EAAE;QAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;QAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAElCrK,aAAa,CAACmE,OAAO,CAAC,CAACC,MAAM,EAAEsC,KAAK,KAAK;UAAA,IAAA+E,qBAAA;UACvC,MAAMC,SAAS,GAAG,EAAAD,qBAAA,GAAArH,MAAM,CAACuH,mBAAmB,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,UAAU,KAAIxH,MAAM,CAACyH,KAAK,IAAI,KAAK;UACjFjC,GAAG,CAACU,IAAI,CAAC,SAAS5D,KAAK,GAAG,CAAC,KAAKgF,SAAS,EAAE,EAAE,EAAE,EAAEb,IAAI,CAAC;UACtDA,IAAI,IAAI,CAAC;QACX,CAAC,CAAC;QAEFA,IAAI,IAAI,CAAC;MACX;;MAEA;MACA,IAAIvM,YAAY,EAAE;QAChBsL,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;QAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAAC,eAAe,EAAE,EAAE,EAAEO,IAAI,CAAC;QACnCA,IAAI,IAAI,EAAE;QAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;QAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;QAElC;QACA,MAAMyB,UAAU,GAAGlC,GAAG,CAACmC,eAAe,CAACzN,YAAY,EAAE,GAAG,CAAC;QACzDsL,GAAG,CAACU,IAAI,CAACwB,UAAU,EAAE,EAAE,EAAEjB,IAAI,CAAC;QAC9BA,IAAI,IAAIiB,UAAU,CAAC/H,MAAM,GAAG,CAAC,GAAG,CAAC;MACnC;;MAEA;MACA,IAAI3E,iBAAiB,IAAIA,iBAAiB,CAAC2E,MAAM,GAAG,CAAC,EAAE;QACrD6F,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;QAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAEO,IAAI,CAAC;QACpCA,IAAI,IAAI,EAAE;QAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;QAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCT,GAAG,CAACU,IAAI,CAAC,qBAAqBlL,iBAAiB,CAAC2E,MAAM,EAAE,EAAE,EAAE,EAAE8G,IAAI,CAAC;QACnEA,IAAI,IAAI,CAAC;QACTjB,GAAG,CAACU,IAAI,CAAC,0CAA0C,EAAE,EAAE,EAAEO,IAAI,CAAC;QAC9DA,IAAI,IAAI,EAAE;MACZ;;MAEA;MACAjB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC;MAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,IAAIG,IAAI,CAAC,CAAC,CAACuB,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEzB,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFX,GAAG,CAACU,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEhF;MACA,MAAM0B,gBAAgB,GAAG5K,WAAW,CAAC6K,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAClEtC,GAAG,CAACuC,IAAI,CAAC,kBAAkBF,gBAAgB,IAAI,IAAIxB,IAAI,CAAC,CAAC,CAAC2B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;MAE3FrL,aAAa,CAAC,KAAK,CAAC;MACpBF,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2BAA2BD,KAAK,CAAC4J,OAAO,EAAE,CAAC;MACpDpH,aAAa,CAAC,KAAK,CAAC;MACpBF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,IAAIhC,OAAO,EAAE,oBACXpB,OAAA;IAAK4O,SAAS,EAAC,kDAAkD;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,eAC3F/O,OAAA,CAAC7B,OAAO;MAAC6Q,SAAS,EAAC,QAAQ;MAACC,IAAI,EAAC,QAAQ;MAACC,OAAO,EAAC,SAAS;MAAAH,QAAA,eACzD/O,OAAA;QAAM4O,SAAS,EAAC,iBAAiB;QAAAG,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;;EAGR;EACA,MAAMC,iBAAiB,GAAIpB,KAAK,IAAK;IACnC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;;IAEvB;IACA7H,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4H,KAAK,CAAC;;IAE3C;IACA,IAAIqB,OAAO;;IAEX;IACA,IAAIrB,KAAK,CAACsB,UAAU,CAAC,GAAG,CAAC,EAAE;MACzBD,OAAO,GAAGrB,KAAK;IACjB;IACA;IAAA,KACK,IAAI,kBAAkB,CAACuB,IAAI,CAACvB,KAAK,CAAC,IAAI,kBAAkB,CAACuB,IAAI,CAACvB,KAAK,CAAC,EAAE;MACzEqB,OAAO,GAAG,IAAIrB,KAAK,EAAE;IACvB;IACA;IAAA,KACK;MACH;MACA,MAAMwB,QAAQ,GAAG;QACf,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACV,CAAC;;MAED;MACAH,OAAO,GAAGG,QAAQ,CAACxB,KAAK,CAACyB,WAAW,CAAC,CAAC,CAAC,IAAIzB,KAAK;IAClD;IAEA,oBACEnO,OAAA,CAACrB,cAAc;MACbkR,SAAS,EAAC,KAAK;MACfC,OAAO,eAAE9P,OAAA,CAACpB,OAAO;QAACgQ,SAAS,EAAC,gBAAgB;QAAAG,QAAA,EAAEZ;MAAK;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAE;MAAAP,QAAA,eAE/D/O,OAAA;QACE4O,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UAAEkB,eAAe,EAAEP;QAAQ,CAAE;QACpC,cAAYrB;MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAErB,CAAC;;EAED;EACA,MAAMU,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMxJ,KAAK,GAAGiD,MAAM,CAAC2D,OAAO,CAAC5K,cAAc,CAAC;IAC5C,MAAMyN,WAAW,GAAG/G,IAAI,CAACC,GAAG,CAAC,GAAG3C,KAAK,CAAC0J,GAAG,CAAC,CAAC,CAACzC,CAAC,EAAEC,GAAG,CAAC,KAAKA,GAAG,CAAC,CAAC;IAE7D,OAAOlH,KAAK,CAAC0J,GAAG,CAAC,CAAC,CAACpI,IAAI,EAAE6F,QAAQ,CAAC,kBAChC3N,OAAA;MAAgB4O,SAAS,EAAC,MAAM;MAAAG,QAAA,gBAC9B/O,OAAA;QAAK4O,SAAS,EAAC,qCAAqC;QAAAG,QAAA,gBAClD/O,OAAA;UAAA+O,QAAA,eAAM/O,OAAA;YAAA+O,QAAA,EAASjH,IAAI,CAAC+F,WAAW,CAAC;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClDtP,OAAA;UAAA+O,QAAA,GAAOpB,QAAQ,EAAC,MAAI;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACNtP,OAAA,CAAC1B,WAAW;QACV6R,GAAG,EAAEF,WAAW,GAAItC,QAAQ,GAAGsC,WAAW,GAAI,GAAG,GAAG,CAAE;QACtDf,OAAO,EAAEvB,QAAQ,GAAG,CAAC,GAAG,MAAM,GAAG,OAAQ;QACzCiB,SAAS,EAAC;MAAmB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA,GATMxH,IAAI;MAAAqH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUT,CACN,CAAC;EACJ,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAGA,CAAA,kBACxBpQ,OAAA,CAAC3B,KAAK;IACJgS,IAAI,EAAEpN,gBAAiB;IACvBqN,MAAM,EAAExF,wBAAyB;IACjCyF,QAAQ;IACR3B,SAAS,EAAC,oBAAoB;IAAAG,QAAA,gBAE9B/O,OAAA,CAAC3B,KAAK,CAACmS,MAAM;MAACC,WAAW;MAAA1B,QAAA,eACvB/O,OAAA,CAAC3B,KAAK,CAACqS,KAAK;QAAA3B,QAAA,EAAC;MAAwB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACftP,OAAA,CAAC3B,KAAK,CAACsS,IAAI;MAAA5B,QAAA,gBACT/O,OAAA;QAAA+O,QAAA,EAAG;MAAyE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChFtP,OAAA,CAACtB,KAAK;QAACkS,QAAQ;QAACC,KAAK;QAAC/I,IAAI,EAAC,IAAI;QAAC8G,SAAS,EAAC,MAAM;QAAAG,QAAA,eAC9C/O,OAAA;UAAA+O,QAAA,gBACE/O,OAAA;YAAA+O,QAAA,gBACE/O,OAAA;cAAA+O,QAAA,eAAI/O,OAAA;gBAAA+O,QAAA,EAAQ;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCtP,OAAA;cAAA+O,QAAA,EAAKpL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GAC1E9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACLtP,OAAA;YAAA+O,QAAA,gBACE/O,OAAA;cAAA+O,QAAA,eAAI/O,OAAA;gBAAA+O,QAAA,EAAQ;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CtP,OAAA;cAAA+O,QAAA,GAAI,MAAI,EAACvO,gBAAgB;YAAA;cAAA2O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACLtP,OAAA;YAAA+O,QAAA,gBACE/O,OAAA;cAAA+O,QAAA,eAAI/O,OAAA;gBAAA+O,QAAA,EAAQ;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxCtP,OAAA;cAAA+O,QAAA,GAAI,MAAI,EAACrO,YAAY;YAAA;cAAAyO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACLtP,OAAA;YAAA+O,QAAA,gBACE/O,OAAA;cAAA+O,QAAA,eAAI/O,OAAA;gBAAA+O,QAAA,EAAQ;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxCtP,OAAA;cAAA+O,QAAA,GAAKtL,YAAY,EAAC,GAAC;YAAA;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,EACJ1O,YAAY,iBACXZ,OAAA;YAAA+O,QAAA,gBACE/O,OAAA;cAAA+O,QAAA,eAAI/O,OAAA;gBAAA+O,QAAA,EAAQ;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCtP,OAAA;cAAA+O,QAAA,EAAKnO;YAAY;cAAAuO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACP9N,gBAAgB,CAAC6E,MAAM,GAAG,CAAC,iBAC1BrG,OAAA;QAAK4O,SAAS,EAAC,kBAAkB;QAAAG,QAAA,gBAC/B/O,OAAA;UAAA+O,QAAA,eAAG/O,OAAA;YAAA+O,QAAA,EAAQ;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtCtP,OAAA,CAAC5B,KAAK;UACJ0S,GAAG,EAAEtP,gBAAgB,CAAC,CAAC,CAAE;UACzBuP,GAAG,EAAC,SAAS;UACbC,SAAS;UACTnC,KAAK,EAAE;YAAEoC,SAAS,EAAE;UAAQ;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eACbtP,OAAA,CAAC3B,KAAK,CAAC6S,MAAM;MAAAnC,QAAA,gBACX/O,OAAA,CAACjC,MAAM;QAACmR,OAAO,EAAC,WAAW;QAACiC,OAAO,EAAErG,wBAAyB;QAAAiE,QAAA,EAAC;MAE/D;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtP,OAAA,CAACjC,MAAM;QAACmR,OAAO,EAAC,SAAS;QAACiC,OAAO,EAAEtH,YAAa;QAAAkF,QAAA,gBAC9C/O,OAAA,CAACnB,OAAO;UAAC+P,SAAS,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACR;EAED,oBACEtP,OAAA,CAAAE,SAAA;IAAA6O,QAAA,gBACE/O,OAAA,CAACH,eAAe;MAAAsP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBtP,OAAA;MAAK4O,SAAS,EAAC,cAAc;MAAAG,QAAA,eAC3B/O,OAAA,CAAC/B,GAAG;QAAC2Q,SAAS,EAAC,wBAAwB;QAAAG,QAAA,eACrC/O,OAAA,CAAC9B,GAAG;UAACkT,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtC,QAAA,eACjB/O,OAAA,CAACnC,IAAI;YAAC+Q,SAAS,EAAC,8BAA8B;YAACC,KAAK,EAAE;cAAEkB,eAAe,EAAE,SAAS;cAAEuB,YAAY,EAAE;YAAO,CAAE;YAAAvC,QAAA,eACzG/O,OAAA,CAACnC,IAAI,CAAC8S,IAAI;cAAA5B,QAAA,gBACR/O,OAAA;gBAAI4O,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,EAAC;cAAwB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAG9DtP,OAAA;gBAAK4O,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,EAC9BlL,aAAa,gBACZ7D,OAAA;kBAAK4O,SAAS,EAAC,kDAAkD;kBAAAG,QAAA,gBAC/D/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;oBAAC3C,SAAS,EAAC,uBAAuB;oBAACC,KAAK,EAAE;sBAAE2C,QAAQ,EAAE;oBAAQ,CAAE;oBAAAzC,QAAA,gBACzE/O,OAAA,CAAClC,IAAI,CAAC2T,OAAO;sBACX7J,IAAI,EAAC,MAAM;sBACX8J,KAAK,EAAE/N,WAAY;sBACnBgO,QAAQ,EAAGvK,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;sBAChDE,SAAS,EAAE,CAAC,CAAC7N,gBAAiB;sBAC9B8N,WAAW,EAAC;oBAAoB;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;sBAAClK,IAAI,EAAC,SAAS;sBAAAmH,QAAA,EAClChL;oBAAgB;sBAAAoL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACbtP,OAAA,CAACjC,MAAM;oBACLmR,OAAO,EAAC,SAAS;oBACjBpH,IAAI,EAAC,IAAI;oBACT8G,SAAS,EAAC,MAAM;oBAChBuC,OAAO,EAAEhG,eAAgB;oBAAA4D,QAAA,eAEzB/O,OAAA,CAACnB,OAAO;sBAAAsQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACTtP,OAAA,CAACjC,MAAM;oBACLmR,OAAO,EAAC,WAAW;oBACnBpH,IAAI,EAAC,IAAI;oBACTqJ,OAAO,EAAEjG,iBAAkB;oBAAA6D,QAAA,eAE3B/O,OAAA,CAACR,MAAM;sBAAA2P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAENtP,OAAA;kBAAK4O,SAAS,EAAC,kDAAkD;kBAAAG,QAAA,gBAC/D/O,OAAA;oBAAG4O,SAAS,EAAC,kCAAkC;oBAAAG,QAAA,EAC5CpL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACJtP,OAAA,CAACjC,MAAM;oBACLmR,OAAO,EAAC,iBAAiB;oBACzBpH,IAAI,EAAC,IAAI;oBACTqJ,OAAO,EAAElG,gBAAiB;oBAAA8D,QAAA,EAC3B;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAELxO,KAAK,iBACJd,OAAA,CAAChC,KAAK;gBAACkR,OAAO,EAAC,QAAQ;gBAACN,SAAS,EAAC,cAAc;gBAAAG,QAAA,eAC9C/O,OAAA;kBAAK4O,SAAS,EAAC,mDAAmD;kBAAAG,QAAA,gBAChE/O,OAAA;oBAAA+O,QAAA,gBACE/O,OAAA,CAACP,qBAAqB;sBAACmP,SAAS,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzCxO,KAAK;kBAAA;oBAAAqO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtP,OAAA,CAACjC,MAAM;oBACLmR,OAAO,EAAC,gBAAgB;oBACxBpH,IAAI,EAAC,IAAI;oBACTqJ,OAAO,EAAEhM,gBAAiB;oBAAA4J,QAAA,EAC3B;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR,EAEAtO,UAAU,iBACThB,OAAA,CAAChC,KAAK;gBAACkR,OAAO,EAAC,SAAS;gBAACN,SAAS,EAAC,cAAc;gBAAAG,QAAA,gBAC/C/O,OAAA,CAACnB,OAAO;kBAAC+P,SAAS,EAAC;gBAAM;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC3BtO,UAAU;cAAA;gBAAAmO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACR,EAEApO,UAAU,gBACTlB,OAAA;gBAAK4O,SAAS,EAAC,oCAAoC;gBAAAG,QAAA,gBACjD/O,OAAA;kBAAI4O,SAAS,EAAC,+BAA+B;kBAAAG,QAAA,gBAC3C/O,OAAA,CAACnB,OAAO;oBAAC+P,SAAS,EAAC;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4BAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAELtP,OAAA;kBAAK4O,SAAS,EAAC,kBAAkB;kBAAAG,QAAA,EAC9BlL,aAAa,gBACZ7D,OAAA;oBAAK4O,SAAS,EAAC,kDAAkD;oBAAAG,QAAA,gBAC/D/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;sBAAC3C,SAAS,EAAC,uBAAuB;sBAACC,KAAK,EAAE;wBAAE2C,QAAQ,EAAE;sBAAQ,CAAE;sBAAAzC,QAAA,gBACzE/O,OAAA,CAAClC,IAAI,CAAC2T,OAAO;wBACX7J,IAAI,EAAC,MAAM;wBACX8J,KAAK,EAAE/N,WAAY;wBACnBgO,QAAQ,EAAGvK,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;wBAChDE,SAAS,EAAE,CAAC,CAAC7N,gBAAiB;wBAC9B8N,WAAW,EAAC;sBAAoB;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;wBAAClK,IAAI,EAAC,SAAS;wBAAAmH,QAAA,EAClChL;sBAAgB;wBAAAoL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACbtP,OAAA,CAACjC,MAAM;sBACLmR,OAAO,EAAC,SAAS;sBACjBpH,IAAI,EAAC,IAAI;sBACT8G,SAAS,EAAC,MAAM;sBAChBuC,OAAO,EAAEhG,eAAgB;sBAAA4D,QAAA,eAEzB/O,OAAA,CAACnB,OAAO;wBAAAsQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACTtP,OAAA,CAACjC,MAAM;sBACLmR,OAAO,EAAC,WAAW;sBACnBpH,IAAI,EAAC,IAAI;sBACTqJ,OAAO,EAAEjG,iBAAkB;sBAAA6D,QAAA,eAE3B/O,OAAA,CAACR,MAAM;wBAAA2P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAENtP,OAAA;oBAAK4O,SAAS,EAAC,kDAAkD;oBAAAG,QAAA,gBAC/D/O,OAAA;sBAAI4O,SAAS,EAAC,WAAW;sBAAAG,QAAA,EACtBpL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACLtP,OAAA,CAACjC,MAAM;sBACLmR,OAAO,EAAC,iBAAiB;sBACzBpH,IAAI,EAAC,IAAI;sBACTqJ,OAAO,EAAElG,gBAAiB;sBAAA8D,QAAA,EAC3B;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENtP,OAAA;kBAAK4O,SAAS,EAAC,2BAA2B;kBAAAG,QAAA,eACtC/O,OAAA,CAAC/B,GAAG;oBAAC2Q,SAAS,EAAC,MAAM;oBAAAG,QAAA,gBACnB/O,OAAA,CAAC9B,GAAG;sBAACkT,EAAE,EAAE,CAAE;sBAAArC,QAAA,gBACT/O,OAAA;wBAAK4O,SAAS,EAAC,wDAAwD;wBAAAG,QAAA,gBACrE/O,OAAA;0BAAI4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAAC/O,OAAA,CAACd,eAAe;4BAAC0P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,uBAAmB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EAChF,CAACrL,eAAe,iBACfjE,OAAA,CAACjC,MAAM;0BACLmR,OAAO,EAAC,iBAAiB;0BACzBpH,IAAI,EAAC,IAAI;0BACTqJ,OAAO,EAAExF,kBAAmB;0BAAAoD,QAAA,EAC7B;wBAED;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAELrL,eAAe,gBACdjE,OAAA;wBAAK4O,SAAS,EAAC,sBAAsB;wBAAAG,QAAA,eACnC/O,OAAA,CAAClC,IAAI;0BAAAiR,QAAA,gBACH/O,OAAA,CAAC/B,GAAG;4BAAA8Q,QAAA,gBACF/O,OAAA,CAAC9B,GAAG;8BAACkT,EAAE,EAAE,CAAE;8BAAArC,QAAA,eACT/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;gCAAC3C,SAAS,EAAC,MAAM;gCAAAG,QAAA,gBAC1B/O,OAAA,CAAClC,IAAI,CAACiU,KAAK;kCAAAhD,QAAA,eAAC/O,OAAA;oCAAA+O,QAAA,EAAQ;kCAAwB;oCAAAI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC,eAClEtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO;kCACX7J,IAAI,EAAC,QAAQ;kCACboK,IAAI,EAAC,MAAM;kCACXC,GAAG,EAAC,GAAG;kCACPP,KAAK,EAAEvN,oBAAqB;kCAC5BwN,QAAQ,EAAGvK,CAAC,IAAKhD,uBAAuB,CAACgD,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;kCACzDE,SAAS,EAAE,CAAC,CAACrN,eAAe,CAACJ;gCAAqB;kCAAAgL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnD,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;kCAAClK,IAAI,EAAC,SAAS;kCAAAmH,QAAA,EAClCxK,eAAe,CAACJ;gCAAoB;kCAAAgL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAChB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNtP,OAAA,CAAC9B,GAAG;8BAACkT,EAAE,EAAE,CAAE;8BAAArC,QAAA,eACT/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;gCAAC3C,SAAS,EAAC,MAAM;gCAAAG,QAAA,gBAC1B/O,OAAA,CAAClC,IAAI,CAACiU,KAAK;kCAAAhD,QAAA,eAAC/O,OAAA;oCAAA+O,QAAA,EAAQ;kCAAoB;oCAAAI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC,eAC9DtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO;kCACX7J,IAAI,EAAC,QAAQ;kCACboK,IAAI,EAAC,MAAM;kCACXC,GAAG,EAAC,GAAG;kCACPP,KAAK,EAAErN,gBAAiB;kCACxBsN,QAAQ,EAAGvK,CAAC,IAAK9C,mBAAmB,CAAC8C,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;kCACrDE,SAAS,EAAE,CAAC,CAACrN,eAAe,CAACF;gCAAiB;kCAAA8K,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC/C,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;kCAAClK,IAAI,EAAC,SAAS;kCAAAmH,QAAA,EAClCxK,eAAe,CAACF;gCAAgB;kCAAA8K,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EAELnL,oBAAoB,IAAIE,gBAAgB,IAAIO,UAAU,CAACT,oBAAoB,CAAC,GAAG,CAAC,IAAIS,UAAU,CAACP,gBAAgB,CAAC,GAAG,CAAC,iBACnHrE,OAAA;4BAAK4O,SAAS,EAAC,wCAAwC;4BAAAG,QAAA,gBACrD/O,OAAA;8BAAA+O,QAAA,EAAQ;4BAAmB;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACnC,CAAE,CAAC1K,UAAU,CAACP,gBAAgB,CAAC,GAAGO,UAAU,CAACT,oBAAoB,CAAC,IAAIS,UAAU,CAACP,gBAAgB,CAAC,GAAI,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,EAAC,GACzH;0BAAA;4BAAAoK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN,eAEDtP,OAAA;4BAAK4O,SAAS,EAAC,cAAc;4BAAAG,QAAA,gBAC3B/O,OAAA,CAACjC,MAAM;8BACLmR,OAAO,EAAC,SAAS;8BACjBpH,IAAI,EAAC,IAAI;8BACTqJ,OAAO,EAAErF,gBAAiB;8BAC1BoG,QAAQ,EAAE9Q,OAAQ;8BAAA2N,QAAA,gBAElB/O,OAAA,CAACnB,OAAO;gCAAC+P,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,gBAE9B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eACTtP,OAAA,CAACjC,MAAM;8BACLmR,OAAO,EAAC,WAAW;8BACnBpH,IAAI,EAAC,IAAI;8BACTqJ,OAAO,EAAEvF,mBAAoB;8BAAAmD,QAAA,gBAE7B/O,OAAA,CAACR,MAAM;gCAACoP,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,UAE7B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,gBAENtP,OAAA,CAACtB,KAAK;wBAACkS,QAAQ;wBAACC,KAAK;wBAAA9B,QAAA,eACnB/O,OAAA;0BAAA+O,QAAA,gBACE/O,OAAA;4BAAA+O,QAAA,gBACE/O,OAAA;8BAAA+O,QAAA,eAAI/O,OAAA;gCAAA+O,QAAA,EAAQ;8BAAkB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC5CtP,OAAA;8BAAA+O,QAAA,GAAI,MAAI,EAACvO,gBAAgB;4BAAA;8BAAA2O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7B,CAAC,eACLtP,OAAA;4BAAA+O,QAAA,gBACE/O,OAAA;8BAAA+O,QAAA,eAAI/O,OAAA;gCAAA+O,QAAA,EAAQ;8BAAc;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACxCtP,OAAA;8BAAA+O,QAAA,GAAI,MAAI,EAACrO,YAAY;4BAAA;8BAAAyO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,eACLtP,OAAA;4BAAA+O,QAAA,gBACE/O,OAAA;8BAAA+O,QAAA,eAAI/O,OAAA;gCAAA+O,QAAA,EAAQ;8BAAc;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACxCtP,OAAA;8BAAA+O,QAAA,GAAKtL,YAAY,EAAC,GAAC;4BAAA;8BAAA0L,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACR,EAEA1O,YAAY,iBACXZ,OAAA;wBAAK4O,SAAS,EAAC,MAAM;wBAAAG,QAAA,gBACnB/O,OAAA;0BAAI4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAAC/O,OAAA,CAACV,eAAe;4BAACsP,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,SAAK;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnEtP,OAAA;0BAAK4O,SAAS,EAAC,sBAAsB;0BAAAG,QAAA,EAClCnO;wBAAY;0BAAAuO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN,eAEDtP,OAAA;wBAAI4O,SAAS,EAAC,WAAW;wBAAAG,QAAA,gBAAC/O,OAAA,CAAChB,MAAM;0BAAC4P,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAAM;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChEtP,OAAA;wBAAK4O,SAAS,EAAC,MAAM;wBAAAG,QAAA,EAClBzM,aAAa,CAAC+D,MAAM,GAAG,CAAC,GACvB/D,aAAa,CAAC4N,GAAG,CAAC,CAACxJ,MAAM,EAAEsC,KAAK;0BAAA,IAAAmJ,sBAAA,EAAAC,sBAAA;0BAAA,oBAC9BpS,OAAA;4BAAiB4O,SAAS,EAAC,MAAM;4BAAAG,QAAA,GAC9BQ,iBAAiB,CAAC,EAAA4C,sBAAA,GAAAzL,MAAM,CAACuH,mBAAmB,cAAAkE,sBAAA,uBAA1BA,sBAAA,CAA4BhE,KAAK,KAAIzH,MAAM,CAACyH,KAAK,IAAI,MAAM,CAAC,eAC/EnO,OAAA;8BAAM4O,SAAS,EAAC,MAAM;8BAAAG,QAAA,EAAE,EAAAqD,sBAAA,GAAA1L,MAAM,CAACuH,mBAAmB,cAAAmE,sBAAA,uBAA1BA,sBAAA,CAA4BlE,UAAU,KAAIxH,MAAM,CAACyH;4BAAK;8BAAAgB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA,GAF9EtG,KAAK;4BAAAmG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAGV,CAAC;wBAAA,CACP,CAAC,gBAEFtP,OAAA;0BAAG4O,SAAS,EAAC,YAAY;0BAAAG,QAAA,EAAC;wBAA8B;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAC5D;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENtP,OAAA;wBAAI4O,SAAS,EAAC,MAAM;wBAAAG,QAAA,gBAAC/O,OAAA,CAACX,SAAS;0BAACuP,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,qBAAiB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACxEU,sBAAsB,CAAC,CAAC,eAEzBhQ,OAAA;wBAAK4O,SAAS,EAAC,MAAM;wBAAAG,QAAA,eACnB/O,OAAA,CAACjC,MAAM;0BACLmR,OAAO,EAAC,iBAAiB;0BACzBN,SAAS,EAAC,OAAO;0BACjBuC,OAAO,EAAEpG,YAAa;0BAAAgE,QAAA,gBAEtB/O,OAAA,CAACN,SAAS;4BAACkP,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,2BAEhC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENtP,OAAA,CAAC9B,GAAG;sBAACkT,EAAE,EAAE,CAAE;sBAACxC,SAAS,EAAC,aAAa;sBAAAG,QAAA,gBACjC/O,OAAA;wBAAI4O,SAAS,EAAC,MAAM;wBAAAG,QAAA,gBAAC/O,OAAA,CAACjB,OAAO;0BAAC6P,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,kBAAc;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACnE5N,iBAAiB,IAAIA,iBAAiB,CAAC2E,MAAM,GAAG,CAAC,gBAChDrG,OAAA;wBAAK4O,SAAS,EAAC,0BAA0B;wBAAAG,QAAA,gBACvC/O,OAAA;0BAAK4O,SAAS,EAAC,qBAAqB;0BAAAG,QAAA,EACjCrN,iBAAiB,CAACwO,GAAG,CAAC,CAACmC,QAAQ,EAAErJ,KAAK,kBACrChJ,OAAA;4BAEE4O,SAAS,EAAE,sBAAsB5F,KAAK,KAAKlH,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;4BAC9EqP,OAAO,EAAEA,CAAA,KAAMpP,mBAAmB,CAACiH,KAAK,CAAE;4BAAA+F,QAAA,gBAE1C/O,OAAA,CAAC5B,KAAK;8BACJ0S,GAAG,EAAEuB,QAAS;8BACdtB,GAAG,EAAE,WAAW/H,KAAK,GAAG,CAAC,EAAG;8BAC5BgI,SAAS;8BACTpC,SAAS,EAAC;4BAAe;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACFtP,OAAA;8BAAM4O,SAAS,EAAC,cAAc;8BAAAG,QAAA,EAAE/F,KAAK,GAAG;4BAAC;8BAAAmG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA,GAV5CtG,KAAK;4BAAAmG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAWP,CACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACNtP,OAAA;0BAAK4O,SAAS,EAAC,2BAA2B;0BAAAG,QAAA,eACxC/O,OAAA,CAAC5B,KAAK;4BACJ0S,GAAG,EAAEpP,iBAAiB,CAACI,gBAAgB,CAAE;4BACzCiP,GAAG,EAAC,SAAS;4BACbC,SAAS;4BACTpC,SAAS,EAAC,oBAAoB;4BAC9BC,KAAK,EAAE;8BAAEoC,SAAS,EAAE;4BAAQ;0BAAE;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAENtP,OAAA;wBAAK4O,SAAS,EAAC,sBAAsB;wBAAAG,QAAA,gBACnC/O,OAAA,CAACjB,OAAO;0BAAC+I,IAAI,EAAE,EAAG;0BAAC8G,SAAS,EAAC;wBAAgB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChDtP,OAAA;0BAAG4O,SAAS,EAAC,iBAAiB;0BAAAG,QAAA,EAAC;wBAAmB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENtP,OAAA;gBAAK4O,SAAS,EAAC,UAAU;gBAAAG,QAAA,eACvB/O,OAAA,CAACxB,IAAI;kBACH6B,EAAE,EAAC,uBAAuB;kBAC1BiS,SAAS,EAAEvP,SAAU;kBACrBwP,QAAQ,EAAGC,CAAC,IAAKxP,YAAY,CAACwP,CAAC,CAAE;kBACjC5D,SAAS,EAAC,MAAM;kBAAAG,QAAA,gBAEhB/O,OAAA,CAACvB,GAAG;oBAACgU,QAAQ,EAAC,SAAS;oBAACC,KAAK,eAAE1S,OAAA;sBAAA+O,QAAA,gBAAM/O,OAAA,CAACf,YAAY;wBAAC2P,SAAS,EAAC;sBAAM;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAAe;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAAAP,QAAA,eAC3F/O,OAAA,CAAC/B,GAAG;sBAAC2Q,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBACnB/O,OAAA,CAAC9B,GAAG;wBAACkT,EAAE,EAAE,CAAE;wBAAArC,QAAA,gBACT/O,OAAA;0BAAI4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAAC/O,OAAA,CAACf,YAAY;4BAAC2P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAY;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACtEzL,aAAa,gBACZ7D,OAAA;0BAAK4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBACnB/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;4BAAAxC,QAAA,gBACT/O,OAAA,CAAClC,IAAI,CAAC2T,OAAO;8BACX7J,IAAI,EAAC,MAAM;8BACX8J,KAAK,EAAE/N,WAAY;8BACnBgO,QAAQ,EAAGvK,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;8BAChDE,SAAS,EAAE,CAAC,CAAC7N,gBAAiB;8BAC9B8N,WAAW,EAAC;4BAAoB;8BAAA1C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjC,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;8BAAClK,IAAI,EAAC,SAAS;8BAAAmH,QAAA,EAClChL;4BAAgB;8BAAAoL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eACbtP,OAAA;4BAAK4O,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBACnB/O,OAAA,CAACjC,MAAM;8BACLmR,OAAO,EAAC,SAAS;8BACjBpH,IAAI,EAAC,IAAI;8BACT8G,SAAS,EAAC,MAAM;8BAChBuC,OAAO,EAAEhG,eAAgB;8BAAA4D,QAAA,gBAEzB/O,OAAA,CAACnB,OAAO;gCAAC+P,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,SAC9B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eACTtP,OAAA,CAACjC,MAAM;8BACLmR,OAAO,EAAC,WAAW;8BACnBpH,IAAI,EAAC,IAAI;8BACTqJ,OAAO,EAAEjG,iBAAkB;8BAAA6D,QAAA,gBAE3B/O,OAAA,CAACR,MAAM;gCAACoP,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,WAC7B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAENtP,OAAA;0BAAK4O,SAAS,EAAC,6EAA6E;0BAAAG,QAAA,gBAC1F/O,OAAA;4BAAA+O,QAAA,EACGpL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;0BAAC;4BAAA8O,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eACTtP,OAAA,CAACjC,MAAM;4BACLmR,OAAO,EAAC,iBAAiB;4BACzBpH,IAAI,EAAC,IAAI;4BACTqJ,OAAO,EAAElG,gBAAiB;4BAAA8D,QAAA,EAC3B;0BAED;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACN,eAEDtP,OAAA;0BAAI4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAAC/O,OAAA,CAAChB,MAAM;4BAAC4P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,UAAM;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3DtP,OAAA;0BAAK4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,EAClBzM,aAAa,CAAC+D,MAAM,GAAG,CAAC,GACvB/D,aAAa,CAAC4N,GAAG,CAAC,CAACxJ,MAAM,EAAEsC,KAAK;4BAAA,IAAA2J,sBAAA,EAAAC,sBAAA;4BAAA,oBAC9B5S,OAAA;8BAAiB4O,SAAS,EAAC,MAAM;8BAAAG,QAAA,GAC9BQ,iBAAiB,CAAC,EAAAoD,sBAAA,GAAAjM,MAAM,CAACuH,mBAAmB,cAAA0E,sBAAA,uBAA1BA,sBAAA,CAA4BxE,KAAK,KAAIzH,MAAM,CAACyH,KAAK,IAAI,MAAM,CAAC,eAC/EnO,OAAA;gCAAM4O,SAAS,EAAC,MAAM;gCAAAG,QAAA,EAAE,EAAA6D,sBAAA,GAAAlM,MAAM,CAACuH,mBAAmB,cAAA2E,sBAAA,uBAA1BA,sBAAA,CAA4B1E,UAAU,KAAIxH,MAAM,CAACyH;8BAAK;gCAAAgB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA,GAF9EtG,KAAK;8BAAAmG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAGV,CAAC;0BAAA,CACP,CAAC,gBAEFtP,OAAA;4BAAG4O,SAAS,EAAC,YAAY;4BAAAG,QAAA,EAAC;0BAA8B;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAC5D;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAENtP,OAAA;0BAAI4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAAC/O,OAAA,CAACX,SAAS;4BAACuP,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,qBAAiB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACxEU,sBAAsB,CAAC,CAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eAENtP,OAAA,CAAC9B,GAAG;wBAACkT,EAAE,EAAE,CAAE;wBAAArC,QAAA,eACT/O,OAAA;0BAAK4O,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBACnB/O,OAAA;4BAAI4O,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAAC/O,OAAA,CAACjB,OAAO;8BAAC6P,SAAS,EAAC;4BAAM;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,kBAAc;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACpEtP,OAAA;4BAAG4O,SAAS,EAAC,iBAAiB;4BAAAG,QAAA,GAAC,kDAAgD,EAACzN,aAAa,CAAC+E,MAAM,EAAC,MAAI;0BAAA;4BAAA8I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,EAG5G9N,gBAAgB,CAAC6E,MAAM,GAAG,CAAC,iBAC1BrG,OAAA;4BAAK4O,SAAS,EAAC,+BAA+B;4BAAAG,QAAA,gBAC5C/O,OAAA;8BAAK4O,SAAS,EAAC,qBAAqB;8BAAAG,QAAA,EACjCvN,gBAAgB,CAAC0O,GAAG,CAAC,CAAC2C,UAAU,EAAE7J,KAAK,kBACtChJ,OAAA;gCAEE4O,SAAS,EAAE,sBAAsB5F,KAAK,KAAKlH,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;gCAC9EqP,OAAO,EAAEA,CAAA,KAAMpP,mBAAmB,CAACiH,KAAK,CAAE;gCAAA+F,QAAA,gBAE1C/O,OAAA;kCAAK4O,SAAS,EAAC,eAAe;kCAAAG,QAAA,eAC5B/O,OAAA,CAACjC,MAAM;oCACLmR,OAAO,EAAC,QAAQ;oCAChBpH,IAAI,EAAC,IAAI;oCACT8G,SAAS,EAAC,kBAAkB;oCAC5BuC,OAAO,EAAG/J,CAAC,IAAK;sCACdA,CAAC,CAAC0L,eAAe,CAAC,CAAC;sCACnB/J,WAAW,CAACC,KAAK,CAAC;oCACpB,CAAE;oCAAA+F,QAAA,eAEF/O,OAAA,CAACT,OAAO;sCAAA4P,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACNtP,OAAA,CAAC5B,KAAK;kCACJ0S,GAAG,EAAE+B,UAAW;kCAChB9B,GAAG,EAAE,WAAW/H,KAAK,GAAG,CAAC,EAAG;kCAC5BgI,SAAS;kCACTpC,SAAS,EAAC;gCAAe;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACFtP,OAAA;kCAAM4O,SAAS,EAAC,cAAc;kCAAAG,QAAA,EAAE/F,KAAK,GAAG;gCAAC;kCAAAmG,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA,GAvB5CtG,KAAK;gCAAAmG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAwBP,CACN;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,EAEL9N,gBAAgB,CAAC6E,MAAM,GAAG,CAAC,iBAC1BrG,OAAA;8BAAK4O,SAAS,EAAC,2BAA2B;8BAAAG,QAAA,eACxC/O,OAAA,CAAC5B,KAAK;gCACJ0S,GAAG,EAAEtP,gBAAgB,CAACM,gBAAgB,CAAE;gCACxCiP,GAAG,EAAC,iBAAiB;gCACrBC,SAAS;gCACTpC,SAAS,EAAC,oBAAoB;gCAC9BC,KAAK,EAAE;kCAAEoC,SAAS,EAAE;gCAAQ;8BAAE;gCAAA9B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CACN,EAGApN,WAAW,gBACVlC,OAAA;4BAAK4O,SAAS,EAAC,kCAAkC;4BAAAG,QAAA,gBAC/C/O,OAAA;8BAAI4O,SAAS,EAAC,MAAM;8BAAAG,QAAA,EAAC;4BAAgB;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC1CtP,OAAA,CAAC1B,WAAW;8BACV6R,GAAG,EAAEnO,cAAe;8BACpB+Q,KAAK,EAAE,GAAG7J,IAAI,CAACqB,KAAK,CAACvI,cAAc,CAAC,GAAI;8BACxCkN,OAAO,EAAC,MAAM;8BACd8D,QAAQ;8BACRpE,SAAS,EAAC;4BAAM;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CAAC,eACFtP,OAAA;8BAAG4O,SAAS,EAAC,YAAY;8BAAAG,QAAA,gBACvB/O,OAAA,CAAClB,QAAQ;gCAAC8P,SAAS,EAAC;8BAAmB;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,cAChC,EAAChO,aAAa,CAAC+E,MAAM,EAAC,YAClC;4BAAA;8BAAA8I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC,GAENhO,aAAa,CAAC+E,MAAM,GAAG,EAAE,iBACvBrG,OAAA;4BAAA,GAAS0I,YAAY,CAAC,CAAC;4BAAEkG,SAAS,EAAE,0BAA0BhG,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;4BAAAmG,QAAA,gBAC3F/O,OAAA;8BAAA,GAAW2I,aAAa,CAAC,CAAC;8BAAEsK,QAAQ;4BAAA;8BAAA9D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACvCtP,OAAA;8BAAK4O,SAAS,EAAC,aAAa;8BAAAG,QAAA,gBAC1B/O,OAAA,CAAClB,QAAQ;gCAACgJ,IAAI,EAAE,EAAG;gCAAC8G,SAAS,EAAC;8BAAmB;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eACpDtP,OAAA;gCAAA+O,QAAA,EAAG;8BAAmD;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eAC1DtP,OAAA;gCAAG4O,SAAS,EAAC,kBAAkB;gCAAAG,QAAA,EAAC;8BAAiD;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eACrFtP,OAAA;gCAAG4O,SAAS,EAAC,kBAAkB;gCAAAG,QAAA,EAAC;8BAAgD;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAER,eAEDtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO;4BACX7J,IAAI,EAAC,MAAM;4BACXsL,GAAG,EAAE3S,YAAa;4BAClBoR,QAAQ,EAAExK,iBAAkB;4BAC5B0B,MAAM,EAAC,SAAS;4BAChBoK,QAAQ;4BACRpE,KAAK,EAAE;8BAAEsE,OAAO,EAAE;4BAAO;0BAAE;4BAAAhE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtP,OAAA,CAACvB,GAAG;oBAACgU,QAAQ,EAAC,SAAS;oBAACC,KAAK,eAAE1S,OAAA;sBAAA+O,QAAA,gBAAM/O,OAAA,CAACd,eAAe;wBAAC0P,SAAS,EAAC;sBAAM;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAAO;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAAAP,QAAA,eACtF/O,OAAA,CAAClC,IAAI;sBAACsV,QAAQ,EAAEzJ,gBAAiB;sBAACiF,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBAChD/O,OAAA,CAAC/B,GAAG;wBAAA8Q,QAAA,gBACF/O,OAAA,CAAC9B,GAAG;0BAACkT,EAAE,EAAE,CAAE;0BAAArC,QAAA,gBACT/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;4BAAC3C,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAC1B/O,OAAA,CAAClC,IAAI,CAACiU,KAAK;8BAAAhD,QAAA,gBACT/O,OAAA;gCAAA+O,QAAA,EAAQ;8BAAwB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACzCtP,OAAA,CAACrB,cAAc;gCACbkR,SAAS,EAAC,KAAK;gCACfC,OAAO,eAAE9P,OAAA,CAACpB,OAAO;kCAAAmQ,QAAA,EAAC;gCAAoC;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAS,CAAE;gCAAAP,QAAA,eAEjE/O,OAAA,CAACf,YAAY;kCAAC2P,SAAS,EAAC;gCAAiB;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACbtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO;8BACX7J,IAAI,EAAC,QAAQ;8BACboK,IAAI,EAAC,MAAM;8BACXC,GAAG,EAAC,GAAG;8BACPP,KAAK,EAAElR,gBAAiB;8BACxBmR,QAAQ,EAAGvK,CAAC,IAAK3G,mBAAmB,CAAC2G,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;8BACrDE,SAAS,EAAE,CAAC,CAACrO,gBAAgB,CAAC/C,gBAAiB;8BAC/C6S,QAAQ;4BAAA;8BAAAlE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;8BAAClK,IAAI,EAAC,SAAS;8BAAAmH,QAAA,EAClCxL,gBAAgB,CAAC/C;4BAAgB;8BAAA2O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACb,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eAEbtP,OAAA,CAAClC,IAAI,CAACyT,KAAK;4BAAC3C,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAC1B/O,OAAA,CAAClC,IAAI,CAACiU,KAAK;8BAAAhD,QAAA,gBACT/O,OAAA;gCAAA+O,QAAA,EAAQ;8BAAoB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACrCtP,OAAA,CAACrB,cAAc;gCACbkR,SAAS,EAAC,KAAK;gCACfC,OAAO,eAAE9P,OAAA,CAACpB,OAAO;kCAAAmQ,QAAA,EAAC;gCAA4C;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAS,CAAE;gCAAAP,QAAA,eAEzE/O,OAAA,CAACf,YAAY;kCAAC2P,SAAS,EAAC;gCAAiB;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACbtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO;8BACX7J,IAAI,EAAC,QAAQ;8BACboK,IAAI,EAAC,MAAM;8BACXC,GAAG,EAAC,GAAG;8BACPP,KAAK,EAAEhR,YAAa;8BACpBiR,QAAQ,EAAGvK,CAAC,IAAKzG,eAAe,CAACyG,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;8BACjDE,SAAS,EAAE,CAAC,CAACrO,gBAAgB,CAAC7C,YAAa;8BAC3C2S,QAAQ;4BAAA;8BAAAlE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;8BAAClK,IAAI,EAAC,SAAS;8BAAAmH,QAAA,EAClCxL,gBAAgB,CAAC7C;4BAAY;8BAAAyO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,EAEZ9O,gBAAgB,IAAIE,YAAY,IAAIkE,UAAU,CAACpE,gBAAgB,CAAC,GAAG,CAAC,IAAIoE,UAAU,CAAClE,YAAY,CAAC,GAAG,CAAC,iBACnGV,OAAA;4BAAK4O,SAAS,EAAC,2BAA2B;4BAAAG,QAAA,gBACxC/O,OAAA;8BAAK4O,SAAS,EAAC,wDAAwD;8BAAAG,QAAA,gBACrE/O,OAAA;gCAAA+O,QAAA,eAAM/O,OAAA;kCAAA+O,QAAA,EAAQ;gCAAc;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAC5CtP,OAAA,CAACzB,KAAK;gCAAC+U,EAAE,EACP7P,YAAY,GAAG,EAAE,GAAG,QAAQ,GAC5BA,YAAY,GAAG,EAAE,GAAG,SAAS,GAC7B,SACD;gCAAAsL,QAAA,gBACC/O,OAAA,CAACZ,YAAY;kCAACwP,SAAS,EAAC;gCAAM;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EAChC7L,YAAY,EAAC,GAChB;8BAAA;gCAAA0L,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACNtP,OAAA;8BAAK4O,SAAS,EAAC;4BAAyB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAC3CtP,OAAA;8BAAK4O,SAAS,EAAC,qCAAqC;8BAAAG,QAAA,gBAClD/O,OAAA;gCAAA+O,QAAA,EAAO;8BAAG;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eAClBtP,OAAA;gCAAA+O,QAAA,EAAO;8BAAI;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAENtP,OAAA,CAAC9B,GAAG;0BAACkT,EAAE,EAAE,CAAE;0BAAArC,QAAA,eACT/O,OAAA,CAAClC,IAAI,CAACyT,KAAK;4BAAC3C,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAC1B/O,OAAA,CAAClC,IAAI,CAACiU,KAAK;8BAAAhD,QAAA,gBACT/O,OAAA;gCAAA+O,QAAA,EAAQ;8BAAc;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eAC/BtP,OAAA,CAACrB,cAAc;gCACbkR,SAAS,EAAC,KAAK;gCACfC,OAAO,eAAE9P,OAAA,CAACpB,OAAO;kCAAAmQ,QAAA,EAAC;gCAAyC;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAS,CAAE;gCAAAP,QAAA,eAEtE/O,OAAA,CAACf,YAAY;kCAAC2P,SAAS,EAAC;gCAAiB;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACbtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO;8BACX8B,EAAE,EAAC,UAAU;8BACbC,IAAI,EAAE,CAAE;8BACR9B,KAAK,EAAE9Q,YAAa;8BACpB+Q,QAAQ,EAAGvK,CAAC,IAAKvG,eAAe,CAACuG,CAAC,CAACG,MAAM,CAACmK,KAAK,CAAE;8BACjDE,SAAS,EAAE,CAAC,CAACrO,gBAAgB,CAAC3C,YAAa;8BAC3CiR,WAAW,EAAC;4BAAkD;8BAAA1C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/D,CAAC,eACFtP,OAAA,CAAClC,IAAI,CAAC2T,OAAO,CAACK,QAAQ;8BAAClK,IAAI,EAAC,SAAS;8BAAAmH,QAAA,EAClCxL,gBAAgB,CAAC3C;4BAAY;8BAAAuO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACxBtP,OAAA,CAAClC,IAAI,CAAC2V,IAAI;8BAAC7E,SAAS,EAAC,YAAY;8BAAAG,QAAA,GAC9BnO,YAAY,GAAG,GAAG,GAAGA,YAAY,CAACyF,MAAM,GAAG,GAAG,EAAC,uBAClD;4BAAA;8BAAA8I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAW,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENtP,OAAA;wBAAK4O,SAAS,EAAC,mBAAmB;wBAAAG,QAAA,eAChC/O,OAAA,CAACjC,MAAM;0BAAC6J,IAAI,EAAC,QAAQ;0BAACgH,SAAS,EAAC,aAAa;0BAAC9G,IAAI,EAAC,IAAI;0BAAAiH,QAAA,gBACrD/O,OAAA,CAACnB,OAAO;4BAAC+P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,mBAE9B;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtP,OAAA,CAACoQ,iBAAiB;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrBtP,OAAA,CAAC3B,KAAK;MAACgS,IAAI,EAAElN,YAAa;MAACmN,MAAM,EAAEtF,aAAc;MAACuF,QAAQ;MAAAxB,QAAA,gBACxD/O,OAAA,CAAC3B,KAAK,CAACmS,MAAM;QAACC,WAAW;QAAA1B,QAAA,eACvB/O,OAAA,CAAC3B,KAAK,CAACqS,KAAK;UAAA3B,QAAA,gBACV/O,OAAA,CAACN,SAAS;YAACkP,SAAS,EAAC;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACftP,OAAA,CAAC3B,KAAK,CAACsS,IAAI;QAAA5B,QAAA,gBACT/O,OAAA;UAAA+O,QAAA,EAAG;QAAgE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvEtP,OAAA;UAAK4O,SAAS,EAAC,sBAAsB;UAAAG,QAAA,gBACnC/O,OAAA;YAAG4O,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAAC/O,OAAA;cAAA+O,QAAA,EAAQ;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3L,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACpH9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE,CAAC;UAAA;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBtP,OAAA;YAAG4O,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAAC/O,OAAA;cAAA+O,QAAA,EAAQ;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAC9O,gBAAgB;UAAA;YAAA2O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFtP,OAAA;YAAG4O,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAAC/O,OAAA;cAAA+O,QAAA,EAAQ;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAC5O,YAAY;UAAA;YAAAyO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EtP,OAAA;YAAG4O,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAAC/O,OAAA;cAAA+O,QAAA,EAAQ;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC7L,YAAY,EAAC,GAAC;UAAA;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbtP,OAAA,CAAC3B,KAAK,CAAC6S,MAAM;QAAAnC,QAAA,gBACX/O,OAAA,CAACjC,MAAM;UAACmR,OAAO,EAAC,WAAW;UAACiC,OAAO,EAAEnG,aAAc;UAAA+D,QAAA,EAAC;QAEpD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtP,OAAA,CAACjC,MAAM;UACLmR,OAAO,EAAC,SAAS;UACjBiC,OAAO,EAAElF,qBAAsB;UAC/BiG,QAAQ,EAAE7O,UAAW;UAAA0L,QAAA,EAEpB1L,UAAU,gBACTrD,OAAA,CAAAE,SAAA;YAAA6O,QAAA,gBACE/O,OAAA,CAAC7B,OAAO;cACNoV,EAAE,EAAC,MAAM;cACTvE,SAAS,EAAC,QAAQ;cAClBlH,IAAI,EAAC,IAAI;cACTmH,IAAI,EAAC,QAAQ;cACb,eAAY,MAAM;cAClBL,SAAS,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,iBAEJ;UAAA,eAAE,CAAC,gBAEHtP,OAAA,CAAAE,SAAA;YAAA6O,QAAA,gBACE/O,OAAA,CAACL,UAAU;cAACiP,SAAS,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEjC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAClP,EAAA,CArgDID,sBAAsB;EAAA,QACXxC,SAAS,EACPC,WAAW,EAwN0BgC,WAAW;AAAA;AAAA8T,EAAA,GA1N7DvT,sBAAsB;AAugD5B,eAAeA,sBAAsB;AAAC,IAAAuT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}