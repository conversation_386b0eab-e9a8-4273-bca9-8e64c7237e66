{"ast": null, "code": "// Import the functions you need from the SDKs you need\nimport{initializeApp}from\"firebase/app\";import{getStorage}from\"firebase/storage\";// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\n// IMPORTANT: Replace these values with your Firebase project configuration\n// You can find these values in your Firebase project settings\nconst firebaseConfig={apiKey:\"YOUR_API_KEY\",authDomain:\"YOUR_AUTH_DOMAIN\",projectId:\"YOUR_PROJECT_ID\",storageBucket:\"YOUR_STORAGE_BUCKET\",messagingSenderId:\"YOUR_MESSAGING_SENDER_ID\",appId:\"YOUR_APP_ID\",measurementId:\"YOUR_MEASUREMENT_ID\"};/*\r\nSETUP INSTRUCTIONS:\r\n1. Go to https://console.firebase.google.com/\r\n2. Create a new project (or use an existing one)\r\n3. Go to Project Settings (gear icon)\r\n4. Scroll down to \"Your apps\" section and click the web icon (</>)\r\n5. Register your app with a nickname\r\n6. Copy the firebaseConfig object and replace the values above\r\n7. Go to \"Storage\" in the Firebase console\r\n8. Set up Storage with appropriate rules (start in test mode for development)\r\n9. Update the rules to allow read/write access as needed\r\n*/// Initialize Firebase\nconst app=initializeApp(firebaseConfig);const storage=getStorage(app);export{storage,app};", "map": {"version": 3, "names": ["initializeApp", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "storage"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/firebase/config.js"], "sourcesContent": ["// Import the functions you need from the SDKs you need\r\nimport { initializeApp } from \"firebase/app\";\r\nimport { getStorage } from \"firebase/storage\";\r\n\r\n// Your web app's Firebase configuration\r\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\r\n// IMPORTANT: Replace these values with your Firebase project configuration\r\n// You can find these values in your Firebase project settings\r\nconst firebaseConfig = {\r\n  apiKey: \"YOUR_API_KEY\",\r\n  authDomain: \"YOUR_AUTH_DOMAIN\",\r\n  projectId: \"YOUR_PROJECT_ID\",\r\n  storageBucket: \"YOUR_STORAGE_BUCKET\",\r\n  messagingSenderId: \"YOUR_MESSAGING_SENDER_ID\",\r\n  appId: \"YOUR_APP_ID\",\r\n  measurementId: \"YOUR_MEASUREMENT_ID\"\r\n};\r\n\r\n/*\r\nSETUP INSTRUCTIONS:\r\n1. Go to https://console.firebase.google.com/\r\n2. Create a new project (or use an existing one)\r\n3. Go to Project Settings (gear icon)\r\n4. Scroll down to \"Your apps\" section and click the web icon (</>)\r\n5. Register your app with a nickname\r\n6. Copy the firebaseConfig object and replace the values above\r\n7. Go to \"Storage\" in the Firebase console\r\n8. Set up Storage with appropriate rules (start in test mode for development)\r\n9. Update the rules to allow read/write access as needed\r\n*/\r\n\r\n// Initialize Firebase\r\nconst app = initializeApp(firebaseConfig);\r\nconst storage = getStorage(app);\r\n\r\nexport { storage, app };\r\n"], "mappings": "AAAA;AACA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,UAAU,KAAQ,kBAAkB,CAE7C;AACA;AACA;AACA;AACA,KAAM,CAAAC,cAAc,CAAG,CACrBC,MAAM,CAAE,cAAc,CACtBC,UAAU,CAAE,kBAAkB,CAC9BC,SAAS,CAAE,iBAAiB,CAC5BC,aAAa,CAAE,qBAAqB,CACpCC,iBAAiB,CAAE,0BAA0B,CAC7CC,KAAK,CAAE,aAAa,CACpBC,aAAa,CAAE,qBACjB,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAEA;AACA,KAAM,CAAAC,GAAG,CAAGV,aAAa,CAACE,cAAc,CAAC,CACzC,KAAM,CAAAS,OAAO,CAAGV,UAAU,CAACS,GAAG,CAAC,CAE/B,OAASC,OAAO,CAAED,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}