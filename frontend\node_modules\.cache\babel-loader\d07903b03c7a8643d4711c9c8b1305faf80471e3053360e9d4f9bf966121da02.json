{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import ColorVariantSelector from\"../components/ColorVariantSelector\";import{Card,Form,Button,Row,Col,Spinner,<PERSON><PERSON>,Badge,Modal,Image}from'react-bootstrap';import{BsScissors,BsPlus,BsTrash,BsCheck2Circle,BsExclamationTriangle,BsFilePdf,BsImage,BsUpload}from'react-icons/bs';import jsPDF from'jspdf';import{uploadImage}from'../firebase/imageUpload';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AddCuttingRecord=()=>{// Overall cutting record fields\nconst[fabricDefinitions,setFabricDefinitions]=useState([]);const[allFabricVariants,setAllFabricVariants]=useState([]);const[cuttingDate,setCuttingDate]=useState('');const[description,setDescription]=useState('');const[productName,setProductName]=useState('');const[cuttingImage,setCuttingImage]=useState('');const[imageFile,setImageFile]=useState(null);const[imagePreview,setImagePreview]=useState('');const[uploadingImage,setUploadingImage]=useState(false);// Fabric definition groups - each group has a fabric definition and its variants\nconst[fabricGroups,setFabricGroups]=useState([{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);// Loading, error, success states\nconst[loadingVariants,setLoadingVariants]=useState(true);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[validated,setValidated]=useState(false);const[showPdfModal,setShowPdfModal]=useState(false);const[submittedRecord,setSubmittedRecord]=useState(null);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Fetch fabric definitions and variants on mount\nuseEffect(()=>{setLoadingVariants(true);// Fetch fabric definitions\nconst fetchDefinitions=axios.get(\"http://localhost:8000/api/fabric-definitions/\");// Fetch all fabric variants\nconst fetchVariants=axios.get(\"http://localhost:8000/api/fabric-variants/\");Promise.all([fetchDefinitions,fetchVariants]).then(_ref=>{let[definitionsRes,variantsRes]=_ref;setFabricDefinitions(definitionsRes.data);setAllFabricVariants(variantsRes.data);setLoadingVariants(false);}).catch(err=>{console.error('Error fetching fabric data:',err);setError('Failed to load fabric data. Please try again.');setLoadingVariants(false);});},[]);// Add a new fabric definition group\nconst addFabricGroup=()=>{setFabricGroups([...fabricGroups,{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);};// Remove a fabric definition group\nconst removeFabricGroup=groupIndex=>{if(fabricGroups.length>1){const newGroups=fabricGroups.filter((_,i)=>i!==groupIndex);setFabricGroups(newGroups);}};// Add a new variant row to a specific fabric group\nconst addVariantToGroup=groupIndex=>{const newGroups=[...fabricGroups];newGroups[groupIndex].variants.push({fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0});setFabricGroups(newGroups);};// Remove a variant from a specific fabric group\nconst removeVariantFromGroup=(groupIndex,variantIndex)=>{const newGroups=[...fabricGroups];if(newGroups[groupIndex].variants.length>1){newGroups[groupIndex].variants=newGroups[groupIndex].variants.filter((_,i)=>i!==variantIndex);setFabricGroups(newGroups);}};// Handle fabric definition change for a group\nconst handleFabricDefinitionChange=(groupIndex,fabricDefinitionId)=>{const newGroups=[...fabricGroups];newGroups[groupIndex].fabric_definition=fabricDefinitionId;// Reset variants when fabric definition changes\nnewGroups[groupIndex].variants=[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}];setFabricGroups(newGroups);};// Handle variant field change\nconst handleVariantChange=(groupIndex,variantIndex,field,value)=>{const newGroups=[...fabricGroups];// If changing fabric variant, check for duplicates within the same group\nif(field==='fabric_variant'){if(isDuplicateFabricVariant(groupIndex,value,variantIndex)){setError(`This fabric variant is already selected in this fabric group. Please select a different variant.`);return;}else{setError('');}}newGroups[groupIndex].variants[variantIndex][field]=value;setFabricGroups(newGroups);};// Check if a fabric variant is already selected in the same group\nconst isDuplicateFabricVariant=(groupIndex,variantId,currentVariantIndex)=>{return fabricGroups[groupIndex].variants.some((variant,idx)=>idx!==currentVariantIndex&&variant.fabric_variant===variantId&&variantId!=='');};// Handle image file selection\nconst handleImageChange=e=>{const file=e.target.files[0];if(file){setImageFile(file);// Create preview URL\nconst previewUrl=URL.createObjectURL(file);setImagePreview(previewUrl);}};// Upload image to Firebase\nconst uploadCuttingImage=async()=>{if(!imageFile)return'';// Return empty string if no file\ntry{setUploadingImage(true);const imageUrl=await uploadImage(imageFile,'cutting-images');setCuttingImage(imageUrl);return imageUrl;}catch(error){console.error('Error uploading image:',error);throw new Error('Failed to upload image');}finally{setUploadingImage(false);}};// Remove image\nconst removeImage=()=>{setImageFile(null);setImagePreview('');setCuttingImage('');};// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();// Form validation\nconst form=e.currentTarget;if(form.checkValidity()===false){e.stopPropagation();setValidated(true);return;}// Check if any fabric group has valid data\nconst hasValidGroups=fabricGroups.some(group=>group.fabric_definition&&group.variants.some(variant=>variant.fabric_variant));if(!hasValidGroups){setError('Please select at least one fabric definition and one fabric variant.');return;}// Validate each fabric group\nlet validationError=false;for(let groupIndex=0;groupIndex<fabricGroups.length;groupIndex++){const group=fabricGroups[groupIndex];if(group.fabric_definition&&group.variants.some(variant=>variant.fabric_variant)){// Check for duplicates within the group\nconst selectedVariants=group.variants.map(variant=>variant.fabric_variant).filter(Boolean);const uniqueVariants=[...new Set(selectedVariants)];if(selectedVariants.length!==uniqueVariants.length){setError(`Duplicate fabric variants detected in fabric group ${groupIndex+1}. Please ensure each variant is selected only once per group.`);validationError=true;break;}// Validate yard availability for each variant\nfor(let variant of group.variants){if(variant.fabric_variant){const variantData=allFabricVariants.find(v=>v.id===variant.fabric_variant);if(variantData&&parseFloat(variant.yard_usage)>(variantData.available_yard||variantData.total_yard)){setError(`Yard usage for ${variantData.color_name||variantData.color} exceeds available yards (${variantData.available_yard||variantData.total_yard} yards available).`);validationError=true;break;}}}}}if(validationError){setValidated(true);return;}setValidated(true);setIsSubmitting(true);setError('');setSuccess('');try{// Upload image if one was selected\nlet finalImageUrl='';if(imageFile){finalImageUrl=await uploadCuttingImage();}// Flatten fabric groups into details array\nconst details=[];fabricGroups.forEach(group=>{if(group.fabric_definition){group.variants.forEach(variant=>{if(variant.fabric_variant){details.push(variant);}});}});const payload={cutting_date:cuttingDate,description:description,product_name:productName,cutting_image:finalImageUrl,details:details};const response=await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\",payload);setSuccess('Cutting record created successfully!');// Store the submitted record for PDF generation\nconst fabricNames=new Set();const recordData={...response.data,details:response.data.details.map(detail=>{var _variant$fabric_defin,_variant$fabric_defin2;const variant=allFabricVariants.find(v=>v.id===detail.fabric_variant);if(variant!==null&&variant!==void 0&&(_variant$fabric_defin=variant.fabric_definition_data)!==null&&_variant$fabric_defin!==void 0&&_variant$fabric_defin.fabric_name){fabricNames.add(variant.fabric_definition_data.fabric_name);}return{...detail,color:(variant===null||variant===void 0?void 0:variant.color)||'Unknown',color_name:(variant===null||variant===void 0?void 0:variant.color_name)||(variant===null||variant===void 0?void 0:variant.color)||'Unknown',fabric_name:(variant===null||variant===void 0?void 0:(_variant$fabric_defin2=variant.fabric_definition_data)===null||_variant$fabric_defin2===void 0?void 0:_variant$fabric_defin2.fabric_name)||'Unknown'};}),fabric_names:Array.from(fabricNames).join(', ')||'Unknown',totalQuantities:totalQuantities};setSubmittedRecord(recordData);// Show the PDF generation modal\nsetShowPdfModal(true);}catch(err){console.error('Error creating cutting record:',err);if(err.response&&err.response.data){// Display more specific error message if available\nconst errorMessage=typeof err.response.data==='string'?err.response.data:'Failed to create cutting record. Please check your inputs.';setError(errorMessage);}else{setError('Failed to create cutting record. Please try again.');}}finally{setIsSubmitting(false);}};// Function to generate PDF directly without using html2canvas\nconst generatePDF=()=>{if(!submittedRecord)return;try{// Create a new PDF document\nconst pdf=new jsPDF({orientation:'portrait',unit:'mm',format:'a4'});// Set font sizes and styles\nconst titleFontSize=18;const headingFontSize=14;const normalFontSize=10;const smallFontSize=8;// Add title\npdf.setFontSize(titleFontSize);pdf.setFont('helvetica','bold');pdf.text('Cutting Record',105,20,{align:'center'});// Add general information section\npdf.setFontSize(headingFontSize);pdf.text('General Information',20,35);pdf.setFontSize(normalFontSize);pdf.setFont('helvetica','normal');// Draw table for general info\npdf.line(20,40,190,40);// Top horizontal line\nconst generalInfoData=[['Record ID',submittedRecord.id.toString()],['Product Name',submittedRecord.product_name],['Fabrics Used',submittedRecord.fabric_names],['Cutting Date',new Date(submittedRecord.cutting_date).toLocaleDateString()],['Description',submittedRecord.description||'N/A']];let yPos=45;generalInfoData.forEach(row=>{pdf.setFont('helvetica','bold');pdf.text(row[0],25,yPos);pdf.setFont('helvetica','normal');pdf.text(row[1],80,yPos);yPos+=8;pdf.line(20,yPos-3,190,yPos-3);// Horizontal line after each row\n});// Add fabric details section\npdf.setFontSize(headingFontSize);pdf.setFont('helvetica','bold');pdf.text('Fabric Details',20,yPos+10);// Table headers for fabric details\nconst headers=['Fabric','Color','Yard Usage','XS','S','M','L','XL','Total'];const colWidths=[35,35,20,12,12,12,12,12,15];// Calculate starting positions for each column\nconst colPositions=[];let currentPos=20;colWidths.forEach(width=>{colPositions.push(currentPos);currentPos+=width;});// Draw table header\nyPos+=15;pdf.setFontSize(normalFontSize);pdf.setFont('helvetica','bold');// Draw header background\npdf.setFillColor(240,240,240);pdf.rect(20,yPos-5,170,8,'F');// Draw header text\nheaders.forEach((header,index)=>{pdf.text(header,colPositions[index]+2,yPos);});// Draw horizontal line after header\nyPos+=3;pdf.line(20,yPos,190,yPos);// Draw table rows\npdf.setFont('helvetica','normal');submittedRecord.details.forEach(detail=>{var _detail$xs,_detail$s,_detail$m,_detail$l,_detail$xl;yPos+=8;// Calculate total for this row\nconst total=parseInt(detail.xs||0)+parseInt(detail.s||0)+parseInt(detail.m||0)+parseInt(detail.l||0)+parseInt(detail.xl||0);// Draw row data\npdf.text(detail.fabric_name||'Unknown',colPositions[0]+2,yPos);pdf.text(detail.color_name||detail.color,colPositions[1]+2,yPos);pdf.text(`${detail.yard_usage} yards`,colPositions[2]+2,yPos);pdf.text(((_detail$xs=detail.xs)===null||_detail$xs===void 0?void 0:_detail$xs.toString())||'0',colPositions[3]+2,yPos);pdf.text(((_detail$s=detail.s)===null||_detail$s===void 0?void 0:_detail$s.toString())||'0',colPositions[4]+2,yPos);pdf.text(((_detail$m=detail.m)===null||_detail$m===void 0?void 0:_detail$m.toString())||'0',colPositions[5]+2,yPos);pdf.text(((_detail$l=detail.l)===null||_detail$l===void 0?void 0:_detail$l.toString())||'0',colPositions[6]+2,yPos);pdf.text(((_detail$xl=detail.xl)===null||_detail$xl===void 0?void 0:_detail$xl.toString())||'0',colPositions[7]+2,yPos);pdf.text(total.toString(),colPositions[8]+2,yPos);// Draw horizontal line after row\nyPos+=3;pdf.line(20,yPos,190,yPos);});// Draw totals row\nyPos+=8;pdf.setFillColor(240,240,240);pdf.rect(20,yPos-5,170,8,'F');pdf.setFont('helvetica','bold');pdf.text('Total',colPositions[0]+2,yPos);pdf.text('',colPositions[1]+2,yPos);pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`,colPositions[2]+2,yPos);pdf.text(submittedRecord.totalQuantities.xs.toString(),colPositions[3]+2,yPos);pdf.text(submittedRecord.totalQuantities.s.toString(),colPositions[4]+2,yPos);pdf.text(submittedRecord.totalQuantities.m.toString(),colPositions[5]+2,yPos);pdf.text(submittedRecord.totalQuantities.l.toString(),colPositions[6]+2,yPos);pdf.text(submittedRecord.totalQuantities.xl.toString(),colPositions[7]+2,yPos);pdf.text(submittedRecord.totalQuantities.total.toString(),colPositions[8]+2,yPos);// Add footer\npdf.setFontSize(smallFontSize);pdf.setFont('helvetica','italic');pdf.text(`Generated on: ${new Date().toLocaleString()}`,105,280,{align:'center'});pdf.text('Fashion Garment Management System',105,285,{align:'center'});// Save the PDF\npdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);// Reset form after PDF generation\nsetShowPdfModal(false);setCuttingDate('');setDescription('');setProductName('');setCuttingImage('');setImageFile(null);setImagePreview('');setFabricGroups([{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);setValidated(false);}catch(error){console.error('Error generating PDF:',error);setError('Failed to generate PDF. Please try again.');setShowPdfModal(false);}};// Function to handle modal close without generating PDF\nconst handleCloseModal=()=>{setShowPdfModal(false);// Reset form\nsetCuttingDate('');setDescription('');setProductName('');setCuttingImage('');setImageFile(null);setImagePreview('');setFabricGroups([{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);setValidated(false);};// Calculate total quantities for all fabric groups\nconst totalQuantities=fabricGroups.reduce((acc,group)=>{group.variants.forEach(variant=>{if(variant.fabric_variant){acc.xs+=parseInt(variant.xs)||0;acc.s+=parseInt(variant.s)||0;acc.m+=parseInt(variant.m)||0;acc.l+=parseInt(variant.l)||0;acc.xl+=parseInt(variant.xl)||0;acc.total+=(parseInt(variant.xs)||0)+(parseInt(variant.s)||0)+(parseInt(variant.m)||0)+(parseInt(variant.l)||0)+(parseInt(variant.xl)||0);acc.yard_usage+=parseFloat(variant.yard_usage)||0;}});return acc;},{xs:0,s:0,m:0,l:0,xl:0,total:0,yard_usage:0});return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(BsScissors,{className:\"me-2\"}),\"Add Cutting Record\"]}),success&&/*#__PURE__*/_jsxs(Alert,{variant:\"success\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsCheck2Circle,{className:\"me-2\",size:20}),success]}),error&&/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsExclamationTriangle,{className:\"me-2\",size:20}),error]}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{noValidate:true,validated:validated,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),placeholder:\"Enter product name\",required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please provide a product name.\"})]})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:cuttingDate,onChange:e=>setCuttingDate(e.target.value),required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please select a cutting date.\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Description\"})}),/*#__PURE__*/_jsx(Form.Control,{as:\"textarea\",rows:3,value:description,onChange:e=>setDescription(e.target.value),placeholder:\"Enter details about this cutting record...\"})]})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Image\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-start gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:imagePreview?/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Image,{src:imagePreview,alt:\"Cutting preview\",style:{width:'150px',height:'150px',objectFit:'cover',border:'2px solid #dee2e6',borderRadius:'8px'}}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",className:\"position-absolute top-0 end-0 m-1\",onClick:removeImage,disabled:isSubmitting||uploadingImage,children:/*#__PURE__*/_jsx(BsTrash,{})})]}):/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center bg-light border border-2 border-dashed\",style:{width:'150px',height:'150px',borderRadius:'8px',color:'#6c757d'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(BsImage,{size:30,className:\"mb-2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"small\",children:\"No Image\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-grow-1\",children:[/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\"image/*\",onChange:handleImageChange,disabled:isSubmitting||uploadingImage,className:\"mb-2\"}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Upload an image of the cutting process. Supported formats: JPG, PNG, GIF (Max: 5MB)\"}),uploadingImage&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"small\",{children:\"Uploading image...\"})]})]})]})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:\"Fabric Details\"}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",onClick:addFabricGroup,disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Fabric Definition\"]})]}),fabricGroups.map((group,groupIndex)=>{// Get fabric variants for the selected fabric definition\nconst groupVariants=group.fabric_definition?allFabricVariants.filter(v=>v.fabric_definition===parseInt(group.fabric_definition)):[];return/*#__PURE__*/_jsxs(Card,{className:\"mb-4 border\",children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"d-flex justify-content-between align-items-center bg-primary text-white\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[\"Fabric Definition #\",groupIndex+1]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>removeFabricGroup(groupIndex),disabled:fabricGroups.length===1,children:[/*#__PURE__*/_jsx(BsTrash,{className:\"me-1\"}),\" Remove\"]})]}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Select Fabric Definition\"})}),loadingVariants?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading fabric definitions...\"})]}):/*#__PURE__*/_jsxs(Form.Select,{value:group.fabric_definition,onChange:e=>handleFabricDefinitionChange(groupIndex,e.target.value),required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Fabric Definition\"}),fabricDefinitions.map(fd=>/*#__PURE__*/_jsxs(\"option\",{value:fd.id,children:[fd.fabric_name,\" - \",fd.supplier_name||'Unknown Supplier']},fd.id))]})]})})}),group.fabric_definition&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Fabric Variants\"}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-success\",size:\"sm\",onClick:()=>addVariantToGroup(groupIndex),disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Variant\"]})]}),group.variants.map((variant,variantIndex)=>{const currentVariant=allFabricVariants.find(v=>v.id===variant.fabric_variant);return/*#__PURE__*/_jsx(Card,{className:\"mb-3 border-light\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-0 me-2\",children:[\"Variant #\",variantIndex+1]}),currentVariant&&/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'16px',height:'16px',backgroundColor:currentVariant.color,border:'1px solid #ccc',borderRadius:'3px',marginRight:'6px'},title:`Color: ${currentVariant.color}`}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:currentVariant.color_name||currentVariant.color})]})]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-danger\",size:\"sm\",onClick:()=>removeVariantFromGroup(groupIndex,variantIndex),disabled:group.variants.length===1,children:[/*#__PURE__*/_jsx(BsTrash,{className:\"me-1\"}),\" Remove\"]})]}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric Variant (Color)\"})}),/*#__PURE__*/_jsx(ColorVariantSelector,{variants:groupVariants,selectedValue:variant.fabric_variant,onSelect:value=>handleVariantChange(groupIndex,variantIndex,'fabric_variant',value),placeholder:\"Select Color Variant\",isDuplicateFunction:isDuplicateFabricVariant,groupIndex:groupIndex,variantIndex:variantIndex})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-1\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"mb-0\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Yard Usage\"})}),currentVariant&&/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'16px',height:'16px',backgroundColor:currentVariant.color,border:'1px solid #ccc',borderRadius:'3px',marginRight:'8px'},title:`Color: ${currentVariant.color_name||currentVariant.color}`}),/*#__PURE__*/_jsxs(\"span\",{className:parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard)?\"text-danger small\":\"text-success small\",children:[currentVariant.color_name||currentVariant.color,\" - Available: \",currentVariant.available_yard||currentVariant.total_yard,\" yards\"]})]})]}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:variant.yard_usage,onChange:e=>handleVariantChange(groupIndex,variantIndex,'yard_usage',e.target.value),required:true,placeholder:\"Enter yards used\",isInvalid:currentVariant&&parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard),className:currentVariant&&parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard)?\"border-danger\":\"\",style:{height:'38px'}}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:currentVariant&&parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard)?`Exceeds available yards (${currentVariant.available_yard||currentVariant.total_yard} yards available)`:\"Please enter valid yard usage.\"})]})})]}),/*#__PURE__*/_jsx(Form.Label,{className:\"mt-2\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Size Quantities\"})}),/*#__PURE__*/_jsxs(Row,{children:[[\"XS\",\"S\",\"M\",\"L\",\"XL\"].map((size,sizeIndex)=>{const sizeKey=size.toLowerCase();return/*#__PURE__*/_jsx(Col,{xs:6,sm:4,md:2,className:\"mb-3\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-center d-block\",children:size}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:variant[sizeKey],onChange:e=>{const val=Math.max(0,parseInt(e.target.value||0));handleVariantChange(groupIndex,variantIndex,sizeKey,val);},className:\"text-center\"})]})},sizeIndex);}),/*#__PURE__*/_jsx(Col,{xs:6,sm:4,md:2,className:\"mb-3\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-center d-block\",children:\"Total\"}),/*#__PURE__*/_jsx(\"div\",{className:\"form-control text-center bg-light\",children:parseInt(variant.xs||0)+parseInt(variant.s||0)+parseInt(variant.m||0)+parseInt(variant.l||0)+parseInt(variant.xl||0)})]})})]})]})},variantIndex);})]})]})]},group.id);}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-end mb-4\",children:/*#__PURE__*/_jsx(Card,{className:\"border-0\",style:{backgroundColor:\"#e8f4fe\"},children:/*#__PURE__*/_jsx(Card.Body,{className:\"py-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex flex-column\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"me-2\",children:\"Total Quantities:\"}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"XS: \",totalQuantities.xs]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"S: \",totalQuantities.s]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"M: \",totalQuantities.m]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"L: \",totalQuantities.l]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"XL: \",totalQuantities.xl]}),/*#__PURE__*/_jsxs(Badge,{bg:\"success\",className:\"ms-2\",children:[\"Total: \",totalQuantities.total]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"me-2\",children:\"Total Yard Usage:\"}),/*#__PURE__*/_jsxs(Badge,{bg:\"info\",children:[totalQuantities.yard_usage.toFixed(2),\" yards\"]})]})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center mt-4\",children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"primary\",size:\"lg\",disabled:isSubmitting,className:\"px-5\",children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Submitting...\"]}):'Submit Cutting Record'})})]})})})]}),/*#__PURE__*/_jsxs(Modal,{show:showPdfModal,onHide:handleCloseModal,size:\"lg\",centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:\"Generate Cutting Record PDF\"})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Would you like to generate a PDF for this cutting record?\"}),submittedRecord&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"The PDF will include the following information:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name:\"}),\" \",submittedRecord.product_name]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric:\"}),\" \",submittedRecord.fabric_name]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date:\"}),\" \",new Date(submittedRecord.cutting_date).toLocaleDateString()]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Quantities:\"}),\" XS: \",submittedRecord.totalQuantities.xs,\", S: \",submittedRecord.totalQuantities.s,\", M: \",submittedRecord.totalQuantities.m,\", L: \",submittedRecord.totalQuantities.l,\", XL: \",submittedRecord.totalQuantities.xl]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Items:\"}),\" \",submittedRecord.totalQuantities.total]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Yard Usage:\"}),\" \",submittedRecord.totalQuantities.yard_usage.toFixed(2),\" yards\"]})]})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleCloseModal,children:\"No, Skip\"}),/*#__PURE__*/_jsxs(Button,{variant:\"primary\",onClick:generatePDF,children:[/*#__PURE__*/_jsx(BsFilePdf,{className:\"me-2\"}),\" Generate PDF\"]})]})]})]});};export default AddCuttingRecord;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "RoleBasedNavBar", "ColorVariantSelector", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Badge", "Modal", "Image", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsFilePdf", "BsImage", "BsUpload", "jsPDF", "uploadImage", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AddCuttingRecord", "fabricDefinitions", "setFabricDefinitions", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "cuttingImage", "setCuttingImage", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "uploadingImage", "setUploadingImage", "fabricGroups", "setFabricGroups", "id", "Date", "now", "fabric_definition", "variants", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "loadingVariants", "setLoadingVariants", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "showPdfModal", "setShowPdfModal", "submittedRecord", "setSubmittedRecord", "handleResize", "addEventListener", "removeEventListener", "fetchDefinitions", "get", "fetchVariants", "Promise", "all", "then", "_ref", "definitionsRes", "variantsRes", "data", "catch", "err", "console", "addFabricGroup", "removeFabricGroup", "groupIndex", "length", "newGroups", "filter", "_", "i", "addVariantToGroup", "push", "removeVariantFromGroup", "variantIndex", "handleFabricDefinitionChange", "fabricDefinitionId", "handleVariantChange", "field", "value", "isDuplicateFabricVariant", "variantId", "currentVariantIndex", "some", "variant", "idx", "handleImageChange", "e", "file", "target", "files", "previewUrl", "URL", "createObjectURL", "uploadCuttingImage", "imageUrl", "Error", "removeImage", "handleSubmit", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidGroups", "group", "validationError", "selectedVariants", "map", "Boolean", "uniqueVariants", "Set", "variantData", "find", "v", "parseFloat", "available_yard", "total_yard", "color_name", "color", "finalImageUrl", "details", "for<PERSON>ach", "payload", "cutting_date", "product_name", "cutting_image", "response", "post", "fabricNames", "recordData", "detail", "_variant$fabric_defin", "_variant$fabric_defin2", "fabric_definition_data", "fabric_name", "add", "fabric_names", "Array", "from", "join", "totalQuantities", "errorMessage", "generatePDF", "pdf", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "line", "generalInfoData", "toString", "toLocaleDateString", "yPos", "row", "headers", "col<PERSON><PERSON><PERSON>", "colPositions", "currentPos", "width", "setFillColor", "rect", "header", "index", "_detail$xs", "_detail$s", "_detail$m", "_detail$l", "_detail$xl", "total", "parseInt", "toFixed", "toLocaleString", "save", "handleCloseModal", "reduce", "acc", "children", "style", "marginLeft", "transition", "padding", "className", "size", "backgroundColor", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "src", "alt", "height", "objectFit", "border", "onClick", "disabled", "accept", "Text", "animation", "groupVariants", "Header", "Select", "fd", "supplier_name", "currentV<PERSON>t", "marginRight", "title", "selected<PERSON><PERSON><PERSON>", "onSelect", "isDuplicateFunction", "step", "min", "isInvalid", "sizeIndex", "sizeKey", "toLowerCase", "sm", "val", "Math", "max", "bg", "role", "show", "onHide", "centered", "closeButton", "Title", "Footer"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/AddCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport ColorVariantSelector from \"../components/ColorVariantSelector\";\r\nimport { Card, Form, Button, Row, Col, Spinner, <PERSON><PERSON>, Badge, Modal, Image } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf, BsImage, BsUpload } from 'react-icons/bs';\r\nimport jsPDF from 'jspdf';\r\nimport { uploadImage } from '../firebase/imageUpload';\r\n\r\nconst AddCuttingRecord = () => {\r\n  // Overall cutting record fields\r\n  const [fabricDefinitions, setFabricDefinitions] = useState([]);\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n  const [cuttingImage, setCuttingImage] = useState('');\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState('');\r\n  const [uploadingImage, setUploadingImage] = useState(false);\r\n\r\n  // Fabric definition groups - each group has a fabric definition and its variants\r\n  const [fabricGroups, setFabricGroups] = useState([\r\n    {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }\r\n  ]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [submittedRecord, setSubmittedRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch fabric definitions and variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n\r\n    // Fetch fabric definitions\r\n    const fetchDefinitions = axios.get(\"http://localhost:8000/api/fabric-definitions/\");\r\n    // Fetch all fabric variants\r\n    const fetchVariants = axios.get(\"http://localhost:8000/api/fabric-variants/\");\r\n\r\n    Promise.all([fetchDefinitions, fetchVariants])\r\n      .then(([definitionsRes, variantsRes]) => {\r\n        setFabricDefinitions(definitionsRes.data);\r\n        setAllFabricVariants(variantsRes.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric data:', err);\r\n        setError('Failed to load fabric data. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n  // Add a new fabric definition group\r\n  const addFabricGroup = () => {\r\n    setFabricGroups([...fabricGroups, {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }]);\r\n  };\r\n\r\n  // Remove a fabric definition group\r\n  const removeFabricGroup = (groupIndex) => {\r\n    if (fabricGroups.length > 1) {\r\n      const newGroups = fabricGroups.filter((_, i) => i !== groupIndex);\r\n      setFabricGroups(newGroups);\r\n    }\r\n  };\r\n\r\n  // Add a new variant row to a specific fabric group\r\n  const addVariantToGroup = (groupIndex) => {\r\n    const newGroups = [...fabricGroups];\r\n    newGroups[groupIndex].variants.push({ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 });\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Remove a variant from a specific fabric group\r\n  const removeVariantFromGroup = (groupIndex, variantIndex) => {\r\n    const newGroups = [...fabricGroups];\r\n    if (newGroups[groupIndex].variants.length > 1) {\r\n      newGroups[groupIndex].variants = newGroups[groupIndex].variants.filter((_, i) => i !== variantIndex);\r\n      setFabricGroups(newGroups);\r\n    }\r\n  };\r\n\r\n  // Handle fabric definition change for a group\r\n  const handleFabricDefinitionChange = (groupIndex, fabricDefinitionId) => {\r\n    const newGroups = [...fabricGroups];\r\n    newGroups[groupIndex].fabric_definition = fabricDefinitionId;\r\n    // Reset variants when fabric definition changes\r\n    newGroups[groupIndex].variants = [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }];\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Handle variant field change\r\n  const handleVariantChange = (groupIndex, variantIndex, field, value) => {\r\n    const newGroups = [...fabricGroups];\r\n\r\n    // If changing fabric variant, check for duplicates within the same group\r\n    if (field === 'fabric_variant') {\r\n      if (isDuplicateFabricVariant(groupIndex, value, variantIndex)) {\r\n        setError(`This fabric variant is already selected in this fabric group. Please select a different variant.`);\r\n        return;\r\n      } else {\r\n        setError('');\r\n      }\r\n    }\r\n\r\n    newGroups[groupIndex].variants[variantIndex][field] = value;\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Check if a fabric variant is already selected in the same group\r\n  const isDuplicateFabricVariant = (groupIndex, variantId, currentVariantIndex) => {\r\n    return fabricGroups[groupIndex].variants.some((variant, idx) =>\r\n      idx !== currentVariantIndex && variant.fabric_variant === variantId && variantId !== ''\r\n    );\r\n  };\r\n\r\n  // Handle image file selection\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setImageFile(file);\r\n      // Create preview URL\r\n      const previewUrl = URL.createObjectURL(file);\r\n      setImagePreview(previewUrl);\r\n    }\r\n  };\r\n\r\n  // Upload image to Firebase\r\n  const uploadCuttingImage = async () => {\r\n    if (!imageFile) return ''; // Return empty string if no file\r\n\r\n    try {\r\n      setUploadingImage(true);\r\n      const imageUrl = await uploadImage(imageFile, 'cutting-images');\r\n      setCuttingImage(imageUrl);\r\n      return imageUrl;\r\n    } catch (error) {\r\n      console.error('Error uploading image:', error);\r\n      throw new Error('Failed to upload image');\r\n    } finally {\r\n      setUploadingImage(false);\r\n    }\r\n  };\r\n\r\n  // Remove image\r\n  const removeImage = () => {\r\n    setImageFile(null);\r\n    setImagePreview('');\r\n    setCuttingImage('');\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any fabric group has valid data\r\n    const hasValidGroups = fabricGroups.some(group =>\r\n      group.fabric_definition && group.variants.some(variant => variant.fabric_variant)\r\n    );\r\n    if (!hasValidGroups) {\r\n      setError('Please select at least one fabric definition and one fabric variant.');\r\n      return;\r\n    }\r\n\r\n    // Validate each fabric group\r\n    let validationError = false;\r\n    for (let groupIndex = 0; groupIndex < fabricGroups.length; groupIndex++) {\r\n      const group = fabricGroups[groupIndex];\r\n\r\n      if (group.fabric_definition && group.variants.some(variant => variant.fabric_variant)) {\r\n        // Check for duplicates within the group\r\n        const selectedVariants = group.variants.map(variant => variant.fabric_variant).filter(Boolean);\r\n        const uniqueVariants = [...new Set(selectedVariants)];\r\n        if (selectedVariants.length !== uniqueVariants.length) {\r\n          setError(`Duplicate fabric variants detected in fabric group ${groupIndex + 1}. Please ensure each variant is selected only once per group.`);\r\n          validationError = true;\r\n          break;\r\n        }\r\n\r\n        // Validate yard availability for each variant\r\n        for (let variant of group.variants) {\r\n          if (variant.fabric_variant) {\r\n            const variantData = allFabricVariants.find(v => v.id === variant.fabric_variant);\r\n            if (variantData && parseFloat(variant.yard_usage) > (variantData.available_yard || variantData.total_yard)) {\r\n              setError(`Yard usage for ${variantData.color_name || variantData.color} exceeds available yards (${variantData.available_yard || variantData.total_yard} yards available).`);\r\n              validationError = true;\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (validationError) {\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      // Upload image if one was selected\r\n      let finalImageUrl = '';\r\n      if (imageFile) {\r\n        finalImageUrl = await uploadCuttingImage();\r\n      }\r\n\r\n      // Flatten fabric groups into details array\r\n      const details = [];\r\n      fabricGroups.forEach(group => {\r\n        if (group.fabric_definition) {\r\n          group.variants.forEach(variant => {\r\n            if (variant.fabric_variant) {\r\n              details.push(variant);\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n      const payload = {\r\n        cutting_date: cuttingDate,\r\n        description: description,\r\n        product_name: productName,\r\n        cutting_image: finalImageUrl,\r\n        details: details\r\n      };\r\n\r\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\r\n      setSuccess('Cutting record created successfully!');\r\n\r\n      // Store the submitted record for PDF generation\r\n      const fabricNames = new Set();\r\n      const recordData = {\r\n        ...response.data,\r\n        details: response.data.details.map(detail => {\r\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n          if (variant?.fabric_definition_data?.fabric_name) {\r\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\r\n          }\r\n          return {\r\n            ...detail,\r\n            color: variant?.color || 'Unknown',\r\n            color_name: variant?.color_name || variant?.color || 'Unknown',\r\n            fabric_name: variant?.fabric_definition_data?.fabric_name || 'Unknown'\r\n          };\r\n        }),\r\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\r\n        totalQuantities: totalQuantities\r\n      };\r\n\r\n      setSubmittedRecord(recordData);\r\n\r\n      // Show the PDF generation modal\r\n      setShowPdfModal(true);\r\n    } catch (err) {\r\n      console.error('Error creating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to create cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to create cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to generate PDF directly without using html2canvas\r\n  const generatePDF = () => {\r\n    if (!submittedRecord) return;\r\n\r\n    try {\r\n      // Create a new PDF document\r\n      const pdf = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add title\r\n      pdf.setFontSize(titleFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Cutting Record', 105, 20, { align: 'center' });\r\n\r\n      // Add general information section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.text('General Information', 20, 35);\r\n\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'normal');\r\n\r\n      // Draw table for general info\r\n      pdf.line(20, 40, 190, 40); // Top horizontal line\r\n\r\n      const generalInfoData = [\r\n        ['Record ID', submittedRecord.id.toString()],\r\n        ['Product Name', submittedRecord.product_name],\r\n        ['Fabrics Used', submittedRecord.fabric_names],\r\n        ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()],\r\n        ['Description', submittedRecord.description || 'N/A']\r\n      ];\r\n\r\n      let yPos = 45;\r\n      generalInfoData.forEach((row) => {\r\n        pdf.setFont('helvetica', 'bold');\r\n        pdf.text(row[0], 25, yPos);\r\n        pdf.setFont('helvetica', 'normal');\r\n        pdf.text(row[1], 80, yPos);\r\n        yPos += 8;\r\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\r\n      });\r\n\r\n      // Add fabric details section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Fabric Details', 20, yPos + 10);\r\n\r\n      // Table headers for fabric details\r\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\r\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\r\n\r\n      // Calculate starting positions for each column\r\n      const colPositions = [];\r\n      let currentPos = 20;\r\n      colWidths.forEach(width => {\r\n        colPositions.push(currentPos);\r\n        currentPos += width;\r\n      });\r\n\r\n      // Draw table header\r\n      yPos += 15;\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n\r\n      // Draw header background\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      // Draw header text\r\n      headers.forEach((header, index) => {\r\n        pdf.text(header, colPositions[index] + 2, yPos);\r\n      });\r\n\r\n      // Draw horizontal line after header\r\n      yPos += 3;\r\n      pdf.line(20, yPos, 190, yPos);\r\n\r\n      // Draw table rows\r\n      pdf.setFont('helvetica', 'normal');\r\n      submittedRecord.details.forEach((detail) => {\r\n        yPos += 8;\r\n\r\n        // Calculate total for this row\r\n        const total = parseInt(detail.xs || 0) +\r\n                      parseInt(detail.s || 0) +\r\n                      parseInt(detail.m || 0) +\r\n                      parseInt(detail.l || 0) +\r\n                      parseInt(detail.xl || 0);\r\n\r\n        // Draw row data\r\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\r\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\r\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\r\n        pdf.text(detail.xs?.toString() || '0', colPositions[3] + 2, yPos);\r\n        pdf.text(detail.s?.toString() || '0', colPositions[4] + 2, yPos);\r\n        pdf.text(detail.m?.toString() || '0', colPositions[5] + 2, yPos);\r\n        pdf.text(detail.l?.toString() || '0', colPositions[6] + 2, yPos);\r\n        pdf.text(detail.xl?.toString() || '0', colPositions[7] + 2, yPos);\r\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\r\n\r\n        // Draw horizontal line after row\r\n        yPos += 3;\r\n        pdf.line(20, yPos, 190, yPos);\r\n      });\r\n\r\n      // Draw totals row\r\n      yPos += 8;\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Total', colPositions[0] + 2, yPos);\r\n      pdf.text('', colPositions[1] + 2, yPos);\r\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\r\n\r\n      // Add footer\r\n      pdf.setFontSize(smallFontSize);\r\n      pdf.setFont('helvetica', 'italic');\r\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      pdf.text('Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\r\n\r\n      // Reset form after PDF generation\r\n      setShowPdfModal(false);\r\n      setCuttingDate('');\r\n      setDescription('');\r\n      setProductName('');\r\n      setCuttingImage('');\r\n      setImageFile(null);\r\n      setImagePreview('');\r\n      setFabricGroups([{\r\n        id: Date.now(),\r\n        fabric_definition: '',\r\n        variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n      }]);\r\n      setValidated(false);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      setError('Failed to generate PDF. Please try again.');\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Function to handle modal close without generating PDF\r\n  const handleCloseModal = () => {\r\n    setShowPdfModal(false);\r\n    // Reset form\r\n    setCuttingDate('');\r\n    setDescription('');\r\n    setProductName('');\r\n    setCuttingImage('');\r\n    setImageFile(null);\r\n    setImagePreview('');\r\n    setFabricGroups([{\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }]);\r\n    setValidated(false);\r\n  };\r\n\r\n\r\n\r\n  // Calculate total quantities for all fabric groups\r\n  const totalQuantities = fabricGroups.reduce((acc, group) => {\r\n    group.variants.forEach(variant => {\r\n      if (variant.fabric_variant) {\r\n        acc.xs += parseInt(variant.xs) || 0;\r\n        acc.s += parseInt(variant.s) || 0;\r\n        acc.m += parseInt(variant.m) || 0;\r\n        acc.l += parseInt(variant.l) || 0;\r\n        acc.xl += parseInt(variant.xl) || 0;\r\n        acc.total += (parseInt(variant.xs) || 0) +\r\n                    (parseInt(variant.s) || 0) +\r\n                    (parseInt(variant.m) || 0) +\r\n                    (parseInt(variant.l) || 0) +\r\n                    (parseInt(variant.xl) || 0);\r\n        acc.yard_usage += parseFloat(variant.yard_usage) || 0;\r\n      }\r\n    });\r\n    return acc;\r\n  }, { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 });\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <BsScissors className=\"me-2\" />\r\n          Add Cutting Record\r\n        </h2>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* Image Upload Section */}\r\n              <Row>\r\n                <Col md={12}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Image</strong></Form.Label>\r\n                    <div className=\"d-flex align-items-start gap-3\">\r\n                      {/* Image Preview */}\r\n                      <div>\r\n                        {imagePreview ? (\r\n                          <div className=\"position-relative\">\r\n                            <Image\r\n                              src={imagePreview}\r\n                              alt=\"Cutting preview\"\r\n                              style={{\r\n                                width: '150px',\r\n                                height: '150px',\r\n                                objectFit: 'cover',\r\n                                border: '2px solid #dee2e6',\r\n                                borderRadius: '8px'\r\n                              }}\r\n                            />\r\n                            <Button\r\n                              variant=\"danger\"\r\n                              size=\"sm\"\r\n                              className=\"position-absolute top-0 end-0 m-1\"\r\n                              onClick={removeImage}\r\n                              disabled={isSubmitting || uploadingImage}\r\n                            >\r\n                              <BsTrash />\r\n                            </Button>\r\n                          </div>\r\n                        ) : (\r\n                          <div\r\n                            className=\"d-flex align-items-center justify-content-center bg-light border border-2 border-dashed\"\r\n                            style={{\r\n                              width: '150px',\r\n                              height: '150px',\r\n                              borderRadius: '8px',\r\n                              color: '#6c757d'\r\n                            }}\r\n                          >\r\n                            <div className=\"text-center\">\r\n                              <BsImage size={30} className=\"mb-2\" />\r\n                              <div className=\"small\">No Image</div>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Upload Controls */}\r\n                      <div className=\"flex-grow-1\">\r\n                        <Form.Control\r\n                          type=\"file\"\r\n                          accept=\"image/*\"\r\n                          onChange={handleImageChange}\r\n                          disabled={isSubmitting || uploadingImage}\r\n                          className=\"mb-2\"\r\n                        />\r\n                        <Form.Text className=\"text-muted\">\r\n                          Upload an image of the cutting process. Supported formats: JPG, PNG, GIF (Max: 5MB)\r\n                        </Form.Text>\r\n                        {uploadingImage && (\r\n                          <div className=\"mt-2\">\r\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                            <small>Uploading image...</small>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addFabricGroup}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Definition\r\n                </Button>\r\n              </div>\r\n\r\n              {fabricGroups.map((group, groupIndex) => {\r\n                // Get fabric variants for the selected fabric definition\r\n                const groupVariants = group.fabric_definition\r\n                  ? allFabricVariants.filter(v => v.fabric_definition === parseInt(group.fabric_definition))\r\n                  : [];\r\n\r\n                return (\r\n                  <Card key={group.id} className=\"mb-4 border\">\r\n                    <Card.Header className=\"d-flex justify-content-between align-items-center bg-primary text-white\">\r\n                      <h5 className=\"mb-0\">Fabric Definition #{groupIndex + 1}</h5>\r\n                      <Button\r\n                        variant=\"outline-light\"\r\n                        size=\"sm\"\r\n                        onClick={() => removeFabricGroup(groupIndex)}\r\n                        disabled={fabricGroups.length === 1}\r\n                      >\r\n                        <BsTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      {/* Fabric Definition Selection */}\r\n                      <Row className=\"mb-3\">\r\n                        <Col md={12}>\r\n                          <Form.Group>\r\n                            <Form.Label><strong>Select Fabric Definition</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading fabric definitions...</span>\r\n                              </div>\r\n                            ) : (\r\n                              <Form.Select\r\n                                value={group.fabric_definition}\r\n                                onChange={(e) => handleFabricDefinitionChange(groupIndex, e.target.value)}\r\n                                required\r\n                              >\r\n                                <option value=\"\">Select Fabric Definition</option>\r\n                                {fabricDefinitions.map((fd) => (\r\n                                  <option key={fd.id} value={fd.id}>\r\n                                    {fd.fabric_name} - {fd.supplier_name || 'Unknown Supplier'}\r\n                                  </option>\r\n                                ))}\r\n                              </Form.Select>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n\r\n                      {/* Fabric Variants Section */}\r\n                      {group.fabric_definition && (\r\n                        <>\r\n                          <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                            <h6 className=\"mb-0\">Fabric Variants</h6>\r\n                            <Button\r\n                              variant=\"outline-success\"\r\n                              size=\"sm\"\r\n                              onClick={() => addVariantToGroup(groupIndex)}\r\n                              disabled={isSubmitting}\r\n                            >\r\n                              <BsPlus className=\"me-1\" /> Add Variant\r\n                            </Button>\r\n                          </div>\r\n\r\n                          {group.variants.map((variant, variantIndex) => {\r\n                            const currentVariant = allFabricVariants.find(v => v.id === variant.fabric_variant);\r\n\r\n                            return (\r\n                              <Card key={variantIndex} className=\"mb-3 border-light\">\r\n                                <Card.Body>\r\n                                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                    <div className=\"d-flex align-items-center\">\r\n                                      <h6 className=\"mb-0 me-2\">Variant #{variantIndex + 1}</h6>\r\n                                      {currentVariant && (\r\n                                        <div className=\"d-flex align-items-center\">\r\n                                          <div\r\n                                            style={{\r\n                                              width: '16px',\r\n                                              height: '16px',\r\n                                              backgroundColor: currentVariant.color,\r\n                                              border: '1px solid #ccc',\r\n                                              borderRadius: '3px',\r\n                                              marginRight: '6px'\r\n                                            }}\r\n                                            title={`Color: ${currentVariant.color}`}\r\n                                          />\r\n                                          <small className=\"text-muted\">\r\n                                            {currentVariant.color_name || currentVariant.color}\r\n                                          </small>\r\n                                        </div>\r\n                                      )}\r\n                                    </div>\r\n                                    <Button\r\n                                      variant=\"outline-danger\"\r\n                                      size=\"sm\"\r\n                                      onClick={() => removeVariantFromGroup(groupIndex, variantIndex)}\r\n                                      disabled={group.variants.length === 1}\r\n                                    >\r\n                                      <BsTrash className=\"me-1\" /> Remove\r\n                                    </Button>\r\n                                  </div>\r\n\r\n                                  <Row>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                                        <ColorVariantSelector\r\n                                          variants={groupVariants}\r\n                                          selectedValue={variant.fabric_variant}\r\n                                          onSelect={(value) => handleVariantChange(groupIndex, variantIndex, 'fabric_variant', value)}\r\n                                          placeholder=\"Select Color Variant\"\r\n                                          isDuplicateFunction={isDuplicateFabricVariant}\r\n                                          groupIndex={groupIndex}\r\n                                          variantIndex={variantIndex}\r\n                                        />\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <div className=\"d-flex justify-content-between align-items-center mb-1\">\r\n                                          <Form.Label className=\"mb-0\"><strong>Yard Usage</strong></Form.Label>\r\n                                          {currentVariant && (\r\n                                            <div className=\"d-flex align-items-center\">\r\n                                              <div\r\n                                                style={{\r\n                                                  width: '16px',\r\n                                                  height: '16px',\r\n                                                  backgroundColor: currentVariant.color,\r\n                                                  border: '1px solid #ccc',\r\n                                                  borderRadius: '3px',\r\n                                                  marginRight: '8px'\r\n                                                }}\r\n                                                title={`Color: ${currentVariant.color_name || currentVariant.color}`}\r\n                                              />\r\n                                              <span className={parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\"}>\r\n                                                {currentVariant.color_name || currentVariant.color} - Available: {currentVariant.available_yard || currentVariant.total_yard} yards\r\n                                              </span>\r\n                                            </div>\r\n                                          )}\r\n                                        </div>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={variant.yard_usage}\r\n                                          onChange={(e) => handleVariantChange(groupIndex, variantIndex, 'yard_usage', e.target.value)}\r\n                                          required\r\n                                          placeholder=\"Enter yards used\"\r\n                                          isInvalid={currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)}\r\n                                          className={currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\"}\r\n                                          style={{ height: '38px' }}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)\r\n                                            ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)`\r\n                                            : \"Please enter valid yard usage.\"}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n\r\n                                  <Form.Label className=\"mt-2\"><strong>Size Quantities</strong></Form.Label>\r\n                                  <Row>\r\n                                    {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\r\n                                      const sizeKey = size.toLowerCase();\r\n                                      return (\r\n                                        <Col key={sizeIndex} xs={6} sm={4} md={2} className=\"mb-3\">\r\n                                          <Form.Group>\r\n                                            <Form.Label className=\"text-center d-block\">{size}</Form.Label>\r\n                                            <Form.Control\r\n                                              type=\"number\"\r\n                                              min=\"0\"\r\n                                              value={variant[sizeKey]}\r\n                                              onChange={(e) => {\r\n                                                const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                                handleVariantChange(groupIndex, variantIndex, sizeKey, val);\r\n                                              }}\r\n                                              className=\"text-center\"\r\n                                            />\r\n                                          </Form.Group>\r\n                                        </Col>\r\n                                      );\r\n                                    })}\r\n                                    <Col xs={6} sm={4} md={2} className=\"mb-3\">\r\n                                      <Form.Group>\r\n                                        <Form.Label className=\"text-center d-block\">Total</Form.Label>\r\n                                        <div className=\"form-control text-center bg-light\">\r\n                                          {parseInt(variant.xs || 0) +\r\n                                           parseInt(variant.s || 0) +\r\n                                           parseInt(variant.m || 0) +\r\n                                           parseInt(variant.l || 0) +\r\n                                           parseInt(variant.xl || 0)}\r\n                                        </div>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n                                </Card.Body>\r\n                              </Card>\r\n                            );\r\n                          })}\r\n                        </>\r\n                      )}\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-end mb-4\">\r\n                <Card className=\"border-0\" style={{ backgroundColor: \"#e8f4fe\" }}>\r\n                  <Card.Body className=\"py-2\">\r\n                    <div className=\"d-flex flex-column\">\r\n                      <div className=\"d-flex align-items-center mb-2\">\r\n                        <strong className=\"me-2\">Total Quantities:</strong>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XS: {totalQuantities.xs}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">S: {totalQuantities.s}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">M: {totalQuantities.m}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">L: {totalQuantities.l}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XL: {totalQuantities.xl}</Badge>\r\n                        <Badge bg=\"success\" className=\"ms-2\">Total: {totalQuantities.total}</Badge>\r\n                      </div>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <strong className=\"me-2\">Total Yard Usage:</strong>\r\n                        <Badge bg=\"info\">{totalQuantities.yard_usage.toFixed(2)} yards</Badge>\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* PDF Generation Modal */}\r\n      <Modal show={showPdfModal} onHide={handleCloseModal} size=\"lg\" centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Generate Cutting Record PDF</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Would you like to generate a PDF for this cutting record?</p>\r\n\r\n          {submittedRecord && (\r\n            <div className=\"mb-3\">\r\n              <p>The PDF will include the following information:</p>\r\n              <ul>\r\n                <li><strong>Product Name:</strong> {submittedRecord.product_name}</li>\r\n                <li><strong>Fabric:</strong> {submittedRecord.fabric_name}</li>\r\n                <li><strong>Cutting Date:</strong> {new Date(submittedRecord.cutting_date).toLocaleDateString()}</li>\r\n                <li><strong>Total Quantities:</strong> XS: {submittedRecord.totalQuantities.xs},\r\n                  S: {submittedRecord.totalQuantities.s},\r\n                  M: {submittedRecord.totalQuantities.m},\r\n                  L: {submittedRecord.totalQuantities.l},\r\n                  XL: {submittedRecord.totalQuantities.xl}</li>\r\n                <li><strong>Total Items:</strong> {submittedRecord.totalQuantities.total}</li>\r\n                <li><strong>Total Yard Usage:</strong> {submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards</li>\r\n              </ul>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\r\n            No, Skip\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={generatePDF}>\r\n            <BsFilePdf className=\"me-2\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddCuttingRecord;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,CAAAC,oBAAoB,KAAM,oCAAoC,CACrE,OAASC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,GAAG,CAAEC,GAAG,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACnG,OAASC,UAAU,CAAEC,MAAM,CAAEC,OAAO,CAAEC,cAAc,CAAEC,qBAAqB,CAAEC,SAAS,CAAEC,OAAO,CAAEC,QAAQ,KAAQ,gBAAgB,CACjI,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtD,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACkC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACoC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACsC,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACwC,WAAW,CAAEC,cAAc,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC0C,YAAY,CAAEC,eAAe,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC4C,SAAS,CAAEC,YAAY,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC8C,YAAY,CAAEC,eAAe,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgD,cAAc,CAAEC,iBAAiB,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAACkD,YAAY,CAAEC,eAAe,CAAC,CAAGnD,QAAQ,CAAC,CAC/C,CACEoD,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CACF,CAAC,CAEF;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGjE,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACkE,KAAK,CAAEC,QAAQ,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoE,OAAO,CAAEC,UAAU,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACsE,YAAY,CAAEC,eAAe,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACwE,aAAa,CAAEC,gBAAgB,CAAC,CAAGzE,QAAQ,CAAC0E,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG7E,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8E,YAAY,CAAEC,eAAe,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgF,eAAe,CAAEC,kBAAkB,CAAC,CAAGjF,QAAQ,CAAC,IAAI,CAAC,CAE5D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiF,YAAY,CAAGA,CAAA,GAAM,CACzBT,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACS,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMR,MAAM,CAACU,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACAjF,SAAS,CAAC,IAAM,CACdgE,kBAAkB,CAAC,IAAI,CAAC,CAExB;AACA,KAAM,CAAAoB,gBAAgB,CAAGnF,KAAK,CAACoF,GAAG,CAAC,+CAA+C,CAAC,CACnF;AACA,KAAM,CAAAC,aAAa,CAAGrF,KAAK,CAACoF,GAAG,CAAC,4CAA4C,CAAC,CAE7EE,OAAO,CAACC,GAAG,CAAC,CAACJ,gBAAgB,CAAEE,aAAa,CAAC,CAAC,CAC3CG,IAAI,CAACC,IAAA,EAAmC,IAAlC,CAACC,cAAc,CAAEC,WAAW,CAAC,CAAAF,IAAA,CAClC1D,oBAAoB,CAAC2D,cAAc,CAACE,IAAI,CAAC,CACzC3D,oBAAoB,CAAC0D,WAAW,CAACC,IAAI,CAAC,CACtC7B,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACD8B,KAAK,CAAEC,GAAG,EAAK,CACdC,OAAO,CAAC/B,KAAK,CAAC,6BAA6B,CAAE8B,GAAG,CAAC,CACjD7B,QAAQ,CAAC,+CAA+C,CAAC,CACzDF,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAiC,cAAc,CAAGA,CAAA,GAAM,CAC3B/C,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAE,CAChCE,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAoC,iBAAiB,CAAIC,UAAU,EAAK,CACxC,GAAIlD,YAAY,CAACmD,MAAM,CAAG,CAAC,CAAE,CAC3B,KAAM,CAAAC,SAAS,CAAGpD,YAAY,CAACqD,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKL,UAAU,CAAC,CACjEjD,eAAe,CAACmD,SAAS,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAI,iBAAiB,CAAIN,UAAU,EAAK,CACxC,KAAM,CAAAE,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CACnCoD,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAACmD,IAAI,CAAC,CAAElD,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAC,CAC3GZ,eAAe,CAACmD,SAAS,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAM,sBAAsB,CAAGA,CAACR,UAAU,CAAES,YAAY,GAAK,CAC3D,KAAM,CAAAP,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CACnC,GAAIoD,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAC6C,MAAM,CAAG,CAAC,CAAE,CAC7CC,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAG8C,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAC+C,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKI,YAAY,CAAC,CACpG1D,eAAe,CAACmD,SAAS,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,4BAA4B,CAAGA,CAACV,UAAU,CAAEW,kBAAkB,GAAK,CACvE,KAAM,CAAAT,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CACnCoD,SAAS,CAACF,UAAU,CAAC,CAAC7C,iBAAiB,CAAGwD,kBAAkB,CAC5D;AACAT,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAG,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAC,CACzGZ,eAAe,CAACmD,SAAS,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAU,mBAAmB,CAAGA,CAACZ,UAAU,CAAES,YAAY,CAAEI,KAAK,CAAEC,KAAK,GAAK,CACtE,KAAM,CAAAZ,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CAEnC;AACA,GAAI+D,KAAK,GAAK,gBAAgB,CAAE,CAC9B,GAAIE,wBAAwB,CAACf,UAAU,CAAEc,KAAK,CAAEL,YAAY,CAAC,CAAE,CAC7D1C,QAAQ,CAAC,kGAAkG,CAAC,CAC5G,OACF,CAAC,IAAM,CACLA,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAEAmC,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAACqD,YAAY,CAAC,CAACI,KAAK,CAAC,CAAGC,KAAK,CAC3D/D,eAAe,CAACmD,SAAS,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAa,wBAAwB,CAAGA,CAACf,UAAU,CAAEgB,SAAS,CAAEC,mBAAmB,GAAK,CAC/E,MAAO,CAAAnE,YAAY,CAACkD,UAAU,CAAC,CAAC5C,QAAQ,CAAC8D,IAAI,CAAC,CAACC,OAAO,CAAEC,GAAG,GACzDA,GAAG,GAAKH,mBAAmB,EAAIE,OAAO,CAAC9D,cAAc,GAAK2D,SAAS,EAAIA,SAAS,GAAK,EACvF,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAK,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAAC,IAAI,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIF,IAAI,CAAE,CACR9E,YAAY,CAAC8E,IAAI,CAAC,CAClB;AACA,KAAM,CAAAG,UAAU,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAC5C5E,eAAe,CAAC+E,UAAU,CAAC,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAACrF,SAAS,CAAE,MAAO,EAAE,CAAE;AAE3B,GAAI,CACFK,iBAAiB,CAAC,IAAI,CAAC,CACvB,KAAM,CAAAiF,QAAQ,CAAG,KAAM,CAAA1G,WAAW,CAACoB,SAAS,CAAE,gBAAgB,CAAC,CAC/DD,eAAe,CAACuF,QAAQ,CAAC,CACzB,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOhE,KAAK,CAAE,CACd+B,OAAO,CAAC/B,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAAiE,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CAAC,OAAS,CACRlF,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAmF,WAAW,CAAGA,CAAA,GAAM,CACxBvF,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,EAAE,CAAC,CACnBJ,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAA0F,YAAY,CAAG,KAAO,CAAAX,CAAC,EAAK,CAChCA,CAAC,CAACY,cAAc,CAAC,CAAC,CAElB;AACA,KAAM,CAAAC,IAAI,CAAGb,CAAC,CAACc,aAAa,CAC5B,GAAID,IAAI,CAACE,aAAa,CAAC,CAAC,GAAK,KAAK,CAAE,CAClCf,CAAC,CAACgB,eAAe,CAAC,CAAC,CACnB7D,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEA;AACA,KAAM,CAAA8D,cAAc,CAAGzF,YAAY,CAACoE,IAAI,CAACsB,KAAK,EAC5CA,KAAK,CAACrF,iBAAiB,EAAIqF,KAAK,CAACpF,QAAQ,CAAC8D,IAAI,CAACC,OAAO,EAAIA,OAAO,CAAC9D,cAAc,CAClF,CAAC,CACD,GAAI,CAACkF,cAAc,CAAE,CACnBxE,QAAQ,CAAC,sEAAsE,CAAC,CAChF,OACF,CAEA;AACA,GAAI,CAAA0E,eAAe,CAAG,KAAK,CAC3B,IAAK,GAAI,CAAAzC,UAAU,CAAG,CAAC,CAAEA,UAAU,CAAGlD,YAAY,CAACmD,MAAM,CAAED,UAAU,EAAE,CAAE,CACvE,KAAM,CAAAwC,KAAK,CAAG1F,YAAY,CAACkD,UAAU,CAAC,CAEtC,GAAIwC,KAAK,CAACrF,iBAAiB,EAAIqF,KAAK,CAACpF,QAAQ,CAAC8D,IAAI,CAACC,OAAO,EAAIA,OAAO,CAAC9D,cAAc,CAAC,CAAE,CACrF;AACA,KAAM,CAAAqF,gBAAgB,CAAGF,KAAK,CAACpF,QAAQ,CAACuF,GAAG,CAACxB,OAAO,EAAIA,OAAO,CAAC9D,cAAc,CAAC,CAAC8C,MAAM,CAACyC,OAAO,CAAC,CAC9F,KAAM,CAAAC,cAAc,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAACJ,gBAAgB,CAAC,CAAC,CACrD,GAAIA,gBAAgB,CAACzC,MAAM,GAAK4C,cAAc,CAAC5C,MAAM,CAAE,CACrDlC,QAAQ,CAAC,sDAAsDiC,UAAU,CAAG,CAAC,+DAA+D,CAAC,CAC7IyC,eAAe,CAAG,IAAI,CACtB,MACF,CAEA;AACA,IAAK,GAAI,CAAAtB,OAAO,GAAI,CAAAqB,KAAK,CAACpF,QAAQ,CAAE,CAClC,GAAI+D,OAAO,CAAC9D,cAAc,CAAE,CAC1B,KAAM,CAAA0F,WAAW,CAAGjH,iBAAiB,CAACkH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACjG,EAAE,GAAKmE,OAAO,CAAC9D,cAAc,CAAC,CAChF,GAAI0F,WAAW,EAAIG,UAAU,CAAC/B,OAAO,CAAC7D,UAAU,CAAC,EAAIyF,WAAW,CAACI,cAAc,EAAIJ,WAAW,CAACK,UAAU,CAAC,CAAE,CAC1GrF,QAAQ,CAAC,kBAAkBgF,WAAW,CAACM,UAAU,EAAIN,WAAW,CAACO,KAAK,6BAA6BP,WAAW,CAACI,cAAc,EAAIJ,WAAW,CAACK,UAAU,oBAAoB,CAAC,CAC5KX,eAAe,CAAG,IAAI,CACtB,MACF,CACF,CACF,CACF,CACF,CAEA,GAAIA,eAAe,CAAE,CACnBhE,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEAA,YAAY,CAAC,IAAI,CAAC,CAClBN,eAAe,CAAC,IAAI,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF;AACA,GAAI,CAAAsF,aAAa,CAAG,EAAE,CACtB,GAAI/G,SAAS,CAAE,CACb+G,aAAa,CAAG,KAAM,CAAA1B,kBAAkB,CAAC,CAAC,CAC5C,CAEA;AACA,KAAM,CAAA2B,OAAO,CAAG,EAAE,CAClB1G,YAAY,CAAC2G,OAAO,CAACjB,KAAK,EAAI,CAC5B,GAAIA,KAAK,CAACrF,iBAAiB,CAAE,CAC3BqF,KAAK,CAACpF,QAAQ,CAACqG,OAAO,CAACtC,OAAO,EAAI,CAChC,GAAIA,OAAO,CAAC9D,cAAc,CAAE,CAC1BmG,OAAO,CAACjD,IAAI,CAACY,OAAO,CAAC,CACvB,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,KAAM,CAAAuC,OAAO,CAAG,CACdC,YAAY,CAAE3H,WAAW,CACzBE,WAAW,CAAEA,WAAW,CACxB0H,YAAY,CAAExH,WAAW,CACzByH,aAAa,CAAEN,aAAa,CAC5BC,OAAO,CAAEA,OACX,CAAC,CAED,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAhK,KAAK,CAACiK,IAAI,CAAC,oDAAoD,CAAEL,OAAO,CAAC,CAChGzF,UAAU,CAAC,sCAAsC,CAAC,CAElD;AACA,KAAM,CAAA+F,WAAW,CAAG,GAAI,CAAAlB,GAAG,CAAC,CAAC,CAC7B,KAAM,CAAAmB,UAAU,CAAG,CACjB,GAAGH,QAAQ,CAACpE,IAAI,CAChB8D,OAAO,CAAEM,QAAQ,CAACpE,IAAI,CAAC8D,OAAO,CAACb,GAAG,CAACuB,MAAM,EAAI,KAAAC,qBAAA,CAAAC,sBAAA,CAC3C,KAAM,CAAAjD,OAAO,CAAGrF,iBAAiB,CAACkH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACjG,EAAE,GAAKkH,MAAM,CAAC7G,cAAc,CAAC,CAC3E,GAAI8D,OAAO,SAAPA,OAAO,YAAAgD,qBAAA,CAAPhD,OAAO,CAAEkD,sBAAsB,UAAAF,qBAAA,WAA/BA,qBAAA,CAAiCG,WAAW,CAAE,CAChDN,WAAW,CAACO,GAAG,CAACpD,OAAO,CAACkD,sBAAsB,CAACC,WAAW,CAAC,CAC7D,CACA,MAAO,CACL,GAAGJ,MAAM,CACTZ,KAAK,CAAE,CAAAnC,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEmC,KAAK,GAAI,SAAS,CAClCD,UAAU,CAAE,CAAAlC,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEkC,UAAU,IAAIlC,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEmC,KAAK,GAAI,SAAS,CAC9DgB,WAAW,CAAE,CAAAnD,OAAO,SAAPA,OAAO,kBAAAiD,sBAAA,CAAPjD,OAAO,CAAEkD,sBAAsB,UAAAD,sBAAA,iBAA/BA,sBAAA,CAAiCE,WAAW,GAAI,SAC/D,CAAC,CACH,CAAC,CAAC,CACFE,YAAY,CAAEC,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,EAAI,SAAS,CAC7DC,eAAe,CAAEA,eACnB,CAAC,CAED/F,kBAAkB,CAACoF,UAAU,CAAC,CAE9B;AACAtF,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,MAAOiB,GAAG,CAAE,CACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,CAAE8B,GAAG,CAAC,CACpD,GAAIA,GAAG,CAACkE,QAAQ,EAAIlE,GAAG,CAACkE,QAAQ,CAACpE,IAAI,CAAE,CACrC;AACA,KAAM,CAAAmF,YAAY,CAAG,MAAO,CAAAjF,GAAG,CAACkE,QAAQ,CAACpE,IAAI,GAAK,QAAQ,CACtDE,GAAG,CAACkE,QAAQ,CAACpE,IAAI,CACjB,4DAA4D,CAChE3B,QAAQ,CAAC8G,YAAY,CAAC,CACxB,CAAC,IAAM,CACL9G,QAAQ,CAAC,oDAAoD,CAAC,CAChE,CACF,CAAC,OAAS,CACRI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAA2G,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI,CAAClG,eAAe,CAAE,OAEtB,GAAI,CACF;AACA,KAAM,CAAAmG,GAAG,CAAG,GAAI,CAAA5J,KAAK,CAAC,CACpB6J,WAAW,CAAE,UAAU,CACvBC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,eAAe,CAAG,EAAE,CAC1B,KAAM,CAAAC,cAAc,CAAG,EAAE,CACzB,KAAM,CAAAC,aAAa,CAAG,CAAC,CAEvB;AACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC,CAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,CAAE,GAAG,CAAE,EAAE,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAExD;AACAX,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACU,IAAI,CAAC,qBAAqB,CAAE,EAAE,CAAE,EAAE,CAAC,CAEvCV,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAElC;AACAT,GAAG,CAACY,IAAI,CAAC,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,EAAE,CAAC,CAAE;AAE3B,KAAM,CAAAC,eAAe,CAAG,CACtB,CAAC,WAAW,CAAEhH,eAAe,CAAC5B,EAAE,CAAC6I,QAAQ,CAAC,CAAC,CAAC,CAC5C,CAAC,cAAc,CAAEjH,eAAe,CAACgF,YAAY,CAAC,CAC9C,CAAC,cAAc,CAAEhF,eAAe,CAAC4F,YAAY,CAAC,CAC9C,CAAC,cAAc,CAAE,GAAI,CAAAvH,IAAI,CAAC2B,eAAe,CAAC+E,YAAY,CAAC,CAACmC,kBAAkB,CAAC,CAAC,CAAC,CAC7E,CAAC,aAAa,CAAElH,eAAe,CAAC1C,WAAW,EAAI,KAAK,CAAC,CACtD,CAED,GAAI,CAAA6J,IAAI,CAAG,EAAE,CACbH,eAAe,CAACnC,OAAO,CAAEuC,GAAG,EAAK,CAC/BjB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,CAAE,EAAE,CAAED,IAAI,CAAC,CAC1BhB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,CAAE,EAAE,CAAED,IAAI,CAAC,CAC1BA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACY,IAAI,CAAC,EAAE,CAAEI,IAAI,CAAG,CAAC,CAAE,GAAG,CAAEA,IAAI,CAAG,CAAC,CAAC,CAAE;AACzC,CAAC,CAAC,CAEF;AACAhB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,CAAE,EAAE,CAAEM,IAAI,CAAG,EAAE,CAAC,CAEzC;AACA,KAAM,CAAAE,OAAO,CAAG,CAAC,QAAQ,CAAE,OAAO,CAAE,YAAY,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,OAAO,CAAC,CACrF,KAAM,CAAAC,SAAS,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEtD;AACA,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,GAAI,CAAAC,UAAU,CAAG,EAAE,CACnBF,SAAS,CAACzC,OAAO,CAAC4C,KAAK,EAAI,CACzBF,YAAY,CAAC5F,IAAI,CAAC6F,UAAU,CAAC,CAC7BA,UAAU,EAAIC,KAAK,CACrB,CAAC,CAAC,CAEF;AACAN,IAAI,EAAI,EAAE,CACVhB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAEhC;AACAT,GAAG,CAACuB,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/BvB,GAAG,CAACwB,IAAI,CAAC,EAAE,CAAER,IAAI,CAAG,CAAC,CAAE,GAAG,CAAE,CAAC,CAAE,GAAG,CAAC,CAEnC;AACAE,OAAO,CAACxC,OAAO,CAAC,CAAC+C,MAAM,CAAEC,KAAK,GAAK,CACjC1B,GAAG,CAACU,IAAI,CAACe,MAAM,CAAEL,YAAY,CAACM,KAAK,CAAC,CAAG,CAAC,CAAEV,IAAI,CAAC,CACjD,CAAC,CAAC,CAEF;AACAA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACY,IAAI,CAAC,EAAE,CAAEI,IAAI,CAAE,GAAG,CAAEA,IAAI,CAAC,CAE7B;AACAhB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClC5G,eAAe,CAAC4E,OAAO,CAACC,OAAO,CAAES,MAAM,EAAK,KAAAwC,UAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAC,UAAA,CAC1Cf,IAAI,EAAI,CAAC,CAET;AACA,KAAM,CAAAgB,KAAK,CAAGC,QAAQ,CAAC9C,MAAM,CAAC3G,EAAE,EAAI,CAAC,CAAC,CACxByJ,QAAQ,CAAC9C,MAAM,CAAC1G,CAAC,EAAI,CAAC,CAAC,CACvBwJ,QAAQ,CAAC9C,MAAM,CAACzG,CAAC,EAAI,CAAC,CAAC,CACvBuJ,QAAQ,CAAC9C,MAAM,CAACxG,CAAC,EAAI,CAAC,CAAC,CACvBsJ,QAAQ,CAAC9C,MAAM,CAACvG,EAAE,EAAI,CAAC,CAAC,CAEtC;AACAoH,GAAG,CAACU,IAAI,CAACvB,MAAM,CAACI,WAAW,EAAI,SAAS,CAAE6B,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACpEhB,GAAG,CAACU,IAAI,CAACvB,MAAM,CAACb,UAAU,EAAIa,MAAM,CAACZ,KAAK,CAAE6C,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACtEhB,GAAG,CAACU,IAAI,CAAC,GAAGvB,MAAM,CAAC5G,UAAU,QAAQ,CAAE6I,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAiB,UAAA,CAAAxC,MAAM,CAAC3G,EAAE,UAAAmJ,UAAA,iBAATA,UAAA,CAAWb,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAkB,SAAA,CAAAzC,MAAM,CAAC1G,CAAC,UAAAmJ,SAAA,iBAARA,SAAA,CAAUd,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAmB,SAAA,CAAA1C,MAAM,CAACzG,CAAC,UAAAmJ,SAAA,iBAARA,SAAA,CAAUf,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAoB,SAAA,CAAA3C,MAAM,CAACxG,CAAC,UAAAmJ,SAAA,iBAARA,SAAA,CAAUhB,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAqB,UAAA,CAAA5C,MAAM,CAACvG,EAAE,UAAAmJ,UAAA,iBAATA,UAAA,CAAWjB,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjEhB,GAAG,CAACU,IAAI,CAACsB,KAAK,CAAClB,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAErD;AACAA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACY,IAAI,CAAC,EAAE,CAAEI,IAAI,CAAE,GAAG,CAAEA,IAAI,CAAC,CAC/B,CAAC,CAAC,CAEF;AACAA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACuB,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/BvB,GAAG,CAACwB,IAAI,CAAC,EAAE,CAAER,IAAI,CAAG,CAAC,CAAE,GAAG,CAAE,CAAC,CAAE,GAAG,CAAC,CAEnChB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,CAAEU,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAC5ChB,GAAG,CAACU,IAAI,CAAC,EAAE,CAAEU,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACvChB,GAAG,CAACU,IAAI,CAAC,GAAG7G,eAAe,CAACgG,eAAe,CAACtH,UAAU,CAAC2J,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAEd,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACrGhB,GAAG,CAACU,IAAI,CAAC7G,eAAe,CAACgG,eAAe,CAACrH,EAAE,CAACsI,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAClFhB,GAAG,CAACU,IAAI,CAAC7G,eAAe,CAACgG,eAAe,CAACpH,CAAC,CAACqI,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjFhB,GAAG,CAACU,IAAI,CAAC7G,eAAe,CAACgG,eAAe,CAACnH,CAAC,CAACoI,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjFhB,GAAG,CAACU,IAAI,CAAC7G,eAAe,CAACgG,eAAe,CAAClH,CAAC,CAACmI,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjFhB,GAAG,CAACU,IAAI,CAAC7G,eAAe,CAACgG,eAAe,CAACjH,EAAE,CAACkI,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAClFhB,GAAG,CAACU,IAAI,CAAC7G,eAAe,CAACgG,eAAe,CAACmC,KAAK,CAAClB,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAErF;AACAhB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC,CAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,GAAI,CAAAxI,IAAI,CAAC,CAAC,CAACiK,cAAc,CAAC,CAAC,EAAE,CAAE,GAAG,CAAE,GAAG,CAAE,CAAExB,KAAK,CAAE,QAAS,CAAC,CAAC,CACvFX,GAAG,CAACU,IAAI,CAAC,mCAAmC,CAAE,GAAG,CAAE,GAAG,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAE5E;AACAX,GAAG,CAACoC,IAAI,CAAC,kBAAkBvI,eAAe,CAAC5B,EAAE,IAAI4B,eAAe,CAACgF,YAAY,MAAM,CAAC,CAEpF;AACAjF,eAAe,CAAC,KAAK,CAAC,CACtB1C,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,eAAe,CAAC,EAAE,CAAC,CACnBE,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,EAAE,CAAC,CACnBI,eAAe,CAAC,CAAC,CACfC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CAAC,CAAC,CACHc,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,MAAOX,KAAK,CAAE,CACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CC,QAAQ,CAAC,2CAA2C,CAAC,CACrDY,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAAyI,gBAAgB,CAAGA,CAAA,GAAM,CAC7BzI,eAAe,CAAC,KAAK,CAAC,CACtB;AACA1C,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,eAAe,CAAC,EAAE,CAAC,CACnBE,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,EAAE,CAAC,CACnBI,eAAe,CAAC,CAAC,CACfC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CAAC,CAAC,CACHc,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAID;AACA,KAAM,CAAAmG,eAAe,CAAG9H,YAAY,CAACuK,MAAM,CAAC,CAACC,GAAG,CAAE9E,KAAK,GAAK,CAC1DA,KAAK,CAACpF,QAAQ,CAACqG,OAAO,CAACtC,OAAO,EAAI,CAChC,GAAIA,OAAO,CAAC9D,cAAc,CAAE,CAC1BiK,GAAG,CAAC/J,EAAE,EAAIyJ,QAAQ,CAAC7F,OAAO,CAAC5D,EAAE,CAAC,EAAI,CAAC,CACnC+J,GAAG,CAAC9J,CAAC,EAAIwJ,QAAQ,CAAC7F,OAAO,CAAC3D,CAAC,CAAC,EAAI,CAAC,CACjC8J,GAAG,CAAC7J,CAAC,EAAIuJ,QAAQ,CAAC7F,OAAO,CAAC1D,CAAC,CAAC,EAAI,CAAC,CACjC6J,GAAG,CAAC5J,CAAC,EAAIsJ,QAAQ,CAAC7F,OAAO,CAACzD,CAAC,CAAC,EAAI,CAAC,CACjC4J,GAAG,CAAC3J,EAAE,EAAIqJ,QAAQ,CAAC7F,OAAO,CAACxD,EAAE,CAAC,EAAI,CAAC,CACnC2J,GAAG,CAACP,KAAK,EAAI,CAACC,QAAQ,CAAC7F,OAAO,CAAC5D,EAAE,CAAC,EAAI,CAAC,GAC1ByJ,QAAQ,CAAC7F,OAAO,CAAC3D,CAAC,CAAC,EAAI,CAAC,CAAC,EACzBwJ,QAAQ,CAAC7F,OAAO,CAAC1D,CAAC,CAAC,EAAI,CAAC,CAAC,EACzBuJ,QAAQ,CAAC7F,OAAO,CAACzD,CAAC,CAAC,EAAI,CAAC,CAAC,EACzBsJ,QAAQ,CAAC7F,OAAO,CAACxD,EAAE,CAAC,EAAI,CAAC,CAAC,CACvC2J,GAAG,CAAChK,UAAU,EAAI4F,UAAU,CAAC/B,OAAO,CAAC7D,UAAU,CAAC,EAAI,CAAC,CACvD,CACF,CAAC,CAAC,CACF,MAAO,CAAAgK,GAAG,CACZ,CAAC,CAAE,CAAE/J,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEoJ,KAAK,CAAE,CAAC,CAAEzJ,UAAU,CAAE,CAAE,CAAC,CAAC,CAE/D,mBACE9B,KAAA,CAAAE,SAAA,EAAA6L,QAAA,eACEjM,IAAA,CAACvB,eAAe,GAAE,CAAC,cACnByB,KAAA,QACEgM,KAAK,CAAE,CACLC,UAAU,CAAErJ,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5CiI,KAAK,CAAE,eAAejI,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDsJ,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAJ,QAAA,eAEF/L,KAAA,OAAIoM,SAAS,CAAC,MAAM,CAAAL,QAAA,eAClBjM,IAAA,CAACX,UAAU,EAACiN,SAAS,CAAC,MAAM,CAAE,CAAC,qBAEjC,EAAI,CAAC,CAEJ5J,OAAO,eACNxC,KAAA,CAACjB,KAAK,EAAC4G,OAAO,CAAC,SAAS,CAACyG,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eAC5DjM,IAAA,CAACR,cAAc,EAAC8M,SAAS,CAAC,MAAM,CAACC,IAAI,CAAE,EAAG,CAAE,CAAC,CAC5C7J,OAAO,EACH,CACR,CAEAF,KAAK,eACJtC,KAAA,CAACjB,KAAK,EAAC4G,OAAO,CAAC,QAAQ,CAACyG,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eAC3DjM,IAAA,CAACP,qBAAqB,EAAC6M,SAAS,CAAC,MAAM,CAACC,IAAI,CAAE,EAAG,CAAE,CAAC,CACnD/J,KAAK,EACD,CACR,cAEDxC,IAAA,CAACrB,IAAI,EAAC2N,SAAS,CAAC,gBAAgB,CAACJ,KAAK,CAAE,CAAEM,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAR,QAAA,cAC3FjM,IAAA,CAACrB,IAAI,CAAC+N,IAAI,EAAAT,QAAA,cACR/L,KAAA,CAACtB,IAAI,EAAC+N,UAAU,MAACzJ,SAAS,CAAEA,SAAU,CAAC0J,QAAQ,CAAEjG,YAAa,CAAAsF,QAAA,eAC5DjM,IAAA,CAAClB,GAAG,EAAAmN,QAAA,cACFjM,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1BjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAAAd,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtDjM,IAAA,CAACpB,IAAI,CAACoO,OAAO,EACXC,IAAI,CAAC,MAAM,CACXzH,KAAK,CAAE1E,WAAY,CACnBoM,QAAQ,CAAGlH,CAAC,EAAKjF,cAAc,CAACiF,CAAC,CAACE,MAAM,CAACV,KAAK,CAAE,CAChD2H,WAAW,CAAC,oBAAoB,CAChCC,QAAQ,MACT,CAAC,cACFpN,IAAA,CAACpB,IAAI,CAACoO,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAAhB,QAAA,CAAC,gCAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,CACH,CAAC,cAEN/L,KAAA,CAACpB,GAAG,EAAAmN,QAAA,eACFjM,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1BjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAAAd,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtDjM,IAAA,CAACpB,IAAI,CAACoO,OAAO,EACXC,IAAI,CAAC,MAAM,CACXzH,KAAK,CAAE9E,WAAY,CACnBwM,QAAQ,CAAGlH,CAAC,EAAKrF,cAAc,CAACqF,CAAC,CAACE,MAAM,CAACV,KAAK,CAAE,CAChD4H,QAAQ,MACT,CAAC,cACFpN,IAAA,CAACpB,IAAI,CAACoO,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAAhB,QAAA,CAAC,+BAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,cACNjM,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1BjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAAAd,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAY,CAAC,cACrDjM,IAAA,CAACpB,IAAI,CAACoO,OAAO,EACXM,EAAE,CAAC,UAAU,CACbC,IAAI,CAAE,CAAE,CACR/H,KAAK,CAAE5E,WAAY,CACnBsM,QAAQ,CAAGlH,CAAC,EAAKnF,cAAc,CAACmF,CAAC,CAACE,MAAM,CAACV,KAAK,CAAE,CAChD2H,WAAW,CAAC,4CAA4C,CACzD,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,cAGNnN,IAAA,CAAClB,GAAG,EAAAmN,QAAA,cACFjM,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,EAAG,CAAAZ,QAAA,cACV/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1BjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAAAd,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,eAAa,CAAQ,CAAC,CAAY,CAAC,cACvD/L,KAAA,QAAKoM,SAAS,CAAC,gCAAgC,CAAAL,QAAA,eAE7CjM,IAAA,QAAAiM,QAAA,CACG7K,YAAY,cACXlB,KAAA,QAAKoM,SAAS,CAAC,mBAAmB,CAAAL,QAAA,eAChCjM,IAAA,CAACZ,KAAK,EACJoO,GAAG,CAAEpM,YAAa,CAClBqM,GAAG,CAAC,iBAAiB,CACrBvB,KAAK,CAAE,CACLnB,KAAK,CAAE,OAAO,CACd2C,MAAM,CAAE,OAAO,CACfC,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAE,mBAAmB,CAC3BnB,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,cACFzM,IAAA,CAACnB,MAAM,EACLgH,OAAO,CAAC,QAAQ,CAChB0G,IAAI,CAAC,IAAI,CACTD,SAAS,CAAC,mCAAmC,CAC7CuB,OAAO,CAAEnH,WAAY,CACrBoH,QAAQ,CAAElL,YAAY,EAAItB,cAAe,CAAA2K,QAAA,cAEzCjM,IAAA,CAACT,OAAO,GAAE,CAAC,CACL,CAAC,EACN,CAAC,cAENS,IAAA,QACEsM,SAAS,CAAC,yFAAyF,CACnGJ,KAAK,CAAE,CACLnB,KAAK,CAAE,OAAO,CACd2C,MAAM,CAAE,OAAO,CACfjB,YAAY,CAAE,KAAK,CACnBzE,KAAK,CAAE,SACT,CAAE,CAAAiE,QAAA,cAEF/L,KAAA,QAAKoM,SAAS,CAAC,aAAa,CAAAL,QAAA,eAC1BjM,IAAA,CAACL,OAAO,EAAC4M,IAAI,CAAE,EAAG,CAACD,SAAS,CAAC,MAAM,CAAE,CAAC,cACtCtM,IAAA,QAAKsM,SAAS,CAAC,OAAO,CAAAL,QAAA,CAAC,UAAQ,CAAK,CAAC,EAClC,CAAC,CACH,CACN,CACE,CAAC,cAGN/L,KAAA,QAAKoM,SAAS,CAAC,aAAa,CAAAL,QAAA,eAC1BjM,IAAA,CAACpB,IAAI,CAACoO,OAAO,EACXC,IAAI,CAAC,MAAM,CACXc,MAAM,CAAC,SAAS,CAChBb,QAAQ,CAAEnH,iBAAkB,CAC5B+H,QAAQ,CAAElL,YAAY,EAAItB,cAAe,CACzCgL,SAAS,CAAC,MAAM,CACjB,CAAC,cACFtM,IAAA,CAACpB,IAAI,CAACoP,IAAI,EAAC1B,SAAS,CAAC,YAAY,CAAAL,QAAA,CAAC,qFAElC,CAAW,CAAC,CACX3K,cAAc,eACbpB,KAAA,QAAKoM,SAAS,CAAC,MAAM,CAAAL,QAAA,eACnBjM,IAAA,CAAChB,OAAO,EAACiP,SAAS,CAAC,QAAQ,CAAC1B,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDtM,IAAA,UAAAiM,QAAA,CAAO,oBAAkB,CAAO,CAAC,EAC9B,CACN,EACE,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,CACH,CAAC,cAEN/L,KAAA,QAAKoM,SAAS,CAAC,gFAAgF,CAAAL,QAAA,eAC7FjM,IAAA,OAAIsM,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,gBAAc,CAAI,CAAC,cACxC/L,KAAA,CAACrB,MAAM,EACLgH,OAAO,CAAC,SAAS,CACjB0G,IAAI,CAAC,IAAI,CACTsB,OAAO,CAAErJ,cAAe,CACxBsJ,QAAQ,CAAElL,YAAa,CAAAqJ,QAAA,eAEvBjM,IAAA,CAACV,MAAM,EAACgN,SAAS,CAAC,MAAM,CAAE,CAAC,yBAC7B,EAAQ,CAAC,EACN,CAAC,CAEL9K,YAAY,CAAC6F,GAAG,CAAC,CAACH,KAAK,CAAExC,UAAU,GAAK,CACvC;AACA,KAAM,CAAAwJ,aAAa,CAAGhH,KAAK,CAACrF,iBAAiB,CACzCrB,iBAAiB,CAACqE,MAAM,CAAC8C,CAAC,EAAIA,CAAC,CAAC9F,iBAAiB,GAAK6J,QAAQ,CAACxE,KAAK,CAACrF,iBAAiB,CAAC,CAAC,CACxF,EAAE,CAEN,mBACE3B,KAAA,CAACvB,IAAI,EAAgB2N,SAAS,CAAC,aAAa,CAAAL,QAAA,eAC1C/L,KAAA,CAACvB,IAAI,CAACwP,MAAM,EAAC7B,SAAS,CAAC,yEAAyE,CAAAL,QAAA,eAC9F/L,KAAA,OAAIoM,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,qBAAmB,CAACvH,UAAU,CAAG,CAAC,EAAK,CAAC,cAC7DxE,KAAA,CAACrB,MAAM,EACLgH,OAAO,CAAC,eAAe,CACvB0G,IAAI,CAAC,IAAI,CACTsB,OAAO,CAAEA,CAAA,GAAMpJ,iBAAiB,CAACC,UAAU,CAAE,CAC7CoJ,QAAQ,CAAEtM,YAAY,CAACmD,MAAM,GAAK,CAAE,CAAAsH,QAAA,eAEpCjM,IAAA,CAACT,OAAO,EAAC+M,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CAAC,EACE,CAAC,cACdpM,KAAA,CAACvB,IAAI,CAAC+N,IAAI,EAAAT,QAAA,eAERjM,IAAA,CAAClB,GAAG,EAACwN,SAAS,CAAC,MAAM,CAAAL,QAAA,cACnBjM,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,EAAG,CAAAZ,QAAA,cACV/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAAAb,QAAA,eACTjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAAAd,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,CAAY,CAAC,CACjE3J,eAAe,cACdpC,KAAA,QAAKoM,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxCjM,IAAA,CAAChB,OAAO,EAACiP,SAAS,CAAC,QAAQ,CAAC1B,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDtM,IAAA,SAAAiM,QAAA,CAAM,+BAA6B,CAAM,CAAC,EACvC,CAAC,cAEN/L,KAAA,CAACtB,IAAI,CAACwP,MAAM,EACV5I,KAAK,CAAE0B,KAAK,CAACrF,iBAAkB,CAC/BqL,QAAQ,CAAGlH,CAAC,EAAKZ,4BAA4B,CAACV,UAAU,CAAEsB,CAAC,CAACE,MAAM,CAACV,KAAK,CAAE,CAC1E4H,QAAQ,MAAAnB,QAAA,eAERjM,IAAA,WAAQwF,KAAK,CAAC,EAAE,CAAAyG,QAAA,CAAC,0BAAwB,CAAQ,CAAC,CACjD3L,iBAAiB,CAAC+G,GAAG,CAAEgH,EAAE,eACxBnO,KAAA,WAAoBsF,KAAK,CAAE6I,EAAE,CAAC3M,EAAG,CAAAuK,QAAA,EAC9BoC,EAAE,CAACrF,WAAW,CAAC,KAAG,CAACqF,EAAE,CAACC,aAAa,EAAI,kBAAkB,GAD/CD,EAAE,CAAC3M,EAER,CACT,CAAC,EACS,CACd,EACS,CAAC,CACV,CAAC,CACH,CAAC,CAGLwF,KAAK,CAACrF,iBAAiB,eACtB3B,KAAA,CAAAE,SAAA,EAAA6L,QAAA,eACE/L,KAAA,QAAKoM,SAAS,CAAC,wDAAwD,CAAAL,QAAA,eACrEjM,IAAA,OAAIsM,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,iBAAe,CAAI,CAAC,cACzC/L,KAAA,CAACrB,MAAM,EACLgH,OAAO,CAAC,iBAAiB,CACzB0G,IAAI,CAAC,IAAI,CACTsB,OAAO,CAAEA,CAAA,GAAM7I,iBAAiB,CAACN,UAAU,CAAE,CAC7CoJ,QAAQ,CAAElL,YAAa,CAAAqJ,QAAA,eAEvBjM,IAAA,CAACV,MAAM,EAACgN,SAAS,CAAC,MAAM,CAAE,CAAC,eAC7B,EAAQ,CAAC,EACN,CAAC,CAELpF,KAAK,CAACpF,QAAQ,CAACuF,GAAG,CAAC,CAACxB,OAAO,CAAEV,YAAY,GAAK,CAC7C,KAAM,CAAAoJ,cAAc,CAAG/N,iBAAiB,CAACkH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACjG,EAAE,GAAKmE,OAAO,CAAC9D,cAAc,CAAC,CAEnF,mBACE/B,IAAA,CAACrB,IAAI,EAAoB2N,SAAS,CAAC,mBAAmB,CAAAL,QAAA,cACpD/L,KAAA,CAACvB,IAAI,CAAC+N,IAAI,EAAAT,QAAA,eACR/L,KAAA,QAAKoM,SAAS,CAAC,wDAAwD,CAAAL,QAAA,eACrE/L,KAAA,QAAKoM,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxC/L,KAAA,OAAIoM,SAAS,CAAC,WAAW,CAAAL,QAAA,EAAC,WAAS,CAAC9G,YAAY,CAAG,CAAC,EAAK,CAAC,CACzDoJ,cAAc,eACbrO,KAAA,QAAKoM,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxCjM,IAAA,QACEkM,KAAK,CAAE,CACLnB,KAAK,CAAE,MAAM,CACb2C,MAAM,CAAE,MAAM,CACdlB,eAAe,CAAE+B,cAAc,CAACvG,KAAK,CACrC4F,MAAM,CAAE,gBAAgB,CACxBnB,YAAY,CAAE,KAAK,CACnB+B,WAAW,CAAE,KACf,CAAE,CACFC,KAAK,CAAE,UAAUF,cAAc,CAACvG,KAAK,EAAG,CACzC,CAAC,cACFhI,IAAA,UAAOsM,SAAS,CAAC,YAAY,CAAAL,QAAA,CAC1BsC,cAAc,CAACxG,UAAU,EAAIwG,cAAc,CAACvG,KAAK,CAC7C,CAAC,EACL,CACN,EACE,CAAC,cACN9H,KAAA,CAACrB,MAAM,EACLgH,OAAO,CAAC,gBAAgB,CACxB0G,IAAI,CAAC,IAAI,CACTsB,OAAO,CAAEA,CAAA,GAAM3I,sBAAsB,CAACR,UAAU,CAAES,YAAY,CAAE,CAChE2I,QAAQ,CAAE5G,KAAK,CAACpF,QAAQ,CAAC6C,MAAM,GAAK,CAAE,CAAAsH,QAAA,eAEtCjM,IAAA,CAACT,OAAO,EAAC+M,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CAAC,EACN,CAAC,cAENpM,KAAA,CAACpB,GAAG,EAAAmN,QAAA,eACFjM,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1BjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAAAd,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAY,CAAC,cAChEjM,IAAA,CAACtB,oBAAoB,EACnBoD,QAAQ,CAAEoM,aAAc,CACxBQ,aAAa,CAAE7I,OAAO,CAAC9D,cAAe,CACtC4M,QAAQ,CAAGnJ,KAAK,EAAKF,mBAAmB,CAACZ,UAAU,CAAES,YAAY,CAAE,gBAAgB,CAAEK,KAAK,CAAE,CAC5F2H,WAAW,CAAC,sBAAsB,CAClCyB,mBAAmB,CAAEnJ,wBAAyB,CAC9Cf,UAAU,CAAEA,UAAW,CACvBS,YAAY,CAAEA,YAAa,CAC5B,CAAC,EACQ,CAAC,CACV,CAAC,cACNnF,IAAA,CAACjB,GAAG,EAAC8N,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1B/L,KAAA,QAAKoM,SAAS,CAAC,wDAAwD,CAAAL,QAAA,eACrEjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAL,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,CACpEsC,cAAc,eACbrO,KAAA,QAAKoM,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxCjM,IAAA,QACEkM,KAAK,CAAE,CACLnB,KAAK,CAAE,MAAM,CACb2C,MAAM,CAAE,MAAM,CACdlB,eAAe,CAAE+B,cAAc,CAACvG,KAAK,CACrC4F,MAAM,CAAE,gBAAgB,CACxBnB,YAAY,CAAE,KAAK,CACnB+B,WAAW,CAAE,KACf,CAAE,CACFC,KAAK,CAAE,UAAUF,cAAc,CAACxG,UAAU,EAAIwG,cAAc,CAACvG,KAAK,EAAG,CACtE,CAAC,cACF9H,KAAA,SAAMoM,SAAS,CAAE1E,UAAU,CAAC/B,OAAO,CAAC7D,UAAU,CAAC,EAAIuM,cAAc,CAAC1G,cAAc,EAAI0G,cAAc,CAACzG,UAAU,CAAC,CAAG,mBAAmB,CAAG,oBAAqB,CAAAmE,QAAA,EACzJsC,cAAc,CAACxG,UAAU,EAAIwG,cAAc,CAACvG,KAAK,CAAC,gBAAc,CAACuG,cAAc,CAAC1G,cAAc,EAAI0G,cAAc,CAACzG,UAAU,CAAC,QAC/H,EAAM,CAAC,EACJ,CACN,EACE,CAAC,cACN9H,IAAA,CAACpB,IAAI,CAACoO,OAAO,EACXC,IAAI,CAAC,QAAQ,CACb4B,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPtJ,KAAK,CAAEK,OAAO,CAAC7D,UAAW,CAC1BkL,QAAQ,CAAGlH,CAAC,EAAKV,mBAAmB,CAACZ,UAAU,CAAES,YAAY,CAAE,YAAY,CAAEa,CAAC,CAACE,MAAM,CAACV,KAAK,CAAE,CAC7F4H,QAAQ,MACRD,WAAW,CAAC,kBAAkB,CAC9B4B,SAAS,CAAER,cAAc,EAAI3G,UAAU,CAAC/B,OAAO,CAAC7D,UAAU,CAAC,EAAIuM,cAAc,CAAC1G,cAAc,EAAI0G,cAAc,CAACzG,UAAU,CAAE,CAC3HwE,SAAS,CAAEiC,cAAc,EAAI3G,UAAU,CAAC/B,OAAO,CAAC7D,UAAU,CAAC,EAAIuM,cAAc,CAAC1G,cAAc,EAAI0G,cAAc,CAACzG,UAAU,CAAC,CAAG,eAAe,CAAG,EAAG,CAClJoE,KAAK,CAAE,CAAEwB,MAAM,CAAE,MAAO,CAAE,CAC3B,CAAC,cACF1N,IAAA,CAACpB,IAAI,CAACoO,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAAhB,QAAA,CAClCsC,cAAc,EAAI3G,UAAU,CAAC/B,OAAO,CAAC7D,UAAU,CAAC,EAAIuM,cAAc,CAAC1G,cAAc,EAAI0G,cAAc,CAACzG,UAAU,CAAC,CAC5G,4BAA4ByG,cAAc,CAAC1G,cAAc,EAAI0G,cAAc,CAACzG,UAAU,mBAAmB,CACzG,gCAAgC,CACf,CAAC,EACd,CAAC,CACV,CAAC,EACH,CAAC,cAEN9H,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAL,QAAA,cAACjM,IAAA,WAAAiM,QAAA,CAAQ,iBAAe,CAAQ,CAAC,CAAY,CAAC,cAC1E/L,KAAA,CAACpB,GAAG,EAAAmN,QAAA,EACD,CAAC,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAAC5E,GAAG,CAAC,CAACkF,IAAI,CAAEyC,SAAS,GAAK,CACpD,KAAM,CAAAC,OAAO,CAAG1C,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAClC,mBACElP,IAAA,CAACjB,GAAG,EAAiBkD,EAAE,CAAE,CAAE,CAACkN,EAAE,CAAE,CAAE,CAACtC,EAAE,CAAE,CAAE,CAACP,SAAS,CAAC,MAAM,CAAAL,QAAA,cACxD/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAAAb,QAAA,eACTjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAACT,SAAS,CAAC,qBAAqB,CAAAL,QAAA,CAAEM,IAAI,CAAa,CAAC,cAC/DvM,IAAA,CAACpB,IAAI,CAACoO,OAAO,EACXC,IAAI,CAAC,QAAQ,CACb6B,GAAG,CAAC,GAAG,CACPtJ,KAAK,CAAEK,OAAO,CAACoJ,OAAO,CAAE,CACxB/B,QAAQ,CAAGlH,CAAC,EAAK,CACf,KAAM,CAAAoJ,GAAG,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE5D,QAAQ,CAAC1F,CAAC,CAACE,MAAM,CAACV,KAAK,EAAI,CAAC,CAAC,CAAC,CACtDF,mBAAmB,CAACZ,UAAU,CAAES,YAAY,CAAE8J,OAAO,CAAEG,GAAG,CAAC,CAC7D,CAAE,CACF9C,SAAS,CAAC,aAAa,CACxB,CAAC,EACQ,CAAC,EAbL0C,SAcL,CAAC,CAEV,CAAC,CAAC,cACFhP,IAAA,CAACjB,GAAG,EAACkD,EAAE,CAAE,CAAE,CAACkN,EAAE,CAAE,CAAE,CAACtC,EAAE,CAAE,CAAE,CAACP,SAAS,CAAC,MAAM,CAAAL,QAAA,cACxC/L,KAAA,CAACtB,IAAI,CAACkO,KAAK,EAAAb,QAAA,eACTjM,IAAA,CAACpB,IAAI,CAACmO,KAAK,EAACT,SAAS,CAAC,qBAAqB,CAAAL,QAAA,CAAC,OAAK,CAAY,CAAC,cAC9DjM,IAAA,QAAKsM,SAAS,CAAC,mCAAmC,CAAAL,QAAA,CAC/CP,QAAQ,CAAC7F,OAAO,CAAC5D,EAAE,EAAI,CAAC,CAAC,CACzByJ,QAAQ,CAAC7F,OAAO,CAAC3D,CAAC,EAAI,CAAC,CAAC,CACxBwJ,QAAQ,CAAC7F,OAAO,CAAC1D,CAAC,EAAI,CAAC,CAAC,CACxBuJ,QAAQ,CAAC7F,OAAO,CAACzD,CAAC,EAAI,CAAC,CAAC,CACxBsJ,QAAQ,CAAC7F,OAAO,CAACxD,EAAE,EAAI,CAAC,CAAC,CACvB,CAAC,EACI,CAAC,CACV,CAAC,EACH,CAAC,EACG,CAAC,EAhIH8C,YAiIL,CAAC,CAEX,CAAC,CAAC,EACF,CACH,EACQ,CAAC,GAlMH+B,KAAK,CAACxF,EAmMX,CAAC,CAEX,CAAC,CAAC,cAEF1B,IAAA,QAAKsM,SAAS,CAAC,iCAAiC,CAAAL,QAAA,cAC9CjM,IAAA,CAACrB,IAAI,EAAC2N,SAAS,CAAC,UAAU,CAACJ,KAAK,CAAE,CAAEM,eAAe,CAAE,SAAU,CAAE,CAAAP,QAAA,cAC/DjM,IAAA,CAACrB,IAAI,CAAC+N,IAAI,EAACJ,SAAS,CAAC,MAAM,CAAAL,QAAA,cACzB/L,KAAA,QAAKoM,SAAS,CAAC,oBAAoB,CAAAL,QAAA,eACjC/L,KAAA,QAAKoM,SAAS,CAAC,gCAAgC,CAAAL,QAAA,eAC7CjM,IAAA,WAAQsM,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cACnD/L,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,SAAS,CAACjD,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,MAAI,CAAC3C,eAAe,CAACrH,EAAE,EAAQ,CAAC,cACrE/B,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,SAAS,CAACjD,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,KAAG,CAAC3C,eAAe,CAACpH,CAAC,EAAQ,CAAC,cACnEhC,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,SAAS,CAACjD,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,KAAG,CAAC3C,eAAe,CAACnH,CAAC,EAAQ,CAAC,cACnEjC,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,SAAS,CAACjD,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,KAAG,CAAC3C,eAAe,CAAClH,CAAC,EAAQ,CAAC,cACnElC,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,SAAS,CAACjD,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,MAAI,CAAC3C,eAAe,CAACjH,EAAE,EAAQ,CAAC,cACrEnC,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,SAAS,CAACjD,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,SAAO,CAAC3C,eAAe,CAACmC,KAAK,EAAQ,CAAC,EACxE,CAAC,cACNvL,KAAA,QAAKoM,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxCjM,IAAA,WAAQsM,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cACnD/L,KAAA,CAAChB,KAAK,EAACqQ,EAAE,CAAC,MAAM,CAAAtD,QAAA,EAAE3C,eAAe,CAACtH,UAAU,CAAC2J,OAAO,CAAC,CAAC,CAAC,CAAC,QAAM,EAAO,CAAC,EACnE,CAAC,EACH,CAAC,CACG,CAAC,CACR,CAAC,CACJ,CAAC,cAEN3L,IAAA,QAAKsM,SAAS,CAAC,oCAAoC,CAAAL,QAAA,cACjDjM,IAAA,CAACnB,MAAM,EACLoO,IAAI,CAAC,QAAQ,CACbpH,OAAO,CAAC,SAAS,CACjB0G,IAAI,CAAC,IAAI,CACTuB,QAAQ,CAAElL,YAAa,CACvB0J,SAAS,CAAC,MAAM,CAAAL,QAAA,CAEfrJ,YAAY,cACX1C,KAAA,CAAAE,SAAA,EAAA6L,QAAA,eACEjM,IAAA,CAAChB,OAAO,EAACsO,EAAE,CAAC,MAAM,CAACW,SAAS,CAAC,QAAQ,CAAC1B,IAAI,CAAC,IAAI,CAACiD,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAClD,SAAS,CAAC,MAAM,CAAE,CAAC,gBAEtG,EAAE,CAAC,CAEH,uBACD,CACK,CAAC,CACN,CAAC,EACF,CAAC,CACE,CAAC,CACR,CAAC,EACJ,CAAC,cAGNpM,KAAA,CAACf,KAAK,EAACsQ,IAAI,CAAErM,YAAa,CAACsM,MAAM,CAAE5D,gBAAiB,CAACS,IAAI,CAAC,IAAI,CAACoD,QAAQ,MAAA1D,QAAA,eACrEjM,IAAA,CAACb,KAAK,CAACgP,MAAM,EAACyB,WAAW,MAAA3D,QAAA,cACvBjM,IAAA,CAACb,KAAK,CAAC0Q,KAAK,EAAA5D,QAAA,CAAC,6BAA2B,CAAa,CAAC,CAC1C,CAAC,cACf/L,KAAA,CAACf,KAAK,CAACuN,IAAI,EAAAT,QAAA,eACTjM,IAAA,MAAAiM,QAAA,CAAG,2DAAyD,CAAG,CAAC,CAE/D3I,eAAe,eACdpD,KAAA,QAAKoM,SAAS,CAAC,MAAM,CAAAL,QAAA,eACnBjM,IAAA,MAAAiM,QAAA,CAAG,iDAA+C,CAAG,CAAC,cACtD/L,KAAA,OAAA+L,QAAA,eACE/L,KAAA,OAAA+L,QAAA,eAAIjM,IAAA,WAAAiM,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAC3I,eAAe,CAACgF,YAAY,EAAK,CAAC,cACtEpI,KAAA,OAAA+L,QAAA,eAAIjM,IAAA,WAAAiM,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAC3I,eAAe,CAAC0F,WAAW,EAAK,CAAC,cAC/D9I,KAAA,OAAA+L,QAAA,eAAIjM,IAAA,WAAAiM,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAAtK,IAAI,CAAC2B,eAAe,CAAC+E,YAAY,CAAC,CAACmC,kBAAkB,CAAC,CAAC,EAAK,CAAC,cACrGtK,KAAA,OAAA+L,QAAA,eAAIjM,IAAA,WAAAiM,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,QAAK,CAAC3I,eAAe,CAACgG,eAAe,CAACrH,EAAE,CAAC,OAC1E,CAACqB,eAAe,CAACgG,eAAe,CAACpH,CAAC,CAAC,OACnC,CAACoB,eAAe,CAACgG,eAAe,CAACnH,CAAC,CAAC,OACnC,CAACmB,eAAe,CAACgG,eAAe,CAAClH,CAAC,CAAC,QAClC,CAACkB,eAAe,CAACgG,eAAe,CAACjH,EAAE,EAAK,CAAC,cAC/CnC,KAAA,OAAA+L,QAAA,eAAIjM,IAAA,WAAAiM,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAAC3I,eAAe,CAACgG,eAAe,CAACmC,KAAK,EAAK,CAAC,cAC9EvL,KAAA,OAAA+L,QAAA,eAAIjM,IAAA,WAAAiM,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,IAAC,CAAC3I,eAAe,CAACgG,eAAe,CAACtH,UAAU,CAAC2J,OAAO,CAAC,CAAC,CAAC,CAAC,QAAM,EAAI,CAAC,EACvG,CAAC,EACF,CACN,EACS,CAAC,cACbzL,KAAA,CAACf,KAAK,CAAC2Q,MAAM,EAAA7D,QAAA,eACXjM,IAAA,CAACnB,MAAM,EAACgH,OAAO,CAAC,WAAW,CAACgI,OAAO,CAAE/B,gBAAiB,CAAAG,QAAA,CAAC,UAEvD,CAAQ,CAAC,cACT/L,KAAA,CAACrB,MAAM,EAACgH,OAAO,CAAC,SAAS,CAACgI,OAAO,CAAErE,WAAY,CAAAyC,QAAA,eAC7CjM,IAAA,CAACN,SAAS,EAAC4M,SAAS,CAAC,MAAM,CAAE,CAAC,gBAChC,EAAQ,CAAC,EACG,CAAC,EACV,CAAC,EACR,CAAC,CAEP,CAAC,CAED,cAAe,CAAAjM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}