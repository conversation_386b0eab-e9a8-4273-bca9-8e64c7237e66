{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{useParams,useNavigate}from'react-router-dom';import Select from'react-select';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import ColorVariantSelector from\"../components/ColorVariantSelector\";import{Card,Form,Button,Row,Col,Spinner,Alert,Container,Badge,Modal,Image}from'react-bootstrap';import{BsScissors,BsPlus,BsTrash,BsCheck2Circle,BsExclamationTriangle,BsArrowLeft,BsImage,BsUpload}from'react-icons/bs';import{uploadImage}from'../firebase/imageUpload';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const EditCutting=()=>{const{id}=useParams();const navigate=useNavigate();// Overall cutting record fields\nconst[allFabricVariants,setAllFabricVariants]=useState([]);const[cuttingDate,setCuttingDate]=useState('');const[description,setDescription]=useState('');const[productName,setProductName]=useState('');const[cuttingImage,setCuttingImage]=useState('');const[imageFile,setImageFile]=useState(null);const[imagePreview,setImagePreview]=useState('');const[uploadingImage,setUploadingImage]=useState(false);// Cutting detail rows\nconst[details,setDetails]=useState([{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]);// Original yard usage for each variant (to calculate available yards)\nconst[originalYardUsage,setOriginalYardUsage]=useState({});// Validation errors for each detail row\nconst[detailErrors,setDetailErrors]=useState([]);// Loading, error, success states\nconst[loadingVariants,setLoadingVariants]=useState(true);const[loadingRecord,setLoadingRecord]=useState(true);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[validated,setValidated]=useState(false);const[originalRecord,setOriginalRecord]=useState(null);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// 1. Fetch the cutting record to edit\nuseEffect(()=>{setLoadingRecord(true);console.log(`Fetching cutting record with ID: ${id}`);axios.get(`http://localhost:8000/api/cutting/cutting-records/${id}/`).then(res=>{const record=res.data;console.log('Cutting record fetched successfully:',record);setOriginalRecord(record);// Set form fields with record data\nsetCuttingDate(record.cutting_date);setDescription(record.description||'');setProductName(record.product_name||'');setCuttingImage(record.cutting_image||'');setImagePreview(record.cutting_image||'');// Set details and store original yard usage\nif(record.details&&record.details.length>0){console.log('Setting details from record:',record.details);// Create a map of original yard usage by variant ID\nconst yardUsageMap={};record.details.forEach(detail=>{yardUsageMap[detail.fabric_variant]=parseFloat(detail.yard_usage);});console.log('Original yard usage map:',yardUsageMap);setOriginalYardUsage(yardUsageMap);// Initialize detail errors array with empty strings\nsetDetailErrors(Array(record.details.length).fill(''));// Set details\nsetDetails(record.details.map(detail=>({id:detail.id,// Keep the original ID for updating\nfabric_variant:detail.fabric_variant,yard_usage:detail.yard_usage,xs:detail.xs||0,s:detail.s||0,m:detail.m||0,l:detail.l||0,xl:detail.xl||0})));}setLoadingRecord(false);}).catch(err=>{console.error('Error fetching cutting record:',err);setError('Failed to load cutting record. Please try again.');setLoadingRecord(false);});},[id]);// 2. Fetch all fabric variants on mount\nuseEffect(()=>{setLoadingVariants(true);axios.get(\"http://localhost:8000/api/fabric-variants/\").then(res=>{console.log('Fabric variants fetched successfully:',res.data);setAllFabricVariants(res.data);setLoadingVariants(false);}).catch(err=>{console.error('Error fetching fabric variants:',err);setError('Failed to load fabric variants. Please try again.');setLoadingVariants(false);});},[]);// 3. Validate yard usage when variants or details change\nuseEffect(()=>{if(allFabricVariants.length>0&&details.length>0){// Create a new array for detail errors\nconst newDetailErrors=[...detailErrors];// Validate each detail\ndetails.forEach((detail,index)=>{if(detail.fabric_variant&&detail.yard_usage){const variantId=detail.fabric_variant;// Find the variant in the allFabricVariants array\nconst variant=allFabricVariants.find(v=>v.id===parseInt(variantId));if(!variant)return;// Get the original yard usage for this variant (or 0 if it's a new detail)\nconst original=originalYardUsage[variantId]||0;// Calculate the maximum allowed yard usage\nconst maxAllowed=parseFloat(variant.available_yard)+original;// Check if the yard usage exceeds the maximum allowed\nif(parseFloat(detail.yard_usage)>maxAllowed){newDetailErrors[index]=`Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;}else if(parseFloat(detail.yard_usage)<=0){newDetailErrors[index]='Yard usage must be greater than 0';}else{newDetailErrors[index]='';}}});// Update detail errors state\nsetDetailErrors(newDetailErrors);}},[allFabricVariants,details,originalYardUsage]);// Handle image file selection\nconst handleImageChange=e=>{const file=e.target.files[0];if(file){setImageFile(file);// Create preview URL\nconst previewUrl=URL.createObjectURL(file);setImagePreview(previewUrl);}};// Upload image to Firebase\nconst uploadCuttingImage=async()=>{if(!imageFile)return cuttingImage;// Return existing image URL if no new file\ntry{setUploadingImage(true);const imageUrl=await uploadImage(imageFile,'cutting-images');setCuttingImage(imageUrl);return imageUrl;}catch(error){console.error('Error uploading image:',error);throw new Error('Failed to upload image');}finally{setUploadingImage(false);}};// Remove image\nconst removeImage=()=>{setImageFile(null);setImagePreview('');setCuttingImage('');};// Add a new empty detail row\nconst addDetailRow=()=>{setDetails([...details,{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]);setDetailErrors([...detailErrors,'']);// Add an empty error for the new row\n};// Delete a detail row\nconst removeDetailRow=index=>{const newDetails=details.filter((_,i)=>i!==index);setDetails(newDetails);// Also remove the corresponding error\nconst newDetailErrors=detailErrors.filter((_,i)=>i!==index);setDetailErrors(newDetailErrors);};// Handle change for each detail row field\nconst handleDetailChange=(index,field,value)=>{const newDetails=[...details];newDetails[index][field]=value;setDetails(newDetails);// Validate yard usage if that's the field being changed\nif(field==='yard_usage'){validateYardUsage(index,value);}};// Validate yard usage against available yards\nconst validateYardUsage=(index,newYardUsage)=>{const newDetailErrors=[...detailErrors];const detail=details[index];const variantId=detail.fabric_variant;// Skip validation if no variant is selected\nif(!variantId){newDetailErrors[index]='';setDetailErrors(newDetailErrors);return;}// Find the variant in the allFabricVariants array\nconst variant=allFabricVariants.find(v=>v.id===parseInt(variantId));if(!variant){newDetailErrors[index]='';setDetailErrors(newDetailErrors);return;}// Get the original yard usage for this variant (or 0 if it's a new detail)\nconst original=originalYardUsage[variantId]||0;// Calculate the maximum allowed yard usage\n// This is the current available yards plus the original yard usage\nconst maxAllowed=parseFloat(variant.available_yard)+original;// Check if the new yard usage exceeds the maximum allowed\nif(parseFloat(newYardUsage)>maxAllowed){newDetailErrors[index]=`Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;}else if(parseFloat(newYardUsage)<=0){newDetailErrors[index]='Yard usage must be greater than 0';}else{newDetailErrors[index]='';}setDetailErrors(newDetailErrors);};// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();// Form validation\nconst form=e.currentTarget;if(form.checkValidity()===false){e.stopPropagation();setValidated(true);return;}// Check if any detail has a fabric variant selected\nconst hasValidDetails=details.some(detail=>detail.fabric_variant);if(!hasValidDetails){setError('Please select at least one fabric variant for your cutting details.');return;}// Validate all yard usage values\nlet hasYardUsageErrors=false;details.forEach((detail,index)=>{validateYardUsage(index,detail.yard_usage);if(detailErrors[index]){hasYardUsageErrors=true;}});// Check if there are any yard usage validation errors\nif(hasYardUsageErrors){setError('Please fix the yard usage errors before submitting.');return;}setValidated(true);setIsSubmitting(true);setError('');setSuccess('');try{// Upload image if a new one was selected\nlet finalImageUrl=cuttingImage;if(imageFile){finalImageUrl=await uploadCuttingImage();}const payload={cutting_date:cuttingDate,description:description,product_name:productName,cutting_image:finalImageUrl,details:details};const response=await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`,payload);setSuccess('Cutting record updated successfully!');// Redirect back to the cutting records list after a short delay\nsetTimeout(()=>{navigate('/viewcutting');},2000);}catch(err){console.error('Error updating cutting record:',err);if(err.response&&err.response.data){// Display more specific error message if available\nconst errorMessage=typeof err.response.data==='string'?err.response.data:'Failed to update cutting record. Please check your inputs.';setError(errorMessage);}else{setError('Failed to update cutting record. Please try again.');}}finally{setIsSubmitting(false);}};// Custom option component that shows a color swatch + label\nconst ColourOption=_ref=>{let{data,innerRef,innerProps}=_ref;return/*#__PURE__*/_jsxs(\"div\",{ref:innerRef,...innerProps,style:{display:'flex',alignItems:'center',padding:'4px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:16,height:16,backgroundColor:data.color,marginRight:8,border:'1px solid #ccc'}}),/*#__PURE__*/_jsx(\"span\",{children:data.label})]});};// Calculate total quantities for all details\nconst totalQuantities=details.reduce((acc,detail)=>{acc.xs+=parseInt(detail.xs)||0;acc.s+=parseInt(detail.s)||0;acc.m+=parseInt(detail.m)||0;acc.l+=parseInt(detail.l)||0;acc.xl+=parseInt(detail.xl)||0;acc.total+=(parseInt(detail.xs)||0)+(parseInt(detail.s)||0)+(parseInt(detail.m)||0)+(parseInt(detail.l)||0)+(parseInt(detail.xl)||0);acc.yard_usage+=parseFloat(detail.yard_usage)||0;return acc;},{xs:0,s:0,m:0,l:0,xl:0,total:0,yard_usage:0});if(loadingRecord){return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsx(Container,{fluid:true,style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center my-5\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",variant:\"primary\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:\"Loading cutting record...\"})]})})]});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mb-4\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",className:\"me-3\",onClick:()=>navigate('/viewcutting'),children:[/*#__PURE__*/_jsx(BsArrowLeft,{className:\"me-1\"}),\" Back\"]}),/*#__PURE__*/_jsxs(\"h2\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(BsScissors,{className:\"me-2\"}),\"Edit Cutting Record\"]})]}),success&&/*#__PURE__*/_jsxs(Alert,{variant:\"success\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsCheck2Circle,{className:\"me-2\",size:20}),success]}),error&&/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsExclamationTriangle,{className:\"me-2\",size:20}),error]}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{noValidate:true,validated:validated,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),placeholder:\"Enter product name\",required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please provide a product name.\"})]})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:cuttingDate,onChange:e=>setCuttingDate(e.target.value),required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please select a cutting date.\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Description\"})}),/*#__PURE__*/_jsx(Form.Control,{as:\"textarea\",rows:3,value:description,onChange:e=>setDescription(e.target.value),placeholder:\"Enter details about this cutting record...\"})]})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Image\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-start gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:imagePreview?/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Image,{src:imagePreview,alt:\"Cutting preview\",style:{width:'150px',height:'150px',objectFit:'cover',border:'2px solid #dee2e6',borderRadius:'8px'}}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",className:\"position-absolute top-0 end-0 m-1\",onClick:removeImage,disabled:isSubmitting||uploadingImage,children:/*#__PURE__*/_jsx(BsTrash,{})})]}):/*#__PURE__*/_jsx(\"div\",{className:\"d-flex align-items-center justify-content-center bg-light border border-2 border-dashed\",style:{width:'150px',height:'150px',borderRadius:'8px',color:'#6c757d'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(BsImage,{size:30,className:\"mb-2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"small\",children:\"No Image\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-grow-1\",children:[/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\"image/*\",onChange:handleImageChange,disabled:isSubmitting||uploadingImage,className:\"mb-2\"}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Upload an image of the cutting process. Supported formats: JPG, PNG, GIF (Max: 5MB)\"}),uploadingImage&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"small\",{children:\"Uploading image...\"})]})]})]})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:\"Fabric Details\"}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",onClick:addDetailRow,disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Fabric Variant\"]})]}),details.map((detail,index)=>{// Find the selected variant object to set the value in React-Select\nconst currentVariant=allFabricVariants.find(v=>v.id===detail.fabric_variant);return/*#__PURE__*/_jsx(Card,{className:\"mb-3 border-light\",children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric Variant (Color)\"})}),loadingVariants?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading variants...\"})]}):allFabricVariants.length===0?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Form.Select,{disabled:true,children:/*#__PURE__*/_jsx(\"option\",{children:\"No variants available\"})}),/*#__PURE__*/_jsx(\"small\",{className:\"text-danger\",children:\"No fabric variants found.\"})]}):/*#__PURE__*/_jsx(ColorVariantSelector,{variants:allFabricVariants,selectedValue:detail.fabric_variant,onSelect:value=>handleDetailChange(index,'fabric_variant',value),placeholder:\"Select Fabric Variant\",disabled:isSubmitting,showFabricName:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please select a fabric variant.\"})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Yard Usage\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0.01\",value:detail.yard_usage,onChange:e=>handleDetailChange(index,'yard_usage',e.target.value),required:true,disabled:isSubmitting,isInvalid:!!detailErrors[index],className:detailErrors[index]?'border-danger':''}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:detailErrors[index]||'Please enter valid yard usage.'}),currentVariant&&/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[\"Available: \",parseFloat(currentVariant.available_yard)+(originalYardUsage[detail.fabric_variant]||0),\" yards (Original: \",originalYardUsage[detail.fabric_variant]||0,\" yards + Current: \",currentVariant.available_yard,\" yards)\"]})]})}),/*#__PURE__*/_jsx(Col,{md:5,children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"XS\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.xs,onChange:e=>handleDetailChange(index,'xs',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"S\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.s,onChange:e=>handleDetailChange(index,'s',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"M\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.m,onChange:e=>handleDetailChange(index,'m',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"L\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.l,onChange:e=>handleDetailChange(index,'l',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"XL\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.xl,onChange:e=>handleDetailChange(index,'xl',e.target.value),disabled:isSubmitting})]})})]})}),/*#__PURE__*/_jsx(Col,{md:1,className:\"d-flex align-items-center justify-content-end\",children:/*#__PURE__*/_jsx(Button,{variant:\"outline-danger\",onClick:()=>removeDetailRow(index),disabled:details.length===1||isSubmitting,className:\"mt-2\",children:/*#__PURE__*/_jsx(BsTrash,{})})})]})})},index);}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between mb-4\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",onClick:addDetailRow,disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Another Variant\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Badge,{bg:\"info\",className:\"me-2 p-2\",children:[\"Total Pieces: \",totalQuantities.total]}),/*#__PURE__*/_jsxs(Badge,{bg:\"warning\",text:\"dark\",className:\"p-2\",children:[\"Total Yard Usage: \",totalQuantities.yard_usage.toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-end\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",className:\"me-2\",onClick:()=>navigate('/viewcutting'),disabled:isSubmitting,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",type:\"submit\",disabled:isSubmitting,children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Updating...\"]}):'Update Cutting Record'})]})]})})})]})]});};export default EditCutting;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "Select", "RoleBasedNavBar", "ColorVariantSelector", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Container", "Badge", "Modal", "Image", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsArrowLeft", "BsImage", "BsUpload", "uploadImage", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "EditCutting", "id", "navigate", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "cuttingImage", "setCuttingImage", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "uploadingImage", "setUploadingImage", "details", "setDetails", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "originalYardUsage", "setOriginalYardUsage", "detailErrors", "setDetailErrors", "loadingVariants", "setLoadingVariants", "loadingRecord", "setLoadingRecord", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "originalRecord", "setOriginalRecord", "handleResize", "addEventListener", "removeEventListener", "console", "log", "get", "then", "res", "record", "data", "cutting_date", "product_name", "cutting_image", "length", "yardUsageMap", "for<PERSON>ach", "detail", "parseFloat", "Array", "fill", "map", "catch", "err", "newDetailErrors", "index", "variantId", "variant", "find", "v", "parseInt", "original", "maxAllowed", "available_yard", "toFixed", "handleImageChange", "e", "file", "target", "files", "previewUrl", "URL", "createObjectURL", "uploadCuttingImage", "imageUrl", "Error", "removeImage", "addDetailRow", "removeDetailRow", "newDetails", "filter", "_", "i", "handleDetailChange", "field", "value", "validateYardUsage", "newYardUsage", "handleSubmit", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidDetails", "some", "hasYardUsageErrors", "finalImageUrl", "payload", "response", "put", "setTimeout", "errorMessage", "ColourOption", "_ref", "innerRef", "innerProps", "ref", "style", "display", "alignItems", "padding", "children", "width", "height", "backgroundColor", "color", "marginRight", "border", "label", "totalQuantities", "reduce", "acc", "total", "fluid", "marginLeft", "transition", "className", "animation", "role", "onClick", "size", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "src", "alt", "objectFit", "disabled", "accept", "Text", "currentV<PERSON>t", "variants", "selected<PERSON><PERSON><PERSON>", "onSelect", "showFabricName", "step", "min", "isInvalid", "bg", "text"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/EditCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport Select from 'react-select';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport ColorVariantSelector from \"../components/ColorVariantSelector\";\r\nimport { Card, Form, Button, Row, Col, Spinner, Alert, Container, Badge, Modal, Image } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsArrowLeft, BsImage, BsUpload } from 'react-icons/bs';\r\nimport { uploadImage } from '../firebase/imageUpload';\r\n\r\nconst EditCutting = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  // Overall cutting record fields\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n  const [cuttingImage, setCuttingImage] = useState('');\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState('');\r\n  const [uploadingImage, setUploadingImage] = useState(false);\r\n\r\n  // Cutting detail rows\r\n  const [details, setDetails] = useState([\r\n    { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }\r\n  ]);\r\n\r\n  // Original yard usage for each variant (to calculate available yards)\r\n  const [originalYardUsage, setOriginalYardUsage] = useState({});\r\n\r\n  // Validation errors for each detail row\r\n  const [detailErrors, setDetailErrors] = useState([]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [loadingRecord, setLoadingRecord] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [originalRecord, setOriginalRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // 1. Fetch the cutting record to edit\r\n  useEffect(() => {\r\n    setLoadingRecord(true);\r\n    console.log(`Fetching cutting record with ID: ${id}`);\r\n\r\n    axios.get(`http://localhost:8000/api/cutting/cutting-records/${id}/`)\r\n      .then((res) => {\r\n        const record = res.data;\r\n        console.log('Cutting record fetched successfully:', record);\r\n        setOriginalRecord(record);\r\n\r\n        // Set form fields with record data\r\n        setCuttingDate(record.cutting_date);\r\n        setDescription(record.description || '');\r\n        setProductName(record.product_name || '');\r\n        setCuttingImage(record.cutting_image || '');\r\n        setImagePreview(record.cutting_image || '');\r\n\r\n        // Set details and store original yard usage\r\n        if (record.details && record.details.length > 0) {\r\n          console.log('Setting details from record:', record.details);\r\n\r\n          // Create a map of original yard usage by variant ID\r\n          const yardUsageMap = {};\r\n          record.details.forEach(detail => {\r\n            yardUsageMap[detail.fabric_variant] = parseFloat(detail.yard_usage);\r\n          });\r\n          console.log('Original yard usage map:', yardUsageMap);\r\n          setOriginalYardUsage(yardUsageMap);\r\n\r\n          // Initialize detail errors array with empty strings\r\n          setDetailErrors(Array(record.details.length).fill(''));\r\n\r\n          // Set details\r\n          setDetails(record.details.map(detail => ({\r\n            id: detail.id, // Keep the original ID for updating\r\n            fabric_variant: detail.fabric_variant,\r\n            yard_usage: detail.yard_usage,\r\n            xs: detail.xs || 0,\r\n            s: detail.s || 0,\r\n            m: detail.m || 0,\r\n            l: detail.l || 0,\r\n            xl: detail.xl || 0\r\n          })));\r\n        }\r\n\r\n        setLoadingRecord(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching cutting record:', err);\r\n        setError('Failed to load cutting record. Please try again.');\r\n        setLoadingRecord(false);\r\n      });\r\n  }, [id]);\r\n\r\n  // 2. Fetch all fabric variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n    axios.get(\"http://localhost:8000/api/fabric-variants/\")\r\n      .then((res) => {\r\n        console.log('Fabric variants fetched successfully:', res.data);\r\n        setAllFabricVariants(res.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric variants:', err);\r\n        setError('Failed to load fabric variants. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n\r\n\r\n  // 3. Validate yard usage when variants or details change\r\n  useEffect(() => {\r\n    if (allFabricVariants.length > 0 && details.length > 0) {\r\n      // Create a new array for detail errors\r\n      const newDetailErrors = [...detailErrors];\r\n\r\n      // Validate each detail\r\n      details.forEach((detail, index) => {\r\n        if (detail.fabric_variant && detail.yard_usage) {\r\n          const variantId = detail.fabric_variant;\r\n\r\n          // Find the variant in the allFabricVariants array\r\n          const variant = allFabricVariants.find(v => v.id === parseInt(variantId));\r\n          if (!variant) return;\r\n\r\n          // Get the original yard usage for this variant (or 0 if it's a new detail)\r\n          const original = originalYardUsage[variantId] || 0;\r\n\r\n          // Calculate the maximum allowed yard usage\r\n          const maxAllowed = parseFloat(variant.available_yard) + original;\r\n\r\n          // Check if the yard usage exceeds the maximum allowed\r\n          if (parseFloat(detail.yard_usage) > maxAllowed) {\r\n            newDetailErrors[index] = `Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;\r\n          } else if (parseFloat(detail.yard_usage) <= 0) {\r\n            newDetailErrors[index] = 'Yard usage must be greater than 0';\r\n          } else {\r\n            newDetailErrors[index] = '';\r\n          }\r\n        }\r\n      });\r\n\r\n      // Update detail errors state\r\n      setDetailErrors(newDetailErrors);\r\n    }\r\n  }, [allFabricVariants, details, originalYardUsage]);\r\n\r\n  // Handle image file selection\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setImageFile(file);\r\n      // Create preview URL\r\n      const previewUrl = URL.createObjectURL(file);\r\n      setImagePreview(previewUrl);\r\n    }\r\n  };\r\n\r\n  // Upload image to Firebase\r\n  const uploadCuttingImage = async () => {\r\n    if (!imageFile) return cuttingImage; // Return existing image URL if no new file\r\n\r\n    try {\r\n      setUploadingImage(true);\r\n      const imageUrl = await uploadImage(imageFile, 'cutting-images');\r\n      setCuttingImage(imageUrl);\r\n      return imageUrl;\r\n    } catch (error) {\r\n      console.error('Error uploading image:', error);\r\n      throw new Error('Failed to upload image');\r\n    } finally {\r\n      setUploadingImage(false);\r\n    }\r\n  };\r\n\r\n  // Remove image\r\n  const removeImage = () => {\r\n    setImageFile(null);\r\n    setImagePreview('');\r\n    setCuttingImage('');\r\n  };\r\n\r\n  // Add a new empty detail row\r\n  const addDetailRow = () => {\r\n    setDetails([...details, { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n    setDetailErrors([...detailErrors, '']); // Add an empty error for the new row\r\n  };\r\n\r\n  // Delete a detail row\r\n  const removeDetailRow = (index) => {\r\n    const newDetails = details.filter((_, i) => i !== index);\r\n    setDetails(newDetails);\r\n\r\n    // Also remove the corresponding error\r\n    const newDetailErrors = detailErrors.filter((_, i) => i !== index);\r\n    setDetailErrors(newDetailErrors);\r\n  };\r\n\r\n  // Handle change for each detail row field\r\n  const handleDetailChange = (index, field, value) => {\r\n    const newDetails = [...details];\r\n    newDetails[index][field] = value;\r\n    setDetails(newDetails);\r\n\r\n    // Validate yard usage if that's the field being changed\r\n    if (field === 'yard_usage') {\r\n      validateYardUsage(index, value);\r\n    }\r\n  };\r\n\r\n  // Validate yard usage against available yards\r\n  const validateYardUsage = (index, newYardUsage) => {\r\n    const newDetailErrors = [...detailErrors];\r\n    const detail = details[index];\r\n    const variantId = detail.fabric_variant;\r\n\r\n    // Skip validation if no variant is selected\r\n    if (!variantId) {\r\n      newDetailErrors[index] = '';\r\n      setDetailErrors(newDetailErrors);\r\n      return;\r\n    }\r\n\r\n    // Find the variant in the allFabricVariants array\r\n    const variant = allFabricVariants.find(v => v.id === parseInt(variantId));\r\n    if (!variant) {\r\n      newDetailErrors[index] = '';\r\n      setDetailErrors(newDetailErrors);\r\n      return;\r\n    }\r\n\r\n    // Get the original yard usage for this variant (or 0 if it's a new detail)\r\n    const original = originalYardUsage[variantId] || 0;\r\n\r\n    // Calculate the maximum allowed yard usage\r\n    // This is the current available yards plus the original yard usage\r\n    const maxAllowed = parseFloat(variant.available_yard) + original;\r\n\r\n    // Check if the new yard usage exceeds the maximum allowed\r\n    if (parseFloat(newYardUsage) > maxAllowed) {\r\n      newDetailErrors[index] = `Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;\r\n    } else if (parseFloat(newYardUsage) <= 0) {\r\n      newDetailErrors[index] = 'Yard usage must be greater than 0';\r\n    } else {\r\n      newDetailErrors[index] = '';\r\n    }\r\n\r\n    setDetailErrors(newDetailErrors);\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any detail has a fabric variant selected\r\n    const hasValidDetails = details.some(detail => detail.fabric_variant);\r\n    if (!hasValidDetails) {\r\n      setError('Please select at least one fabric variant for your cutting details.');\r\n      return;\r\n    }\r\n\r\n    // Validate all yard usage values\r\n    let hasYardUsageErrors = false;\r\n    details.forEach((detail, index) => {\r\n      validateYardUsage(index, detail.yard_usage);\r\n      if (detailErrors[index]) {\r\n        hasYardUsageErrors = true;\r\n      }\r\n    });\r\n\r\n    // Check if there are any yard usage validation errors\r\n    if (hasYardUsageErrors) {\r\n      setError('Please fix the yard usage errors before submitting.');\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      // Upload image if a new one was selected\r\n      let finalImageUrl = cuttingImage;\r\n      if (imageFile) {\r\n        finalImageUrl = await uploadCuttingImage();\r\n      }\r\n\r\n      const payload = {\r\n        cutting_date: cuttingDate,\r\n        description: description,\r\n        product_name: productName,\r\n        cutting_image: finalImageUrl,\r\n        details: details\r\n      };\r\n\r\n      const response = await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\r\n      setSuccess('Cutting record updated successfully!');\r\n\r\n      // Redirect back to the cutting records list after a short delay\r\n      setTimeout(() => {\r\n        navigate('/viewcutting');\r\n      }, 2000);\r\n    } catch (err) {\r\n      console.error('Error updating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to update cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to update cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Custom option component that shows a color swatch + label\r\n  const ColourOption = ({ data, innerRef, innerProps }) => (\r\n    <div\r\n      ref={innerRef}\r\n      {...innerProps}\r\n      style={{ display: 'flex', alignItems: 'center', padding: '4px' }}\r\n    >\r\n      <div\r\n        style={{\r\n          width: 16,\r\n          height: 16,\r\n          backgroundColor: data.color,\r\n          marginRight: 8,\r\n          border: '1px solid #ccc'\r\n        }}\r\n      />\r\n      <span>{data.label}</span>\r\n    </div>\r\n  );\r\n\r\n  // Calculate total quantities for all details\r\n  const totalQuantities = details.reduce(\r\n    (acc, detail) => {\r\n      acc.xs += parseInt(detail.xs) || 0;\r\n      acc.s += parseInt(detail.s) || 0;\r\n      acc.m += parseInt(detail.m) || 0;\r\n      acc.l += parseInt(detail.l) || 0;\r\n      acc.xl += parseInt(detail.xl) || 0;\r\n      acc.total += (parseInt(detail.xs) || 0) +\r\n                  (parseInt(detail.s) || 0) +\r\n                  (parseInt(detail.m) || 0) +\r\n                  (parseInt(detail.l) || 0) +\r\n                  (parseInt(detail.xl) || 0);\r\n      acc.yard_usage += parseFloat(detail.yard_usage) || 0;\r\n      return acc;\r\n    },\r\n    { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 }\r\n  );\r\n\r\n  if (loadingRecord) {\r\n    return (\r\n      <>\r\n        <RoleBasedNavBar />\r\n        <Container fluid\r\n          style={{\r\n            marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n            width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n            transition: \"all 0.3s ease\",\r\n            padding: \"20px\"\r\n          }}\r\n        >\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading cutting record...</p>\r\n          </div>\r\n        </Container>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <div className=\"d-flex align-items-center mb-4\">\r\n          <Button\r\n            variant=\"outline-secondary\"\r\n            className=\"me-3\"\r\n            onClick={() => navigate('/viewcutting')}\r\n          >\r\n            <BsArrowLeft className=\"me-1\" /> Back\r\n          </Button>\r\n          <h2 className=\"mb-0\">\r\n            <BsScissors className=\"me-2\" />\r\n            Edit Cutting Record\r\n          </h2>\r\n        </div>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* Image Upload Section */}\r\n              <Row>\r\n                <Col md={12}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Image</strong></Form.Label>\r\n                    <div className=\"d-flex align-items-start gap-3\">\r\n                      {/* Image Preview */}\r\n                      <div>\r\n                        {imagePreview ? (\r\n                          <div className=\"position-relative\">\r\n                            <Image\r\n                              src={imagePreview}\r\n                              alt=\"Cutting preview\"\r\n                              style={{\r\n                                width: '150px',\r\n                                height: '150px',\r\n                                objectFit: 'cover',\r\n                                border: '2px solid #dee2e6',\r\n                                borderRadius: '8px'\r\n                              }}\r\n                            />\r\n                            <Button\r\n                              variant=\"danger\"\r\n                              size=\"sm\"\r\n                              className=\"position-absolute top-0 end-0 m-1\"\r\n                              onClick={removeImage}\r\n                              disabled={isSubmitting || uploadingImage}\r\n                            >\r\n                              <BsTrash />\r\n                            </Button>\r\n                          </div>\r\n                        ) : (\r\n                          <div\r\n                            className=\"d-flex align-items-center justify-content-center bg-light border border-2 border-dashed\"\r\n                            style={{\r\n                              width: '150px',\r\n                              height: '150px',\r\n                              borderRadius: '8px',\r\n                              color: '#6c757d'\r\n                            }}\r\n                          >\r\n                            <div className=\"text-center\">\r\n                              <BsImage size={30} className=\"mb-2\" />\r\n                              <div className=\"small\">No Image</div>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Upload Controls */}\r\n                      <div className=\"flex-grow-1\">\r\n                        <Form.Control\r\n                          type=\"file\"\r\n                          accept=\"image/*\"\r\n                          onChange={handleImageChange}\r\n                          disabled={isSubmitting || uploadingImage}\r\n                          className=\"mb-2\"\r\n                        />\r\n                        <Form.Text className=\"text-muted\">\r\n                          Upload an image of the cutting process. Supported formats: JPG, PNG, GIF (Max: 5MB)\r\n                        </Form.Text>\r\n                        {uploadingImage && (\r\n                          <div className=\"mt-2\">\r\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                            <small>Uploading image...</small>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addDetailRow}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Variant\r\n                </Button>\r\n              </div>\r\n\r\n              {details.map((detail, index) => {\r\n                // Find the selected variant object to set the value in React-Select\r\n                const currentVariant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n\r\n                return (\r\n                  <Card key={index} className=\"mb-3 border-light\">\r\n                    <Card.Body>\r\n                      <Row className=\"align-items-end\">\r\n                        <Col md={4}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading variants...</span>\r\n                              </div>\r\n                            ) : allFabricVariants.length === 0 ? (\r\n                              <div>\r\n                                <Form.Select disabled>\r\n                                  <option>No variants available</option>\r\n                                </Form.Select>\r\n                                <small className=\"text-danger\">\r\n                                  No fabric variants found.\r\n                                </small>\r\n                              </div>\r\n                            ) : (\r\n                              <ColorVariantSelector\r\n                                variants={allFabricVariants}\r\n                                selectedValue={detail.fabric_variant}\r\n                                onSelect={(value) => handleDetailChange(index, 'fabric_variant', value)}\r\n                                placeholder=\"Select Fabric Variant\"\r\n                                disabled={isSubmitting}\r\n                                showFabricName={true}\r\n                              />\r\n                            )}\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              Please select a fabric variant.\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={2}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Yard Usage</strong></Form.Label>\r\n                            <Form.Control\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0.01\"\r\n                              value={detail.yard_usage}\r\n                              onChange={(e) => handleDetailChange(index, 'yard_usage', e.target.value)}\r\n                              required\r\n                              disabled={isSubmitting}\r\n                              isInvalid={!!detailErrors[index]}\r\n                              className={detailErrors[index] ? 'border-danger' : ''}\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {detailErrors[index] || 'Please enter valid yard usage.'}\r\n                            </Form.Control.Feedback>\r\n                            {currentVariant && (\r\n                              <small className=\"text-muted\">\r\n                                Available: {parseFloat(currentVariant.available_yard) + (originalYardUsage[detail.fabric_variant] || 0)} yards\r\n                                (Original: {originalYardUsage[detail.fabric_variant] || 0} yards + Current: {currentVariant.available_yard} yards)\r\n                              </small>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={5}>\r\n                          <Row>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>XS</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.xs}\r\n                                  onChange={(e) => handleDetailChange(index, 'xs', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>S</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.s}\r\n                                  onChange={(e) => handleDetailChange(index, 's', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>M</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.m}\r\n                                  onChange={(e) => handleDetailChange(index, 'm', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>L</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.l}\r\n                                  onChange={(e) => handleDetailChange(index, 'l', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>XL</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.xl}\r\n                                  onChange={(e) => handleDetailChange(index, 'xl', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                          </Row>\r\n                        </Col>\r\n                        <Col md={1} className=\"d-flex align-items-center justify-content-end\">\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            onClick={() => removeDetailRow(index)}\r\n                            disabled={details.length === 1 || isSubmitting}\r\n                            className=\"mt-2\"\r\n                          >\r\n                            <BsTrash />\r\n                          </Button>\r\n                        </Col>\r\n                      </Row>\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-between mb-4\">\r\n                <Button\r\n                  variant=\"outline-primary\"\r\n                  onClick={addDetailRow}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Another Variant\r\n                </Button>\r\n                <div>\r\n                  <Badge bg=\"info\" className=\"me-2 p-2\">\r\n                    Total Pieces: {totalQuantities.total}\r\n                  </Badge>\r\n                  <Badge bg=\"warning\" text=\"dark\" className=\"p-2\">\r\n                    Total Yard Usage: {totalQuantities.yard_usage.toFixed(2)}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-end\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  className=\"me-2\"\r\n                  onClick={() => navigate('/viewcutting')}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  variant=\"primary\"\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Updating...\r\n                    </>\r\n                  ) : (\r\n                    'Update Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default EditCutting;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,CAAAC,oBAAoB,KAAM,oCAAoC,CACrE,OAASC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,GAAG,CAAEC,GAAG,CAAEC,OAAO,CAAEC,KAAK,CAAEC,SAAS,CAAEC,KAAK,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CAC9G,OAASC,UAAU,CAAEC,MAAM,CAAEC,OAAO,CAAEC,cAAc,CAAEC,qBAAqB,CAAEC,WAAW,CAAEC,OAAO,CAAEC,QAAQ,KAAQ,gBAAgB,CACnI,OAASC,WAAW,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,EAAG,CAAC,CAAGhC,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAAiC,QAAQ,CAAGhC,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACiC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACuC,WAAW,CAAEC,cAAc,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACyC,WAAW,CAAEC,cAAc,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC2C,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+C,SAAS,CAAEC,YAAY,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACiD,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACmD,cAAc,CAAEC,iBAAiB,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,CACrC,CAAEuD,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACvE,CAAC,CAEF;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE9D;AACA,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAACkE,eAAe,CAAEC,kBAAkB,CAAC,CAAGnE,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACoE,aAAa,CAAEC,gBAAgB,CAAC,CAAGrE,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACsE,KAAK,CAAEC,QAAQ,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwE,OAAO,CAAEC,UAAU,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0E,YAAY,CAAEC,eAAe,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAG7E,QAAQ,CAAC8E,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGjF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACkF,cAAc,CAAEC,iBAAiB,CAAC,CAAGnF,QAAQ,CAAC,IAAI,CAAC,CAE1D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmF,YAAY,CAAGA,CAAA,GAAM,CACzBP,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACO,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMN,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACAnF,SAAS,CAAC,IAAM,CACdoE,gBAAgB,CAAC,IAAI,CAAC,CACtBkB,OAAO,CAACC,GAAG,CAAC,oCAAoCrD,EAAE,EAAE,CAAC,CAErDjC,KAAK,CAACuF,GAAG,CAAC,qDAAqDtD,EAAE,GAAG,CAAC,CAClEuD,IAAI,CAAEC,GAAG,EAAK,CACb,KAAM,CAAAC,MAAM,CAAGD,GAAG,CAACE,IAAI,CACvBN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEI,MAAM,CAAC,CAC3DT,iBAAiB,CAACS,MAAM,CAAC,CAEzB;AACApD,cAAc,CAACoD,MAAM,CAACE,YAAY,CAAC,CACnCpD,cAAc,CAACkD,MAAM,CAACnD,WAAW,EAAI,EAAE,CAAC,CACxCG,cAAc,CAACgD,MAAM,CAACG,YAAY,EAAI,EAAE,CAAC,CACzCjD,eAAe,CAAC8C,MAAM,CAACI,aAAa,EAAI,EAAE,CAAC,CAC3C9C,eAAe,CAAC0C,MAAM,CAACI,aAAa,EAAI,EAAE,CAAC,CAE3C;AACA,GAAIJ,MAAM,CAACvC,OAAO,EAAIuC,MAAM,CAACvC,OAAO,CAAC4C,MAAM,CAAG,CAAC,CAAE,CAC/CV,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEI,MAAM,CAACvC,OAAO,CAAC,CAE3D;AACA,KAAM,CAAA6C,YAAY,CAAG,CAAC,CAAC,CACvBN,MAAM,CAACvC,OAAO,CAAC8C,OAAO,CAACC,MAAM,EAAI,CAC/BF,YAAY,CAACE,MAAM,CAAC7C,cAAc,CAAC,CAAG8C,UAAU,CAACD,MAAM,CAAC5C,UAAU,CAAC,CACrE,CAAC,CAAC,CACF+B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEU,YAAY,CAAC,CACrDnC,oBAAoB,CAACmC,YAAY,CAAC,CAElC;AACAjC,eAAe,CAACqC,KAAK,CAACV,MAAM,CAACvC,OAAO,CAAC4C,MAAM,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,CAAC,CAEtD;AACAjD,UAAU,CAACsC,MAAM,CAACvC,OAAO,CAACmD,GAAG,CAACJ,MAAM,GAAK,CACvCjE,EAAE,CAAEiE,MAAM,CAACjE,EAAE,CAAE;AACfoB,cAAc,CAAE6C,MAAM,CAAC7C,cAAc,CACrCC,UAAU,CAAE4C,MAAM,CAAC5C,UAAU,CAC7BC,EAAE,CAAE2C,MAAM,CAAC3C,EAAE,EAAI,CAAC,CAClBC,CAAC,CAAE0C,MAAM,CAAC1C,CAAC,EAAI,CAAC,CAChBC,CAAC,CAAEyC,MAAM,CAACzC,CAAC,EAAI,CAAC,CAChBC,CAAC,CAAEwC,MAAM,CAACxC,CAAC,EAAI,CAAC,CAChBC,EAAE,CAAEuC,MAAM,CAACvC,EAAE,EAAI,CACnB,CAAC,CAAC,CAAC,CAAC,CACN,CAEAQ,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAC,CACDoC,KAAK,CAAEC,GAAG,EAAK,CACdnB,OAAO,CAACjB,KAAK,CAAC,gCAAgC,CAAEoC,GAAG,CAAC,CACpDnC,QAAQ,CAAC,kDAAkD,CAAC,CAC5DF,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAC,CACN,CAAC,CAAE,CAAClC,EAAE,CAAC,CAAC,CAER;AACAlC,SAAS,CAAC,IAAM,CACdkE,kBAAkB,CAAC,IAAI,CAAC,CACxBjE,KAAK,CAACuF,GAAG,CAAC,4CAA4C,CAAC,CACpDC,IAAI,CAAEC,GAAG,EAAK,CACbJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEG,GAAG,CAACE,IAAI,CAAC,CAC9DvD,oBAAoB,CAACqD,GAAG,CAACE,IAAI,CAAC,CAC9B1B,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACDsC,KAAK,CAAEC,GAAG,EAAK,CACdnB,OAAO,CAACjB,KAAK,CAAC,iCAAiC,CAAEoC,GAAG,CAAC,CACrDnC,QAAQ,CAAC,mDAAmD,CAAC,CAC7DJ,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAIN;AACAlE,SAAS,CAAC,IAAM,CACd,GAAIoC,iBAAiB,CAAC4D,MAAM,CAAG,CAAC,EAAI5C,OAAO,CAAC4C,MAAM,CAAG,CAAC,CAAE,CACtD;AACA,KAAM,CAAAU,eAAe,CAAG,CAAC,GAAG3C,YAAY,CAAC,CAEzC;AACAX,OAAO,CAAC8C,OAAO,CAAC,CAACC,MAAM,CAAEQ,KAAK,GAAK,CACjC,GAAIR,MAAM,CAAC7C,cAAc,EAAI6C,MAAM,CAAC5C,UAAU,CAAE,CAC9C,KAAM,CAAAqD,SAAS,CAAGT,MAAM,CAAC7C,cAAc,CAEvC;AACA,KAAM,CAAAuD,OAAO,CAAGzE,iBAAiB,CAAC0E,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC7E,EAAE,GAAK8E,QAAQ,CAACJ,SAAS,CAAC,CAAC,CACzE,GAAI,CAACC,OAAO,CAAE,OAEd;AACA,KAAM,CAAAI,QAAQ,CAAGpD,iBAAiB,CAAC+C,SAAS,CAAC,EAAI,CAAC,CAElD;AACA,KAAM,CAAAM,UAAU,CAAGd,UAAU,CAACS,OAAO,CAACM,cAAc,CAAC,CAAGF,QAAQ,CAEhE;AACA,GAAIb,UAAU,CAACD,MAAM,CAAC5C,UAAU,CAAC,CAAG2D,UAAU,CAAE,CAC9CR,eAAe,CAACC,KAAK,CAAC,CAAG,6CAA6CO,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CACrG,CAAC,IAAM,IAAIhB,UAAU,CAACD,MAAM,CAAC5C,UAAU,CAAC,EAAI,CAAC,CAAE,CAC7CmD,eAAe,CAACC,KAAK,CAAC,CAAG,mCAAmC,CAC9D,CAAC,IAAM,CACLD,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC7B,CACF,CACF,CAAC,CAAC,CAEF;AACA3C,eAAe,CAAC0C,eAAe,CAAC,CAClC,CACF,CAAC,CAAE,CAACtE,iBAAiB,CAAEgB,OAAO,CAAES,iBAAiB,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAwD,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAAC,IAAI,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIF,IAAI,CAAE,CACRxE,YAAY,CAACwE,IAAI,CAAC,CAClB;AACA,KAAM,CAAAG,UAAU,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAC5CtE,eAAe,CAACyE,UAAU,CAAC,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAAC/E,SAAS,CAAE,MAAO,CAAAF,YAAY,CAAE;AAErC,GAAI,CACFO,iBAAiB,CAAC,IAAI,CAAC,CACvB,KAAM,CAAA2E,QAAQ,CAAG,KAAM,CAAApG,WAAW,CAACoB,SAAS,CAAE,gBAAgB,CAAC,CAC/DD,eAAe,CAACiF,QAAQ,CAAC,CACzB,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOzD,KAAK,CAAE,CACdiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,KAAM,IAAI,CAAA0D,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CAAC,OAAS,CACR5E,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAA6E,WAAW,CAAGA,CAAA,GAAM,CACxBjF,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,EAAE,CAAC,CACnBJ,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAoF,YAAY,CAAGA,CAAA,GAAM,CACzB5E,UAAU,CAAC,CAAC,GAAGD,OAAO,CAAE,CAAEE,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAC,CAAC,CAChGI,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAE,EAAE,CAAC,CAAC,CAAE;AAC1C,CAAC,CAED;AACA,KAAM,CAAAmE,eAAe,CAAIvB,KAAK,EAAK,CACjC,KAAM,CAAAwB,UAAU,CAAG/E,OAAO,CAACgF,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAK3B,KAAK,CAAC,CACxDtD,UAAU,CAAC8E,UAAU,CAAC,CAEtB;AACA,KAAM,CAAAzB,eAAe,CAAG3C,YAAY,CAACqE,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAK3B,KAAK,CAAC,CAClE3C,eAAe,CAAC0C,eAAe,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAA6B,kBAAkB,CAAGA,CAAC5B,KAAK,CAAE6B,KAAK,CAAEC,KAAK,GAAK,CAClD,KAAM,CAAAN,UAAU,CAAG,CAAC,GAAG/E,OAAO,CAAC,CAC/B+E,UAAU,CAACxB,KAAK,CAAC,CAAC6B,KAAK,CAAC,CAAGC,KAAK,CAChCpF,UAAU,CAAC8E,UAAU,CAAC,CAEtB;AACA,GAAIK,KAAK,GAAK,YAAY,CAAE,CAC1BE,iBAAiB,CAAC/B,KAAK,CAAE8B,KAAK,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAAC/B,KAAK,CAAEgC,YAAY,GAAK,CACjD,KAAM,CAAAjC,eAAe,CAAG,CAAC,GAAG3C,YAAY,CAAC,CACzC,KAAM,CAAAoC,MAAM,CAAG/C,OAAO,CAACuD,KAAK,CAAC,CAC7B,KAAM,CAAAC,SAAS,CAAGT,MAAM,CAAC7C,cAAc,CAEvC;AACA,GAAI,CAACsD,SAAS,CAAE,CACdF,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC3B3C,eAAe,CAAC0C,eAAe,CAAC,CAChC,OACF,CAEA;AACA,KAAM,CAAAG,OAAO,CAAGzE,iBAAiB,CAAC0E,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC7E,EAAE,GAAK8E,QAAQ,CAACJ,SAAS,CAAC,CAAC,CACzE,GAAI,CAACC,OAAO,CAAE,CACZH,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC3B3C,eAAe,CAAC0C,eAAe,CAAC,CAChC,OACF,CAEA;AACA,KAAM,CAAAO,QAAQ,CAAGpD,iBAAiB,CAAC+C,SAAS,CAAC,EAAI,CAAC,CAElD;AACA;AACA,KAAM,CAAAM,UAAU,CAAGd,UAAU,CAACS,OAAO,CAACM,cAAc,CAAC,CAAGF,QAAQ,CAEhE;AACA,GAAIb,UAAU,CAACuC,YAAY,CAAC,CAAGzB,UAAU,CAAE,CACzCR,eAAe,CAACC,KAAK,CAAC,CAAG,6CAA6CO,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CACrG,CAAC,IAAM,IAAIhB,UAAU,CAACuC,YAAY,CAAC,EAAI,CAAC,CAAE,CACxCjC,eAAe,CAACC,KAAK,CAAC,CAAG,mCAAmC,CAC9D,CAAC,IAAM,CACLD,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC7B,CAEA3C,eAAe,CAAC0C,eAAe,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAkC,YAAY,CAAG,KAAO,CAAAtB,CAAC,EAAK,CAChCA,CAAC,CAACuB,cAAc,CAAC,CAAC,CAElB;AACA,KAAM,CAAAC,IAAI,CAAGxB,CAAC,CAACyB,aAAa,CAC5B,GAAID,IAAI,CAACE,aAAa,CAAC,CAAC,GAAK,KAAK,CAAE,CAClC1B,CAAC,CAAC2B,eAAe,CAAC,CAAC,CACnBjE,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEA;AACA,KAAM,CAAAkE,eAAe,CAAG9F,OAAO,CAAC+F,IAAI,CAAChD,MAAM,EAAIA,MAAM,CAAC7C,cAAc,CAAC,CACrE,GAAI,CAAC4F,eAAe,CAAE,CACpB5E,QAAQ,CAAC,qEAAqE,CAAC,CAC/E,OACF,CAEA;AACA,GAAI,CAAA8E,kBAAkB,CAAG,KAAK,CAC9BhG,OAAO,CAAC8C,OAAO,CAAC,CAACC,MAAM,CAAEQ,KAAK,GAAK,CACjC+B,iBAAiB,CAAC/B,KAAK,CAAER,MAAM,CAAC5C,UAAU,CAAC,CAC3C,GAAIQ,YAAY,CAAC4C,KAAK,CAAC,CAAE,CACvByC,kBAAkB,CAAG,IAAI,CAC3B,CACF,CAAC,CAAC,CAEF;AACA,GAAIA,kBAAkB,CAAE,CACtB9E,QAAQ,CAAC,qDAAqD,CAAC,CAC/D,OACF,CAEAU,YAAY,CAAC,IAAI,CAAC,CAClBN,eAAe,CAAC,IAAI,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF;AACA,GAAI,CAAA6E,aAAa,CAAGzG,YAAY,CAChC,GAAIE,SAAS,CAAE,CACbuG,aAAa,CAAG,KAAM,CAAAxB,kBAAkB,CAAC,CAAC,CAC5C,CAEA,KAAM,CAAAyB,OAAO,CAAG,CACdzD,YAAY,CAAEvD,WAAW,CACzBE,WAAW,CAAEA,WAAW,CACxBsD,YAAY,CAAEpD,WAAW,CACzBqD,aAAa,CAAEsD,aAAa,CAC5BjG,OAAO,CAAEA,OACX,CAAC,CAED,KAAM,CAAAmG,QAAQ,CAAG,KAAM,CAAAtJ,KAAK,CAACuJ,GAAG,CAAC,qDAAqDtH,EAAE,GAAG,CAAEoH,OAAO,CAAC,CACrG9E,UAAU,CAAC,sCAAsC,CAAC,CAElD;AACAiF,UAAU,CAAC,IAAM,CACftH,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOsE,GAAG,CAAE,CACZnB,OAAO,CAACjB,KAAK,CAAC,gCAAgC,CAAEoC,GAAG,CAAC,CACpD,GAAIA,GAAG,CAAC8C,QAAQ,EAAI9C,GAAG,CAAC8C,QAAQ,CAAC3D,IAAI,CAAE,CACrC;AACA,KAAM,CAAA8D,YAAY,CAAG,MAAO,CAAAjD,GAAG,CAAC8C,QAAQ,CAAC3D,IAAI,GAAK,QAAQ,CACtDa,GAAG,CAAC8C,QAAQ,CAAC3D,IAAI,CACjB,4DAA4D,CAChEtB,QAAQ,CAACoF,YAAY,CAAC,CACxB,CAAC,IAAM,CACLpF,QAAQ,CAAC,oDAAoD,CAAC,CAChE,CACF,CAAC,OAAS,CACRI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAAiF,YAAY,CAAGC,IAAA,MAAC,CAAEhE,IAAI,CAAEiE,QAAQ,CAAEC,UAAW,CAAC,CAAAF,IAAA,oBAClD9H,KAAA,QACEiI,GAAG,CAAEF,QAAS,IACVC,UAAU,CACdE,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,OAAO,CAAE,KAAM,CAAE,CAAAC,QAAA,eAEjExI,IAAA,QACEoI,KAAK,CAAE,CACLK,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,eAAe,CAAE3E,IAAI,CAAC4E,KAAK,CAC3BC,WAAW,CAAE,CAAC,CACdC,MAAM,CAAE,gBACV,CAAE,CACH,CAAC,cACF9I,IAAA,SAAAwI,QAAA,CAAOxE,IAAI,CAAC+E,KAAK,CAAO,CAAC,EACtB,CAAC,EACP,CAED;AACA,KAAM,CAAAC,eAAe,CAAGxH,OAAO,CAACyH,MAAM,CACpC,CAACC,GAAG,CAAE3E,MAAM,GAAK,CACf2E,GAAG,CAACtH,EAAE,EAAIwD,QAAQ,CAACb,MAAM,CAAC3C,EAAE,CAAC,EAAI,CAAC,CAClCsH,GAAG,CAACrH,CAAC,EAAIuD,QAAQ,CAACb,MAAM,CAAC1C,CAAC,CAAC,EAAI,CAAC,CAChCqH,GAAG,CAACpH,CAAC,EAAIsD,QAAQ,CAACb,MAAM,CAACzC,CAAC,CAAC,EAAI,CAAC,CAChCoH,GAAG,CAACnH,CAAC,EAAIqD,QAAQ,CAACb,MAAM,CAACxC,CAAC,CAAC,EAAI,CAAC,CAChCmH,GAAG,CAAClH,EAAE,EAAIoD,QAAQ,CAACb,MAAM,CAACvC,EAAE,CAAC,EAAI,CAAC,CAClCkH,GAAG,CAACC,KAAK,EAAI,CAAC/D,QAAQ,CAACb,MAAM,CAAC3C,EAAE,CAAC,EAAI,CAAC,GACzBwD,QAAQ,CAACb,MAAM,CAAC1C,CAAC,CAAC,EAAI,CAAC,CAAC,EACxBuD,QAAQ,CAACb,MAAM,CAACzC,CAAC,CAAC,EAAI,CAAC,CAAC,EACxBsD,QAAQ,CAACb,MAAM,CAACxC,CAAC,CAAC,EAAI,CAAC,CAAC,EACxBqD,QAAQ,CAACb,MAAM,CAACvC,EAAE,CAAC,EAAI,CAAC,CAAC,CACtCkH,GAAG,CAACvH,UAAU,EAAI6C,UAAU,CAACD,MAAM,CAAC5C,UAAU,CAAC,EAAI,CAAC,CACpD,MAAO,CAAAuH,GAAG,CACZ,CAAC,CACD,CAAEtH,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEmH,KAAK,CAAE,CAAC,CAAExH,UAAU,CAAE,CAAE,CAC5D,CAAC,CAED,GAAIY,aAAa,CAAE,CACjB,mBACErC,KAAA,CAAAE,SAAA,EAAAoI,QAAA,eACExI,IAAA,CAACvB,eAAe,GAAE,CAAC,cACnBuB,IAAA,CAACd,SAAS,EAACkK,KAAK,MACdhB,KAAK,CAAE,CACLiB,UAAU,CAAEtG,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5C0F,KAAK,CAAE,eAAe1F,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDuG,UAAU,CAAE,eAAe,CAC3Bf,OAAO,CAAE,MACX,CAAE,CAAAC,QAAA,cAEFtI,KAAA,QAAKqJ,SAAS,CAAC,kBAAkB,CAAAf,QAAA,eAC/BxI,IAAA,CAAChB,OAAO,EAACwK,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAACxE,OAAO,CAAC,SAAS,CAAAuD,QAAA,cACzDxI,IAAA,SAAMuJ,SAAS,CAAC,iBAAiB,CAAAf,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,cACVxI,IAAA,MAAGuJ,SAAS,CAAC,MAAM,CAAAf,QAAA,CAAC,2BAAyB,CAAG,CAAC,EAC9C,CAAC,CACG,CAAC,EACZ,CAAC,CAEP,CAEA,mBACEtI,KAAA,CAAAE,SAAA,EAAAoI,QAAA,eACExI,IAAA,CAACvB,eAAe,GAAE,CAAC,cACnByB,KAAA,QACEkI,KAAK,CAAE,CACLiB,UAAU,CAAEtG,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5C0F,KAAK,CAAE,eAAe1F,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDuG,UAAU,CAAE,eAAe,CAC3Bf,OAAO,CAAE,MACX,CAAE,CAAAC,QAAA,eAEFtI,KAAA,QAAKqJ,SAAS,CAAC,gCAAgC,CAAAf,QAAA,eAC7CtI,KAAA,CAACrB,MAAM,EACLoG,OAAO,CAAC,mBAAmB,CAC3BsE,SAAS,CAAC,MAAM,CAChBG,OAAO,CAAEA,CAAA,GAAMnJ,QAAQ,CAAC,cAAc,CAAE,CAAAiI,QAAA,eAExCxI,IAAA,CAACL,WAAW,EAAC4J,SAAS,CAAC,MAAM,CAAE,CAAC,QAClC,EAAQ,CAAC,cACTrJ,KAAA,OAAIqJ,SAAS,CAAC,MAAM,CAAAf,QAAA,eAClBxI,IAAA,CAACV,UAAU,EAACiK,SAAS,CAAC,MAAM,CAAE,CAAC,sBAEjC,EAAI,CAAC,EACF,CAAC,CAEL5G,OAAO,eACNzC,KAAA,CAACjB,KAAK,EAACgG,OAAO,CAAC,SAAS,CAACsE,SAAS,CAAC,2BAA2B,CAAAf,QAAA,eAC5DxI,IAAA,CAACP,cAAc,EAAC8J,SAAS,CAAC,MAAM,CAACI,IAAI,CAAE,EAAG,CAAE,CAAC,CAC5ChH,OAAO,EACH,CACR,CAEAF,KAAK,eACJvC,KAAA,CAACjB,KAAK,EAACgG,OAAO,CAAC,QAAQ,CAACsE,SAAS,CAAC,2BAA2B,CAAAf,QAAA,eAC3DxI,IAAA,CAACN,qBAAqB,EAAC6J,SAAS,CAAC,MAAM,CAACI,IAAI,CAAE,EAAG,CAAE,CAAC,CACnDlH,KAAK,EACD,CACR,cAEDzC,IAAA,CAACrB,IAAI,EAAC4K,SAAS,CAAC,gBAAgB,CAACnB,KAAK,CAAE,CAAEO,eAAe,CAAE,SAAS,CAAEiB,YAAY,CAAE,MAAO,CAAE,CAAApB,QAAA,cAC3FxI,IAAA,CAACrB,IAAI,CAACkL,IAAI,EAAArB,QAAA,cACRtI,KAAA,CAACtB,IAAI,EAACkL,UAAU,MAAC3G,SAAS,CAAEA,SAAU,CAAC4G,QAAQ,CAAE/C,YAAa,CAAAwB,QAAA,eAC5DxI,IAAA,CAAClB,GAAG,EAAA0J,QAAA,cACFxI,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtDxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAE/F,WAAY,CACnBuJ,QAAQ,CAAG3E,CAAC,EAAK3E,cAAc,CAAC2E,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CAChDyD,WAAW,CAAC,oBAAoB,CAChCC,QAAQ,MACT,CAAC,cACFvK,IAAA,CAACpB,IAAI,CAACuL,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAAC,gCAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,CACH,CAAC,cAENtI,KAAA,CAACpB,GAAG,EAAA0J,QAAA,eACFxI,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtDxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAEnG,WAAY,CACnB2J,QAAQ,CAAG3E,CAAC,EAAK/E,cAAc,CAAC+E,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CAChD0D,QAAQ,MACT,CAAC,cACFvK,IAAA,CAACpB,IAAI,CAACuL,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAAC,+BAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,cACNxI,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAY,CAAC,cACrDxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXM,EAAE,CAAC,UAAU,CACbC,IAAI,CAAE,CAAE,CACR7D,KAAK,CAAEjG,WAAY,CACnByJ,QAAQ,CAAG3E,CAAC,EAAK7E,cAAc,CAAC6E,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CAChDyD,WAAW,CAAC,4CAA4C,CACzD,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,cAGNtK,IAAA,CAAClB,GAAG,EAAA0J,QAAA,cACFxI,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,EAAG,CAAAxB,QAAA,cACVtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,eAAa,CAAQ,CAAC,CAAY,CAAC,cACvDtI,KAAA,QAAKqJ,SAAS,CAAC,gCAAgC,CAAAf,QAAA,eAE7CxI,IAAA,QAAAwI,QAAA,CACGpH,YAAY,cACXlB,KAAA,QAAKqJ,SAAS,CAAC,mBAAmB,CAAAf,QAAA,eAChCxI,IAAA,CAACX,KAAK,EACJsL,GAAG,CAAEvJ,YAAa,CAClBwJ,GAAG,CAAC,iBAAiB,CACrBxC,KAAK,CAAE,CACLK,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfmC,SAAS,CAAE,OAAO,CAClB/B,MAAM,CAAE,mBAAmB,CAC3Bc,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,cACF5J,IAAA,CAACnB,MAAM,EACLoG,OAAO,CAAC,QAAQ,CAChB0E,IAAI,CAAC,IAAI,CACTJ,SAAS,CAAC,mCAAmC,CAC7CG,OAAO,CAAEtD,WAAY,CACrB0E,QAAQ,CAAEjI,YAAY,EAAIvB,cAAe,CAAAkH,QAAA,cAEzCxI,IAAA,CAACR,OAAO,GAAE,CAAC,CACL,CAAC,EACN,CAAC,cAENQ,IAAA,QACEuJ,SAAS,CAAC,yFAAyF,CACnGnB,KAAK,CAAE,CACLK,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfkB,YAAY,CAAE,KAAK,CACnBhB,KAAK,CAAE,SACT,CAAE,CAAAJ,QAAA,cAEFtI,KAAA,QAAKqJ,SAAS,CAAC,aAAa,CAAAf,QAAA,eAC1BxI,IAAA,CAACJ,OAAO,EAAC+J,IAAI,CAAE,EAAG,CAACJ,SAAS,CAAC,MAAM,CAAE,CAAC,cACtCvJ,IAAA,QAAKuJ,SAAS,CAAC,OAAO,CAAAf,QAAA,CAAC,UAAQ,CAAK,CAAC,EAClC,CAAC,CACH,CACN,CACE,CAAC,cAGNtI,KAAA,QAAKqJ,SAAS,CAAC,aAAa,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,MAAM,CACXW,MAAM,CAAC,SAAS,CAChBV,QAAQ,CAAE5E,iBAAkB,CAC5BqF,QAAQ,CAAEjI,YAAY,EAAIvB,cAAe,CACzCiI,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvJ,IAAA,CAACpB,IAAI,CAACoM,IAAI,EAACzB,SAAS,CAAC,YAAY,CAAAf,QAAA,CAAC,qFAElC,CAAW,CAAC,CACXlH,cAAc,eACbpB,KAAA,QAAKqJ,SAAS,CAAC,MAAM,CAAAf,QAAA,eACnBxI,IAAA,CAAChB,OAAO,EAACwK,SAAS,CAAC,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACJ,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDvJ,IAAA,UAAAwI,QAAA,CAAO,oBAAkB,CAAO,CAAC,EAC9B,CACN,EACE,CAAC,EACH,CAAC,EACI,CAAC,CACV,CAAC,CACH,CAAC,cAENtI,KAAA,QAAKqJ,SAAS,CAAC,gFAAgF,CAAAf,QAAA,eAC7FxI,IAAA,OAAIuJ,SAAS,CAAC,MAAM,CAAAf,QAAA,CAAC,gBAAc,CAAI,CAAC,cACxCtI,KAAA,CAACrB,MAAM,EACLoG,OAAO,CAAC,SAAS,CACjB0E,IAAI,CAAC,IAAI,CACTD,OAAO,CAAErD,YAAa,CACtByE,QAAQ,CAAEjI,YAAa,CAAA2F,QAAA,eAEvBxI,IAAA,CAACT,MAAM,EAACgK,SAAS,CAAC,MAAM,CAAE,CAAC,sBAC7B,EAAQ,CAAC,EACN,CAAC,CAEL/H,OAAO,CAACmD,GAAG,CAAC,CAACJ,MAAM,CAAEQ,KAAK,GAAK,CAC9B;AACA,KAAM,CAAAkG,cAAc,CAAGzK,iBAAiB,CAAC0E,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC7E,EAAE,GAAKiE,MAAM,CAAC7C,cAAc,CAAC,CAElF,mBACE1B,IAAA,CAACrB,IAAI,EAAa4K,SAAS,CAAC,mBAAmB,CAAAf,QAAA,cAC7CxI,IAAA,CAACrB,IAAI,CAACkL,IAAI,EAAArB,QAAA,cACRtI,KAAA,CAACpB,GAAG,EAACyK,SAAS,CAAC,iBAAiB,CAAAf,QAAA,eAC9BxI,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAY,CAAC,CAC/DnG,eAAe,cACdnC,KAAA,QAAKqJ,SAAS,CAAC,2BAA2B,CAAAf,QAAA,eACxCxI,IAAA,CAAChB,OAAO,EAACwK,SAAS,CAAC,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACJ,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDvJ,IAAA,SAAAwI,QAAA,CAAM,qBAAmB,CAAM,CAAC,EAC7B,CAAC,CACJhI,iBAAiB,CAAC4D,MAAM,GAAK,CAAC,cAChClE,KAAA,QAAAsI,QAAA,eACExI,IAAA,CAACpB,IAAI,CAACJ,MAAM,EAACsM,QAAQ,MAAAtC,QAAA,cACnBxI,IAAA,WAAAwI,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,CAC3B,CAAC,cACdxI,IAAA,UAAOuJ,SAAS,CAAC,aAAa,CAAAf,QAAA,CAAC,2BAE/B,CAAO,CAAC,EACL,CAAC,cAENxI,IAAA,CAACtB,oBAAoB,EACnBwM,QAAQ,CAAE1K,iBAAkB,CAC5B2K,aAAa,CAAE5G,MAAM,CAAC7C,cAAe,CACrC0J,QAAQ,CAAGvE,KAAK,EAAKF,kBAAkB,CAAC5B,KAAK,CAAE,gBAAgB,CAAE8B,KAAK,CAAE,CACxEyD,WAAW,CAAC,uBAAuB,CACnCQ,QAAQ,CAAEjI,YAAa,CACvBwI,cAAc,CAAE,IAAK,CACtB,CACF,cACDrL,IAAA,CAACpB,IAAI,CAACuL,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAAC,iCAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,cACNxI,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,cACpDxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbkB,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,MAAM,CACV1E,KAAK,CAAEtC,MAAM,CAAC5C,UAAW,CACzB0I,QAAQ,CAAG3E,CAAC,EAAKiB,kBAAkB,CAAC5B,KAAK,CAAE,YAAY,CAAEW,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CACzE0D,QAAQ,MACRO,QAAQ,CAAEjI,YAAa,CACvB2I,SAAS,CAAE,CAAC,CAACrJ,YAAY,CAAC4C,KAAK,CAAE,CACjCwE,SAAS,CAAEpH,YAAY,CAAC4C,KAAK,CAAC,CAAG,eAAe,CAAG,EAAG,CACvD,CAAC,cACF/E,IAAA,CAACpB,IAAI,CAACuL,OAAO,CAACK,QAAQ,EAACJ,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAClCrG,YAAY,CAAC4C,KAAK,CAAC,EAAI,gCAAgC,CACnC,CAAC,CACvBkG,cAAc,eACb/K,KAAA,UAAOqJ,SAAS,CAAC,YAAY,CAAAf,QAAA,EAAC,aACjB,CAAChE,UAAU,CAACyG,cAAc,CAAC1F,cAAc,CAAC,EAAItD,iBAAiB,CAACsC,MAAM,CAAC7C,cAAc,CAAC,EAAI,CAAC,CAAC,CAAC,oBAC7F,CAACO,iBAAiB,CAACsC,MAAM,CAAC7C,cAAc,CAAC,EAAI,CAAC,CAAC,oBAAkB,CAACuJ,cAAc,CAAC1F,cAAc,CAAC,SAC7G,EAAO,CACR,EACS,CAAC,CACV,CAAC,cACNvF,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTtI,KAAA,CAACpB,GAAG,EAAA0J,QAAA,eACFxI,IAAA,CAACjB,GAAG,EAAAyJ,QAAA,cACFtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,IAAE,CAAQ,CAAC,CAAY,CAAC,cAC5CxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbmB,GAAG,CAAC,GAAG,CACP1E,KAAK,CAAEtC,MAAM,CAAC3C,EAAG,CACjByI,QAAQ,CAAG3E,CAAC,EAAKiB,kBAAkB,CAAC5B,KAAK,CAAE,IAAI,CAAEW,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CACjEiE,QAAQ,CAAEjI,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACN7C,IAAA,CAACjB,GAAG,EAAAyJ,QAAA,cACFtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,GAAC,CAAQ,CAAC,CAAY,CAAC,cAC3CxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbmB,GAAG,CAAC,GAAG,CACP1E,KAAK,CAAEtC,MAAM,CAAC1C,CAAE,CAChBwI,QAAQ,CAAG3E,CAAC,EAAKiB,kBAAkB,CAAC5B,KAAK,CAAE,GAAG,CAAEW,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CAChEiE,QAAQ,CAAEjI,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACN7C,IAAA,CAACjB,GAAG,EAAAyJ,QAAA,cACFtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,GAAC,CAAQ,CAAC,CAAY,CAAC,cAC3CxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbmB,GAAG,CAAC,GAAG,CACP1E,KAAK,CAAEtC,MAAM,CAACzC,CAAE,CAChBuI,QAAQ,CAAG3E,CAAC,EAAKiB,kBAAkB,CAAC5B,KAAK,CAAE,GAAG,CAAEW,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CAChEiE,QAAQ,CAAEjI,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACN7C,IAAA,CAACjB,GAAG,EAAAyJ,QAAA,cACFtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,GAAC,CAAQ,CAAC,CAAY,CAAC,cAC3CxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbmB,GAAG,CAAC,GAAG,CACP1E,KAAK,CAAEtC,MAAM,CAACxC,CAAE,CAChBsI,QAAQ,CAAG3E,CAAC,EAAKiB,kBAAkB,CAAC5B,KAAK,CAAE,GAAG,CAAEW,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CAChEiE,QAAQ,CAAEjI,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACN7C,IAAA,CAACjB,GAAG,EAAAyJ,QAAA,cACFtI,KAAA,CAACtB,IAAI,CAACqL,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BxI,IAAA,CAACpB,IAAI,CAACsL,KAAK,EAAA1B,QAAA,cAACxI,IAAA,WAAAwI,QAAA,CAAQ,IAAE,CAAQ,CAAC,CAAY,CAAC,cAC5CxI,IAAA,CAACpB,IAAI,CAACuL,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbmB,GAAG,CAAC,GAAG,CACP1E,KAAK,CAAEtC,MAAM,CAACvC,EAAG,CACjBqI,QAAQ,CAAG3E,CAAC,EAAKiB,kBAAkB,CAAC5B,KAAK,CAAE,IAAI,CAAEW,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE,CACjEiE,QAAQ,CAAEjI,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,CACH,CAAC,cACN7C,IAAA,CAACjB,GAAG,EAACiL,EAAE,CAAE,CAAE,CAACT,SAAS,CAAC,+CAA+C,CAAAf,QAAA,cACnExI,IAAA,CAACnB,MAAM,EACLoG,OAAO,CAAC,gBAAgB,CACxByE,OAAO,CAAEA,CAAA,GAAMpD,eAAe,CAACvB,KAAK,CAAE,CACtC+F,QAAQ,CAAEtJ,OAAO,CAAC4C,MAAM,GAAK,CAAC,EAAIvB,YAAa,CAC/C0G,SAAS,CAAC,MAAM,CAAAf,QAAA,cAEhBxI,IAAA,CAACR,OAAO,GAAE,CAAC,CACL,CAAC,CACN,CAAC,EACH,CAAC,CACG,CAAC,EAvIHuF,KAwIL,CAAC,CAEX,CAAC,CAAC,cAEF7E,KAAA,QAAKqJ,SAAS,CAAC,qCAAqC,CAAAf,QAAA,eAClDtI,KAAA,CAACrB,MAAM,EACLoG,OAAO,CAAC,iBAAiB,CACzByE,OAAO,CAAErD,YAAa,CACtByE,QAAQ,CAAEjI,YAAa,CAAA2F,QAAA,eAEvBxI,IAAA,CAACT,MAAM,EAACgK,SAAS,CAAC,MAAM,CAAE,CAAC,uBAC7B,EAAQ,CAAC,cACTrJ,KAAA,QAAAsI,QAAA,eACEtI,KAAA,CAACf,KAAK,EAACsM,EAAE,CAAC,MAAM,CAAClC,SAAS,CAAC,UAAU,CAAAf,QAAA,EAAC,gBACtB,CAACQ,eAAe,CAACG,KAAK,EAC/B,CAAC,cACRjJ,KAAA,CAACf,KAAK,EAACsM,EAAE,CAAC,SAAS,CAACC,IAAI,CAAC,MAAM,CAACnC,SAAS,CAAC,KAAK,CAAAf,QAAA,EAAC,oBAC5B,CAACQ,eAAe,CAACrH,UAAU,CAAC6D,OAAO,CAAC,CAAC,CAAC,EACnD,CAAC,EACL,CAAC,EACH,CAAC,cAENtF,KAAA,QAAKqJ,SAAS,CAAC,4BAA4B,CAAAf,QAAA,eACzCxI,IAAA,CAACnB,MAAM,EACLoG,OAAO,CAAC,WAAW,CACnBsE,SAAS,CAAC,MAAM,CAChBG,OAAO,CAAEA,CAAA,GAAMnJ,QAAQ,CAAC,cAAc,CAAE,CACxCuK,QAAQ,CAAEjI,YAAa,CAAA2F,QAAA,CACxB,QAED,CAAQ,CAAC,cACTxI,IAAA,CAACnB,MAAM,EACLoG,OAAO,CAAC,SAAS,CACjBmF,IAAI,CAAC,QAAQ,CACbU,QAAQ,CAAEjI,YAAa,CAAA2F,QAAA,CAEtB3F,YAAY,cACX3C,KAAA,CAAAE,SAAA,EAAAoI,QAAA,eACExI,IAAA,CAAChB,OAAO,EAACyL,EAAE,CAAC,MAAM,CAACjB,SAAS,CAAC,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACF,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,cAEtG,EAAE,CAAC,CAEH,uBACD,CACK,CAAC,EACN,CAAC,EACF,CAAC,CACE,CAAC,CACR,CAAC,EACJ,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAAlJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}