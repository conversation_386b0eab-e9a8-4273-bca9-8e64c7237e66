{"name": "@electron/rebuild", "version": "4.0.1", "description": "Electron supporting package to rebuild native node modules against the currently installed electron", "type": "module", "exports": "./lib/main.js", "typings": "lib/main.d.ts", "scripts": {"build": "tsc", "build:docs": "typedoc src/main.ts", "coverage": "npm run prewarm-headers && nyc npm run spec", "coverage:report": "nyc report --reporter=text-lcov > coverage.lcov", "watch": "tsc -w", "prepare": "npm run build", "lint": "eslint --ext .ts .", "spec": "tsc && mocha", "test": "npm run prewarm-headers && npm run lint && npm run spec", "prewarm-headers": "node-gyp install --ensure"}, "bin": {"electron-rebuild": "lib/cli.js"}, "files": ["lib"], "repository": {"type": "git", "url": "git+https://github.com/electron/rebuild.git"}, "keywords": ["electron"], "authors": ["<PERSON><PERSON> <<EMAIL>>", "Electron Community"], "license": "MIT", "bugs": {"url": "https://github.com/electron/rebuild/issues"}, "homepage": "https://github.com/electron/rebuild", "engines": {"node": ">=22.12.0"}, "publishConfig": {"provenance": true}, "dependencies": {"@malept/cross-spawn-promise": "^2.0.0", "chalk": "^4.0.0", "debug": "^4.1.1", "detect-libc": "^2.0.1", "got": "^11.7.0", "graceful-fs": "^4.2.11", "node-abi": "^4.2.0", "node-api-version": "^0.2.1", "node-gyp": "^11.2.0", "ora": "^5.1.0", "read-binary-file-arch": "^1.0.6", "semver": "^7.3.5", "tar": "^6.0.5", "yargs": "^17.0.1"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@tsconfig/node22": "^22.0.0", "@types/chai": "^4.2.12", "@types/chai-as-promised": "^7.1.3", "@types/debug": "^4.1.5", "@types/graceful-fs": "^4.1.9", "@types/mocha": "^10.0.10", "@types/node": "~22.10.7", "@types/node-abi": "^3.0.0", "@types/semver": "^7.3.9", "@types/tar": "^6.1.0", "@types/yargs": "^17.0.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "electron": "^22.0.0", "eslint": "^7.7.0", "eslint-plugin-mocha": "^9.0.0", "mocha": "^11.1.0", "nyc": "^15.1.0", "tsx": "^4.19.3", "typedoc": "~0.25.13", "typescript": "~5.4.5"}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript"}}