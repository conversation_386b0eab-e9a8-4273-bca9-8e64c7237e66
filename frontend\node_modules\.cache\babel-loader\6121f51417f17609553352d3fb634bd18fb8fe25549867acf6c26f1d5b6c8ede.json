{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import ColorVariantSelector from\"../components/ColorVariantSelector\";import{Card,Form,But<PERSON>,Row,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>ge,Modal}from'react-bootstrap';import{BsScissors,BsPlus,BsTrash,BsCheck2Circle,BsExclamationTriangle,BsFilePdf}from'react-icons/bs';import jsPDF from'jspdf';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AddCuttingRecord=()=>{// Overall cutting record fields\nconst[fabricDefinitions,setFabricDefinitions]=useState([]);const[allFabricVariants,setAllFabricVariants]=useState([]);const[cuttingDate,setCuttingDate]=useState('');const[description,setDescription]=useState('');const[productName,setProductName]=useState('');// Fabric definition groups - each group has a fabric definition and its variants\nconst[fabricGroups,setFabricGroups]=useState([{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);// Loading, error, success states\nconst[loadingVariants,setLoadingVariants]=useState(true);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[validated,setValidated]=useState(false);const[showPdfModal,setShowPdfModal]=useState(false);const[submittedRecord,setSubmittedRecord]=useState(null);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Fetch fabric definitions and variants on mount\nuseEffect(()=>{setLoadingVariants(true);// Fetch fabric definitions\nconst fetchDefinitions=axios.get(\"http://localhost:8000/api/fabric-definitions/\");// Fetch all fabric variants\nconst fetchVariants=axios.get(\"http://localhost:8000/api/fabric-variants/\");Promise.all([fetchDefinitions,fetchVariants]).then(_ref=>{let[definitionsRes,variantsRes]=_ref;setFabricDefinitions(definitionsRes.data);setAllFabricVariants(variantsRes.data);setLoadingVariants(false);}).catch(err=>{console.error('Error fetching fabric data:',err);setError('Failed to load fabric data. Please try again.');setLoadingVariants(false);});},[]);// Add a new fabric definition group\nconst addFabricGroup=()=>{setFabricGroups([...fabricGroups,{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);};// Remove a fabric definition group\nconst removeFabricGroup=groupIndex=>{if(fabricGroups.length>1){const newGroups=fabricGroups.filter((_,i)=>i!==groupIndex);setFabricGroups(newGroups);}};// Add a new variant row to a specific fabric group\nconst addVariantToGroup=groupIndex=>{const newGroups=[...fabricGroups];newGroups[groupIndex].variants.push({fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0});setFabricGroups(newGroups);};// Remove a variant from a specific fabric group\nconst removeVariantFromGroup=(groupIndex,variantIndex)=>{const newGroups=[...fabricGroups];if(newGroups[groupIndex].variants.length>1){newGroups[groupIndex].variants=newGroups[groupIndex].variants.filter((_,i)=>i!==variantIndex);setFabricGroups(newGroups);}};// Handle fabric definition change for a group\nconst handleFabricDefinitionChange=(groupIndex,fabricDefinitionId)=>{const newGroups=[...fabricGroups];newGroups[groupIndex].fabric_definition=fabricDefinitionId;// Reset variants when fabric definition changes\nnewGroups[groupIndex].variants=[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}];setFabricGroups(newGroups);};// Handle variant field change\nconst handleVariantChange=(groupIndex,variantIndex,field,value)=>{const newGroups=[...fabricGroups];// If changing fabric variant, check for duplicates within the same group\nif(field==='fabric_variant'){if(isDuplicateFabricVariant(groupIndex,value,variantIndex)){setError(`This fabric variant is already selected in this fabric group. Please select a different variant.`);return;}else{setError('');}}newGroups[groupIndex].variants[variantIndex][field]=value;setFabricGroups(newGroups);};// Check if a fabric variant is already selected in the same group\nconst isDuplicateFabricVariant=(groupIndex,variantId,currentVariantIndex)=>{return fabricGroups[groupIndex].variants.some((variant,idx)=>idx!==currentVariantIndex&&variant.fabric_variant===variantId&&variantId!=='');};// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();// Form validation\nconst form=e.currentTarget;if(form.checkValidity()===false){e.stopPropagation();setValidated(true);return;}// Check if any fabric group has valid data\nconst hasValidGroups=fabricGroups.some(group=>group.fabric_definition&&group.variants.some(variant=>variant.fabric_variant));if(!hasValidGroups){setError('Please select at least one fabric definition and one fabric variant.');return;}// Validate each fabric group\nlet validationError=false;for(let groupIndex=0;groupIndex<fabricGroups.length;groupIndex++){const group=fabricGroups[groupIndex];if(group.fabric_definition&&group.variants.some(variant=>variant.fabric_variant)){// Check for duplicates within the group\nconst selectedVariants=group.variants.map(variant=>variant.fabric_variant).filter(Boolean);const uniqueVariants=[...new Set(selectedVariants)];if(selectedVariants.length!==uniqueVariants.length){setError(`Duplicate fabric variants detected in fabric group ${groupIndex+1}. Please ensure each variant is selected only once per group.`);validationError=true;break;}// Validate yard availability for each variant\nfor(let variant of group.variants){if(variant.fabric_variant){const variantData=allFabricVariants.find(v=>v.id===variant.fabric_variant);if(variantData&&parseFloat(variant.yard_usage)>(variantData.available_yard||variantData.total_yard)){setError(`Yard usage for ${variantData.color_name||variantData.color} exceeds available yards (${variantData.available_yard||variantData.total_yard} yards available).`);validationError=true;break;}}}}}if(validationError){setValidated(true);return;}setValidated(true);setIsSubmitting(true);setError('');setSuccess('');// Flatten fabric groups into details array\nconst details=[];fabricGroups.forEach(group=>{if(group.fabric_definition){group.variants.forEach(variant=>{if(variant.fabric_variant){details.push(variant);}});}});const payload={cutting_date:cuttingDate,description:description,product_name:productName,details:details};try{const response=await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\",payload);setSuccess('Cutting record created successfully!');// Store the submitted record for PDF generation\nconst fabricNames=new Set();const recordData={...response.data,details:response.data.details.map(detail=>{var _variant$fabric_defin,_variant$fabric_defin2;const variant=allFabricVariants.find(v=>v.id===detail.fabric_variant);if(variant!==null&&variant!==void 0&&(_variant$fabric_defin=variant.fabric_definition_data)!==null&&_variant$fabric_defin!==void 0&&_variant$fabric_defin.fabric_name){fabricNames.add(variant.fabric_definition_data.fabric_name);}return{...detail,color:(variant===null||variant===void 0?void 0:variant.color)||'Unknown',color_name:(variant===null||variant===void 0?void 0:variant.color_name)||(variant===null||variant===void 0?void 0:variant.color)||'Unknown',fabric_name:(variant===null||variant===void 0?void 0:(_variant$fabric_defin2=variant.fabric_definition_data)===null||_variant$fabric_defin2===void 0?void 0:_variant$fabric_defin2.fabric_name)||'Unknown'};}),fabric_names:Array.from(fabricNames).join(', ')||'Unknown',totalQuantities:totalQuantities};setSubmittedRecord(recordData);// Show the PDF generation modal\nsetShowPdfModal(true);}catch(err){console.error('Error creating cutting record:',err);if(err.response&&err.response.data){// Display more specific error message if available\nconst errorMessage=typeof err.response.data==='string'?err.response.data:'Failed to create cutting record. Please check your inputs.';setError(errorMessage);}else{setError('Failed to create cutting record. Please try again.');}}finally{setIsSubmitting(false);}};// Function to generate PDF directly without using html2canvas\nconst generatePDF=()=>{if(!submittedRecord)return;try{// Create a new PDF document\nconst pdf=new jsPDF({orientation:'portrait',unit:'mm',format:'a4'});// Set font sizes and styles\nconst titleFontSize=18;const headingFontSize=14;const normalFontSize=10;const smallFontSize=8;// Add title\npdf.setFontSize(titleFontSize);pdf.setFont('helvetica','bold');pdf.text('Cutting Record',105,20,{align:'center'});// Add general information section\npdf.setFontSize(headingFontSize);pdf.text('General Information',20,35);pdf.setFontSize(normalFontSize);pdf.setFont('helvetica','normal');// Draw table for general info\npdf.line(20,40,190,40);// Top horizontal line\nconst generalInfoData=[['Record ID',submittedRecord.id.toString()],['Product Name',submittedRecord.product_name],['Fabrics Used',submittedRecord.fabric_names],['Cutting Date',new Date(submittedRecord.cutting_date).toLocaleDateString()],['Description',submittedRecord.description||'N/A']];let yPos=45;generalInfoData.forEach(row=>{pdf.setFont('helvetica','bold');pdf.text(row[0],25,yPos);pdf.setFont('helvetica','normal');pdf.text(row[1],80,yPos);yPos+=8;pdf.line(20,yPos-3,190,yPos-3);// Horizontal line after each row\n});// Add fabric details section\npdf.setFontSize(headingFontSize);pdf.setFont('helvetica','bold');pdf.text('Fabric Details',20,yPos+10);// Table headers for fabric details\nconst headers=['Fabric','Color','Yard Usage','XS','S','M','L','XL','Total'];const colWidths=[35,35,20,12,12,12,12,12,15];// Calculate starting positions for each column\nconst colPositions=[];let currentPos=20;colWidths.forEach(width=>{colPositions.push(currentPos);currentPos+=width;});// Draw table header\nyPos+=15;pdf.setFontSize(normalFontSize);pdf.setFont('helvetica','bold');// Draw header background\npdf.setFillColor(240,240,240);pdf.rect(20,yPos-5,170,8,'F');// Draw header text\nheaders.forEach((header,index)=>{pdf.text(header,colPositions[index]+2,yPos);});// Draw horizontal line after header\nyPos+=3;pdf.line(20,yPos,190,yPos);// Draw table rows\npdf.setFont('helvetica','normal');submittedRecord.details.forEach(detail=>{var _detail$xs,_detail$s,_detail$m,_detail$l,_detail$xl;yPos+=8;// Calculate total for this row\nconst total=parseInt(detail.xs||0)+parseInt(detail.s||0)+parseInt(detail.m||0)+parseInt(detail.l||0)+parseInt(detail.xl||0);// Draw row data\npdf.text(detail.fabric_name||'Unknown',colPositions[0]+2,yPos);pdf.text(detail.color_name||detail.color,colPositions[1]+2,yPos);pdf.text(`${detail.yard_usage} yards`,colPositions[2]+2,yPos);pdf.text(((_detail$xs=detail.xs)===null||_detail$xs===void 0?void 0:_detail$xs.toString())||'0',colPositions[3]+2,yPos);pdf.text(((_detail$s=detail.s)===null||_detail$s===void 0?void 0:_detail$s.toString())||'0',colPositions[4]+2,yPos);pdf.text(((_detail$m=detail.m)===null||_detail$m===void 0?void 0:_detail$m.toString())||'0',colPositions[5]+2,yPos);pdf.text(((_detail$l=detail.l)===null||_detail$l===void 0?void 0:_detail$l.toString())||'0',colPositions[6]+2,yPos);pdf.text(((_detail$xl=detail.xl)===null||_detail$xl===void 0?void 0:_detail$xl.toString())||'0',colPositions[7]+2,yPos);pdf.text(total.toString(),colPositions[8]+2,yPos);// Draw horizontal line after row\nyPos+=3;pdf.line(20,yPos,190,yPos);});// Draw totals row\nyPos+=8;pdf.setFillColor(240,240,240);pdf.rect(20,yPos-5,170,8,'F');pdf.setFont('helvetica','bold');pdf.text('Total',colPositions[0]+2,yPos);pdf.text('',colPositions[1]+2,yPos);pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`,colPositions[2]+2,yPos);pdf.text(submittedRecord.totalQuantities.xs.toString(),colPositions[3]+2,yPos);pdf.text(submittedRecord.totalQuantities.s.toString(),colPositions[4]+2,yPos);pdf.text(submittedRecord.totalQuantities.m.toString(),colPositions[5]+2,yPos);pdf.text(submittedRecord.totalQuantities.l.toString(),colPositions[6]+2,yPos);pdf.text(submittedRecord.totalQuantities.xl.toString(),colPositions[7]+2,yPos);pdf.text(submittedRecord.totalQuantities.total.toString(),colPositions[8]+2,yPos);// Add footer\npdf.setFontSize(smallFontSize);pdf.setFont('helvetica','italic');pdf.text(`Generated on: ${new Date().toLocaleString()}`,105,280,{align:'center'});pdf.text('Fashion Garment Management System',105,285,{align:'center'});// Save the PDF\npdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);// Reset form after PDF generation\nsetShowPdfModal(false);setCuttingDate('');setDescription('');setProductName('');setFabricGroups([{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);setValidated(false);}catch(error){console.error('Error generating PDF:',error);setError('Failed to generate PDF. Please try again.');setShowPdfModal(false);}};// Function to handle modal close without generating PDF\nconst handleCloseModal=()=>{setShowPdfModal(false);// Reset form\nsetCuttingDate('');setDescription('');setProductName('');setFabricGroups([{id:Date.now(),fabric_definition:'',variants:[{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]}]);setValidated(false);};// Calculate total quantities for all fabric groups\nconst totalQuantities=fabricGroups.reduce((acc,group)=>{group.variants.forEach(variant=>{if(variant.fabric_variant){acc.xs+=parseInt(variant.xs)||0;acc.s+=parseInt(variant.s)||0;acc.m+=parseInt(variant.m)||0;acc.l+=parseInt(variant.l)||0;acc.xl+=parseInt(variant.xl)||0;acc.total+=(parseInt(variant.xs)||0)+(parseInt(variant.s)||0)+(parseInt(variant.m)||0)+(parseInt(variant.l)||0)+(parseInt(variant.xl)||0);acc.yard_usage+=parseFloat(variant.yard_usage)||0;}});return acc;},{xs:0,s:0,m:0,l:0,xl:0,total:0,yard_usage:0});return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(BsScissors,{className:\"me-2\"}),\"Add Cutting Record\"]}),success&&/*#__PURE__*/_jsxs(Alert,{variant:\"success\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsCheck2Circle,{className:\"me-2\",size:20}),success]}),error&&/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsExclamationTriangle,{className:\"me-2\",size:20}),error]}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{noValidate:true,validated:validated,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),placeholder:\"Enter product name\",required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please provide a product name.\"})]})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:cuttingDate,onChange:e=>setCuttingDate(e.target.value),required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please select a cutting date.\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Description\"})}),/*#__PURE__*/_jsx(Form.Control,{as:\"textarea\",rows:3,value:description,onChange:e=>setDescription(e.target.value),placeholder:\"Enter details about this cutting record...\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:\"Fabric Details\"}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",onClick:addFabricGroup,disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Fabric Definition\"]})]}),fabricGroups.map((group,groupIndex)=>{// Get fabric variants for the selected fabric definition\nconst groupVariants=group.fabric_definition?allFabricVariants.filter(v=>v.fabric_definition===parseInt(group.fabric_definition)):[];return/*#__PURE__*/_jsxs(Card,{className:\"mb-4 border\",children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"d-flex justify-content-between align-items-center bg-primary text-white\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[\"Fabric Definition #\",groupIndex+1]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>removeFabricGroup(groupIndex),disabled:fabricGroups.length===1,children:[/*#__PURE__*/_jsx(BsTrash,{className:\"me-1\"}),\" Remove\"]})]}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{md:12,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Select Fabric Definition\"})}),loadingVariants?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading fabric definitions...\"})]}):/*#__PURE__*/_jsxs(Form.Select,{value:group.fabric_definition,onChange:e=>handleFabricDefinitionChange(groupIndex,e.target.value),required:true,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Fabric Definition\"}),fabricDefinitions.map(fd=>/*#__PURE__*/_jsxs(\"option\",{value:fd.id,children:[fd.fabric_name,\" - \",fd.supplier_name||'Unknown Supplier']},fd.id))]})]})})}),group.fabric_definition&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-3\",children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0\",children:\"Fabric Variants\"}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-success\",size:\"sm\",onClick:()=>addVariantToGroup(groupIndex),disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Variant\"]})]}),group.variants.map((variant,variantIndex)=>{const currentVariant=allFabricVariants.find(v=>v.id===variant.fabric_variant);return/*#__PURE__*/_jsx(Card,{className:\"mb-3 border-light\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsxs(\"h6\",{className:\"mb-0 me-2\",children:[\"Variant #\",variantIndex+1]}),currentVariant&&/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'16px',height:'16px',backgroundColor:currentVariant.color,border:'1px solid #ccc',borderRadius:'3px',marginRight:'6px'},title:`Color: ${currentVariant.color}`}),/*#__PURE__*/_jsx(\"small\",{className:\"text-muted\",children:currentVariant.color_name||currentVariant.color})]})]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-danger\",size:\"sm\",onClick:()=>removeVariantFromGroup(groupIndex,variantIndex),disabled:group.variants.length===1,children:[/*#__PURE__*/_jsx(BsTrash,{className:\"me-1\"}),\" Remove\"]})]}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric Variant (Color)\"})}),/*#__PURE__*/_jsx(ColorVariantSelector,{variants:groupVariants,selectedValue:variant.fabric_variant,onSelect:value=>handleVariantChange(groupIndex,variantIndex,'fabric_variant',value),placeholder:\"Select Color Variant\",isDuplicateFunction:isDuplicateFabricVariant,groupIndex:groupIndex,variantIndex:variantIndex})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-1\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"mb-0\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Yard Usage\"})}),currentVariant&&/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'16px',height:'16px',backgroundColor:currentVariant.color,border:'1px solid #ccc',borderRadius:'3px',marginRight:'8px'},title:`Color: ${currentVariant.color_name||currentVariant.color}`}),/*#__PURE__*/_jsxs(\"span\",{className:parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard)?\"text-danger small\":\"text-success small\",children:[currentVariant.color_name||currentVariant.color,\" - Available: \",currentVariant.available_yard||currentVariant.total_yard,\" yards\"]})]})]}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:variant.yard_usage,onChange:e=>handleVariantChange(groupIndex,variantIndex,'yard_usage',e.target.value),required:true,placeholder:\"Enter yards used\",isInvalid:currentVariant&&parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard),className:currentVariant&&parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard)?\"border-danger\":\"\",style:{height:'38px'}}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:currentVariant&&parseFloat(variant.yard_usage)>(currentVariant.available_yard||currentVariant.total_yard)?`Exceeds available yards (${currentVariant.available_yard||currentVariant.total_yard} yards available)`:\"Please enter valid yard usage.\"})]})})]}),/*#__PURE__*/_jsx(Form.Label,{className:\"mt-2\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Size Quantities\"})}),/*#__PURE__*/_jsxs(Row,{children:[[\"XS\",\"S\",\"M\",\"L\",\"XL\"].map((size,sizeIndex)=>{const sizeKey=size.toLowerCase();return/*#__PURE__*/_jsx(Col,{xs:6,sm:4,md:2,className:\"mb-3\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-center d-block\",children:size}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:variant[sizeKey],onChange:e=>{const val=Math.max(0,parseInt(e.target.value||0));handleVariantChange(groupIndex,variantIndex,sizeKey,val);},className:\"text-center\"})]})},sizeIndex);}),/*#__PURE__*/_jsx(Col,{xs:6,sm:4,md:2,className:\"mb-3\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-center d-block\",children:\"Total\"}),/*#__PURE__*/_jsx(\"div\",{className:\"form-control text-center bg-light\",children:parseInt(variant.xs||0)+parseInt(variant.s||0)+parseInt(variant.m||0)+parseInt(variant.l||0)+parseInt(variant.xl||0)})]})})]})]})},variantIndex);})]})]})]},group.id);}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-end mb-4\",children:/*#__PURE__*/_jsx(Card,{className:\"border-0\",style:{backgroundColor:\"#e8f4fe\"},children:/*#__PURE__*/_jsx(Card.Body,{className:\"py-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex flex-column\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"me-2\",children:\"Total Quantities:\"}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"XS: \",totalQuantities.xs]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"S: \",totalQuantities.s]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"M: \",totalQuantities.m]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"L: \",totalQuantities.l]}),/*#__PURE__*/_jsxs(Badge,{bg:\"primary\",className:\"me-1\",children:[\"XL: \",totalQuantities.xl]}),/*#__PURE__*/_jsxs(Badge,{bg:\"success\",className:\"ms-2\",children:[\"Total: \",totalQuantities.total]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"me-2\",children:\"Total Yard Usage:\"}),/*#__PURE__*/_jsxs(Badge,{bg:\"info\",children:[totalQuantities.yard_usage.toFixed(2),\" yards\"]})]})]})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center mt-4\",children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"primary\",size:\"lg\",disabled:isSubmitting,className:\"px-5\",children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Submitting...\"]}):'Submit Cutting Record'})})]})})})]}),/*#__PURE__*/_jsxs(Modal,{show:showPdfModal,onHide:handleCloseModal,size:\"lg\",centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:\"Generate Cutting Record PDF\"})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Would you like to generate a PDF for this cutting record?\"}),submittedRecord&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"The PDF will include the following information:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name:\"}),\" \",submittedRecord.product_name]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric:\"}),\" \",submittedRecord.fabric_name]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date:\"}),\" \",new Date(submittedRecord.cutting_date).toLocaleDateString()]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Quantities:\"}),\" XS: \",submittedRecord.totalQuantities.xs,\", S: \",submittedRecord.totalQuantities.s,\", M: \",submittedRecord.totalQuantities.m,\", L: \",submittedRecord.totalQuantities.l,\", XL: \",submittedRecord.totalQuantities.xl]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Items:\"}),\" \",submittedRecord.totalQuantities.total]}),/*#__PURE__*/_jsxs(\"li\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Yard Usage:\"}),\" \",submittedRecord.totalQuantities.yard_usage.toFixed(2),\" yards\"]})]})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleCloseModal,children:\"No, Skip\"}),/*#__PURE__*/_jsxs(Button,{variant:\"primary\",onClick:generatePDF,children:[/*#__PURE__*/_jsx(BsFilePdf,{className:\"me-2\"}),\" Generate PDF\"]})]})]})]});};export default AddCuttingRecord;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "RoleBasedNavBar", "ColorVariantSelector", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Badge", "Modal", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsFilePdf", "jsPDF", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AddCuttingRecord", "fabricDefinitions", "setFabricDefinitions", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "fabricGroups", "setFabricGroups", "id", "Date", "now", "fabric_definition", "variants", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "loadingVariants", "setLoadingVariants", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "showPdfModal", "setShowPdfModal", "submittedRecord", "setSubmittedRecord", "handleResize", "addEventListener", "removeEventListener", "fetchDefinitions", "get", "fetchVariants", "Promise", "all", "then", "_ref", "definitionsRes", "variantsRes", "data", "catch", "err", "console", "addFabricGroup", "removeFabricGroup", "groupIndex", "length", "newGroups", "filter", "_", "i", "addVariantToGroup", "push", "removeVariantFromGroup", "variantIndex", "handleFabricDefinitionChange", "fabricDefinitionId", "handleVariantChange", "field", "value", "isDuplicateFabricVariant", "variantId", "currentVariantIndex", "some", "variant", "idx", "handleSubmit", "e", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidGroups", "group", "validationError", "selectedVariants", "map", "Boolean", "uniqueVariants", "Set", "variantData", "find", "v", "parseFloat", "available_yard", "total_yard", "color_name", "color", "details", "for<PERSON>ach", "payload", "cutting_date", "product_name", "response", "post", "fabricNames", "recordData", "detail", "_variant$fabric_defin", "_variant$fabric_defin2", "fabric_definition_data", "fabric_name", "add", "fabric_names", "Array", "from", "join", "totalQuantities", "errorMessage", "generatePDF", "pdf", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "line", "generalInfoData", "toString", "toLocaleDateString", "yPos", "row", "headers", "col<PERSON><PERSON><PERSON>", "colPositions", "currentPos", "width", "setFillColor", "rect", "header", "index", "_detail$xs", "_detail$s", "_detail$m", "_detail$l", "_detail$xl", "total", "parseInt", "toFixed", "toLocaleString", "save", "handleCloseModal", "reduce", "acc", "children", "style", "marginLeft", "transition", "padding", "className", "size", "backgroundColor", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "onClick", "disabled", "groupVariants", "Header", "animation", "Select", "fd", "supplier_name", "currentV<PERSON>t", "height", "border", "marginRight", "title", "selected<PERSON><PERSON><PERSON>", "onSelect", "isDuplicateFunction", "step", "min", "isInvalid", "sizeIndex", "sizeKey", "toLowerCase", "sm", "val", "Math", "max", "bg", "role", "show", "onHide", "centered", "closeButton", "Title", "Footer"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/AddCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport ColorVariantSelector from \"../components/ColorVariantSelector\";\r\nimport { Card, Form, Button, Row, Col, <PERSON>, <PERSON><PERSON>, <PERSON>ge, Modal } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\r\nimport jsPDF from 'jspdf';\r\n\r\nconst AddCuttingRecord = () => {\r\n  // Overall cutting record fields\r\n  const [fabricDefinitions, setFabricDefinitions] = useState([]);\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n\r\n  // Fabric definition groups - each group has a fabric definition and its variants\r\n  const [fabricGroups, setFabricGroups] = useState([\r\n    {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }\r\n  ]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [submittedRecord, setSubmittedRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch fabric definitions and variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n\r\n    // Fetch fabric definitions\r\n    const fetchDefinitions = axios.get(\"http://localhost:8000/api/fabric-definitions/\");\r\n    // Fetch all fabric variants\r\n    const fetchVariants = axios.get(\"http://localhost:8000/api/fabric-variants/\");\r\n\r\n    Promise.all([fetchDefinitions, fetchVariants])\r\n      .then(([definitionsRes, variantsRes]) => {\r\n        setFabricDefinitions(definitionsRes.data);\r\n        setAllFabricVariants(variantsRes.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric data:', err);\r\n        setError('Failed to load fabric data. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n  // Add a new fabric definition group\r\n  const addFabricGroup = () => {\r\n    setFabricGroups([...fabricGroups, {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }]);\r\n  };\r\n\r\n  // Remove a fabric definition group\r\n  const removeFabricGroup = (groupIndex) => {\r\n    if (fabricGroups.length > 1) {\r\n      const newGroups = fabricGroups.filter((_, i) => i !== groupIndex);\r\n      setFabricGroups(newGroups);\r\n    }\r\n  };\r\n\r\n  // Add a new variant row to a specific fabric group\r\n  const addVariantToGroup = (groupIndex) => {\r\n    const newGroups = [...fabricGroups];\r\n    newGroups[groupIndex].variants.push({ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 });\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Remove a variant from a specific fabric group\r\n  const removeVariantFromGroup = (groupIndex, variantIndex) => {\r\n    const newGroups = [...fabricGroups];\r\n    if (newGroups[groupIndex].variants.length > 1) {\r\n      newGroups[groupIndex].variants = newGroups[groupIndex].variants.filter((_, i) => i !== variantIndex);\r\n      setFabricGroups(newGroups);\r\n    }\r\n  };\r\n\r\n  // Handle fabric definition change for a group\r\n  const handleFabricDefinitionChange = (groupIndex, fabricDefinitionId) => {\r\n    const newGroups = [...fabricGroups];\r\n    newGroups[groupIndex].fabric_definition = fabricDefinitionId;\r\n    // Reset variants when fabric definition changes\r\n    newGroups[groupIndex].variants = [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }];\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Handle variant field change\r\n  const handleVariantChange = (groupIndex, variantIndex, field, value) => {\r\n    const newGroups = [...fabricGroups];\r\n\r\n    // If changing fabric variant, check for duplicates within the same group\r\n    if (field === 'fabric_variant') {\r\n      if (isDuplicateFabricVariant(groupIndex, value, variantIndex)) {\r\n        setError(`This fabric variant is already selected in this fabric group. Please select a different variant.`);\r\n        return;\r\n      } else {\r\n        setError('');\r\n      }\r\n    }\r\n\r\n    newGroups[groupIndex].variants[variantIndex][field] = value;\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Check if a fabric variant is already selected in the same group\r\n  const isDuplicateFabricVariant = (groupIndex, variantId, currentVariantIndex) => {\r\n    return fabricGroups[groupIndex].variants.some((variant, idx) =>\r\n      idx !== currentVariantIndex && variant.fabric_variant === variantId && variantId !== ''\r\n    );\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any fabric group has valid data\r\n    const hasValidGroups = fabricGroups.some(group =>\r\n      group.fabric_definition && group.variants.some(variant => variant.fabric_variant)\r\n    );\r\n    if (!hasValidGroups) {\r\n      setError('Please select at least one fabric definition and one fabric variant.');\r\n      return;\r\n    }\r\n\r\n    // Validate each fabric group\r\n    let validationError = false;\r\n    for (let groupIndex = 0; groupIndex < fabricGroups.length; groupIndex++) {\r\n      const group = fabricGroups[groupIndex];\r\n\r\n      if (group.fabric_definition && group.variants.some(variant => variant.fabric_variant)) {\r\n        // Check for duplicates within the group\r\n        const selectedVariants = group.variants.map(variant => variant.fabric_variant).filter(Boolean);\r\n        const uniqueVariants = [...new Set(selectedVariants)];\r\n        if (selectedVariants.length !== uniqueVariants.length) {\r\n          setError(`Duplicate fabric variants detected in fabric group ${groupIndex + 1}. Please ensure each variant is selected only once per group.`);\r\n          validationError = true;\r\n          break;\r\n        }\r\n\r\n        // Validate yard availability for each variant\r\n        for (let variant of group.variants) {\r\n          if (variant.fabric_variant) {\r\n            const variantData = allFabricVariants.find(v => v.id === variant.fabric_variant);\r\n            if (variantData && parseFloat(variant.yard_usage) > (variantData.available_yard || variantData.total_yard)) {\r\n              setError(`Yard usage for ${variantData.color_name || variantData.color} exceeds available yards (${variantData.available_yard || variantData.total_yard} yards available).`);\r\n              validationError = true;\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (validationError) {\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    // Flatten fabric groups into details array\r\n    const details = [];\r\n    fabricGroups.forEach(group => {\r\n      if (group.fabric_definition) {\r\n        group.variants.forEach(variant => {\r\n          if (variant.fabric_variant) {\r\n            details.push(variant);\r\n          }\r\n        });\r\n      }\r\n    });\r\n\r\n    const payload = {\r\n      cutting_date: cuttingDate,\r\n      description: description,\r\n      product_name: productName,\r\n      details: details\r\n    };\r\n\r\n    try {\r\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\r\n      setSuccess('Cutting record created successfully!');\r\n\r\n      // Store the submitted record for PDF generation\r\n      const fabricNames = new Set();\r\n      const recordData = {\r\n        ...response.data,\r\n        details: response.data.details.map(detail => {\r\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n          if (variant?.fabric_definition_data?.fabric_name) {\r\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\r\n          }\r\n          return {\r\n            ...detail,\r\n            color: variant?.color || 'Unknown',\r\n            color_name: variant?.color_name || variant?.color || 'Unknown',\r\n            fabric_name: variant?.fabric_definition_data?.fabric_name || 'Unknown'\r\n          };\r\n        }),\r\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\r\n        totalQuantities: totalQuantities\r\n      };\r\n\r\n      setSubmittedRecord(recordData);\r\n\r\n      // Show the PDF generation modal\r\n      setShowPdfModal(true);\r\n    } catch (err) {\r\n      console.error('Error creating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to create cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to create cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to generate PDF directly without using html2canvas\r\n  const generatePDF = () => {\r\n    if (!submittedRecord) return;\r\n\r\n    try {\r\n      // Create a new PDF document\r\n      const pdf = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add title\r\n      pdf.setFontSize(titleFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Cutting Record', 105, 20, { align: 'center' });\r\n\r\n      // Add general information section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.text('General Information', 20, 35);\r\n\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'normal');\r\n\r\n      // Draw table for general info\r\n      pdf.line(20, 40, 190, 40); // Top horizontal line\r\n\r\n      const generalInfoData = [\r\n        ['Record ID', submittedRecord.id.toString()],\r\n        ['Product Name', submittedRecord.product_name],\r\n        ['Fabrics Used', submittedRecord.fabric_names],\r\n        ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()],\r\n        ['Description', submittedRecord.description || 'N/A']\r\n      ];\r\n\r\n      let yPos = 45;\r\n      generalInfoData.forEach((row) => {\r\n        pdf.setFont('helvetica', 'bold');\r\n        pdf.text(row[0], 25, yPos);\r\n        pdf.setFont('helvetica', 'normal');\r\n        pdf.text(row[1], 80, yPos);\r\n        yPos += 8;\r\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\r\n      });\r\n\r\n      // Add fabric details section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Fabric Details', 20, yPos + 10);\r\n\r\n      // Table headers for fabric details\r\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\r\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\r\n\r\n      // Calculate starting positions for each column\r\n      const colPositions = [];\r\n      let currentPos = 20;\r\n      colWidths.forEach(width => {\r\n        colPositions.push(currentPos);\r\n        currentPos += width;\r\n      });\r\n\r\n      // Draw table header\r\n      yPos += 15;\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n\r\n      // Draw header background\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      // Draw header text\r\n      headers.forEach((header, index) => {\r\n        pdf.text(header, colPositions[index] + 2, yPos);\r\n      });\r\n\r\n      // Draw horizontal line after header\r\n      yPos += 3;\r\n      pdf.line(20, yPos, 190, yPos);\r\n\r\n      // Draw table rows\r\n      pdf.setFont('helvetica', 'normal');\r\n      submittedRecord.details.forEach((detail) => {\r\n        yPos += 8;\r\n\r\n        // Calculate total for this row\r\n        const total = parseInt(detail.xs || 0) +\r\n                      parseInt(detail.s || 0) +\r\n                      parseInt(detail.m || 0) +\r\n                      parseInt(detail.l || 0) +\r\n                      parseInt(detail.xl || 0);\r\n\r\n        // Draw row data\r\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\r\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\r\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\r\n        pdf.text(detail.xs?.toString() || '0', colPositions[3] + 2, yPos);\r\n        pdf.text(detail.s?.toString() || '0', colPositions[4] + 2, yPos);\r\n        pdf.text(detail.m?.toString() || '0', colPositions[5] + 2, yPos);\r\n        pdf.text(detail.l?.toString() || '0', colPositions[6] + 2, yPos);\r\n        pdf.text(detail.xl?.toString() || '0', colPositions[7] + 2, yPos);\r\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\r\n\r\n        // Draw horizontal line after row\r\n        yPos += 3;\r\n        pdf.line(20, yPos, 190, yPos);\r\n      });\r\n\r\n      // Draw totals row\r\n      yPos += 8;\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Total', colPositions[0] + 2, yPos);\r\n      pdf.text('', colPositions[1] + 2, yPos);\r\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\r\n\r\n      // Add footer\r\n      pdf.setFontSize(smallFontSize);\r\n      pdf.setFont('helvetica', 'italic');\r\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      pdf.text('Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\r\n\r\n      // Reset form after PDF generation\r\n      setShowPdfModal(false);\r\n      setCuttingDate('');\r\n      setDescription('');\r\n      setProductName('');\r\n      setFabricGroups([{\r\n        id: Date.now(),\r\n        fabric_definition: '',\r\n        variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n      }]);\r\n      setValidated(false);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      setError('Failed to generate PDF. Please try again.');\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Function to handle modal close without generating PDF\r\n  const handleCloseModal = () => {\r\n    setShowPdfModal(false);\r\n    // Reset form\r\n    setCuttingDate('');\r\n    setDescription('');\r\n    setProductName('');\r\n    setFabricGroups([{\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }]);\r\n    setValidated(false);\r\n  };\r\n\r\n\r\n\r\n  // Calculate total quantities for all fabric groups\r\n  const totalQuantities = fabricGroups.reduce((acc, group) => {\r\n    group.variants.forEach(variant => {\r\n      if (variant.fabric_variant) {\r\n        acc.xs += parseInt(variant.xs) || 0;\r\n        acc.s += parseInt(variant.s) || 0;\r\n        acc.m += parseInt(variant.m) || 0;\r\n        acc.l += parseInt(variant.l) || 0;\r\n        acc.xl += parseInt(variant.xl) || 0;\r\n        acc.total += (parseInt(variant.xs) || 0) +\r\n                    (parseInt(variant.s) || 0) +\r\n                    (parseInt(variant.m) || 0) +\r\n                    (parseInt(variant.l) || 0) +\r\n                    (parseInt(variant.xl) || 0);\r\n        acc.yard_usage += parseFloat(variant.yard_usage) || 0;\r\n      }\r\n    });\r\n    return acc;\r\n  }, { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 });\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <BsScissors className=\"me-2\" />\r\n          Add Cutting Record\r\n        </h2>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addFabricGroup}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Definition\r\n                </Button>\r\n              </div>\r\n\r\n              {fabricGroups.map((group, groupIndex) => {\r\n                // Get fabric variants for the selected fabric definition\r\n                const groupVariants = group.fabric_definition\r\n                  ? allFabricVariants.filter(v => v.fabric_definition === parseInt(group.fabric_definition))\r\n                  : [];\r\n\r\n                return (\r\n                  <Card key={group.id} className=\"mb-4 border\">\r\n                    <Card.Header className=\"d-flex justify-content-between align-items-center bg-primary text-white\">\r\n                      <h5 className=\"mb-0\">Fabric Definition #{groupIndex + 1}</h5>\r\n                      <Button\r\n                        variant=\"outline-light\"\r\n                        size=\"sm\"\r\n                        onClick={() => removeFabricGroup(groupIndex)}\r\n                        disabled={fabricGroups.length === 1}\r\n                      >\r\n                        <BsTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      {/* Fabric Definition Selection */}\r\n                      <Row className=\"mb-3\">\r\n                        <Col md={12}>\r\n                          <Form.Group>\r\n                            <Form.Label><strong>Select Fabric Definition</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading fabric definitions...</span>\r\n                              </div>\r\n                            ) : (\r\n                              <Form.Select\r\n                                value={group.fabric_definition}\r\n                                onChange={(e) => handleFabricDefinitionChange(groupIndex, e.target.value)}\r\n                                required\r\n                              >\r\n                                <option value=\"\">Select Fabric Definition</option>\r\n                                {fabricDefinitions.map((fd) => (\r\n                                  <option key={fd.id} value={fd.id}>\r\n                                    {fd.fabric_name} - {fd.supplier_name || 'Unknown Supplier'}\r\n                                  </option>\r\n                                ))}\r\n                              </Form.Select>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n\r\n                      {/* Fabric Variants Section */}\r\n                      {group.fabric_definition && (\r\n                        <>\r\n                          <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                            <h6 className=\"mb-0\">Fabric Variants</h6>\r\n                            <Button\r\n                              variant=\"outline-success\"\r\n                              size=\"sm\"\r\n                              onClick={() => addVariantToGroup(groupIndex)}\r\n                              disabled={isSubmitting}\r\n                            >\r\n                              <BsPlus className=\"me-1\" /> Add Variant\r\n                            </Button>\r\n                          </div>\r\n\r\n                          {group.variants.map((variant, variantIndex) => {\r\n                            const currentVariant = allFabricVariants.find(v => v.id === variant.fabric_variant);\r\n\r\n                            return (\r\n                              <Card key={variantIndex} className=\"mb-3 border-light\">\r\n                                <Card.Body>\r\n                                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                    <div className=\"d-flex align-items-center\">\r\n                                      <h6 className=\"mb-0 me-2\">Variant #{variantIndex + 1}</h6>\r\n                                      {currentVariant && (\r\n                                        <div className=\"d-flex align-items-center\">\r\n                                          <div\r\n                                            style={{\r\n                                              width: '16px',\r\n                                              height: '16px',\r\n                                              backgroundColor: currentVariant.color,\r\n                                              border: '1px solid #ccc',\r\n                                              borderRadius: '3px',\r\n                                              marginRight: '6px'\r\n                                            }}\r\n                                            title={`Color: ${currentVariant.color}`}\r\n                                          />\r\n                                          <small className=\"text-muted\">\r\n                                            {currentVariant.color_name || currentVariant.color}\r\n                                          </small>\r\n                                        </div>\r\n                                      )}\r\n                                    </div>\r\n                                    <Button\r\n                                      variant=\"outline-danger\"\r\n                                      size=\"sm\"\r\n                                      onClick={() => removeVariantFromGroup(groupIndex, variantIndex)}\r\n                                      disabled={group.variants.length === 1}\r\n                                    >\r\n                                      <BsTrash className=\"me-1\" /> Remove\r\n                                    </Button>\r\n                                  </div>\r\n\r\n                                  <Row>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                                        <ColorVariantSelector\r\n                                          variants={groupVariants}\r\n                                          selectedValue={variant.fabric_variant}\r\n                                          onSelect={(value) => handleVariantChange(groupIndex, variantIndex, 'fabric_variant', value)}\r\n                                          placeholder=\"Select Color Variant\"\r\n                                          isDuplicateFunction={isDuplicateFabricVariant}\r\n                                          groupIndex={groupIndex}\r\n                                          variantIndex={variantIndex}\r\n                                        />\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <div className=\"d-flex justify-content-between align-items-center mb-1\">\r\n                                          <Form.Label className=\"mb-0\"><strong>Yard Usage</strong></Form.Label>\r\n                                          {currentVariant && (\r\n                                            <div className=\"d-flex align-items-center\">\r\n                                              <div\r\n                                                style={{\r\n                                                  width: '16px',\r\n                                                  height: '16px',\r\n                                                  backgroundColor: currentVariant.color,\r\n                                                  border: '1px solid #ccc',\r\n                                                  borderRadius: '3px',\r\n                                                  marginRight: '8px'\r\n                                                }}\r\n                                                title={`Color: ${currentVariant.color_name || currentVariant.color}`}\r\n                                              />\r\n                                              <span className={parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\"}>\r\n                                                {currentVariant.color_name || currentVariant.color} - Available: {currentVariant.available_yard || currentVariant.total_yard} yards\r\n                                              </span>\r\n                                            </div>\r\n                                          )}\r\n                                        </div>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={variant.yard_usage}\r\n                                          onChange={(e) => handleVariantChange(groupIndex, variantIndex, 'yard_usage', e.target.value)}\r\n                                          required\r\n                                          placeholder=\"Enter yards used\"\r\n                                          isInvalid={currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)}\r\n                                          className={currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\"}\r\n                                          style={{ height: '38px' }}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)\r\n                                            ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)`\r\n                                            : \"Please enter valid yard usage.\"}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n\r\n                                  <Form.Label className=\"mt-2\"><strong>Size Quantities</strong></Form.Label>\r\n                                  <Row>\r\n                                    {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\r\n                                      const sizeKey = size.toLowerCase();\r\n                                      return (\r\n                                        <Col key={sizeIndex} xs={6} sm={4} md={2} className=\"mb-3\">\r\n                                          <Form.Group>\r\n                                            <Form.Label className=\"text-center d-block\">{size}</Form.Label>\r\n                                            <Form.Control\r\n                                              type=\"number\"\r\n                                              min=\"0\"\r\n                                              value={variant[sizeKey]}\r\n                                              onChange={(e) => {\r\n                                                const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                                handleVariantChange(groupIndex, variantIndex, sizeKey, val);\r\n                                              }}\r\n                                              className=\"text-center\"\r\n                                            />\r\n                                          </Form.Group>\r\n                                        </Col>\r\n                                      );\r\n                                    })}\r\n                                    <Col xs={6} sm={4} md={2} className=\"mb-3\">\r\n                                      <Form.Group>\r\n                                        <Form.Label className=\"text-center d-block\">Total</Form.Label>\r\n                                        <div className=\"form-control text-center bg-light\">\r\n                                          {parseInt(variant.xs || 0) +\r\n                                           parseInt(variant.s || 0) +\r\n                                           parseInt(variant.m || 0) +\r\n                                           parseInt(variant.l || 0) +\r\n                                           parseInt(variant.xl || 0)}\r\n                                        </div>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n                                </Card.Body>\r\n                              </Card>\r\n                            );\r\n                          })}\r\n                        </>\r\n                      )}\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-end mb-4\">\r\n                <Card className=\"border-0\" style={{ backgroundColor: \"#e8f4fe\" }}>\r\n                  <Card.Body className=\"py-2\">\r\n                    <div className=\"d-flex flex-column\">\r\n                      <div className=\"d-flex align-items-center mb-2\">\r\n                        <strong className=\"me-2\">Total Quantities:</strong>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XS: {totalQuantities.xs}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">S: {totalQuantities.s}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">M: {totalQuantities.m}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">L: {totalQuantities.l}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XL: {totalQuantities.xl}</Badge>\r\n                        <Badge bg=\"success\" className=\"ms-2\">Total: {totalQuantities.total}</Badge>\r\n                      </div>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <strong className=\"me-2\">Total Yard Usage:</strong>\r\n                        <Badge bg=\"info\">{totalQuantities.yard_usage.toFixed(2)} yards</Badge>\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* PDF Generation Modal */}\r\n      <Modal show={showPdfModal} onHide={handleCloseModal} size=\"lg\" centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Generate Cutting Record PDF</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Would you like to generate a PDF for this cutting record?</p>\r\n\r\n          {submittedRecord && (\r\n            <div className=\"mb-3\">\r\n              <p>The PDF will include the following information:</p>\r\n              <ul>\r\n                <li><strong>Product Name:</strong> {submittedRecord.product_name}</li>\r\n                <li><strong>Fabric:</strong> {submittedRecord.fabric_name}</li>\r\n                <li><strong>Cutting Date:</strong> {new Date(submittedRecord.cutting_date).toLocaleDateString()}</li>\r\n                <li><strong>Total Quantities:</strong> XS: {submittedRecord.totalQuantities.xs},\r\n                  S: {submittedRecord.totalQuantities.s},\r\n                  M: {submittedRecord.totalQuantities.m},\r\n                  L: {submittedRecord.totalQuantities.l},\r\n                  XL: {submittedRecord.totalQuantities.xl}</li>\r\n                <li><strong>Total Items:</strong> {submittedRecord.totalQuantities.total}</li>\r\n                <li><strong>Total Yard Usage:</strong> {submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards</li>\r\n              </ul>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\r\n            No, Skip\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={generatePDF}>\r\n            <BsFilePdf className=\"me-2\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddCuttingRecord;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,CAAAC,oBAAoB,KAAM,oCAAoC,CACrE,OAASC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,GAAG,CAAEC,GAAG,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CAC5F,OAASC,UAAU,CAAEC,MAAM,CAAEC,OAAO,CAAEC,cAAc,CAAEC,qBAAqB,CAAEC,SAAS,KAAQ,gBAAgB,CAC9G,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1B,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC8B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACkC,WAAW,CAAEC,cAAc,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACoC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAElD;AACA,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAC,CAC/C,CACEwC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CACF,CAAC,CAEF;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACsD,KAAK,CAAEC,QAAQ,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwD,OAAO,CAAEC,UAAU,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0D,YAAY,CAAEC,eAAe,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4D,aAAa,CAAEC,gBAAgB,CAAC,CAAG7D,QAAQ,CAAC8D,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGjE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACoE,eAAe,CAAEC,kBAAkB,CAAC,CAAGrE,QAAQ,CAAC,IAAI,CAAC,CAE5D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqE,YAAY,CAAGA,CAAA,GAAM,CACzBT,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACS,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMR,MAAM,CAACU,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACArE,SAAS,CAAC,IAAM,CACdoD,kBAAkB,CAAC,IAAI,CAAC,CAExB;AACA,KAAM,CAAAoB,gBAAgB,CAAGvE,KAAK,CAACwE,GAAG,CAAC,+CAA+C,CAAC,CACnF;AACA,KAAM,CAAAC,aAAa,CAAGzE,KAAK,CAACwE,GAAG,CAAC,4CAA4C,CAAC,CAE7EE,OAAO,CAACC,GAAG,CAAC,CAACJ,gBAAgB,CAAEE,aAAa,CAAC,CAAC,CAC3CG,IAAI,CAACC,IAAA,EAAmC,IAAlC,CAACC,cAAc,CAAEC,WAAW,CAAC,CAAAF,IAAA,CAClClD,oBAAoB,CAACmD,cAAc,CAACE,IAAI,CAAC,CACzCnD,oBAAoB,CAACkD,WAAW,CAACC,IAAI,CAAC,CACtC7B,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACD8B,KAAK,CAAEC,GAAG,EAAK,CACdC,OAAO,CAAC/B,KAAK,CAAC,6BAA6B,CAAE8B,GAAG,CAAC,CACjD7B,QAAQ,CAAC,+CAA+C,CAAC,CACzDF,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAiC,cAAc,CAAGA,CAAA,GAAM,CAC3B/C,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAE,CAChCE,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAoC,iBAAiB,CAAIC,UAAU,EAAK,CACxC,GAAIlD,YAAY,CAACmD,MAAM,CAAG,CAAC,CAAE,CAC3B,KAAM,CAAAC,SAAS,CAAGpD,YAAY,CAACqD,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKL,UAAU,CAAC,CACjEjD,eAAe,CAACmD,SAAS,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAI,iBAAiB,CAAIN,UAAU,EAAK,CACxC,KAAM,CAAAE,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CACnCoD,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAACmD,IAAI,CAAC,CAAElD,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAC,CAC3GZ,eAAe,CAACmD,SAAS,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAM,sBAAsB,CAAGA,CAACR,UAAU,CAAES,YAAY,GAAK,CAC3D,KAAM,CAAAP,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CACnC,GAAIoD,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAC6C,MAAM,CAAG,CAAC,CAAE,CAC7CC,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAG8C,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAC+C,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKI,YAAY,CAAC,CACpG1D,eAAe,CAACmD,SAAS,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,4BAA4B,CAAGA,CAACV,UAAU,CAAEW,kBAAkB,GAAK,CACvE,KAAM,CAAAT,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CACnCoD,SAAS,CAACF,UAAU,CAAC,CAAC7C,iBAAiB,CAAGwD,kBAAkB,CAC5D;AACAT,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAAG,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAC,CACzGZ,eAAe,CAACmD,SAAS,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAU,mBAAmB,CAAGA,CAACZ,UAAU,CAAES,YAAY,CAAEI,KAAK,CAAEC,KAAK,GAAK,CACtE,KAAM,CAAAZ,SAAS,CAAG,CAAC,GAAGpD,YAAY,CAAC,CAEnC;AACA,GAAI+D,KAAK,GAAK,gBAAgB,CAAE,CAC9B,GAAIE,wBAAwB,CAACf,UAAU,CAAEc,KAAK,CAAEL,YAAY,CAAC,CAAE,CAC7D1C,QAAQ,CAAC,kGAAkG,CAAC,CAC5G,OACF,CAAC,IAAM,CACLA,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAEAmC,SAAS,CAACF,UAAU,CAAC,CAAC5C,QAAQ,CAACqD,YAAY,CAAC,CAACI,KAAK,CAAC,CAAGC,KAAK,CAC3D/D,eAAe,CAACmD,SAAS,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAa,wBAAwB,CAAGA,CAACf,UAAU,CAAEgB,SAAS,CAAEC,mBAAmB,GAAK,CAC/E,MAAO,CAAAnE,YAAY,CAACkD,UAAU,CAAC,CAAC5C,QAAQ,CAAC8D,IAAI,CAAC,CAACC,OAAO,CAAEC,GAAG,GACzDA,GAAG,GAAKH,mBAAmB,EAAIE,OAAO,CAAC9D,cAAc,GAAK2D,SAAS,EAAIA,SAAS,GAAK,EACvF,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB;AACA,KAAM,CAAAC,IAAI,CAAGF,CAAC,CAACG,aAAa,CAC5B,GAAID,IAAI,CAACE,aAAa,CAAC,CAAC,GAAK,KAAK,CAAE,CAClCJ,CAAC,CAACK,eAAe,CAAC,CAAC,CACnBlD,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEA;AACA,KAAM,CAAAmD,cAAc,CAAG9E,YAAY,CAACoE,IAAI,CAACW,KAAK,EAC5CA,KAAK,CAAC1E,iBAAiB,EAAI0E,KAAK,CAACzE,QAAQ,CAAC8D,IAAI,CAACC,OAAO,EAAIA,OAAO,CAAC9D,cAAc,CAClF,CAAC,CACD,GAAI,CAACuE,cAAc,CAAE,CACnB7D,QAAQ,CAAC,sEAAsE,CAAC,CAChF,OACF,CAEA;AACA,GAAI,CAAA+D,eAAe,CAAG,KAAK,CAC3B,IAAK,GAAI,CAAA9B,UAAU,CAAG,CAAC,CAAEA,UAAU,CAAGlD,YAAY,CAACmD,MAAM,CAAED,UAAU,EAAE,CAAE,CACvE,KAAM,CAAA6B,KAAK,CAAG/E,YAAY,CAACkD,UAAU,CAAC,CAEtC,GAAI6B,KAAK,CAAC1E,iBAAiB,EAAI0E,KAAK,CAACzE,QAAQ,CAAC8D,IAAI,CAACC,OAAO,EAAIA,OAAO,CAAC9D,cAAc,CAAC,CAAE,CACrF;AACA,KAAM,CAAA0E,gBAAgB,CAAGF,KAAK,CAACzE,QAAQ,CAAC4E,GAAG,CAACb,OAAO,EAAIA,OAAO,CAAC9D,cAAc,CAAC,CAAC8C,MAAM,CAAC8B,OAAO,CAAC,CAC9F,KAAM,CAAAC,cAAc,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAACJ,gBAAgB,CAAC,CAAC,CACrD,GAAIA,gBAAgB,CAAC9B,MAAM,GAAKiC,cAAc,CAACjC,MAAM,CAAE,CACrDlC,QAAQ,CAAC,sDAAsDiC,UAAU,CAAG,CAAC,+DAA+D,CAAC,CAC7I8B,eAAe,CAAG,IAAI,CACtB,MACF,CAEA;AACA,IAAK,GAAI,CAAAX,OAAO,GAAI,CAAAU,KAAK,CAACzE,QAAQ,CAAE,CAClC,GAAI+D,OAAO,CAAC9D,cAAc,CAAE,CAC1B,KAAM,CAAA+E,WAAW,CAAG9F,iBAAiB,CAAC+F,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACtF,EAAE,GAAKmE,OAAO,CAAC9D,cAAc,CAAC,CAChF,GAAI+E,WAAW,EAAIG,UAAU,CAACpB,OAAO,CAAC7D,UAAU,CAAC,EAAI8E,WAAW,CAACI,cAAc,EAAIJ,WAAW,CAACK,UAAU,CAAC,CAAE,CAC1G1E,QAAQ,CAAC,kBAAkBqE,WAAW,CAACM,UAAU,EAAIN,WAAW,CAACO,KAAK,6BAA6BP,WAAW,CAACI,cAAc,EAAIJ,WAAW,CAACK,UAAU,oBAAoB,CAAC,CAC5KX,eAAe,CAAG,IAAI,CACtB,MACF,CACF,CACF,CACF,CACF,CAEA,GAAIA,eAAe,CAAE,CACnBrD,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEAA,YAAY,CAAC,IAAI,CAAC,CAClBN,eAAe,CAAC,IAAI,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd;AACA,KAAM,CAAA2E,OAAO,CAAG,EAAE,CAClB9F,YAAY,CAAC+F,OAAO,CAAChB,KAAK,EAAI,CAC5B,GAAIA,KAAK,CAAC1E,iBAAiB,CAAE,CAC3B0E,KAAK,CAACzE,QAAQ,CAACyF,OAAO,CAAC1B,OAAO,EAAI,CAChC,GAAIA,OAAO,CAAC9D,cAAc,CAAE,CAC1BuF,OAAO,CAACrC,IAAI,CAACY,OAAO,CAAC,CACvB,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,KAAM,CAAA2B,OAAO,CAAG,CACdC,YAAY,CAAEvG,WAAW,CACzBE,WAAW,CAAEA,WAAW,CACxBsG,YAAY,CAAEpG,WAAW,CACzBgG,OAAO,CAAEA,OACX,CAAC,CAED,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAvI,KAAK,CAACwI,IAAI,CAAC,oDAAoD,CAAEJ,OAAO,CAAC,CAChG7E,UAAU,CAAC,sCAAsC,CAAC,CAElD;AACA,KAAM,CAAAkF,WAAW,CAAG,GAAI,CAAAhB,GAAG,CAAC,CAAC,CAC7B,KAAM,CAAAiB,UAAU,CAAG,CACjB,GAAGH,QAAQ,CAACvD,IAAI,CAChBkD,OAAO,CAAEK,QAAQ,CAACvD,IAAI,CAACkD,OAAO,CAACZ,GAAG,CAACqB,MAAM,EAAI,KAAAC,qBAAA,CAAAC,sBAAA,CAC3C,KAAM,CAAApC,OAAO,CAAG7E,iBAAiB,CAAC+F,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACtF,EAAE,GAAKqG,MAAM,CAAChG,cAAc,CAAC,CAC3E,GAAI8D,OAAO,SAAPA,OAAO,YAAAmC,qBAAA,CAAPnC,OAAO,CAAEqC,sBAAsB,UAAAF,qBAAA,WAA/BA,qBAAA,CAAiCG,WAAW,CAAE,CAChDN,WAAW,CAACO,GAAG,CAACvC,OAAO,CAACqC,sBAAsB,CAACC,WAAW,CAAC,CAC7D,CACA,MAAO,CACL,GAAGJ,MAAM,CACTV,KAAK,CAAE,CAAAxB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEwB,KAAK,GAAI,SAAS,CAClCD,UAAU,CAAE,CAAAvB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEuB,UAAU,IAAIvB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEwB,KAAK,GAAI,SAAS,CAC9Dc,WAAW,CAAE,CAAAtC,OAAO,SAAPA,OAAO,kBAAAoC,sBAAA,CAAPpC,OAAO,CAAEqC,sBAAsB,UAAAD,sBAAA,iBAA/BA,sBAAA,CAAiCE,WAAW,GAAI,SAC/D,CAAC,CACH,CAAC,CAAC,CACFE,YAAY,CAAEC,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,EAAI,SAAS,CAC7DC,eAAe,CAAEA,eACnB,CAAC,CAEDlF,kBAAkB,CAACuE,UAAU,CAAC,CAE9B;AACAzE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,MAAOiB,GAAG,CAAE,CACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,CAAE8B,GAAG,CAAC,CACpD,GAAIA,GAAG,CAACqD,QAAQ,EAAIrD,GAAG,CAACqD,QAAQ,CAACvD,IAAI,CAAE,CACrC;AACA,KAAM,CAAAsE,YAAY,CAAG,MAAO,CAAApE,GAAG,CAACqD,QAAQ,CAACvD,IAAI,GAAK,QAAQ,CACtDE,GAAG,CAACqD,QAAQ,CAACvD,IAAI,CACjB,4DAA4D,CAChE3B,QAAQ,CAACiG,YAAY,CAAC,CACxB,CAAC,IAAM,CACLjG,QAAQ,CAAC,oDAAoD,CAAC,CAChE,CACF,CAAC,OAAS,CACRI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAA8F,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI,CAACrF,eAAe,CAAE,OAEtB,GAAI,CACF;AACA,KAAM,CAAAsF,GAAG,CAAG,GAAI,CAAAtI,KAAK,CAAC,CACpBuI,WAAW,CAAE,UAAU,CACvBC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,eAAe,CAAG,EAAE,CAC1B,KAAM,CAAAC,cAAc,CAAG,EAAE,CACzB,KAAM,CAAAC,aAAa,CAAG,CAAC,CAEvB;AACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC,CAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,CAAE,GAAG,CAAE,EAAE,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAExD;AACAX,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACU,IAAI,CAAC,qBAAqB,CAAE,EAAE,CAAE,EAAE,CAAC,CAEvCV,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAElC;AACAT,GAAG,CAACY,IAAI,CAAC,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,EAAE,CAAC,CAAE;AAE3B,KAAM,CAAAC,eAAe,CAAG,CACtB,CAAC,WAAW,CAAEnG,eAAe,CAAC5B,EAAE,CAACgI,QAAQ,CAAC,CAAC,CAAC,CAC5C,CAAC,cAAc,CAAEpG,eAAe,CAACoE,YAAY,CAAC,CAC9C,CAAC,cAAc,CAAEpE,eAAe,CAAC+E,YAAY,CAAC,CAC9C,CAAC,cAAc,CAAE,GAAI,CAAA1G,IAAI,CAAC2B,eAAe,CAACmE,YAAY,CAAC,CAACkC,kBAAkB,CAAC,CAAC,CAAC,CAC7E,CAAC,aAAa,CAAErG,eAAe,CAAClC,WAAW,EAAI,KAAK,CAAC,CACtD,CAED,GAAI,CAAAwI,IAAI,CAAG,EAAE,CACbH,eAAe,CAAClC,OAAO,CAAEsC,GAAG,EAAK,CAC/BjB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,CAAE,EAAE,CAAED,IAAI,CAAC,CAC1BhB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,CAAE,EAAE,CAAED,IAAI,CAAC,CAC1BA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACY,IAAI,CAAC,EAAE,CAAEI,IAAI,CAAG,CAAC,CAAE,GAAG,CAAEA,IAAI,CAAG,CAAC,CAAC,CAAE;AACzC,CAAC,CAAC,CAEF;AACAhB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,CAAE,EAAE,CAAEM,IAAI,CAAG,EAAE,CAAC,CAEzC;AACA,KAAM,CAAAE,OAAO,CAAG,CAAC,QAAQ,CAAE,OAAO,CAAE,YAAY,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,OAAO,CAAC,CACrF,KAAM,CAAAC,SAAS,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEtD;AACA,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,GAAI,CAAAC,UAAU,CAAG,EAAE,CACnBF,SAAS,CAACxC,OAAO,CAAC2C,KAAK,EAAI,CACzBF,YAAY,CAAC/E,IAAI,CAACgF,UAAU,CAAC,CAC7BA,UAAU,EAAIC,KAAK,CACrB,CAAC,CAAC,CAEF;AACAN,IAAI,EAAI,EAAE,CACVhB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAEhC;AACAT,GAAG,CAACuB,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/BvB,GAAG,CAACwB,IAAI,CAAC,EAAE,CAAER,IAAI,CAAG,CAAC,CAAE,GAAG,CAAE,CAAC,CAAE,GAAG,CAAC,CAEnC;AACAE,OAAO,CAACvC,OAAO,CAAC,CAAC8C,MAAM,CAAEC,KAAK,GAAK,CACjC1B,GAAG,CAACU,IAAI,CAACe,MAAM,CAAEL,YAAY,CAACM,KAAK,CAAC,CAAG,CAAC,CAAEV,IAAI,CAAC,CACjD,CAAC,CAAC,CAEF;AACAA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACY,IAAI,CAAC,EAAE,CAAEI,IAAI,CAAE,GAAG,CAAEA,IAAI,CAAC,CAE7B;AACAhB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClC/F,eAAe,CAACgE,OAAO,CAACC,OAAO,CAAEQ,MAAM,EAAK,KAAAwC,UAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAC,SAAA,CAAAC,UAAA,CAC1Cf,IAAI,EAAI,CAAC,CAET;AACA,KAAM,CAAAgB,KAAK,CAAGC,QAAQ,CAAC9C,MAAM,CAAC9F,EAAE,EAAI,CAAC,CAAC,CACxB4I,QAAQ,CAAC9C,MAAM,CAAC7F,CAAC,EAAI,CAAC,CAAC,CACvB2I,QAAQ,CAAC9C,MAAM,CAAC5F,CAAC,EAAI,CAAC,CAAC,CACvB0I,QAAQ,CAAC9C,MAAM,CAAC3F,CAAC,EAAI,CAAC,CAAC,CACvByI,QAAQ,CAAC9C,MAAM,CAAC1F,EAAE,EAAI,CAAC,CAAC,CAEtC;AACAuG,GAAG,CAACU,IAAI,CAACvB,MAAM,CAACI,WAAW,EAAI,SAAS,CAAE6B,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACpEhB,GAAG,CAACU,IAAI,CAACvB,MAAM,CAACX,UAAU,EAAIW,MAAM,CAACV,KAAK,CAAE2C,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACtEhB,GAAG,CAACU,IAAI,CAAC,GAAGvB,MAAM,CAAC/F,UAAU,QAAQ,CAAEgI,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAiB,UAAA,CAAAxC,MAAM,CAAC9F,EAAE,UAAAsI,UAAA,iBAATA,UAAA,CAAWb,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAkB,SAAA,CAAAzC,MAAM,CAAC7F,CAAC,UAAAsI,SAAA,iBAARA,SAAA,CAAUd,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAmB,SAAA,CAAA1C,MAAM,CAAC5F,CAAC,UAAAsI,SAAA,iBAARA,SAAA,CAAUf,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAoB,SAAA,CAAA3C,MAAM,CAAC3F,CAAC,UAAAsI,SAAA,iBAARA,SAAA,CAAUhB,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAqB,UAAA,CAAA5C,MAAM,CAAC1F,EAAE,UAAAsI,UAAA,iBAATA,UAAA,CAAWjB,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjEhB,GAAG,CAACU,IAAI,CAACsB,KAAK,CAAClB,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAErD;AACAA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACY,IAAI,CAAC,EAAE,CAAEI,IAAI,CAAE,GAAG,CAAEA,IAAI,CAAC,CAC/B,CAAC,CAAC,CAEF;AACAA,IAAI,EAAI,CAAC,CACThB,GAAG,CAACuB,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/BvB,GAAG,CAACwB,IAAI,CAAC,EAAE,CAAER,IAAI,CAAG,CAAC,CAAE,GAAG,CAAE,CAAC,CAAE,GAAG,CAAC,CAEnChB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,CAAEU,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAC5ChB,GAAG,CAACU,IAAI,CAAC,EAAE,CAAEU,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACvChB,GAAG,CAACU,IAAI,CAAC,GAAGhG,eAAe,CAACmF,eAAe,CAACzG,UAAU,CAAC8I,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAEd,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACrGhB,GAAG,CAACU,IAAI,CAAChG,eAAe,CAACmF,eAAe,CAACxG,EAAE,CAACyH,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAClFhB,GAAG,CAACU,IAAI,CAAChG,eAAe,CAACmF,eAAe,CAACvG,CAAC,CAACwH,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjFhB,GAAG,CAACU,IAAI,CAAChG,eAAe,CAACmF,eAAe,CAACtG,CAAC,CAACuH,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjFhB,GAAG,CAACU,IAAI,CAAChG,eAAe,CAACmF,eAAe,CAACrG,CAAC,CAACsH,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CACjFhB,GAAG,CAACU,IAAI,CAAChG,eAAe,CAACmF,eAAe,CAACpG,EAAE,CAACqH,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAClFhB,GAAG,CAACU,IAAI,CAAChG,eAAe,CAACmF,eAAe,CAACmC,KAAK,CAAClB,QAAQ,CAAC,CAAC,CAAEM,YAAY,CAAC,CAAC,CAAC,CAAG,CAAC,CAAEJ,IAAI,CAAC,CAErF;AACAhB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC,CAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,GAAI,CAAA3H,IAAI,CAAC,CAAC,CAACoJ,cAAc,CAAC,CAAC,EAAE,CAAE,GAAG,CAAE,GAAG,CAAE,CAAExB,KAAK,CAAE,QAAS,CAAC,CAAC,CACvFX,GAAG,CAACU,IAAI,CAAC,mCAAmC,CAAE,GAAG,CAAE,GAAG,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAE5E;AACAX,GAAG,CAACoC,IAAI,CAAC,kBAAkB1H,eAAe,CAAC5B,EAAE,IAAI4B,eAAe,CAACoE,YAAY,MAAM,CAAC,CAEpF;AACArE,eAAe,CAAC,KAAK,CAAC,CACtBlC,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,eAAe,CAAC,CAAC,CACfC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CAAC,CAAC,CACHc,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,MAAOX,KAAK,CAAE,CACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CC,QAAQ,CAAC,2CAA2C,CAAC,CACrDY,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAA4H,gBAAgB,CAAGA,CAAA,GAAM,CAC7B5H,eAAe,CAAC,KAAK,CAAC,CACtB;AACAlC,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,cAAc,CAAC,EAAE,CAAC,CAClBE,eAAe,CAAC,CAAC,CACfC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,iBAAiB,CAAE,EAAE,CACrBC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnF,CAAC,CAAC,CAAC,CACHc,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAID;AACA,KAAM,CAAAsF,eAAe,CAAGjH,YAAY,CAAC0J,MAAM,CAAC,CAACC,GAAG,CAAE5E,KAAK,GAAK,CAC1DA,KAAK,CAACzE,QAAQ,CAACyF,OAAO,CAAC1B,OAAO,EAAI,CAChC,GAAIA,OAAO,CAAC9D,cAAc,CAAE,CAC1BoJ,GAAG,CAAClJ,EAAE,EAAI4I,QAAQ,CAAChF,OAAO,CAAC5D,EAAE,CAAC,EAAI,CAAC,CACnCkJ,GAAG,CAACjJ,CAAC,EAAI2I,QAAQ,CAAChF,OAAO,CAAC3D,CAAC,CAAC,EAAI,CAAC,CACjCiJ,GAAG,CAAChJ,CAAC,EAAI0I,QAAQ,CAAChF,OAAO,CAAC1D,CAAC,CAAC,EAAI,CAAC,CACjCgJ,GAAG,CAAC/I,CAAC,EAAIyI,QAAQ,CAAChF,OAAO,CAACzD,CAAC,CAAC,EAAI,CAAC,CACjC+I,GAAG,CAAC9I,EAAE,EAAIwI,QAAQ,CAAChF,OAAO,CAACxD,EAAE,CAAC,EAAI,CAAC,CACnC8I,GAAG,CAACP,KAAK,EAAI,CAACC,QAAQ,CAAChF,OAAO,CAAC5D,EAAE,CAAC,EAAI,CAAC,GAC1B4I,QAAQ,CAAChF,OAAO,CAAC3D,CAAC,CAAC,EAAI,CAAC,CAAC,EACzB2I,QAAQ,CAAChF,OAAO,CAAC1D,CAAC,CAAC,EAAI,CAAC,CAAC,EACzB0I,QAAQ,CAAChF,OAAO,CAACzD,CAAC,CAAC,EAAI,CAAC,CAAC,EACzByI,QAAQ,CAAChF,OAAO,CAACxD,EAAE,CAAC,EAAI,CAAC,CAAC,CACvC8I,GAAG,CAACnJ,UAAU,EAAIiF,UAAU,CAACpB,OAAO,CAAC7D,UAAU,CAAC,EAAI,CAAC,CACvD,CACF,CAAC,CAAC,CACF,MAAO,CAAAmJ,GAAG,CACZ,CAAC,CAAE,CAAElJ,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEuI,KAAK,CAAE,CAAC,CAAE5I,UAAU,CAAE,CAAE,CAAC,CAAC,CAE/D,mBACEtB,KAAA,CAAAE,SAAA,EAAAwK,QAAA,eACE5K,IAAA,CAACnB,eAAe,GAAE,CAAC,cACnBqB,KAAA,QACE2K,KAAK,CAAE,CACLC,UAAU,CAAExI,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5CoH,KAAK,CAAE,eAAepH,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDyI,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAJ,QAAA,eAEF1K,KAAA,OAAI+K,SAAS,CAAC,MAAM,CAAAL,QAAA,eAClB5K,IAAA,CAACR,UAAU,EAACyL,SAAS,CAAC,MAAM,CAAE,CAAC,qBAEjC,EAAI,CAAC,CAEJ/I,OAAO,eACNhC,KAAA,CAACb,KAAK,EAACgG,OAAO,CAAC,SAAS,CAAC4F,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eAC5D5K,IAAA,CAACL,cAAc,EAACsL,SAAS,CAAC,MAAM,CAACC,IAAI,CAAE,EAAG,CAAE,CAAC,CAC5ChJ,OAAO,EACH,CACR,CAEAF,KAAK,eACJ9B,KAAA,CAACb,KAAK,EAACgG,OAAO,CAAC,QAAQ,CAAC4F,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eAC3D5K,IAAA,CAACJ,qBAAqB,EAACqL,SAAS,CAAC,MAAM,CAACC,IAAI,CAAE,EAAG,CAAE,CAAC,CACnDlJ,KAAK,EACD,CACR,cAEDhC,IAAA,CAACjB,IAAI,EAACkM,SAAS,CAAC,gBAAgB,CAACJ,KAAK,CAAE,CAAEM,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAR,QAAA,cAC3F5K,IAAA,CAACjB,IAAI,CAACsM,IAAI,EAAAT,QAAA,cACR1K,KAAA,CAAClB,IAAI,EAACsM,UAAU,MAAC5I,SAAS,CAAEA,SAAU,CAAC6I,QAAQ,CAAEhG,YAAa,CAAAqF,QAAA,eAC5D5K,IAAA,CAACd,GAAG,EAAA0L,QAAA,cACF5K,IAAA,CAACb,GAAG,EAACqM,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1B5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAAAd,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtD5K,IAAA,CAAChB,IAAI,CAAC2M,OAAO,EACXC,IAAI,CAAC,MAAM,CACX5G,KAAK,CAAElE,WAAY,CACnB+K,QAAQ,CAAGrG,CAAC,EAAKzE,cAAc,CAACyE,CAAC,CAACsG,MAAM,CAAC9G,KAAK,CAAE,CAChD+G,WAAW,CAAC,oBAAoB,CAChCC,QAAQ,MACT,CAAC,cACFhM,IAAA,CAAChB,IAAI,CAAC2M,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAAhB,QAAA,CAAC,gCAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,CACH,CAAC,cAEN1K,KAAA,CAAChB,GAAG,EAAA0L,QAAA,eACF5K,IAAA,CAACb,GAAG,EAACqM,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1B5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAAAd,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtD5K,IAAA,CAAChB,IAAI,CAAC2M,OAAO,EACXC,IAAI,CAAC,MAAM,CACX5G,KAAK,CAAEtE,WAAY,CACnBmL,QAAQ,CAAGrG,CAAC,EAAK7E,cAAc,CAAC6E,CAAC,CAACsG,MAAM,CAAC9G,KAAK,CAAE,CAChDgH,QAAQ,MACT,CAAC,cACFhM,IAAA,CAAChB,IAAI,CAAC2M,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAAhB,QAAA,CAAC,+BAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,cACN5K,IAAA,CAACb,GAAG,EAACqM,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1B5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAAAd,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAY,CAAC,cACrD5K,IAAA,CAAChB,IAAI,CAAC2M,OAAO,EACXO,EAAE,CAAC,UAAU,CACbC,IAAI,CAAE,CAAE,CACRnH,KAAK,CAAEpE,WAAY,CACnBiL,QAAQ,CAAGrG,CAAC,EAAK3E,cAAc,CAAC2E,CAAC,CAACsG,MAAM,CAAC9G,KAAK,CAAE,CAChD+G,WAAW,CAAC,4CAA4C,CACzD,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,cAEN7L,KAAA,QAAK+K,SAAS,CAAC,gFAAgF,CAAAL,QAAA,eAC7F5K,IAAA,OAAIiL,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,gBAAc,CAAI,CAAC,cACxC1K,KAAA,CAACjB,MAAM,EACLoG,OAAO,CAAC,SAAS,CACjB6F,IAAI,CAAC,IAAI,CACTkB,OAAO,CAAEpI,cAAe,CACxBqI,QAAQ,CAAEjK,YAAa,CAAAwI,QAAA,eAEvB5K,IAAA,CAACP,MAAM,EAACwL,SAAS,CAAC,MAAM,CAAE,CAAC,yBAC7B,EAAQ,CAAC,EACN,CAAC,CAELjK,YAAY,CAACkF,GAAG,CAAC,CAACH,KAAK,CAAE7B,UAAU,GAAK,CACvC;AACA,KAAM,CAAAoI,aAAa,CAAGvG,KAAK,CAAC1E,iBAAiB,CACzCb,iBAAiB,CAAC6D,MAAM,CAACmC,CAAC,EAAIA,CAAC,CAACnF,iBAAiB,GAAKgJ,QAAQ,CAACtE,KAAK,CAAC1E,iBAAiB,CAAC,CAAC,CACxF,EAAE,CAEN,mBACEnB,KAAA,CAACnB,IAAI,EAAgBkM,SAAS,CAAC,aAAa,CAAAL,QAAA,eAC1C1K,KAAA,CAACnB,IAAI,CAACwN,MAAM,EAACtB,SAAS,CAAC,yEAAyE,CAAAL,QAAA,eAC9F1K,KAAA,OAAI+K,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,qBAAmB,CAAC1G,UAAU,CAAG,CAAC,EAAK,CAAC,cAC7DhE,KAAA,CAACjB,MAAM,EACLoG,OAAO,CAAC,eAAe,CACvB6F,IAAI,CAAC,IAAI,CACTkB,OAAO,CAAEA,CAAA,GAAMnI,iBAAiB,CAACC,UAAU,CAAE,CAC7CmI,QAAQ,CAAErL,YAAY,CAACmD,MAAM,GAAK,CAAE,CAAAyG,QAAA,eAEpC5K,IAAA,CAACN,OAAO,EAACuL,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CAAC,EACE,CAAC,cACd/K,KAAA,CAACnB,IAAI,CAACsM,IAAI,EAAAT,QAAA,eAER5K,IAAA,CAACd,GAAG,EAAC+L,SAAS,CAAC,MAAM,CAAAL,QAAA,cACnB5K,IAAA,CAACb,GAAG,EAACqM,EAAE,CAAE,EAAG,CAAAZ,QAAA,cACV1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAAAb,QAAA,eACT5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAAAd,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,CAAY,CAAC,CACjE9I,eAAe,cACd5B,KAAA,QAAK+K,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxC5K,IAAA,CAACZ,OAAO,EAACoN,SAAS,CAAC,QAAQ,CAACtB,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDjL,IAAA,SAAA4K,QAAA,CAAM,+BAA6B,CAAM,CAAC,EACvC,CAAC,cAEN1K,KAAA,CAAClB,IAAI,CAACyN,MAAM,EACVzH,KAAK,CAAEe,KAAK,CAAC1E,iBAAkB,CAC/BwK,QAAQ,CAAGrG,CAAC,EAAKZ,4BAA4B,CAACV,UAAU,CAAEsB,CAAC,CAACsG,MAAM,CAAC9G,KAAK,CAAE,CAC1EgH,QAAQ,MAAApB,QAAA,eAER5K,IAAA,WAAQgF,KAAK,CAAC,EAAE,CAAA4F,QAAA,CAAC,0BAAwB,CAAQ,CAAC,CACjDtK,iBAAiB,CAAC4F,GAAG,CAAEwG,EAAE,eACxBxM,KAAA,WAAoB8E,KAAK,CAAE0H,EAAE,CAACxL,EAAG,CAAA0J,QAAA,EAC9B8B,EAAE,CAAC/E,WAAW,CAAC,KAAG,CAAC+E,EAAE,CAACC,aAAa,EAAI,kBAAkB,GAD/CD,EAAE,CAACxL,EAER,CACT,CAAC,EACS,CACd,EACS,CAAC,CACV,CAAC,CACH,CAAC,CAGL6E,KAAK,CAAC1E,iBAAiB,eACtBnB,KAAA,CAAAE,SAAA,EAAAwK,QAAA,eACE1K,KAAA,QAAK+K,SAAS,CAAC,wDAAwD,CAAAL,QAAA,eACrE5K,IAAA,OAAIiL,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,iBAAe,CAAI,CAAC,cACzC1K,KAAA,CAACjB,MAAM,EACLoG,OAAO,CAAC,iBAAiB,CACzB6F,IAAI,CAAC,IAAI,CACTkB,OAAO,CAAEA,CAAA,GAAM5H,iBAAiB,CAACN,UAAU,CAAE,CAC7CmI,QAAQ,CAAEjK,YAAa,CAAAwI,QAAA,eAEvB5K,IAAA,CAACP,MAAM,EAACwL,SAAS,CAAC,MAAM,CAAE,CAAC,eAC7B,EAAQ,CAAC,EACN,CAAC,CAELlF,KAAK,CAACzE,QAAQ,CAAC4E,GAAG,CAAC,CAACb,OAAO,CAAEV,YAAY,GAAK,CAC7C,KAAM,CAAAiI,cAAc,CAAGpM,iBAAiB,CAAC+F,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACtF,EAAE,GAAKmE,OAAO,CAAC9D,cAAc,CAAC,CAEnF,mBACEvB,IAAA,CAACjB,IAAI,EAAoBkM,SAAS,CAAC,mBAAmB,CAAAL,QAAA,cACpD1K,KAAA,CAACnB,IAAI,CAACsM,IAAI,EAAAT,QAAA,eACR1K,KAAA,QAAK+K,SAAS,CAAC,wDAAwD,CAAAL,QAAA,eACrE1K,KAAA,QAAK+K,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxC1K,KAAA,OAAI+K,SAAS,CAAC,WAAW,CAAAL,QAAA,EAAC,WAAS,CAACjG,YAAY,CAAG,CAAC,EAAK,CAAC,CACzDiI,cAAc,eACb1M,KAAA,QAAK+K,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxC5K,IAAA,QACE6K,KAAK,CAAE,CACLnB,KAAK,CAAE,MAAM,CACbmD,MAAM,CAAE,MAAM,CACd1B,eAAe,CAAEyB,cAAc,CAAC/F,KAAK,CACrCiG,MAAM,CAAE,gBAAgB,CACxB1B,YAAY,CAAE,KAAK,CACnB2B,WAAW,CAAE,KACf,CAAE,CACFC,KAAK,CAAE,UAAUJ,cAAc,CAAC/F,KAAK,EAAG,CACzC,CAAC,cACF7G,IAAA,UAAOiL,SAAS,CAAC,YAAY,CAAAL,QAAA,CAC1BgC,cAAc,CAAChG,UAAU,EAAIgG,cAAc,CAAC/F,KAAK,CAC7C,CAAC,EACL,CACN,EACE,CAAC,cACN3G,KAAA,CAACjB,MAAM,EACLoG,OAAO,CAAC,gBAAgB,CACxB6F,IAAI,CAAC,IAAI,CACTkB,OAAO,CAAEA,CAAA,GAAM1H,sBAAsB,CAACR,UAAU,CAAES,YAAY,CAAE,CAChE0H,QAAQ,CAAEtG,KAAK,CAACzE,QAAQ,CAAC6C,MAAM,GAAK,CAAE,CAAAyG,QAAA,eAEtC5K,IAAA,CAACN,OAAO,EAACuL,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CAAC,EACN,CAAC,cAEN/K,KAAA,CAAChB,GAAG,EAAA0L,QAAA,eACF5K,IAAA,CAACb,GAAG,EAACqM,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1B5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAAAd,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAY,CAAC,cAChE5K,IAAA,CAAClB,oBAAoB,EACnBwC,QAAQ,CAAEgL,aAAc,CACxBW,aAAa,CAAE5H,OAAO,CAAC9D,cAAe,CACtC2L,QAAQ,CAAGlI,KAAK,EAAKF,mBAAmB,CAACZ,UAAU,CAAES,YAAY,CAAE,gBAAgB,CAAEK,KAAK,CAAE,CAC5F+G,WAAW,CAAC,sBAAsB,CAClCoB,mBAAmB,CAAElI,wBAAyB,CAC9Cf,UAAU,CAAEA,UAAW,CACvBS,YAAY,CAAEA,YAAa,CAC5B,CAAC,EACQ,CAAC,CACV,CAAC,cACN3E,IAAA,CAACb,GAAG,EAACqM,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACT1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAACR,SAAS,CAAC,MAAM,CAAAL,QAAA,eAC1B1K,KAAA,QAAK+K,SAAS,CAAC,wDAAwD,CAAAL,QAAA,eACrE5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAL,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,CACpEgC,cAAc,eACb1M,KAAA,QAAK+K,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxC5K,IAAA,QACE6K,KAAK,CAAE,CACLnB,KAAK,CAAE,MAAM,CACbmD,MAAM,CAAE,MAAM,CACd1B,eAAe,CAAEyB,cAAc,CAAC/F,KAAK,CACrCiG,MAAM,CAAE,gBAAgB,CACxB1B,YAAY,CAAE,KAAK,CACnB2B,WAAW,CAAE,KACf,CAAE,CACFC,KAAK,CAAE,UAAUJ,cAAc,CAAChG,UAAU,EAAIgG,cAAc,CAAC/F,KAAK,EAAG,CACtE,CAAC,cACF3G,KAAA,SAAM+K,SAAS,CAAExE,UAAU,CAACpB,OAAO,CAAC7D,UAAU,CAAC,EAAIoL,cAAc,CAAClG,cAAc,EAAIkG,cAAc,CAACjG,UAAU,CAAC,CAAG,mBAAmB,CAAG,oBAAqB,CAAAiE,QAAA,EACzJgC,cAAc,CAAChG,UAAU,EAAIgG,cAAc,CAAC/F,KAAK,CAAC,gBAAc,CAAC+F,cAAc,CAAClG,cAAc,EAAIkG,cAAc,CAACjG,UAAU,CAAC,QAC/H,EAAM,CAAC,EACJ,CACN,EACE,CAAC,cACN3G,IAAA,CAAChB,IAAI,CAAC2M,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbwB,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPrI,KAAK,CAAEK,OAAO,CAAC7D,UAAW,CAC1BqK,QAAQ,CAAGrG,CAAC,EAAKV,mBAAmB,CAACZ,UAAU,CAAES,YAAY,CAAE,YAAY,CAAEa,CAAC,CAACsG,MAAM,CAAC9G,KAAK,CAAE,CAC7FgH,QAAQ,MACRD,WAAW,CAAC,kBAAkB,CAC9BuB,SAAS,CAAEV,cAAc,EAAInG,UAAU,CAACpB,OAAO,CAAC7D,UAAU,CAAC,EAAIoL,cAAc,CAAClG,cAAc,EAAIkG,cAAc,CAACjG,UAAU,CAAE,CAC3HsE,SAAS,CAAE2B,cAAc,EAAInG,UAAU,CAACpB,OAAO,CAAC7D,UAAU,CAAC,EAAIoL,cAAc,CAAClG,cAAc,EAAIkG,cAAc,CAACjG,UAAU,CAAC,CAAG,eAAe,CAAG,EAAG,CAClJkE,KAAK,CAAE,CAAEgC,MAAM,CAAE,MAAO,CAAE,CAC3B,CAAC,cACF7M,IAAA,CAAChB,IAAI,CAAC2M,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAAhB,QAAA,CAClCgC,cAAc,EAAInG,UAAU,CAACpB,OAAO,CAAC7D,UAAU,CAAC,EAAIoL,cAAc,CAAClG,cAAc,EAAIkG,cAAc,CAACjG,UAAU,CAAC,CAC5G,4BAA4BiG,cAAc,CAAClG,cAAc,EAAIkG,cAAc,CAACjG,UAAU,mBAAmB,CACzG,gCAAgC,CACf,CAAC,EACd,CAAC,CACV,CAAC,EACH,CAAC,cAEN3G,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAL,QAAA,cAAC5K,IAAA,WAAA4K,QAAA,CAAQ,iBAAe,CAAQ,CAAC,CAAY,CAAC,cAC1E1K,KAAA,CAAChB,GAAG,EAAA0L,QAAA,EACD,CAAC,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAAC1E,GAAG,CAAC,CAACgF,IAAI,CAAEqC,SAAS,GAAK,CACpD,KAAM,CAAAC,OAAO,CAAGtC,IAAI,CAACuC,WAAW,CAAC,CAAC,CAClC,mBACEzN,IAAA,CAACb,GAAG,EAAiBsC,EAAE,CAAE,CAAE,CAACiM,EAAE,CAAE,CAAE,CAAClC,EAAE,CAAE,CAAE,CAACP,SAAS,CAAC,MAAM,CAAAL,QAAA,cACxD1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAAAb,QAAA,eACT5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAACT,SAAS,CAAC,qBAAqB,CAAAL,QAAA,CAAEM,IAAI,CAAa,CAAC,cAC/DlL,IAAA,CAAChB,IAAI,CAAC2M,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbyB,GAAG,CAAC,GAAG,CACPrI,KAAK,CAAEK,OAAO,CAACmI,OAAO,CAAE,CACxB3B,QAAQ,CAAGrG,CAAC,EAAK,CACf,KAAM,CAAAmI,GAAG,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAExD,QAAQ,CAAC7E,CAAC,CAACsG,MAAM,CAAC9G,KAAK,EAAI,CAAC,CAAC,CAAC,CACtDF,mBAAmB,CAACZ,UAAU,CAAES,YAAY,CAAE6I,OAAO,CAAEG,GAAG,CAAC,CAC7D,CAAE,CACF1C,SAAS,CAAC,aAAa,CACxB,CAAC,EACQ,CAAC,EAbLsC,SAcL,CAAC,CAEV,CAAC,CAAC,cACFvN,IAAA,CAACb,GAAG,EAACsC,EAAE,CAAE,CAAE,CAACiM,EAAE,CAAE,CAAE,CAAClC,EAAE,CAAE,CAAE,CAACP,SAAS,CAAC,MAAM,CAAAL,QAAA,cACxC1K,KAAA,CAAClB,IAAI,CAACyM,KAAK,EAAAb,QAAA,eACT5K,IAAA,CAAChB,IAAI,CAAC0M,KAAK,EAACT,SAAS,CAAC,qBAAqB,CAAAL,QAAA,CAAC,OAAK,CAAY,CAAC,cAC9D5K,IAAA,QAAKiL,SAAS,CAAC,mCAAmC,CAAAL,QAAA,CAC/CP,QAAQ,CAAChF,OAAO,CAAC5D,EAAE,EAAI,CAAC,CAAC,CACzB4I,QAAQ,CAAChF,OAAO,CAAC3D,CAAC,EAAI,CAAC,CAAC,CACxB2I,QAAQ,CAAChF,OAAO,CAAC1D,CAAC,EAAI,CAAC,CAAC,CACxB0I,QAAQ,CAAChF,OAAO,CAACzD,CAAC,EAAI,CAAC,CAAC,CACxByI,QAAQ,CAAChF,OAAO,CAACxD,EAAE,EAAI,CAAC,CAAC,CACvB,CAAC,EACI,CAAC,CACV,CAAC,EACH,CAAC,EACG,CAAC,EAhIH8C,YAiIL,CAAC,CAEX,CAAC,CAAC,EACF,CACH,EACQ,CAAC,GAlMHoB,KAAK,CAAC7E,EAmMX,CAAC,CAEX,CAAC,CAAC,cAEFlB,IAAA,QAAKiL,SAAS,CAAC,iCAAiC,CAAAL,QAAA,cAC9C5K,IAAA,CAACjB,IAAI,EAACkM,SAAS,CAAC,UAAU,CAACJ,KAAK,CAAE,CAAEM,eAAe,CAAE,SAAU,CAAE,CAAAP,QAAA,cAC/D5K,IAAA,CAACjB,IAAI,CAACsM,IAAI,EAACJ,SAAS,CAAC,MAAM,CAAAL,QAAA,cACzB1K,KAAA,QAAK+K,SAAS,CAAC,oBAAoB,CAAAL,QAAA,eACjC1K,KAAA,QAAK+K,SAAS,CAAC,gCAAgC,CAAAL,QAAA,eAC7C5K,IAAA,WAAQiL,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cACnD1K,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,SAAS,CAAC7C,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,MAAI,CAAC3C,eAAe,CAACxG,EAAE,EAAQ,CAAC,cACrEvB,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,SAAS,CAAC7C,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,KAAG,CAAC3C,eAAe,CAACvG,CAAC,EAAQ,CAAC,cACnExB,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,SAAS,CAAC7C,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,KAAG,CAAC3C,eAAe,CAACtG,CAAC,EAAQ,CAAC,cACnEzB,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,SAAS,CAAC7C,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,KAAG,CAAC3C,eAAe,CAACrG,CAAC,EAAQ,CAAC,cACnE1B,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,SAAS,CAAC7C,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,MAAI,CAAC3C,eAAe,CAACpG,EAAE,EAAQ,CAAC,cACrE3B,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,SAAS,CAAC7C,SAAS,CAAC,MAAM,CAAAL,QAAA,EAAC,SAAO,CAAC3C,eAAe,CAACmC,KAAK,EAAQ,CAAC,EACxE,CAAC,cACNlK,KAAA,QAAK+K,SAAS,CAAC,2BAA2B,CAAAL,QAAA,eACxC5K,IAAA,WAAQiL,SAAS,CAAC,MAAM,CAAAL,QAAA,CAAC,mBAAiB,CAAQ,CAAC,cACnD1K,KAAA,CAACZ,KAAK,EAACwO,EAAE,CAAC,MAAM,CAAAlD,QAAA,EAAE3C,eAAe,CAACzG,UAAU,CAAC8I,OAAO,CAAC,CAAC,CAAC,CAAC,QAAM,EAAO,CAAC,EACnE,CAAC,EACH,CAAC,CACG,CAAC,CACR,CAAC,CACJ,CAAC,cAENtK,IAAA,QAAKiL,SAAS,CAAC,oCAAoC,CAAAL,QAAA,cACjD5K,IAAA,CAACf,MAAM,EACL2M,IAAI,CAAC,QAAQ,CACbvG,OAAO,CAAC,SAAS,CACjB6F,IAAI,CAAC,IAAI,CACTmB,QAAQ,CAAEjK,YAAa,CACvB6I,SAAS,CAAC,MAAM,CAAAL,QAAA,CAEfxI,YAAY,cACXlC,KAAA,CAAAE,SAAA,EAAAwK,QAAA,eACE5K,IAAA,CAACZ,OAAO,EAAC8M,EAAE,CAAC,MAAM,CAACM,SAAS,CAAC,QAAQ,CAACtB,IAAI,CAAC,IAAI,CAAC6C,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAC9C,SAAS,CAAC,MAAM,CAAE,CAAC,gBAEtG,EAAE,CAAC,CAEH,uBACD,CACK,CAAC,CACN,CAAC,EACF,CAAC,CACE,CAAC,CACR,CAAC,EACJ,CAAC,cAGN/K,KAAA,CAACX,KAAK,EAACyO,IAAI,CAAEpL,YAAa,CAACqL,MAAM,CAAExD,gBAAiB,CAACS,IAAI,CAAC,IAAI,CAACgD,QAAQ,MAAAtD,QAAA,eACrE5K,IAAA,CAACT,KAAK,CAACgN,MAAM,EAAC4B,WAAW,MAAAvD,QAAA,cACvB5K,IAAA,CAACT,KAAK,CAAC6O,KAAK,EAAAxD,QAAA,CAAC,6BAA2B,CAAa,CAAC,CAC1C,CAAC,cACf1K,KAAA,CAACX,KAAK,CAAC8L,IAAI,EAAAT,QAAA,eACT5K,IAAA,MAAA4K,QAAA,CAAG,2DAAyD,CAAG,CAAC,CAE/D9H,eAAe,eACd5C,KAAA,QAAK+K,SAAS,CAAC,MAAM,CAAAL,QAAA,eACnB5K,IAAA,MAAA4K,QAAA,CAAG,iDAA+C,CAAG,CAAC,cACtD1K,KAAA,OAAA0K,QAAA,eACE1K,KAAA,OAAA0K,QAAA,eAAI5K,IAAA,WAAA4K,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAC9H,eAAe,CAACoE,YAAY,EAAK,CAAC,cACtEhH,KAAA,OAAA0K,QAAA,eAAI5K,IAAA,WAAA4K,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAC9H,eAAe,CAAC6E,WAAW,EAAK,CAAC,cAC/DzH,KAAA,OAAA0K,QAAA,eAAI5K,IAAA,WAAA4K,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAAzJ,IAAI,CAAC2B,eAAe,CAACmE,YAAY,CAAC,CAACkC,kBAAkB,CAAC,CAAC,EAAK,CAAC,cACrGjJ,KAAA,OAAA0K,QAAA,eAAI5K,IAAA,WAAA4K,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,QAAK,CAAC9H,eAAe,CAACmF,eAAe,CAACxG,EAAE,CAAC,OAC1E,CAACqB,eAAe,CAACmF,eAAe,CAACvG,CAAC,CAAC,OACnC,CAACoB,eAAe,CAACmF,eAAe,CAACtG,CAAC,CAAC,OACnC,CAACmB,eAAe,CAACmF,eAAe,CAACrG,CAAC,CAAC,QAClC,CAACkB,eAAe,CAACmF,eAAe,CAACpG,EAAE,EAAK,CAAC,cAC/C3B,KAAA,OAAA0K,QAAA,eAAI5K,IAAA,WAAA4K,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAAC9H,eAAe,CAACmF,eAAe,CAACmC,KAAK,EAAK,CAAC,cAC9ElK,KAAA,OAAA0K,QAAA,eAAI5K,IAAA,WAAA4K,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,IAAC,CAAC9H,eAAe,CAACmF,eAAe,CAACzG,UAAU,CAAC8I,OAAO,CAAC,CAAC,CAAC,CAAC,QAAM,EAAI,CAAC,EACvG,CAAC,EACF,CACN,EACS,CAAC,cACbpK,KAAA,CAACX,KAAK,CAAC8O,MAAM,EAAAzD,QAAA,eACX5K,IAAA,CAACf,MAAM,EAACoG,OAAO,CAAC,WAAW,CAAC+G,OAAO,CAAE3B,gBAAiB,CAAAG,QAAA,CAAC,UAEvD,CAAQ,CAAC,cACT1K,KAAA,CAACjB,MAAM,EAACoG,OAAO,CAAC,SAAS,CAAC+G,OAAO,CAAEjE,WAAY,CAAAyC,QAAA,eAC7C5K,IAAA,CAACH,SAAS,EAACoL,SAAS,CAAC,MAAM,CAAE,CAAC,gBAChC,EAAQ,CAAC,EACG,CAAC,EACV,CAAC,EACR,CAAC,CAEP,CAAC,CAED,cAAe,CAAA5K,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}