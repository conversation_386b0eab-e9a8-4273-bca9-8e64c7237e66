{"ast": null, "code": "import React,{useState}from'react';import{Dropdown}from'react-bootstrap';import{BsChevronDown}from'react-icons/bs';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ColorVariantSelector=_ref=>{var _selectedVariant$fabr;let{variants,selectedValue,onSelect,placeholder=\"Select Color Variant\",isDuplicateFunction,groupIndex,variantIndex,disabled=false,showFabricName=false// For EditCutting where we need to show fabric name too\n}=_ref;const[isOpen,setIsOpen]=useState(false);const selectedVariant=variants.find(v=>v.id===selectedValue);return/*#__PURE__*/_jsxs(Dropdown,{show:isOpen&&!disabled,onToggle:setIsOpen,children:[/*#__PURE__*/_jsxs(Dropdown.Toggle,{variant:\"outline-secondary\",className:\"w-100 d-flex justify-content-between align-items-center\",style:{textAlign:'left'},disabled:disabled,children:[selectedVariant?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'20px',height:'20px',backgroundColor:selectedVariant.color,border:'1px solid #ccc',borderRadius:'4px',marginRight:'8px'}}),/*#__PURE__*/_jsxs(\"span\",{children:[showFabricName&&((_selectedVariant$fabr=selectedVariant.fabric_definition_data)===null||_selectedVariant$fabr===void 0?void 0:_selectedVariant$fabr.fabric_name)&&`${selectedVariant.fabric_definition_data.fabric_name} - `,selectedVariant.color_name||selectedVariant.color,\" - \",selectedVariant.available_yard,\" yards available\"]})]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:placeholder}),/*#__PURE__*/_jsx(BsChevronDown,{})]}),/*#__PURE__*/_jsx(Dropdown.Menu,{className:\"w-100\",style:{maxHeight:'300px',overflowY:'auto'},children:variants.length===0?/*#__PURE__*/_jsx(Dropdown.Item,{disabled:true,children:\"No variants available\"}):variants.map(variant=>{var _variant$fabric_defin;const isAlreadySelected=isDuplicateFunction?isDuplicateFunction(groupIndex,variant.id,variantIndex):false;return/*#__PURE__*/_jsx(Dropdown.Item,{onClick:()=>{if(!isAlreadySelected){onSelect(variant.id);setIsOpen(false);}},disabled:isAlreadySelected,className:isAlreadySelected?'text-muted':'',children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'20px',height:'20px',backgroundColor:variant.color,border:'1px solid #ccc',borderRadius:'4px',marginRight:'8px'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"fw-bold\",children:[showFabricName&&((_variant$fabric_defin=variant.fabric_definition_data)===null||_variant$fabric_defin===void 0?void 0:_variant$fabric_defin.fabric_name)&&`${variant.fabric_definition_data.fabric_name} - `,variant.color_name||variant.color]}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[variant.available_yard,\" yards available\",isAlreadySelected?' (Already Selected)':'']})]})]})},variant.id);})})]});};export default ColorVariantSelector;", "map": {"version": 3, "names": ["React", "useState", "Dropdown", "BsChevronDown", "jsx", "_jsx", "jsxs", "_jsxs", "ColorVariantSelector", "_ref", "_selectedVariant$fabr", "variants", "selected<PERSON><PERSON><PERSON>", "onSelect", "placeholder", "isDuplicateFunction", "groupIndex", "variantIndex", "disabled", "showFabricName", "isOpen", "setIsOpen", "<PERSON><PERSON><PERSON><PERSON>", "find", "v", "id", "show", "onToggle", "children", "Toggle", "variant", "className", "style", "textAlign", "width", "height", "backgroundColor", "color", "border", "borderRadius", "marginRight", "fabric_definition_data", "fabric_name", "color_name", "available_yard", "<PERSON><PERSON>", "maxHeight", "overflowY", "length", "<PERSON><PERSON>", "map", "_variant$fabric_defin", "isAlreadySelected", "onClick"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/components/ColorVariantSelector.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Dropdown } from 'react-bootstrap';\nimport { BsChevronDown } from 'react-icons/bs';\n\nconst ColorVariantSelector = ({ \n  variants, \n  selectedValue, \n  onSelect, \n  placeholder = \"Select Color Variant\",\n  isDuplicateFunction,\n  groupIndex,\n  variantIndex,\n  disabled = false,\n  showFabricName = false // For EditCutting where we need to show fabric name too\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const selectedVariant = variants.find(v => v.id === selectedValue);\n\n  return (\n    <Dropdown show={isOpen && !disabled} onToggle={setIsOpen}>\n      <Dropdown.Toggle \n        variant=\"outline-secondary\" \n        className=\"w-100 d-flex justify-content-between align-items-center\"\n        style={{ textAlign: 'left' }}\n        disabled={disabled}\n      >\n        {selectedVariant ? (\n          <div className=\"d-flex align-items-center\">\n            <div\n              style={{\n                width: '20px',\n                height: '20px',\n                backgroundColor: selectedVariant.color,\n                border: '1px solid #ccc',\n                borderRadius: '4px',\n                marginRight: '8px'\n              }}\n            />\n            <span>\n              {showFabricName && selectedVariant.fabric_definition_data?.fabric_name && \n                `${selectedVariant.fabric_definition_data.fabric_name} - `\n              }\n              {selectedVariant.color_name || selectedVariant.color} - {selectedVariant.available_yard} yards available\n            </span>\n          </div>\n        ) : (\n          <span className=\"text-muted\">{placeholder}</span>\n        )}\n        <BsChevronDown />\n      </Dropdown.Toggle>\n\n      <Dropdown.Menu className=\"w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n        {variants.length === 0 ? (\n          <Dropdown.Item disabled>No variants available</Dropdown.Item>\n        ) : (\n          variants.map((variant) => {\n            const isAlreadySelected = isDuplicateFunction ? isDuplicateFunction(groupIndex, variant.id, variantIndex) : false;\n            return (\n              <Dropdown.Item\n                key={variant.id}\n                onClick={() => {\n                  if (!isAlreadySelected) {\n                    onSelect(variant.id);\n                    setIsOpen(false);\n                  }\n                }}\n                disabled={isAlreadySelected}\n                className={isAlreadySelected ? 'text-muted' : ''}\n              >\n                <div className=\"d-flex align-items-center\">\n                  <div\n                    style={{\n                      width: '20px',\n                      height: '20px',\n                      backgroundColor: variant.color,\n                      border: '1px solid #ccc',\n                      borderRadius: '4px',\n                      marginRight: '8px'\n                    }}\n                  />\n                  <div>\n                    <div className=\"fw-bold\">\n                      {showFabricName && variant.fabric_definition_data?.fabric_name && \n                        `${variant.fabric_definition_data.fabric_name} - `\n                      }\n                      {variant.color_name || variant.color}\n                    </div>\n                    <small className=\"text-muted\">\n                      {variant.available_yard} yards available\n                      {isAlreadySelected ? ' (Already Selected)' : ''}\n                    </small>\n                  </div>\n                </div>\n              </Dropdown.Item>\n            );\n          })\n        )}\n      </Dropdown.Menu>\n    </Dropdown>\n  );\n};\n\nexport default ColorVariantSelector;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,QAAQ,KAAQ,iBAAiB,CAC1C,OAASC,aAAa,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,oBAAoB,CAAGC,IAAA,EAUvB,KAAAC,qBAAA,IAVwB,CAC5BC,QAAQ,CACRC,aAAa,CACbC,QAAQ,CACRC,WAAW,CAAG,sBAAsB,CACpCC,mBAAmB,CACnBC,UAAU,CACVC,YAAY,CACZC,QAAQ,CAAG,KAAK,CAChBC,cAAc,CAAG,KAAM;AACzB,CAAC,CAAAV,IAAA,CACC,KAAM,CAACW,MAAM,CAAEC,SAAS,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAAqB,eAAe,CAAGX,QAAQ,CAACY,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKb,aAAa,CAAC,CAElE,mBACEL,KAAA,CAACL,QAAQ,EAACwB,IAAI,CAAEN,MAAM,EAAI,CAACF,QAAS,CAACS,QAAQ,CAAEN,SAAU,CAAAO,QAAA,eACvDrB,KAAA,CAACL,QAAQ,CAAC2B,MAAM,EACdC,OAAO,CAAC,mBAAmB,CAC3BC,SAAS,CAAC,yDAAyD,CACnEC,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAO,CAAE,CAC7Bf,QAAQ,CAAEA,QAAS,CAAAU,QAAA,EAElBN,eAAe,cACdf,KAAA,QAAKwB,SAAS,CAAC,2BAA2B,CAAAH,QAAA,eACxCvB,IAAA,QACE2B,KAAK,CAAE,CACLE,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,eAAe,CAAEd,eAAe,CAACe,KAAK,CACtCC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBC,WAAW,CAAE,KACf,CAAE,CACH,CAAC,cACFjC,KAAA,SAAAqB,QAAA,EACGT,cAAc,IAAAT,qBAAA,CAAIY,eAAe,CAACmB,sBAAsB,UAAA/B,qBAAA,iBAAtCA,qBAAA,CAAwCgC,WAAW,GACpE,GAAGpB,eAAe,CAACmB,sBAAsB,CAACC,WAAW,KAAK,CAE3DpB,eAAe,CAACqB,UAAU,EAAIrB,eAAe,CAACe,KAAK,CAAC,KAAG,CAACf,eAAe,CAACsB,cAAc,CAAC,kBAC1F,EAAM,CAAC,EACJ,CAAC,cAENvC,IAAA,SAAM0B,SAAS,CAAC,YAAY,CAAAH,QAAA,CAAEd,WAAW,CAAO,CACjD,cACDT,IAAA,CAACF,aAAa,GAAE,CAAC,EACF,CAAC,cAElBE,IAAA,CAACH,QAAQ,CAAC2C,IAAI,EAACd,SAAS,CAAC,OAAO,CAACC,KAAK,CAAE,CAAEc,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAnB,QAAA,CAC/EjB,QAAQ,CAACqC,MAAM,GAAK,CAAC,cACpB3C,IAAA,CAACH,QAAQ,CAAC+C,IAAI,EAAC/B,QAAQ,MAAAU,QAAA,CAAC,uBAAqB,CAAe,CAAC,CAE7DjB,QAAQ,CAACuC,GAAG,CAAEpB,OAAO,EAAK,KAAAqB,qBAAA,CACxB,KAAM,CAAAC,iBAAiB,CAAGrC,mBAAmB,CAAGA,mBAAmB,CAACC,UAAU,CAAEc,OAAO,CAACL,EAAE,CAAER,YAAY,CAAC,CAAG,KAAK,CACjH,mBACEZ,IAAA,CAACH,QAAQ,CAAC+C,IAAI,EAEZI,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAACD,iBAAiB,CAAE,CACtBvC,QAAQ,CAACiB,OAAO,CAACL,EAAE,CAAC,CACpBJ,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAE,CACFH,QAAQ,CAAEkC,iBAAkB,CAC5BrB,SAAS,CAAEqB,iBAAiB,CAAG,YAAY,CAAG,EAAG,CAAAxB,QAAA,cAEjDrB,KAAA,QAAKwB,SAAS,CAAC,2BAA2B,CAAAH,QAAA,eACxCvB,IAAA,QACE2B,KAAK,CAAE,CACLE,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,eAAe,CAAEN,OAAO,CAACO,KAAK,CAC9BC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBC,WAAW,CAAE,KACf,CAAE,CACH,CAAC,cACFjC,KAAA,QAAAqB,QAAA,eACErB,KAAA,QAAKwB,SAAS,CAAC,SAAS,CAAAH,QAAA,EACrBT,cAAc,IAAAgC,qBAAA,CAAIrB,OAAO,CAACW,sBAAsB,UAAAU,qBAAA,iBAA9BA,qBAAA,CAAgCT,WAAW,GAC5D,GAAGZ,OAAO,CAACW,sBAAsB,CAACC,WAAW,KAAK,CAEnDZ,OAAO,CAACa,UAAU,EAAIb,OAAO,CAACO,KAAK,EACjC,CAAC,cACN9B,KAAA,UAAOwB,SAAS,CAAC,YAAY,CAAAH,QAAA,EAC1BE,OAAO,CAACc,cAAc,CAAC,kBACxB,CAACQ,iBAAiB,CAAG,qBAAqB,CAAG,EAAE,EAC1C,CAAC,EACL,CAAC,EACH,CAAC,EAjCDtB,OAAO,CAACL,EAkCA,CAAC,CAEpB,CAAC,CACF,CACY,CAAC,EACR,CAAC,CAEf,CAAC,CAED,cAAe,CAAAjB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}