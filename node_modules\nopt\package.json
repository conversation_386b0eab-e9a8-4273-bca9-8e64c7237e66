{"name": "nopt", "version": "8.1.0", "description": "Option parsing for Node, supporting types, shorthands, etc. Used by npm.", "author": "GitHub Inc.", "main": "lib/nopt.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/nopt.git"}, "bin": {"nopt": "bin/nopt.js"}, "license": "ISC", "dependencies": {"abbrev": "^3.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.6", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.23.6", "publish": true}}