{"ast": null, "code": "// src/pages/ApproveFinishedProduct.jsx\nimport React,{useState,useEffect,useRef,useCallback}from'react';import axios from'axios';import{useParams,useNavigate}from'react-router-dom';import{Card,Form,Button,Alert,Row,Col,Spinner,Image,Modal,ProgressBar,Badge,Tabs,Tab,Table,OverlayTrigger,Tooltip}from'react-bootstrap';import{FaCheck,FaUpload,FaImage,FaTags,FaInfoCircle,FaMoneyBillWave,FaArrowRight,FaPercentage,FaBoxOpen,FaClipboardList,FaTrash,FaUndo,FaExclamationTriangle,FaFilePdf,FaDownload}from'react-icons/fa';import{useDropzone}from'react-dropzone';import RoleBasedNavBar from'../components/RoleBasedNavBar';import'./ApproveFinishedProduct.css';import{jsPDF}from'jspdf';// No need to import uploadMultipleImages as we're using FormData directly\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ApproveFinishedProduct=()=>{const{id}=useParams();const navigate=useNavigate();const fileInputRef=useRef(null);// Basic form state\nconst[manufacturePrice,setManufacturePrice]=useState('');const[sellingPrice,setSellingPrice]=useState('');const[productNotes,setProductNotes]=useState('');const[error,setError]=useState('');const[successMsg,setSuccessMsg]=useState('');const[isApproved,setIsApproved]=useState(false);const[loading,setLoading]=useState(true);// Image handling state\nconst[productImages,setProductImages]=useState([]);const[imagePreviewUrls,setImagePreviewUrls]=useState([]);const[existingImageUrls,setExistingImageUrls]=useState([]);const[isDragging,setIsDragging]=useState(false);const[activeImageIndex,setActiveImageIndex]=useState(0);const[uploadProgress,setUploadProgress]=useState(0);const[isUploading,setIsUploading]=useState(false);// Product details state\nconst[productDetails,setProductDetails]=useState(null);const[fabricDetails,setFabricDetails]=useState([]);const[sizeQuantities,setSizeQuantities]=useState({xs:0,s:0,m:0,l:0,xl:0});// UI state\nconst[activeTab,setActiveTab]=useState('details');const[showConfirmModal,setShowConfirmModal]=useState(false);const[showPdfModal,setShowPdfModal]=useState(false);const[pdfLoading,setPdfLoading]=useState(false);const[validationErrors,setValidationErrors]=useState({});const[profitMargin,setProfitMargin]=useState(0);// Product name editing state\nconst[productName,setProductName]=useState('');const[isEditingName,setIsEditingName]=useState(false);const[productNameError,setProductNameError]=useState('');// Price editing state for approved products\nconst[isEditingPrices,setIsEditingPrices]=useState(false);const[editManufacturePrice,setEditManufacturePrice]=useState('');const[editSellingPrice,setEditSellingPrice]=useState('');const[priceEditErrors,setPriceEditErrors]=useState({});const[finishedProductId,setFinishedProductId]=useState(null);// Calculate profit margin whenever prices change\nuseEffect(()=>{if(manufacturePrice&&sellingPrice){const mPrice=parseFloat(manufacturePrice);const sPrice=parseFloat(sellingPrice);if(mPrice>0&&sPrice>0){const margin=(sPrice-mPrice)/sPrice*100;setProfitMargin(margin.toFixed(2));}}},[manufacturePrice,sellingPrice]);// State for retry mechanism\nconst[retryCount,setRetryCount]=useState(0);const maxRetries=3;// Function to fetch product data with retry mechanism\nconst fetchProductData=useCallback(async()=>{try{setLoading(true);setError('');// Fetch approval status\nconst approvalRes=await axios.get(`http://localhost:8000/api/finished_product/status/${id}/`);// Fetch cutting record details\nconst cuttingRes=await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);if(approvalRes.data&&approvalRes.data.is_approved){setIsApproved(true);setManufacturePrice(approvalRes.data.manufacture_price);setSellingPrice(approvalRes.data.selling_price);setFinishedProductId(approvalRes.data.finished_product_id);// Set existing image URLs if available\nif(approvalRes.data.product_images&&Array.isArray(approvalRes.data.product_images)){setExistingImageUrls(approvalRes.data.product_images);}else if(approvalRes.data.product_image){// For backward compatibility with single image\nsetExistingImageUrls([approvalRes.data.product_image]);}if(approvalRes.data.notes){setProductNotes(approvalRes.data.notes);}}if(cuttingRes.data){setProductDetails(cuttingRes.data);// Set product name from API response\nif(cuttingRes.data.product_name){setProductName(cuttingRes.data.product_name);}else if(cuttingRes.data.fabric_definition_data){// If no product name, use fabric name as default\nsetProductName(cuttingRes.data.fabric_definition_data.fabric_name);}// Extract fabric details\nif(cuttingRes.data.details&&cuttingRes.data.details.length>0){// Debug: Log the fabric details to see what color data we're getting\nconsole.log('Fabric details from API:',cuttingRes.data.details);setFabricDetails(cuttingRes.data.details);// Calculate size quantities\nconst sizes={xs:0,s:0,m:0,l:0,xl:0};cuttingRes.data.details.forEach(detail=>{sizes.xs+=detail.xs||0;sizes.s+=detail.s||0;sizes.m+=detail.m||0;sizes.l+=detail.l||0;sizes.xl+=detail.xl||0;});setSizeQuantities(sizes);}}// Reset retry count on success\nsetRetryCount(0);}catch(err){console.error(\"Failed to fetch product data:\",err);// Provide more detailed error message\nconst errorMessage=err.response?`Error: ${err.response.status} - ${err.response.statusText}`:err.request?\"No response received from server. Check if the backend is running.\":\"Failed to make request. Check your network connection.\";setError(`Unable to fetch product data. ${errorMessage}`);// Implement retry mechanism\nif(retryCount<maxRetries){setRetryCount(prev=>prev+1);setTimeout(()=>{fetchProductData();},2000);// Wait 2 seconds before retrying\n}}finally{setLoading(false);}},[id,retryCount]);// Fetch product details and approval status\nuseEffect(()=>{fetchProductData();},[fetchProductData]);// Handle image selection from file input\nconst handleImageChange=e=>{const files=Array.from(e.target.files);if(files.length>0){processImageFiles(files);}};// Process the selected image files\nconst processImageFiles=useCallback(files=>{// Check if adding these files would exceed the limit\nif(productImages.length+files.length>10){setError(`You can only upload up to 10 images. You already have ${productImages.length} images.`);return;}const newImages=[];const newPreviewUrls=[...imagePreviewUrls];files.forEach(file=>{// Validate file type\nif(!file.type.match('image.*')){setError('Please select image files only (JPEG, PNG, etc.)');return;}// Validate file size (max 5MB)\nif(file.size>5*1024*1024){setError('Each image size should be less than 5MB');return;}newImages.push(file);// Create a preview URL\nconst reader=new FileReader();reader.onloadend=()=>{newPreviewUrls.push(reader.result);setImagePreviewUrls([...newPreviewUrls]);};reader.readAsDataURL(file);});setProductImages([...productImages,...newImages]);},[productImages,imagePreviewUrls]);// Trigger file input click\nconst triggerFileInput=()=>{fileInputRef.current.click();};// Handle drag and drop functionality\nconst onDrop=useCallback(acceptedFiles=>{if(acceptedFiles&&acceptedFiles.length>0){processImageFiles(acceptedFiles);}},[processImageFiles]);const{getRootProps,getInputProps,isDragActive}=useDropzone({onDrop,accept:{'image/*':['.jpeg','.jpg','.png','.gif']},maxFiles:10});// Remove an uploaded image\nconst removeImage=index=>{const newImages=[...productImages];const newPreviewUrls=[...imagePreviewUrls];newImages.splice(index,1);newPreviewUrls.splice(index,1);setProductImages(newImages);setImagePreviewUrls(newPreviewUrls);// Adjust active index if needed\nif(index===activeImageIndex){setActiveImageIndex(Math.max(0,index-1));}else if(index<activeImageIndex){setActiveImageIndex(activeImageIndex-1);}};// Set active image\nconst setActiveImage=index=>{setActiveImageIndex(index);};// Validate form inputs\nconst validateForm=()=>{const errors={};// Validate manufacture price\nif(!manufacturePrice||manufacturePrice.trim()===''){errors.manufacturePrice=\"Manufacture price is required\";}else if(parseFloat(manufacturePrice)<=0){errors.manufacturePrice=\"Manufacture price must be greater than zero\";}else if(isNaN(parseFloat(manufacturePrice))){errors.manufacturePrice=\"Manufacture price must be a valid number\";}// Validate selling price\nif(!sellingPrice||sellingPrice.trim()===''){errors.sellingPrice=\"Selling price is required\";}else if(parseFloat(sellingPrice)<=0){errors.sellingPrice=\"Selling price must be greater than zero\";}else if(isNaN(parseFloat(sellingPrice))){errors.sellingPrice=\"Selling price must be a valid number\";}else if(parseFloat(sellingPrice)<parseFloat(manufacturePrice)){errors.sellingPrice=\"Selling price should be greater than or equal to manufacture price\";}// Validate product notes (optional)\nif(productNotes&&productNotes.length>500){errors.productNotes=\"Notes should be less than 500 characters\";}setValidationErrors(errors);return Object.keys(errors).length===0;};// Show confirmation modal\nconst handleFormSubmit=e=>{e.preventDefault();// Validate form\nif(!validateForm()){return;}// Show confirmation modal\nsetShowConfirmModal(true);};// Handle actual submission\nconst handleSubmit=async()=>{setError('');setSuccessMsg('');setShowConfirmModal(false);try{// Show loading state\nsetLoading(true);setIsUploading(true);// Create FormData object for API request\nconst formData=new FormData();formData.append('cutting_record',id);formData.append('manufacture_price',parseFloat(manufacturePrice));formData.append('selling_price',parseFloat(sellingPrice));if(productNotes){formData.append('notes',productNotes);}// Add images directly to the FormData if there are any\nif(productImages&&productImages.length>0){setUploadProgress(0);// Append each image to the FormData\nproductImages.forEach(image=>{formData.append('product_images',image);});}// Make the API request with progress tracking\nconst response=await axios.post('http://localhost:8000/api/finished_product/approve/',formData,{headers:{'Content-Type':'multipart/form-data'},onUploadProgress:progressEvent=>{const percentCompleted=Math.round(progressEvent.loaded*100/progressEvent.total);setUploadProgress(percentCompleted);}});setSuccessMsg(response.data.message||'Product approved successfully!');setIsUploading(false);// Redirect after a delay\nsetTimeout(()=>{navigate('/approveproduct-list');},2000);}catch(err){console.error(\"Error approving finished product:\",err);const errMsg=err.response&&err.response.data?typeof err.response.data==='object'?JSON.stringify(err.response.data):err.response.data:\"Failed to approve finished product. Please try again.\";setError(errMsg);setIsUploading(false);}finally{setLoading(false);}};// Cancel confirmation\nconst handleCancelConfirmation=()=>{setShowConfirmModal(false);};// Open PDF modal\nconst openPdfModal=()=>{setShowPdfModal(true);};// Close PDF modal\nconst closePdfModal=()=>{setShowPdfModal(false);};// Start editing product name\nconst startEditingName=()=>{setIsEditingName(true);};// Cancel editing product name\nconst cancelEditingName=()=>{setIsEditingName(false);setProductNameError('');// Reset to original name from product details\nif(productDetails&&productDetails.product_name){setProductName(productDetails.product_name);}else if(productDetails&&productDetails.fabric_definition_data){setProductName(productDetails.fabric_definition_data.fabric_name);}};// Save updated product name\nconst saveProductName=async()=>{// Validate product name\nif(!productName.trim()){setProductNameError('Product name cannot be empty');return;}setProductNameError('');setLoading(true);try{// First, get the current cutting record data\nconst currentRecord=await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);// Create a payload with all the required fields, including details\nconst payload={fabric_definition:currentRecord.data.fabric_definition,cutting_date:currentRecord.data.cutting_date,product_name:productName,details:currentRecord.data.details// Include existing details without modification\n};// Update the product name in the backend\nawait axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`,payload);// Update local state\nsetProductDetails({...productDetails,product_name:productName});setIsEditingName(false);setSuccessMsg('Product name updated successfully');// Clear success message after 3 seconds\nsetTimeout(()=>{setSuccessMsg('');},3000);}catch(err){console.error('Error updating product name:',err);// Check if the product name was actually updated despite the error\ntry{const updatedRecord=await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);if(updatedRecord.data.product_name===productName){// If the name was updated successfully despite the error, show success message\nsetProductDetails({...productDetails,product_name:productName});setIsEditingName(false);setSuccessMsg('Product name updated successfully');// Clear success message after 3 seconds\nsetTimeout(()=>{setSuccessMsg('');},3000);return;}}catch(checkErr){// If we can't check, just show the original error\nconsole.error('Error checking product name update:',checkErr);}// Show error message if the name wasn't updated\nsetError('Failed to update product name. Please try again.');}finally{setLoading(false);}};// Start editing prices\nconst startEditingPrices=()=>{setEditManufacturePrice(manufacturePrice);setEditSellingPrice(sellingPrice);setIsEditingPrices(true);setPriceEditErrors({});};// Cancel editing prices\nconst cancelEditingPrices=()=>{setIsEditingPrices(false);setEditManufacturePrice('');setEditSellingPrice('');setPriceEditErrors({});};// Validate price editing form\nconst validatePriceEdit=()=>{const errors={};// Validate manufacture price\nif(!editManufacturePrice||editManufacturePrice.trim()===''){errors.editManufacturePrice=\"Manufacture price is required\";}else if(parseFloat(editManufacturePrice)<=0){errors.editManufacturePrice=\"Manufacture price must be greater than zero\";}else if(isNaN(parseFloat(editManufacturePrice))){errors.editManufacturePrice=\"Manufacture price must be a valid number\";}// Validate selling price\nif(!editSellingPrice||editSellingPrice.trim()===''){errors.editSellingPrice=\"Selling price is required\";}else if(parseFloat(editSellingPrice)<=0){errors.editSellingPrice=\"Selling price must be greater than zero\";}else if(isNaN(parseFloat(editSellingPrice))){errors.editSellingPrice=\"Selling price must be a valid number\";}else if(parseFloat(editSellingPrice)<parseFloat(editManufacturePrice)){errors.editSellingPrice=\"Selling price should be greater than or equal to manufacture price\";}setPriceEditErrors(errors);return Object.keys(errors).length===0;};// Save updated prices\nconst savePriceChanges=async()=>{alert('Save button clicked!');// Debug alert\nconsole.log('savePriceChanges called');console.log('editManufacturePrice:',editManufacturePrice);console.log('editSellingPrice:',editSellingPrice);console.log('finishedProductId:',finishedProductId);if(!validatePriceEdit()){console.log('Validation failed');return;}if(!finishedProductId){console.log('No finished product ID');setError('Could not find finished product record');return;}setLoading(true);setError('');try{// Update the prices using the PATCH endpoint\nconst updateData={manufacture_price:parseFloat(editManufacturePrice),selling_price:parseFloat(editSellingPrice)};console.log('Sending update data:',updateData);await axios.patch(`http://localhost:8000/api/finished_product/update/${finishedProductId}/`,updateData);// Update local state\nsetManufacturePrice(editManufacturePrice);setSellingPrice(editSellingPrice);setIsEditingPrices(false);setSuccessMsg('Prices updated successfully');// Clear success message after 3 seconds\nsetTimeout(()=>{setSuccessMsg('');},3000);}catch(err){console.error('Error updating prices:',err);const errMsg=err.response&&err.response.data?typeof err.response.data==='object'?JSON.stringify(err.response.data):err.response.data:\"Failed to update prices. Please try again.\";setError(errMsg);}finally{setLoading(false);}};// Generate PDF report for the product\nconst generateProductReport=()=>{setPdfLoading(true);try{// Create PDF document\nconst doc=new jsPDF({orientation:'portrait',unit:'mm',format:'a4'});// Set font sizes\nconst titleFontSize=16;const headingFontSize=12;const normalFontSize=10;const smallFontSize=8;// Add header\ndoc.setFontSize(titleFontSize);doc.setFont('helvetica','bold');doc.text('Product Report',105,20,{align:'center'});// Add product name\nconst pdfProductName=productName||(productDetails&&productDetails.fabric_definition_data?productDetails.fabric_definition_data.fabric_name:`Batch ID: ${id}`);doc.setFontSize(headingFontSize);doc.text(pdfProductName,105,30,{align:'center'});// Add approval date\ndoc.setFontSize(normalFontSize);doc.setFont('helvetica','normal');doc.text(`Approval Date: ${new Date().toLocaleDateString()}`,105,40,{align:'center'});// Add horizontal line\ndoc.setDrawColor(200,200,200);doc.line(20,45,190,45);// Start Y position for content\nlet yPos=55;// Add pricing information\ndoc.setFontSize(headingFontSize);doc.setFont('helvetica','bold');doc.text('Pricing Information',20,yPos);yPos+=10;doc.setFontSize(normalFontSize);doc.setFont('helvetica','normal');doc.text(`Manufacture Price: LKR ${manufacturePrice}`,25,yPos);yPos+=7;doc.text(`Selling Price: LKR ${sellingPrice}`,25,yPos);yPos+=7;doc.text(`Profit Margin: ${profitMargin}%`,25,yPos);yPos+=15;// Add size distribution\ndoc.setFontSize(headingFontSize);doc.setFont('helvetica','bold');doc.text('Size Distribution',20,yPos);yPos+=10;doc.setFontSize(normalFontSize);doc.setFont('helvetica','normal');// Create a table for size distribution\nconst sizes=Object.entries(sizeQuantities);const sizeHeaders=['Size','Quantity','Percentage'];const totalQuantity=sizes.reduce((sum,_ref)=>{let[_,qty]=_ref;return sum+qty;},0);// Draw table headers\ndoc.setFont('helvetica','bold');doc.text(sizeHeaders[0],25,yPos);doc.text(sizeHeaders[1],60,yPos);doc.text(sizeHeaders[2],95,yPos);yPos+=7;// Draw table rows\ndoc.setFont('helvetica','normal');sizes.forEach(_ref2=>{let[size,quantity]=_ref2;const percentage=totalQuantity>0?(quantity/totalQuantity*100).toFixed(1):'0.0';doc.text(size.toUpperCase(),25,yPos);doc.text(quantity.toString(),60,yPos);doc.text(`${percentage}%`,95,yPos);yPos+=7;});// Add total row\ndoc.setFont('helvetica','bold');doc.text('Total',25,yPos);doc.text(totalQuantity.toString(),60,yPos);doc.text('100.0%',95,yPos);yPos+=15;// Add color information\nif(fabricDetails.length>0){doc.setFontSize(headingFontSize);doc.setFont('helvetica','bold');doc.text('Color Information',20,yPos);yPos+=10;doc.setFontSize(normalFontSize);doc.setFont('helvetica','normal');fabricDetails.forEach((detail,index)=>{var _detail$fabric_varian;const colorName=((_detail$fabric_varian=detail.fabric_variant_data)===null||_detail$fabric_varian===void 0?void 0:_detail$fabric_varian.color_name)||detail.color||'N/A';doc.text(`Color ${index+1}: ${colorName}`,25,yPos);yPos+=7;});yPos+=8;}// Add product notes if available\nif(productNotes){doc.setFontSize(headingFontSize);doc.setFont('helvetica','bold');doc.text('Product Notes',20,yPos);yPos+=10;doc.setFontSize(normalFontSize);doc.setFont('helvetica','normal');// Split notes into multiple lines if needed\nconst splitNotes=doc.splitTextToSize(productNotes,160);doc.text(splitNotes,25,yPos);yPos+=splitNotes.length*7+8;}// Add image information\nif(existingImageUrls&&existingImageUrls.length>0){doc.setFontSize(headingFontSize);doc.setFont('helvetica','bold');doc.text('Product Images',20,yPos);yPos+=10;doc.setFontSize(normalFontSize);doc.setFont('helvetica','normal');doc.text(`Number of Images: ${existingImageUrls.length}`,25,yPos);yPos+=7;doc.text('Note: Images can be viewed in the system',25,yPos);yPos+=15;}// Add footer\ndoc.setFontSize(smallFontSize);doc.setFont('helvetica','italic');doc.text(`Generated on: ${new Date().toLocaleString()}`,105,280,{align:'center'});doc.text('Pri Fashion Garment Management System',105,285,{align:'center'});// Save the PDF\nconst cleanProductName=productName.replace(/[^a-zA-Z0-9]/g,'_');doc.save(`Product_Report_${cleanProductName}_${new Date().toISOString().slice(0,10)}.pdf`);setPdfLoading(false);setShowPdfModal(false);}catch(error){console.error(\"Error generating PDF:\",error);setError(`Failed to generate PDF: ${error.message}`);setPdfLoading(false);setShowPdfModal(false);}};// Loading spinner\nif(loading)return/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center align-items-center\",style:{height:\"100vh\"},children:/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",variant:\"primary\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})})});// Render color swatch\nconst renderColorSwatch=color=>{if(!color)return null;// For debugging - log the color value we're receiving\nconsole.log('Color value received:',color);// SIMPLIFIED APPROACH: Directly use the color value if it looks like a hex code\nlet bgColor;// If it starts with #, use it directly\nif(color.startsWith('#')){bgColor=color;}// If it looks like a hex code without #, add the #\nelse if(/^[0-9A-Fa-f]{6}$/.test(color)||/^[0-9A-Fa-f]{3}$/.test(color)){bgColor=`#${color}`;}// For named colors like \"Black\", \"Red\", etc.\nelse{// Common color names mapping\nconst colorMap={'red':'#dc3545','blue':'#0d6efd','green':'#198754','yellow':'#ffc107','black':'#212529','white':'#f8f9fa','purple':'#6f42c1','orange':'#fd7e14','pink':'#d63384','brown':'#8B4513','gray':'#6c757d'};// Try to get from color map or use the name directly\nbgColor=colorMap[color.toLowerCase()]||color;}return/*#__PURE__*/_jsx(OverlayTrigger,{placement:\"top\",overlay:/*#__PURE__*/_jsx(Tooltip,{className:\"custom-tooltip\",children:color}),children:/*#__PURE__*/_jsx(\"div\",{className:\"color-swatch\",style:{backgroundColor:bgColor},\"data-color\":color})});};// Render size quantity bars\nconst renderSizeQuantityBars=()=>{const sizes=Object.entries(sizeQuantities);const maxQuantity=Math.max(...sizes.map(_ref3=>{let[_,qty]=_ref3;return qty;}));return sizes.map(_ref4=>{let[size,quantity]=_ref4;return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"strong\",{children:size.toUpperCase()})}),/*#__PURE__*/_jsxs(\"span\",{children:[quantity,\" pcs\"]})]}),/*#__PURE__*/_jsx(ProgressBar,{now:maxQuantity?quantity/maxQuantity*100:0,variant:quantity>0?\"info\":\"light\",className:\"size-quantity-bar\"})]},size);});};// Confirmation Modal\nconst ConfirmationModal=()=>/*#__PURE__*/_jsxs(Modal,{show:showConfirmModal,onHide:handleCancelConfirmation,centered:true,className:\"confirmation-modal\",children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:\"Confirm Product Approval\"})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Are you sure you want to approve this product with the following details?\"}),/*#__PURE__*/_jsx(Table,{bordered:true,hover:true,size:\"sm\",className:\"mt-3\",children:/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name:\"})}),/*#__PURE__*/_jsx(\"td\",{children:productName||(productDetails&&productDetails.fabric_definition_data?productDetails.fabric_definition_data.fabric_name:`Batch ID: ${id}`)})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Manufacture Price:\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[\"LKR \",manufacturePrice]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Selling Price:\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[\"LKR \",sellingPrice]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Profit Margin:\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[profitMargin,\"%\"]})]}),productNotes&&/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Notes:\"})}),/*#__PURE__*/_jsx(\"td\",{children:productNotes})]})]})}),imagePreviewUrls.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mt-3\",children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Product Image:\"})}),/*#__PURE__*/_jsx(Image,{src:imagePreviewUrls[0],alt:\"Product\",thumbnail:true,style:{maxHeight:\"100px\"}})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleCancelConfirmation,children:\"Cancel\"}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",onClick:handleSubmit,children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-2\"}),\"Confirm Approval\"]})]})]});return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsx(\"div\",{className:\"main-content\",children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{md:10,lg:8,children:/*#__PURE__*/_jsx(Card,{className:\"shadow product-card slide-in\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-center mb-3\",children:\"Approve Finished Product\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mb-4\",children:isEditingName?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-center align-items-center\",children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-0 me-2 flex-grow-1\",style:{maxWidth:'300px'},children:[/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),isInvalid:!!productNameError,placeholder:\"Enter product name\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:productNameError})]}),/*#__PURE__*/_jsx(Button,{variant:\"success\",size:\"sm\",className:\"me-1\",onClick:saveProductName,children:/*#__PURE__*/_jsx(FaCheck,{})}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:cancelEditingName,children:/*#__PURE__*/_jsx(FaUndo,{})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-center align-items-center\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-center text-muted mb-0 me-2\",children:productName||(productDetails&&productDetails.fabric_definition_data?productDetails.fabric_definition_data.fabric_name:`Batch ID: ${id}`)}),/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",size:\"sm\",onClick:startEditingName,children:\"Edit Name\"})]})}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"mb-4 fade-in\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(FaExclamationTriangle,{className:\"me-2\"}),error]}),/*#__PURE__*/_jsx(Button,{variant:\"outline-danger\",size:\"sm\",onClick:fetchProductData,children:\"Retry\"})]})}),successMsg&&/*#__PURE__*/_jsxs(Alert,{variant:\"success\",className:\"mb-4 fade-in\",children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-2\"}),successMsg]}),isApproved?/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 bg-white rounded mb-3 slide-in\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"text-center mb-4 text-success\",children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-2\"}),\"Product Already Approved\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mb-4\",children:isEditingName?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-center align-items-center\",children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-0 me-2 flex-grow-1\",style:{maxWidth:'300px'},children:[/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),isInvalid:!!productNameError,placeholder:\"Enter product name\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:productNameError})]}),/*#__PURE__*/_jsx(Button,{variant:\"success\",size:\"sm\",className:\"me-1\",onClick:saveProductName,children:/*#__PURE__*/_jsx(FaCheck,{})}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:cancelEditingName,children:/*#__PURE__*/_jsx(FaUndo,{})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-center align-items-center\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-0 me-2\",children:productName||(productDetails&&productDetails.fabric_definition_data?productDetails.fabric_definition_data.fabric_name:`Batch ID: ${id}`)}),/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",size:\"sm\",onClick:startEditingName,children:\"Edit Name\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-4 bg-white rounded mb-3\",children:/*#__PURE__*/_jsxs(Row,{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-3\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(FaMoneyBillWave,{className:\"me-2\"}),\"Pricing Information\"]}),!isEditingPrices&&/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",size:\"sm\",onClick:startEditingPrices,children:\"Edit Prices\"})]}),isEditingPrices?/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-light rounded\",children:/*#__PURE__*/_jsxs(Form,{onSubmit:e=>e.preventDefault(),children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Manufacture Price (LKR):\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:editManufacturePrice,onChange:e=>setEditManufacturePrice(e.target.value),isInvalid:!!priceEditErrors.editManufacturePrice}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:priceEditErrors.editManufacturePrice})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Selling Price (LKR):\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:editSellingPrice,onChange:e=>setEditSellingPrice(e.target.value),isInvalid:!!priceEditErrors.editSellingPrice}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:priceEditErrors.editSellingPrice})]})})]}),editManufacturePrice&&editSellingPrice&&parseFloat(editManufacturePrice)>0&&parseFloat(editSellingPrice)>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3 p-2 bg-info bg-opacity-10 rounded\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"New Profit Margin: \"}),((parseFloat(editSellingPrice)-parseFloat(editManufacturePrice))/parseFloat(editSellingPrice)*100).toFixed(2),\"%\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(Button,{type:\"button\",variant:\"success\",size:\"sm\",onClick:savePriceChanges,disabled:loading,children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-1\"}),\"Save Changes\"]}),/*#__PURE__*/_jsxs(Button,{type:\"button\",variant:\"secondary\",size:\"sm\",onClick:cancelEditingPrices,children:[/*#__PURE__*/_jsx(FaUndo,{className:\"me-1\"}),\"Cancel\"]})]})]})}):/*#__PURE__*/_jsx(Table,{bordered:true,hover:true,children:/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Manufacture Price:\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[\"LKR \",manufacturePrice]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Selling Price:\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[\"LKR \",sellingPrice]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Profit Margin:\"})}),/*#__PURE__*/_jsxs(\"td\",{children:[profitMargin,\"%\"]})]})]})}),productNotes&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaClipboardList,{className:\"me-2\"}),\"Notes\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-light rounded\",children:productNotes})]}),/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3 mt-4\",children:[/*#__PURE__*/_jsx(FaTags,{className:\"me-2\"}),\"Colors\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:fabricDetails.length>0?fabricDetails.map((detail,index)=>{var _detail$fabric_varian2,_detail$fabric_varian3;return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[renderColorSwatch(((_detail$fabric_varian2=detail.fabric_variant_data)===null||_detail$fabric_varian2===void 0?void 0:_detail$fabric_varian2.color)||detail.color||'gray'),/*#__PURE__*/_jsx(\"span\",{className:\"ms-2\",children:((_detail$fabric_varian3=detail.fabric_variant_data)===null||_detail$fabric_varian3===void 0?void 0:_detail$fabric_varian3.color_name)||detail.color})]},index);}):/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:\"No color information available\"})}),/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaBoxOpen,{className:\"me-2\"}),\"Size Distribution\"]}),renderSizeQuantityBars(),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4\",children:/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",className:\"w-100\",onClick:openPdfModal,children:[/*#__PURE__*/_jsx(FaFilePdf,{className:\"me-2\"}),\"Generate Product Report\"]})})]}),/*#__PURE__*/_jsxs(Col,{md:6,className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaImage,{className:\"me-2\"}),\"Product Images\"]}),existingImageUrls&&existingImageUrls.length>0?/*#__PURE__*/_jsxs(\"div\",{className:\"product-images-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"product-images-grid\",children:existingImageUrls.map((imageUrl,index)=>/*#__PURE__*/_jsxs(\"div\",{className:`product-image-item ${index===activeImageIndex?'active':''}`,onClick:()=>setActiveImageIndex(index),children:[/*#__PURE__*/_jsx(Image,{src:imageUrl,alt:`Product ${index+1}`,thumbnail:true,className:\"image-preview\"}),/*#__PURE__*/_jsx(\"span\",{className:\"image-number\",children:index+1})]},index))}),/*#__PURE__*/_jsx(\"div\",{className:\"main-image-container mt-3\",children:/*#__PURE__*/_jsx(Image,{src:existingImageUrls[activeImageIndex],alt:\"Product\",thumbnail:true,className:\"main-image-preview\",style:{maxHeight:\"250px\"}})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"p-5 bg-light rounded\",children:[/*#__PURE__*/_jsx(FaImage,{size:60,className:\"text-secondary\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-3 text-muted\",children:\"No images available\"})]})]})]})})]}):/*#__PURE__*/_jsx(\"div\",{className:\"slide-in\",children:/*#__PURE__*/_jsxs(Tabs,{id:\"product-approval-tabs\",activeKey:activeTab,onSelect:k=>setActiveTab(k),className:\"mb-4\",children:[/*#__PURE__*/_jsx(Tab,{eventKey:\"details\",title:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(FaInfoCircle,{className:\"me-2\"}),\"Product Details\"]}),children:/*#__PURE__*/_jsxs(Row,{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaInfoCircle,{className:\"me-2\"}),\"Product Name\"]}),isEditingName?/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),isInvalid:!!productNameError,placeholder:\"Enter product name\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:productNameError})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",className:\"me-1\",onClick:saveProductName,children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-1\"}),\" Save\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"secondary\",size:\"sm\",onClick:cancelEditingName,children:[/*#__PURE__*/_jsx(FaUndo,{className:\"me-1\"}),\" Cancel\"]})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 bg-light rounded mb-4 d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"strong\",{children:productName||(productDetails&&productDetails.fabric_definition_data?productDetails.fabric_definition_data.fabric_name:`Batch ID: ${id}`)}),/*#__PURE__*/_jsx(Button,{variant:\"outline-primary\",size:\"sm\",onClick:startEditingName,children:\"Edit Name\"})]}),/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaTags,{className:\"me-2\"}),\"Colors\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:fabricDetails.length>0?fabricDetails.map((detail,index)=>{var _detail$fabric_varian4,_detail$fabric_varian5;return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[renderColorSwatch(((_detail$fabric_varian4=detail.fabric_variant_data)===null||_detail$fabric_varian4===void 0?void 0:_detail$fabric_varian4.color)||detail.color||'gray'),/*#__PURE__*/_jsx(\"span\",{className:\"ms-2\",children:((_detail$fabric_varian5=detail.fabric_variant_data)===null||_detail$fabric_varian5===void 0?void 0:_detail$fabric_varian5.color_name)||detail.color})]},index);}):/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:\"No color information available\"})}),/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaBoxOpen,{className:\"me-2\"}),\"Size Distribution\"]}),renderSizeQuantityBars()]}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-3\",children:[/*#__PURE__*/_jsx(FaImage,{className:\"me-2\"}),\"Product Images\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-muted mb-3\",children:[\"Upload up to 10 images of the product (Current: \",productImages.length,\"/10)\"]}),imagePreviewUrls.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"product-images-container mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"product-images-grid\",children:imagePreviewUrls.map((previewUrl,index)=>/*#__PURE__*/_jsxs(\"div\",{className:`product-image-item ${index===activeImageIndex?'active':''}`,onClick:()=>setActiveImageIndex(index),children:[/*#__PURE__*/_jsx(\"div\",{className:\"image-actions\",children:/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",className:\"btn-remove-image\",onClick:e=>{e.stopPropagation();removeImage(index);},children:/*#__PURE__*/_jsx(FaTrash,{})})}),/*#__PURE__*/_jsx(Image,{src:previewUrl,alt:`Preview ${index+1}`,thumbnail:true,className:\"image-preview\"}),/*#__PURE__*/_jsx(\"span\",{className:\"image-number\",children:index+1})]},index))}),imagePreviewUrls.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"main-image-container mt-3\",children:/*#__PURE__*/_jsx(Image,{src:imagePreviewUrls[activeImageIndex],alt:\"Product Preview\",thumbnail:true,className:\"main-image-preview\",style:{maxHeight:\"250px\"}})})]}),isUploading?/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 bg-light rounded text-center\",children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-3\",children:\"Uploading Images\"}),/*#__PURE__*/_jsx(ProgressBar,{now:uploadProgress,label:`${Math.round(uploadProgress)}%`,variant:\"info\",animated:true,className:\"mb-3\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(FaUpload,{className:\"me-2 text-primary\"}),\"Uploading \",productImages.length,\" images...\"]})]}):productImages.length<10&&/*#__PURE__*/_jsxs(\"div\",{...getRootProps(),className:`image-upload-container ${isDragActive?'active':''}`,children:[/*#__PURE__*/_jsx(\"input\",{...getInputProps(),multiple:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(FaUpload,{size:40,className:\"mb-3 text-primary\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Drag & drop product images here, or click to select\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted small\",children:\"Supported formats: JPEG, PNG, GIF (Max: 5MB each)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted small\",children:\"You can select multiple images at once (Max: 10)\"})]})]}),/*#__PURE__*/_jsx(Form.Control,{type:\"file\",ref:fileInputRef,onChange:handleImageChange,accept:\"image/*\",multiple:true,style:{display:'none'}})]})})]})}),/*#__PURE__*/_jsx(Tab,{eventKey:\"pricing\",title:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(FaMoneyBillWave,{className:\"me-2\"}),\"Pricing\"]}),children:/*#__PURE__*/_jsxs(Form,{onSubmit:handleFormSubmit,className:\"mt-3\",children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(Form.Label,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Manufacture Price (LKR):\"}),/*#__PURE__*/_jsx(OverlayTrigger,{placement:\"top\",overlay:/*#__PURE__*/_jsx(Tooltip,{children:\"The cost to manufacture this product\"}),children:/*#__PURE__*/_jsx(FaInfoCircle,{className:\"ms-2 text-muted\"})})]}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:manufacturePrice,onChange:e=>setManufacturePrice(e.target.value),isInvalid:!!validationErrors.manufacturePrice,required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:validationErrors.manufacturePrice})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(Form.Label,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Selling Price (LKR):\"}),/*#__PURE__*/_jsx(OverlayTrigger,{placement:\"top\",overlay:/*#__PURE__*/_jsx(Tooltip,{children:\"The price at which this product will be sold\"}),children:/*#__PURE__*/_jsx(FaInfoCircle,{className:\"ms-2 text-muted\"})})]}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:sellingPrice,onChange:e=>setSellingPrice(e.target.value),isInvalid:!!validationErrors.sellingPrice,required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:validationErrors.sellingPrice})]}),manufacturePrice&&sellingPrice&&parseFloat(manufacturePrice)>0&&parseFloat(sellingPrice)>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-3 bg-light rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Profit Margin:\"})}),/*#__PURE__*/_jsxs(Badge,{bg:profitMargin<10?\"danger\":profitMargin<20?\"warning\":\"success\",children:[/*#__PURE__*/_jsx(FaPercentage,{className:\"me-1\"}),profitMargin,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"profit-margin-indicator\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between mt-1\",children:[/*#__PURE__*/_jsx(\"small\",{children:\"Low\"}),/*#__PURE__*/_jsx(\"small\",{children:\"High\"})]})]})]}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(Form.Label,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Product Notes:\"}),/*#__PURE__*/_jsx(OverlayTrigger,{placement:\"top\",overlay:/*#__PURE__*/_jsx(Tooltip,{children:\"Additional information about this product\"}),children:/*#__PURE__*/_jsx(FaInfoCircle,{className:\"ms-2 text-muted\"})})]}),/*#__PURE__*/_jsx(Form.Control,{as:\"textarea\",rows:5,value:productNotes,onChange:e=>setProductNotes(e.target.value),isInvalid:!!validationErrors.productNotes,placeholder:\"Enter any additional notes about this product...\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:validationErrors.productNotes}),/*#__PURE__*/_jsxs(Form.Text,{className:\"text-muted\",children:[productNotes?500-productNotes.length:500,\" characters remaining\"]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"d-grid gap-2 mt-4\",children:/*#__PURE__*/_jsxs(Button,{type:\"submit\",className:\"btn-approve\",size:\"lg\",children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-2\"}),\"Approve Product\"]})})]})})]})})]})})})})}),/*#__PURE__*/_jsx(ConfirmationModal,{}),/*#__PURE__*/_jsxs(Modal,{show:showPdfModal,onHide:closePdfModal,centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsxs(Modal.Title,{children:[/*#__PURE__*/_jsx(FaFilePdf,{className:\"text-danger me-2\"}),\"Generate Product Report\"]})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Are you sure you want to generate a PDF report for this product?\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-light p-3 rounded\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Product:\"}),\" \",productName||(productDetails&&productDetails.fabric_definition_data?productDetails.fabric_definition_data.fabric_name:`Batch ID: ${id}`)]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Manufacture Price:\"}),\" LKR \",manufacturePrice]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Selling Price:\"}),\" LKR \",sellingPrice]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Profit Margin:\"}),\" \",profitMargin,\"%\"]})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:closePdfModal,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:generateProductReport,disabled:pdfLoading,children:pdfLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Generating...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaDownload,{className:\"me-2\"}),\"Generate PDF\"]})})]})]})]});};export default ApproveFinishedProduct;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "axios", "useParams", "useNavigate", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Row", "Col", "Spinner", "Image", "Modal", "ProgressBar", "Badge", "Tabs", "Tab", "Table", "OverlayTrigger", "<PERSON><PERSON><PERSON>", "FaCheck", "FaUpload", "FaImage", "FaTags", "FaInfoCircle", "FaMoneyBillWave", "FaArrowRight", "FaPercentage", "FaBoxOpen", "FaClipboardList", "FaTrash", "FaUndo", "FaExclamationTriangle", "FaFilePdf", "FaDownload", "useDropzone", "RoleBasedNavBar", "jsPDF", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ApproveFinishedProduct", "id", "navigate", "fileInputRef", "manufacturePrice", "setManufacturePrice", "sellingPrice", "setSellingPrice", "productNotes", "setProductNotes", "error", "setError", "successMsg", "setSuccessMsg", "isApproved", "setIsApproved", "loading", "setLoading", "productImages", "setProductImages", "imagePreviewUrls", "setImagePreviewUrls", "existingImageUrls", "setExistingImageUrls", "isDragging", "setIsDragging", "activeImageIndex", "setActiveImageIndex", "uploadProgress", "setUploadProgress", "isUploading", "setIsUploading", "productDetails", "setProductDetails", "fabricDetails", "setFabricDetails", "sizeQuantities", "setSizeQuantities", "xs", "s", "m", "l", "xl", "activeTab", "setActiveTab", "showConfirmModal", "setShowConfirmModal", "showPdfModal", "setShowPdfModal", "pdfLoading", "setPdfLoading", "validationErrors", "setValidationErrors", "profitMargin", "setProfitMargin", "productName", "setProductName", "isEditingName", "setIsEditingName", "productNameError", "setProductNameError", "isEditingPrices", "setIsEditingPrices", "editManufacturePrice", "setEditManufacturePrice", "editSellingPrice", "setEditSellingPrice", "priceEditErrors", "setPriceEditErrors", "finishedProductId", "setFinishedProductId", "mPrice", "parseFloat", "sPrice", "margin", "toFixed", "retryCount", "setRetryCount", "maxRetries", "fetchProductData", "approvalRes", "get", "cuttingRes", "data", "is_approved", "manufacture_price", "selling_price", "finished_product_id", "product_images", "Array", "isArray", "product_image", "notes", "product_name", "fabric_definition_data", "fabric_name", "details", "length", "console", "log", "sizes", "for<PERSON>ach", "detail", "err", "errorMessage", "response", "status", "statusText", "request", "prev", "setTimeout", "handleImageChange", "e", "files", "from", "target", "processImageFiles", "newImages", "newPreviewUrls", "file", "type", "match", "size", "push", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "triggerFileInput", "current", "click", "onDrop", "acceptedFiles", "getRootProps", "getInputProps", "isDragActive", "accept", "maxFiles", "removeImage", "index", "splice", "Math", "max", "setActiveImage", "validateForm", "errors", "trim", "isNaN", "Object", "keys", "handleFormSubmit", "preventDefault", "handleSubmit", "formData", "FormData", "append", "image", "post", "headers", "onUploadProgress", "progressEvent", "percentCompleted", "round", "loaded", "total", "message", "errMsg", "JSON", "stringify", "handleCancelConfirmation", "openPdfModal", "closePdfModal", "startEditingName", "cancelEditingName", "saveProductName", "currentRecord", "payload", "fabric_definition", "cutting_date", "put", "updatedRecord", "checkErr", "startEditingPrices", "cancelEditingPrices", "validatePriceEdit", "savePriceChanges", "alert", "updateData", "patch", "generateProductReport", "doc", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "pdfProductName", "Date", "toLocaleDateString", "setDrawColor", "line", "yPos", "entries", "sizeHeaders", "totalQuantity", "reduce", "sum", "_ref", "_", "qty", "_ref2", "quantity", "percentage", "toUpperCase", "toString", "_detail$fabric_varian", "colorName", "fabric_variant_data", "color_name", "color", "splitNotes", "splitTextToSize", "toLocaleString", "cleanProductName", "replace", "save", "toISOString", "slice", "className", "style", "height", "children", "animation", "role", "variant", "renderColorSwatch", "bgColor", "startsWith", "test", "colorMap", "toLowerCase", "placement", "overlay", "backgroundColor", "renderSizeQuantityBars", "maxQuantity", "map", "_ref3", "_ref4", "now", "ConfirmationModal", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "bordered", "hover", "src", "alt", "thumbnail", "maxHeight", "Footer", "onClick", "md", "lg", "borderRadius", "Group", "max<PERSON><PERSON><PERSON>", "Control", "value", "onChange", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "onSubmit", "Label", "step", "min", "disabled", "_detail$fabric_varian2", "_detail$fabric_varian3", "imageUrl", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "_detail$fabric_varian4", "_detail$fabric_varian5", "previewUrl", "stopPropagation", "label", "animated", "multiple", "ref", "display", "required", "bg", "as", "rows", "Text"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/ApproveFinishedProduct.js"], "sourcesContent": ["// src/pages/ApproveFinishedProduct.jsx\r\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { Card, Form, Button, Alert, Row, Col, Spinner, Image, Modal, ProgressBar, Badge, Tabs, Tab, Table, OverlayTrigger, Tooltip } from 'react-bootstrap';\r\nimport {\r\n  FaCheck, FaUpload, FaImage, FaTags, FaInfoCircle, FaMoneyBillWave,\r\n  FaArrowRight, FaPercentage, FaBoxOpen, FaClipboardList,\r\n  FaTrash, FaUndo, FaExclamationTriangle, FaFilePdf, FaDownload\r\n} from 'react-icons/fa';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport './ApproveFinishedProduct.css';\r\nimport { jsPDF } from 'jspdf';\r\n// No need to import uploadMultipleImages as we're using FormData directly\r\n\r\nconst ApproveFinishedProduct = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Basic form state\r\n  const [manufacturePrice, setManufacturePrice] = useState('');\r\n  const [sellingPrice, setSellingPrice] = useState('');\r\n  const [productNotes, setProductNotes] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [successMsg, setSuccessMsg] = useState('');\r\n  const [isApproved, setIsApproved] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Image handling state\r\n  const [productImages, setProductImages] = useState([]);\r\n  const [imagePreviewUrls, setImagePreviewUrls] = useState([]);\r\n  const [existingImageUrls, setExistingImageUrls] = useState([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [activeImageIndex, setActiveImageIndex] = useState(0);\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n\r\n  // Product details state\r\n  const [productDetails, setProductDetails] = useState(null);\r\n  const [fabricDetails, setFabricDetails] = useState([]);\r\n  const [sizeQuantities, setSizeQuantities] = useState({\r\n    xs: 0, s: 0, m: 0, l: 0, xl: 0\r\n  });\r\n\r\n  // UI state\r\n  const [activeTab, setActiveTab] = useState('details');\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [pdfLoading, setPdfLoading] = useState(false);\r\n  const [validationErrors, setValidationErrors] = useState({});\r\n  const [profitMargin, setProfitMargin] = useState(0);\r\n\r\n  // Product name editing state\r\n  const [productName, setProductName] = useState('');\r\n  const [isEditingName, setIsEditingName] = useState(false);\r\n  const [productNameError, setProductNameError] = useState('');\r\n\r\n  // Price editing state for approved products\r\n  const [isEditingPrices, setIsEditingPrices] = useState(false);\r\n  const [editManufacturePrice, setEditManufacturePrice] = useState('');\r\n  const [editSellingPrice, setEditSellingPrice] = useState('');\r\n  const [priceEditErrors, setPriceEditErrors] = useState({});\r\n  const [finishedProductId, setFinishedProductId] = useState(null);\r\n\r\n  // Calculate profit margin whenever prices change\r\n  useEffect(() => {\r\n    if (manufacturePrice && sellingPrice) {\r\n      const mPrice = parseFloat(manufacturePrice);\r\n      const sPrice = parseFloat(sellingPrice);\r\n\r\n      if (mPrice > 0 && sPrice > 0) {\r\n        const margin = ((sPrice - mPrice) / sPrice) * 100;\r\n        setProfitMargin(margin.toFixed(2));\r\n      }\r\n    }\r\n  }, [manufacturePrice, sellingPrice]);\r\n\r\n  // State for retry mechanism\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const maxRetries = 3;\r\n\r\n  // Function to fetch product data with retry mechanism\r\n  const fetchProductData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n\r\n      // Fetch approval status\r\n      const approvalRes = await axios.get(`http://localhost:8000/api/finished_product/status/${id}/`);\r\n\r\n      // Fetch cutting record details\r\n      const cuttingRes = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n\r\n      if (approvalRes.data && approvalRes.data.is_approved) {\r\n        setIsApproved(true);\r\n        setManufacturePrice(approvalRes.data.manufacture_price);\r\n        setSellingPrice(approvalRes.data.selling_price);\r\n        setFinishedProductId(approvalRes.data.finished_product_id);\r\n\r\n        // Set existing image URLs if available\r\n        if (approvalRes.data.product_images && Array.isArray(approvalRes.data.product_images)) {\r\n          setExistingImageUrls(approvalRes.data.product_images);\r\n        } else if (approvalRes.data.product_image) {\r\n          // For backward compatibility with single image\r\n          setExistingImageUrls([approvalRes.data.product_image]);\r\n        }\r\n\r\n        if (approvalRes.data.notes) {\r\n          setProductNotes(approvalRes.data.notes);\r\n        }\r\n      }\r\n\r\n      if (cuttingRes.data) {\r\n        setProductDetails(cuttingRes.data);\r\n\r\n        // Set product name from API response\r\n        if (cuttingRes.data.product_name) {\r\n          setProductName(cuttingRes.data.product_name);\r\n        } else if (cuttingRes.data.fabric_definition_data) {\r\n          // If no product name, use fabric name as default\r\n          setProductName(cuttingRes.data.fabric_definition_data.fabric_name);\r\n        }\r\n\r\n        // Extract fabric details\r\n        if (cuttingRes.data.details && cuttingRes.data.details.length > 0) {\r\n          // Debug: Log the fabric details to see what color data we're getting\r\n          console.log('Fabric details from API:', cuttingRes.data.details);\r\n\r\n          setFabricDetails(cuttingRes.data.details);\r\n\r\n          // Calculate size quantities\r\n          const sizes = {xs: 0, s: 0, m: 0, l: 0, xl: 0};\r\n          cuttingRes.data.details.forEach(detail => {\r\n            sizes.xs += detail.xs || 0;\r\n            sizes.s += detail.s || 0;\r\n            sizes.m += detail.m || 0;\r\n            sizes.l += detail.l || 0;\r\n            sizes.xl += detail.xl || 0;\r\n          });\r\n          setSizeQuantities(sizes);\r\n        }\r\n      }\r\n\r\n      // Reset retry count on success\r\n      setRetryCount(0);\r\n    } catch (err) {\r\n      console.error(\"Failed to fetch product data:\", err);\r\n\r\n      // Provide more detailed error message\r\n      const errorMessage = err.response\r\n        ? `Error: ${err.response.status} - ${err.response.statusText}`\r\n        : err.request\r\n          ? \"No response received from server. Check if the backend is running.\"\r\n          : \"Failed to make request. Check your network connection.\";\r\n\r\n      setError(`Unable to fetch product data. ${errorMessage}`);\r\n\r\n      // Implement retry mechanism\r\n      if (retryCount < maxRetries) {\r\n        setRetryCount(prev => prev + 1);\r\n        setTimeout(() => {\r\n          fetchProductData();\r\n        }, 2000); // Wait 2 seconds before retrying\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [id, retryCount]);\r\n\r\n  // Fetch product details and approval status\r\n  useEffect(() => {\r\n    fetchProductData();\r\n  }, [fetchProductData]);\r\n\r\n  // Handle image selection from file input\r\n  const handleImageChange = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    if (files.length > 0) {\r\n      processImageFiles(files);\r\n    }\r\n  };\r\n\r\n  // Process the selected image files\r\n  const processImageFiles = useCallback((files) => {\r\n    // Check if adding these files would exceed the limit\r\n    if (productImages.length + files.length > 10) {\r\n      setError(`You can only upload up to 10 images. You already have ${productImages.length} images.`);\r\n      return;\r\n    }\r\n\r\n    const newImages = [];\r\n    const newPreviewUrls = [...imagePreviewUrls];\r\n\r\n    files.forEach(file => {\r\n      // Validate file type\r\n      if (!file.type.match('image.*')) {\r\n        setError('Please select image files only (JPEG, PNG, etc.)');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        setError('Each image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      newImages.push(file);\r\n\r\n      // Create a preview URL\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        newPreviewUrls.push(reader.result);\r\n        setImagePreviewUrls([...newPreviewUrls]);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    });\r\n\r\n    setProductImages([...productImages, ...newImages]);\r\n  }, [productImages, imagePreviewUrls]);\r\n\r\n  // Trigger file input click\r\n  const triggerFileInput = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  // Handle drag and drop functionality\r\n  const onDrop = useCallback((acceptedFiles) => {\r\n    if (acceptedFiles && acceptedFiles.length > 0) {\r\n      processImageFiles(acceptedFiles);\r\n    }\r\n  }, [processImageFiles]);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif']\r\n    },\r\n    maxFiles: 10\r\n  });\r\n\r\n  // Remove an uploaded image\r\n  const removeImage = (index) => {\r\n    const newImages = [...productImages];\r\n    const newPreviewUrls = [...imagePreviewUrls];\r\n\r\n    newImages.splice(index, 1);\r\n    newPreviewUrls.splice(index, 1);\r\n\r\n    setProductImages(newImages);\r\n    setImagePreviewUrls(newPreviewUrls);\r\n\r\n    // Adjust active index if needed\r\n    if (index === activeImageIndex) {\r\n      setActiveImageIndex(Math.max(0, index - 1));\r\n    } else if (index < activeImageIndex) {\r\n      setActiveImageIndex(activeImageIndex - 1);\r\n    }\r\n  };\r\n\r\n  // Set active image\r\n  const setActiveImage = (index) => {\r\n    setActiveImageIndex(index);\r\n  };\r\n\r\n  // Validate form inputs\r\n  const validateForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate manufacture price\r\n    if (!manufacturePrice || manufacturePrice.trim() === '') {\r\n      errors.manufacturePrice = \"Manufacture price is required\";\r\n    } else if (parseFloat(manufacturePrice) <= 0) {\r\n      errors.manufacturePrice = \"Manufacture price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(manufacturePrice))) {\r\n      errors.manufacturePrice = \"Manufacture price must be a valid number\";\r\n    }\r\n\r\n    // Validate selling price\r\n    if (!sellingPrice || sellingPrice.trim() === '') {\r\n      errors.sellingPrice = \"Selling price is required\";\r\n    } else if (parseFloat(sellingPrice) <= 0) {\r\n      errors.sellingPrice = \"Selling price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(sellingPrice))) {\r\n      errors.sellingPrice = \"Selling price must be a valid number\";\r\n    } else if (parseFloat(sellingPrice) < parseFloat(manufacturePrice)) {\r\n      errors.sellingPrice = \"Selling price should be greater than or equal to manufacture price\";\r\n    }\r\n\r\n    // Validate product notes (optional)\r\n    if (productNotes && productNotes.length > 500) {\r\n      errors.productNotes = \"Notes should be less than 500 characters\";\r\n    }\r\n\r\n    setValidationErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Show confirmation modal\r\n  const handleFormSubmit = (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    // Show confirmation modal\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  // Handle actual submission\r\n  const handleSubmit = async () => {\r\n    setError('');\r\n    setSuccessMsg('');\r\n    setShowConfirmModal(false);\r\n\r\n    try {\r\n      // Show loading state\r\n      setLoading(true);\r\n      setIsUploading(true);\r\n\r\n      // Create FormData object for API request\r\n      const formData = new FormData();\r\n      formData.append('cutting_record', id);\r\n      formData.append('manufacture_price', parseFloat(manufacturePrice));\r\n      formData.append('selling_price', parseFloat(sellingPrice));\r\n\r\n      if (productNotes) {\r\n        formData.append('notes', productNotes);\r\n      }\r\n\r\n      // Add images directly to the FormData if there are any\r\n      if (productImages && productImages.length > 0) {\r\n        setUploadProgress(0);\r\n\r\n        // Append each image to the FormData\r\n        productImages.forEach(image => {\r\n          formData.append('product_images', image);\r\n        });\r\n      }\r\n\r\n      // Make the API request with progress tracking\r\n      const response = await axios.post(\r\n        'http://localhost:8000/api/finished_product/approve/',\r\n        formData,\r\n        {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n          onUploadProgress: (progressEvent) => {\r\n            const percentCompleted = Math.round(\r\n              (progressEvent.loaded * 100) / progressEvent.total\r\n            );\r\n            setUploadProgress(percentCompleted);\r\n          }\r\n        }\r\n      );\r\n\r\n      setSuccessMsg(response.data.message || 'Product approved successfully!');\r\n      setIsUploading(false);\r\n\r\n      // Redirect after a delay\r\n      setTimeout(() => {\r\n        navigate('/approveproduct-list');\r\n      }, 2000);\r\n    } catch (err) {\r\n      console.error(\"Error approving finished product:\", err);\r\n      const errMsg = err.response && err.response.data\r\n        ? typeof err.response.data === 'object'\r\n          ? JSON.stringify(err.response.data)\r\n          : err.response.data\r\n        : \"Failed to approve finished product. Please try again.\";\r\n      setError(errMsg);\r\n      setIsUploading(false);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Cancel confirmation\r\n  const handleCancelConfirmation = () => {\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  // Open PDF modal\r\n  const openPdfModal = () => {\r\n    setShowPdfModal(true);\r\n  };\r\n\r\n  // Close PDF modal\r\n  const closePdfModal = () => {\r\n    setShowPdfModal(false);\r\n  };\r\n\r\n  // Start editing product name\r\n  const startEditingName = () => {\r\n    setIsEditingName(true);\r\n  };\r\n\r\n  // Cancel editing product name\r\n  const cancelEditingName = () => {\r\n    setIsEditingName(false);\r\n    setProductNameError('');\r\n    // Reset to original name from product details\r\n    if (productDetails && productDetails.product_name) {\r\n      setProductName(productDetails.product_name);\r\n    } else if (productDetails && productDetails.fabric_definition_data) {\r\n      setProductName(productDetails.fabric_definition_data.fabric_name);\r\n    }\r\n  };\r\n\r\n  // Save updated product name\r\n  const saveProductName = async () => {\r\n    // Validate product name\r\n    if (!productName.trim()) {\r\n      setProductNameError('Product name cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setProductNameError('');\r\n    setLoading(true);\r\n\r\n    try {\r\n      // First, get the current cutting record data\r\n      const currentRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n\r\n      // Create a payload with all the required fields, including details\r\n      const payload = {\r\n        fabric_definition: currentRecord.data.fabric_definition,\r\n        cutting_date: currentRecord.data.cutting_date,\r\n        product_name: productName,\r\n        details: currentRecord.data.details  // Include existing details without modification\r\n      };\r\n\r\n      // Update the product name in the backend\r\n      await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\r\n\r\n      // Update local state\r\n      setProductDetails({\r\n        ...productDetails,\r\n        product_name: productName\r\n      });\r\n\r\n      setIsEditingName(false);\r\n      setSuccessMsg('Product name updated successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMsg('');\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Error updating product name:', err);\r\n\r\n      // Check if the product name was actually updated despite the error\r\n      try {\r\n        const updatedRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n        if (updatedRecord.data.product_name === productName) {\r\n          // If the name was updated successfully despite the error, show success message\r\n          setProductDetails({\r\n            ...productDetails,\r\n            product_name: productName\r\n          });\r\n          setIsEditingName(false);\r\n          setSuccessMsg('Product name updated successfully');\r\n\r\n          // Clear success message after 3 seconds\r\n          setTimeout(() => {\r\n            setSuccessMsg('');\r\n          }, 3000);\r\n          return;\r\n        }\r\n      } catch (checkErr) {\r\n        // If we can't check, just show the original error\r\n        console.error('Error checking product name update:', checkErr);\r\n      }\r\n\r\n      // Show error message if the name wasn't updated\r\n      setError('Failed to update product name. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Start editing prices\r\n  const startEditingPrices = () => {\r\n    setEditManufacturePrice(manufacturePrice);\r\n    setEditSellingPrice(sellingPrice);\r\n    setIsEditingPrices(true);\r\n    setPriceEditErrors({});\r\n  };\r\n\r\n  // Cancel editing prices\r\n  const cancelEditingPrices = () => {\r\n    setIsEditingPrices(false);\r\n    setEditManufacturePrice('');\r\n    setEditSellingPrice('');\r\n    setPriceEditErrors({});\r\n  };\r\n\r\n  // Validate price editing form\r\n  const validatePriceEdit = () => {\r\n    const errors = {};\r\n\r\n    // Validate manufacture price\r\n    if (!editManufacturePrice || editManufacturePrice.trim() === '') {\r\n      errors.editManufacturePrice = \"Manufacture price is required\";\r\n    } else if (parseFloat(editManufacturePrice) <= 0) {\r\n      errors.editManufacturePrice = \"Manufacture price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(editManufacturePrice))) {\r\n      errors.editManufacturePrice = \"Manufacture price must be a valid number\";\r\n    }\r\n\r\n    // Validate selling price\r\n    if (!editSellingPrice || editSellingPrice.trim() === '') {\r\n      errors.editSellingPrice = \"Selling price is required\";\r\n    } else if (parseFloat(editSellingPrice) <= 0) {\r\n      errors.editSellingPrice = \"Selling price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(editSellingPrice))) {\r\n      errors.editSellingPrice = \"Selling price must be a valid number\";\r\n    } else if (parseFloat(editSellingPrice) < parseFloat(editManufacturePrice)) {\r\n      errors.editSellingPrice = \"Selling price should be greater than or equal to manufacture price\";\r\n    }\r\n\r\n    setPriceEditErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Save updated prices\r\n  const savePriceChanges = async () => {\r\n    alert('Save button clicked!'); // Debug alert\r\n    console.log('savePriceChanges called');\r\n    console.log('editManufacturePrice:', editManufacturePrice);\r\n    console.log('editSellingPrice:', editSellingPrice);\r\n    console.log('finishedProductId:', finishedProductId);\r\n\r\n    if (!validatePriceEdit()) {\r\n      console.log('Validation failed');\r\n      return;\r\n    }\r\n\r\n    if (!finishedProductId) {\r\n      console.log('No finished product ID');\r\n      setError('Could not find finished product record');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      // Update the prices using the PATCH endpoint\r\n      const updateData = {\r\n        manufacture_price: parseFloat(editManufacturePrice),\r\n        selling_price: parseFloat(editSellingPrice)\r\n      };\r\n\r\n      console.log('Sending update data:', updateData);\r\n      await axios.patch(`http://localhost:8000/api/finished_product/update/${finishedProductId}/`, updateData);\r\n\r\n      // Update local state\r\n      setManufacturePrice(editManufacturePrice);\r\n      setSellingPrice(editSellingPrice);\r\n      setIsEditingPrices(false);\r\n      setSuccessMsg('Prices updated successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMsg('');\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Error updating prices:', err);\r\n      const errMsg = err.response && err.response.data\r\n        ? typeof err.response.data === 'object'\r\n          ? JSON.stringify(err.response.data)\r\n          : err.response.data\r\n        : \"Failed to update prices. Please try again.\";\r\n      setError(errMsg);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Generate PDF report for the product\r\n  const generateProductReport = () => {\r\n    setPdfLoading(true);\r\n\r\n    try {\r\n      // Create PDF document\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes\r\n      const titleFontSize = 16;\r\n      const headingFontSize = 12;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add header\r\n      doc.setFontSize(titleFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Product Report', 105, 20, { align: 'center' });\r\n\r\n      // Add product name\r\n      const pdfProductName = productName || (productDetails && productDetails.fabric_definition_data\r\n        ? productDetails.fabric_definition_data.fabric_name\r\n        : `Batch ID: ${id}`);\r\n      doc.setFontSize(headingFontSize);\r\n      doc.text(pdfProductName, 105, 30, { align: 'center' });\r\n\r\n      // Add approval date\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Approval Date: ${new Date().toLocaleDateString()}`, 105, 40, { align: 'center' });\r\n\r\n      // Add horizontal line\r\n      doc.setDrawColor(200, 200, 200);\r\n      doc.line(20, 45, 190, 45);\r\n\r\n      // Start Y position for content\r\n      let yPos = 55;\r\n\r\n      // Add pricing information\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Pricing Information', 20, yPos);\r\n      yPos += 10;\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Manufacture Price: LKR ${manufacturePrice}`, 25, yPos);\r\n      yPos += 7;\r\n      doc.text(`Selling Price: LKR ${sellingPrice}`, 25, yPos);\r\n      yPos += 7;\r\n      doc.text(`Profit Margin: ${profitMargin}%`, 25, yPos);\r\n      yPos += 15;\r\n\r\n      // Add size distribution\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Size Distribution', 20, yPos);\r\n      yPos += 10;\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n\r\n      // Create a table for size distribution\r\n      const sizes = Object.entries(sizeQuantities);\r\n      const sizeHeaders = ['Size', 'Quantity', 'Percentage'];\r\n      const totalQuantity = sizes.reduce((sum, [_, qty]) => sum + qty, 0);\r\n\r\n      // Draw table headers\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text(sizeHeaders[0], 25, yPos);\r\n      doc.text(sizeHeaders[1], 60, yPos);\r\n      doc.text(sizeHeaders[2], 95, yPos);\r\n      yPos += 7;\r\n\r\n      // Draw table rows\r\n      doc.setFont('helvetica', 'normal');\r\n      sizes.forEach(([size, quantity]) => {\r\n        const percentage = totalQuantity > 0 ? ((quantity / totalQuantity) * 100).toFixed(1) : '0.0';\r\n        doc.text(size.toUpperCase(), 25, yPos);\r\n        doc.text(quantity.toString(), 60, yPos);\r\n        doc.text(`${percentage}%`, 95, yPos);\r\n        yPos += 7;\r\n      });\r\n\r\n      // Add total row\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Total', 25, yPos);\r\n      doc.text(totalQuantity.toString(), 60, yPos);\r\n      doc.text('100.0%', 95, yPos);\r\n      yPos += 15;\r\n\r\n      // Add color information\r\n      if (fabricDetails.length > 0) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Color Information', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n\r\n        fabricDetails.forEach((detail, index) => {\r\n          const colorName = detail.fabric_variant_data?.color_name || detail.color || 'N/A';\r\n          doc.text(`Color ${index + 1}: ${colorName}`, 25, yPos);\r\n          yPos += 7;\r\n        });\r\n\r\n        yPos += 8;\r\n      }\r\n\r\n      // Add product notes if available\r\n      if (productNotes) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Product Notes', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n\r\n        // Split notes into multiple lines if needed\r\n        const splitNotes = doc.splitTextToSize(productNotes, 160);\r\n        doc.text(splitNotes, 25, yPos);\r\n        yPos += splitNotes.length * 7 + 8;\r\n      }\r\n\r\n      // Add image information\r\n      if (existingImageUrls && existingImageUrls.length > 0) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Product Images', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n        doc.text(`Number of Images: ${existingImageUrls.length}`, 25, yPos);\r\n        yPos += 7;\r\n        doc.text('Note: Images can be viewed in the system', 25, yPos);\r\n        yPos += 15;\r\n      }\r\n\r\n      // Add footer\r\n      doc.setFontSize(smallFontSize);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      doc.text('Pri Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Product_Report_${cleanProductName}_${new Date().toISOString().slice(0, 10)}.pdf`);\r\n\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      setError(`Failed to generate PDF: ${error.message}`);\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Loading spinner\r\n  if (loading) return (\r\n    <div className=\"d-flex justify-content-center align-items-center\" style={{ height: \"100vh\" }}>\r\n      <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n        <span className=\"visually-hidden\">Loading...</span>\r\n      </Spinner>\r\n    </div>\r\n  );\r\n\r\n  // Render color swatch\r\n  const renderColorSwatch = (color) => {\r\n    if (!color) return null;\r\n\r\n    // For debugging - log the color value we're receiving\r\n    console.log('Color value received:', color);\r\n\r\n    // SIMPLIFIED APPROACH: Directly use the color value if it looks like a hex code\r\n    let bgColor;\r\n\r\n    // If it starts with #, use it directly\r\n    if (color.startsWith('#')) {\r\n      bgColor = color;\r\n    }\r\n    // If it looks like a hex code without #, add the #\r\n    else if (/^[0-9A-Fa-f]{6}$/.test(color) || /^[0-9A-Fa-f]{3}$/.test(color)) {\r\n      bgColor = `#${color}`;\r\n    }\r\n    // For named colors like \"Black\", \"Red\", etc.\r\n    else {\r\n      // Common color names mapping\r\n      const colorMap = {\r\n        'red': '#dc3545',\r\n        'blue': '#0d6efd',\r\n        'green': '#198754',\r\n        'yellow': '#ffc107',\r\n        'black': '#212529',\r\n        'white': '#f8f9fa',\r\n        'purple': '#6f42c1',\r\n        'orange': '#fd7e14',\r\n        'pink': '#d63384',\r\n        'brown': '#8B4513',\r\n        'gray': '#6c757d',\r\n      };\r\n\r\n      // Try to get from color map or use the name directly\r\n      bgColor = colorMap[color.toLowerCase()] || color;\r\n    }\r\n\r\n    return (\r\n      <OverlayTrigger\r\n        placement=\"top\"\r\n        overlay={<Tooltip className=\"custom-tooltip\">{color}</Tooltip>}\r\n      >\r\n        <div\r\n          className=\"color-swatch\"\r\n          style={{ backgroundColor: bgColor }}\r\n          data-color={color}\r\n        />\r\n      </OverlayTrigger>\r\n    );\r\n  };\r\n\r\n  // Render size quantity bars\r\n  const renderSizeQuantityBars = () => {\r\n    const sizes = Object.entries(sizeQuantities);\r\n    const maxQuantity = Math.max(...sizes.map(([_, qty]) => qty));\r\n\r\n    return sizes.map(([size, quantity]) => (\r\n      <div key={size} className=\"mb-2\">\r\n        <div className=\"d-flex justify-content-between mb-1\">\r\n          <span><strong>{size.toUpperCase()}</strong></span>\r\n          <span>{quantity} pcs</span>\r\n        </div>\r\n        <ProgressBar\r\n          now={maxQuantity ? (quantity / maxQuantity) * 100 : 0}\r\n          variant={quantity > 0 ? \"info\" : \"light\"}\r\n          className=\"size-quantity-bar\"\r\n        />\r\n      </div>\r\n    ));\r\n  };\r\n\r\n  // Confirmation Modal\r\n  const ConfirmationModal = () => (\r\n    <Modal\r\n      show={showConfirmModal}\r\n      onHide={handleCancelConfirmation}\r\n      centered\r\n      className=\"confirmation-modal\"\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title>Confirm Product Approval</Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        <p>Are you sure you want to approve this product with the following details?</p>\r\n        <Table bordered hover size=\"sm\" className=\"mt-3\">\r\n          <tbody>\r\n            <tr>\r\n              <td><strong>Product Name:</strong></td>\r\n              <td>{productName || (productDetails && productDetails.fabric_definition_data ?\r\n                productDetails.fabric_definition_data.fabric_name :\r\n                `Batch ID: ${id}`)}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Manufacture Price:</strong></td>\r\n              <td>LKR {manufacturePrice}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Selling Price:</strong></td>\r\n              <td>LKR {sellingPrice}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Profit Margin:</strong></td>\r\n              <td>{profitMargin}%</td>\r\n            </tr>\r\n            {productNotes && (\r\n              <tr>\r\n                <td><strong>Notes:</strong></td>\r\n                <td>{productNotes}</td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </Table>\r\n        {imagePreviewUrls.length > 0 && (\r\n          <div className=\"text-center mt-3\">\r\n            <p><strong>Product Image:</strong></p>\r\n            <Image\r\n              src={imagePreviewUrls[0]}\r\n              alt=\"Product\"\r\n              thumbnail\r\n              style={{ maxHeight: \"100px\" }}\r\n            />\r\n          </div>\r\n        )}\r\n      </Modal.Body>\r\n      <Modal.Footer>\r\n        <Button variant=\"secondary\" onClick={handleCancelConfirmation}>\r\n          Cancel\r\n        </Button>\r\n        <Button variant=\"success\" onClick={handleSubmit}>\r\n          <FaCheck className=\"me-2\" />\r\n          Confirm Approval\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div className=\"main-content\">\r\n        <Row className=\"justify-content-center\">\r\n          <Col md={10} lg={8}>\r\n            <Card className=\"shadow product-card slide-in\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n              <Card.Body>\r\n                <h2 className=\"text-center mb-3\">Approve Finished Product</h2>\r\n\r\n                {/* Product Name with Edit Functionality */}\r\n                <div className=\"text-center mb-4\">\r\n                  {isEditingName ? (\r\n                    <div className=\"d-flex justify-content-center align-items-center\">\r\n                      <Form.Group className=\"mb-0 me-2 flex-grow-1\" style={{ maxWidth: '300px' }}>\r\n                        <Form.Control\r\n                          type=\"text\"\r\n                          value={productName}\r\n                          onChange={(e) => setProductName(e.target.value)}\r\n                          isInvalid={!!productNameError}\r\n                          placeholder=\"Enter product name\"\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {productNameError}\r\n                        </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                      <Button\r\n                        variant=\"success\"\r\n                        size=\"sm\"\r\n                        className=\"me-1\"\r\n                        onClick={saveProductName}\r\n                      >\r\n                        <FaCheck />\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"secondary\"\r\n                        size=\"sm\"\r\n                        onClick={cancelEditingName}\r\n                      >\r\n                        <FaUndo />\r\n                      </Button>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"d-flex justify-content-center align-items-center\">\r\n                      <p className=\"text-center text-muted mb-0 me-2\">\r\n                        {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                          productDetails.fabric_definition_data.fabric_name :\r\n                          `Batch ID: ${id}`)}\r\n                      </p>\r\n                      <Button\r\n                        variant=\"outline-primary\"\r\n                        size=\"sm\"\r\n                        onClick={startEditingName}\r\n                      >\r\n                        Edit Name\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {error && (\r\n                  <Alert variant=\"danger\" className=\"mb-4 fade-in\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div>\r\n                        <FaExclamationTriangle className=\"me-2\" />\r\n                        {error}\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={fetchProductData}\r\n                      >\r\n                        Retry\r\n                      </Button>\r\n                    </div>\r\n                  </Alert>\r\n                )}\r\n\r\n                {successMsg && (\r\n                  <Alert variant=\"success\" className=\"mb-4 fade-in\">\r\n                    <FaCheck className=\"me-2\" />\r\n                    {successMsg}\r\n                  </Alert>\r\n                )}\r\n\r\n                {isApproved ? (\r\n                  <div className=\"p-4 bg-white rounded mb-3 slide-in\">\r\n                    <h4 className=\"text-center mb-4 text-success\">\r\n                      <FaCheck className=\"me-2\" />\r\n                      Product Already Approved\r\n                    </h4>\r\n\r\n                    <div className=\"text-center mb-4\">\r\n                      {isEditingName ? (\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <Form.Group className=\"mb-0 me-2 flex-grow-1\" style={{ maxWidth: '300px' }}>\r\n                            <Form.Control\r\n                              type=\"text\"\r\n                              value={productName}\r\n                              onChange={(e) => setProductName(e.target.value)}\r\n                              isInvalid={!!productNameError}\r\n                              placeholder=\"Enter product name\"\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {productNameError}\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                          <Button\r\n                            variant=\"success\"\r\n                            size=\"sm\"\r\n                            className=\"me-1\"\r\n                            onClick={saveProductName}\r\n                          >\r\n                            <FaCheck />\r\n                          </Button>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"sm\"\r\n                            onClick={cancelEditingName}\r\n                          >\r\n                            <FaUndo />\r\n                          </Button>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <h5 className=\"mb-0 me-2\">\r\n                            {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                              productDetails.fabric_definition_data.fabric_name :\r\n                              `Batch ID: ${id}`)}\r\n                          </h5>\r\n                          <Button\r\n                            variant=\"outline-primary\"\r\n                            size=\"sm\"\r\n                            onClick={startEditingName}\r\n                          >\r\n                            Edit Name\r\n                          </Button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"p-4 bg-white rounded mb-3\">\r\n                        <Row className=\"mt-3\">\r\n                          <Col md={6}>\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                              <h5 className=\"mb-0\"><FaMoneyBillWave className=\"me-2\" />Pricing Information</h5>\r\n                              {!isEditingPrices && (\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={startEditingPrices}\r\n                                >\r\n                                  Edit Prices\r\n                                </Button>\r\n                              )}\r\n                            </div>\r\n\r\n                            {isEditingPrices ? (\r\n                              <div className=\"p-3 bg-light rounded\">\r\n                                <Form onSubmit={(e) => e.preventDefault()}>\r\n                                  <Row>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Manufacture Price (LKR):</strong></Form.Label>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={editManufacturePrice}\r\n                                          onChange={(e) => setEditManufacturePrice(e.target.value)}\r\n                                          isInvalid={!!priceEditErrors.editManufacturePrice}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {priceEditErrors.editManufacturePrice}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Selling Price (LKR):</strong></Form.Label>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={editSellingPrice}\r\n                                          onChange={(e) => setEditSellingPrice(e.target.value)}\r\n                                          isInvalid={!!priceEditErrors.editSellingPrice}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {priceEditErrors.editSellingPrice}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n\r\n                                  {editManufacturePrice && editSellingPrice && parseFloat(editManufacturePrice) > 0 && parseFloat(editSellingPrice) > 0 && (\r\n                                    <div className=\"mb-3 p-2 bg-info bg-opacity-10 rounded\">\r\n                                      <strong>New Profit Margin: </strong>\r\n                                      {(((parseFloat(editSellingPrice) - parseFloat(editManufacturePrice)) / parseFloat(editSellingPrice)) * 100).toFixed(2)}%\r\n                                    </div>\r\n                                  )}\r\n\r\n                                  <div className=\"d-flex gap-2\">\r\n                                    <Button\r\n                                      type=\"button\"\r\n                                      variant=\"success\"\r\n                                      size=\"sm\"\r\n                                      onClick={savePriceChanges}\r\n                                      disabled={loading}\r\n                                    >\r\n                                      <FaCheck className=\"me-1\" />\r\n                                      Save Changes\r\n                                    </Button>\r\n                                    <Button\r\n                                      type=\"button\"\r\n                                      variant=\"secondary\"\r\n                                      size=\"sm\"\r\n                                      onClick={cancelEditingPrices}\r\n                                    >\r\n                                      <FaUndo className=\"me-1\" />\r\n                                      Cancel\r\n                                    </Button>\r\n                                  </div>\r\n                                </Form>\r\n                              </div>\r\n                            ) : (\r\n                              <Table bordered hover>\r\n                                <tbody>\r\n                                  <tr>\r\n                                    <td><strong>Manufacture Price:</strong></td>\r\n                                    <td>LKR {manufacturePrice}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <td><strong>Selling Price:</strong></td>\r\n                                    <td>LKR {sellingPrice}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <td><strong>Profit Margin:</strong></td>\r\n                                    <td>{profitMargin}%</td>\r\n                                  </tr>\r\n                                </tbody>\r\n                              </Table>\r\n                            )}\r\n\r\n                            {productNotes && (\r\n                              <div className=\"mt-4\">\r\n                                <h5 className=\"mb-3\"><FaClipboardList className=\"me-2\" />Notes</h5>\r\n                                <div className=\"p-3 bg-light rounded\">\r\n                                  {productNotes}\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n\r\n                            <h5 className=\"mb-3 mt-4\"><FaTags className=\"me-2\" />Colors</h5>\r\n                            <div className=\"mb-4\">\r\n                              {fabricDetails.length > 0 ? (\r\n                                fabricDetails.map((detail, index) => (\r\n                                  <div key={index} className=\"mb-2\">\r\n                                    {renderColorSwatch(detail.fabric_variant_data?.color || detail.color || 'gray')}\r\n                                    <span className=\"ms-2\">{detail.fabric_variant_data?.color_name || detail.color}</span>\r\n                                  </div>\r\n                                ))\r\n                              ) : (\r\n                                <p className=\"text-muted\">No color information available</p>\r\n                              )}\r\n                            </div>\r\n\r\n                            <h5 className=\"mb-3\"><FaBoxOpen className=\"me-2\" />Size Distribution</h5>\r\n                            {renderSizeQuantityBars()}\r\n\r\n                            <div className=\"mt-4\">\r\n                              <Button\r\n                                variant=\"outline-primary\"\r\n                                className=\"w-100\"\r\n                                onClick={openPdfModal}\r\n                              >\r\n                                <FaFilePdf className=\"me-2\" />\r\n                                Generate Product Report\r\n                              </Button>\r\n                            </div>\r\n                          </Col>\r\n\r\n                          <Col md={6} className=\"text-center\">\r\n                            <h5 className=\"mb-3\"><FaImage className=\"me-2\" />Product Images</h5>\r\n                            {existingImageUrls && existingImageUrls.length > 0 ? (\r\n                              <div className=\"product-images-container\">\r\n                                <div className=\"product-images-grid\">\r\n                                  {existingImageUrls.map((imageUrl, index) => (\r\n                                    <div\r\n                                      key={index}\r\n                                      className={`product-image-item ${index === activeImageIndex ? 'active' : ''}`}\r\n                                      onClick={() => setActiveImageIndex(index)}\r\n                                    >\r\n                                      <Image\r\n                                        src={imageUrl}\r\n                                        alt={`Product ${index + 1}`}\r\n                                        thumbnail\r\n                                        className=\"image-preview\"\r\n                                      />\r\n                                      <span className=\"image-number\">{index + 1}</span>\r\n                                    </div>\r\n                                  ))}\r\n                                </div>\r\n                                <div className=\"main-image-container mt-3\">\r\n                                  <Image\r\n                                    src={existingImageUrls[activeImageIndex]}\r\n                                    alt=\"Product\"\r\n                                    thumbnail\r\n                                    className=\"main-image-preview\"\r\n                                    style={{ maxHeight: \"250px\" }}\r\n                                  />\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"p-5 bg-light rounded\">\r\n                                <FaImage size={60} className=\"text-secondary\" />\r\n                                <p className=\"mt-3 text-muted\">No images available</p>\r\n                              </div>\r\n                            )}\r\n                          </Col>\r\n                        </Row>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"slide-in\">\r\n                    <Tabs\r\n                      id=\"product-approval-tabs\"\r\n                      activeKey={activeTab}\r\n                      onSelect={(k) => setActiveTab(k)}\r\n                      className=\"mb-4\"\r\n                    >\r\n                      <Tab eventKey=\"details\" title={<span><FaInfoCircle className=\"me-2\" />Product Details</span>}>\r\n                        <Row className=\"mt-3\">\r\n                          <Col md={6}>\r\n                            <h5 className=\"mb-3\"><FaInfoCircle className=\"me-2\" />Product Name</h5>\r\n                            {isEditingName ? (\r\n                              <div className=\"mb-4\">\r\n                                <Form.Group>\r\n                                  <Form.Control\r\n                                    type=\"text\"\r\n                                    value={productName}\r\n                                    onChange={(e) => setProductName(e.target.value)}\r\n                                    isInvalid={!!productNameError}\r\n                                    placeholder=\"Enter product name\"\r\n                                  />\r\n                                  <Form.Control.Feedback type=\"invalid\">\r\n                                    {productNameError}\r\n                                  </Form.Control.Feedback>\r\n                                </Form.Group>\r\n                                <div className=\"mt-2\">\r\n                                  <Button\r\n                                    variant=\"success\"\r\n                                    size=\"sm\"\r\n                                    className=\"me-1\"\r\n                                    onClick={saveProductName}\r\n                                  >\r\n                                    <FaCheck className=\"me-1\" /> Save\r\n                                  </Button>\r\n                                  <Button\r\n                                    variant=\"secondary\"\r\n                                    size=\"sm\"\r\n                                    onClick={cancelEditingName}\r\n                                  >\r\n                                    <FaUndo className=\"me-1\" /> Cancel\r\n                                  </Button>\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"p-3 bg-light rounded mb-4 d-flex justify-content-between align-items-center\">\r\n                                <strong>\r\n                                  {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                                    productDetails.fabric_definition_data.fabric_name :\r\n                                    `Batch ID: ${id}`)}\r\n                                </strong>\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={startEditingName}\r\n                                >\r\n                                  Edit Name\r\n                                </Button>\r\n                              </div>\r\n                            )}\r\n\r\n                            <h5 className=\"mb-3\"><FaTags className=\"me-2\" />Colors</h5>\r\n                            <div className=\"mb-4\">\r\n                              {fabricDetails.length > 0 ? (\r\n                                fabricDetails.map((detail, index) => (\r\n                                  <div key={index} className=\"mb-2\">\r\n                                    {renderColorSwatch(detail.fabric_variant_data?.color || detail.color || 'gray')}\r\n                                    <span className=\"ms-2\">{detail.fabric_variant_data?.color_name || detail.color}</span>\r\n                                  </div>\r\n                                ))\r\n                              ) : (\r\n                                <p className=\"text-muted\">No color information available</p>\r\n                              )}\r\n                            </div>\r\n\r\n                            <h5 className=\"mb-3\"><FaBoxOpen className=\"me-2\" />Size Distribution</h5>\r\n                            {renderSizeQuantityBars()}\r\n                          </Col>\r\n\r\n                          <Col md={6}>\r\n                            <div className=\"mb-3\">\r\n                              <h5 className=\"mb-3\"><FaImage className=\"me-2\" />Product Images</h5>\r\n                              <p className=\"text-muted mb-3\">Upload up to 10 images of the product (Current: {productImages.length}/10)</p>\r\n\r\n                              {/* Image preview grid */}\r\n                              {imagePreviewUrls.length > 0 && (\r\n                                <div className=\"product-images-container mb-4\">\r\n                                  <div className=\"product-images-grid\">\r\n                                    {imagePreviewUrls.map((previewUrl, index) => (\r\n                                      <div\r\n                                        key={index}\r\n                                        className={`product-image-item ${index === activeImageIndex ? 'active' : ''}`}\r\n                                        onClick={() => setActiveImageIndex(index)}\r\n                                      >\r\n                                        <div className=\"image-actions\">\r\n                                          <Button\r\n                                            variant=\"danger\"\r\n                                            size=\"sm\"\r\n                                            className=\"btn-remove-image\"\r\n                                            onClick={(e) => {\r\n                                              e.stopPropagation();\r\n                                              removeImage(index);\r\n                                            }}\r\n                                          >\r\n                                            <FaTrash />\r\n                                          </Button>\r\n                                        </div>\r\n                                        <Image\r\n                                          src={previewUrl}\r\n                                          alt={`Preview ${index + 1}`}\r\n                                          thumbnail\r\n                                          className=\"image-preview\"\r\n                                        />\r\n                                        <span className=\"image-number\">{index + 1}</span>\r\n                                      </div>\r\n                                    ))}\r\n                                  </div>\r\n\r\n                                  {imagePreviewUrls.length > 0 && (\r\n                                    <div className=\"main-image-container mt-3\">\r\n                                      <Image\r\n                                        src={imagePreviewUrls[activeImageIndex]}\r\n                                        alt=\"Product Preview\"\r\n                                        thumbnail\r\n                                        className=\"main-image-preview\"\r\n                                        style={{ maxHeight: \"250px\" }}\r\n                                      />\r\n                                    </div>\r\n                                  )}\r\n                                </div>\r\n                              )}\r\n\r\n                              {/* Upload area */}\r\n                              {isUploading ? (\r\n                                <div className=\"p-4 bg-light rounded text-center\">\r\n                                  <h5 className=\"mb-3\">Uploading Images</h5>\r\n                                  <ProgressBar\r\n                                    now={uploadProgress}\r\n                                    label={`${Math.round(uploadProgress)}%`}\r\n                                    variant=\"info\"\r\n                                    animated\r\n                                    className=\"mb-3\"\r\n                                  />\r\n                                  <p className=\"text-muted\">\r\n                                    <FaUpload className=\"me-2 text-primary\" />\r\n                                    Uploading {productImages.length} images...\r\n                                  </p>\r\n                                </div>\r\n                              ) : (\r\n                                productImages.length < 10 && (\r\n                                  <div {...getRootProps()} className={`image-upload-container ${isDragActive ? 'active' : ''}`}>\r\n                                    <input {...getInputProps()} multiple />\r\n                                    <div className=\"text-center\">\r\n                                      <FaUpload size={40} className=\"mb-3 text-primary\" />\r\n                                      <p>Drag & drop product images here, or click to select</p>\r\n                                      <p className=\"text-muted small\">Supported formats: JPEG, PNG, GIF (Max: 5MB each)</p>\r\n                                      <p className=\"text-muted small\">You can select multiple images at once (Max: 10)</p>\r\n                                    </div>\r\n                                  </div>\r\n                                )\r\n                              )}\r\n\r\n                              <Form.Control\r\n                                type=\"file\"\r\n                                ref={fileInputRef}\r\n                                onChange={handleImageChange}\r\n                                accept=\"image/*\"\r\n                                multiple\r\n                                style={{ display: 'none' }}\r\n                              />\r\n                            </div>\r\n                          </Col>\r\n                        </Row>\r\n                      </Tab>\r\n\r\n                      <Tab eventKey=\"pricing\" title={<span><FaMoneyBillWave className=\"me-2\" />Pricing</span>}>\r\n                        <Form onSubmit={handleFormSubmit} className=\"mt-3\">\r\n                          <Row>\r\n                            <Col md={6}>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Manufacture Price (LKR):</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>The cost to manufacture this product</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  step=\"0.01\"\r\n                                  min=\"0\"\r\n                                  value={manufacturePrice}\r\n                                  onChange={(e) => setManufacturePrice(e.target.value)}\r\n                                  isInvalid={!!validationErrors.manufacturePrice}\r\n                                  required\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.manufacturePrice}\r\n                                </Form.Control.Feedback>\r\n                              </Form.Group>\r\n\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Selling Price (LKR):</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>The price at which this product will be sold</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  step=\"0.01\"\r\n                                  min=\"0\"\r\n                                  value={sellingPrice}\r\n                                  onChange={(e) => setSellingPrice(e.target.value)}\r\n                                  isInvalid={!!validationErrors.sellingPrice}\r\n                                  required\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.sellingPrice}\r\n                                </Form.Control.Feedback>\r\n                              </Form.Group>\r\n\r\n                              {manufacturePrice && sellingPrice && parseFloat(manufacturePrice) > 0 && parseFloat(sellingPrice) > 0 && (\r\n                                <div className=\"mb-4 p-3 bg-light rounded\">\r\n                                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                    <span><strong>Profit Margin:</strong></span>\r\n                                    <Badge bg={\r\n                                      profitMargin < 10 ? \"danger\" :\r\n                                      profitMargin < 20 ? \"warning\" :\r\n                                      \"success\"\r\n                                    }>\r\n                                      <FaPercentage className=\"me-1\" />\r\n                                      {profitMargin}%\r\n                                    </Badge>\r\n                                  </div>\r\n                                  <div className=\"profit-margin-indicator\" />\r\n                                  <div className=\"d-flex justify-content-between mt-1\">\r\n                                    <small>Low</small>\r\n                                    <small>High</small>\r\n                                  </div>\r\n                                </div>\r\n                              )}\r\n                            </Col>\r\n\r\n                            <Col md={6}>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Product Notes:</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>Additional information about this product</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  as=\"textarea\"\r\n                                  rows={5}\r\n                                  value={productNotes}\r\n                                  onChange={(e) => setProductNotes(e.target.value)}\r\n                                  isInvalid={!!validationErrors.productNotes}\r\n                                  placeholder=\"Enter any additional notes about this product...\"\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.productNotes}\r\n                                </Form.Control.Feedback>\r\n                                <Form.Text className=\"text-muted\">\r\n                                  {productNotes ? 500 - productNotes.length : 500} characters remaining\r\n                                </Form.Text>\r\n                              </Form.Group>\r\n                            </Col>\r\n                          </Row>\r\n\r\n                          <div className=\"d-grid gap-2 mt-4\">\r\n                            <Button type=\"submit\" className=\"btn-approve\" size=\"lg\">\r\n                              <FaCheck className=\"me-2\" />\r\n                              Approve Product\r\n                            </Button>\r\n                          </div>\r\n                        </Form>\r\n                      </Tab>\r\n                    </Tabs>\r\n                  </div>\r\n                )}\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      </div>\r\n\r\n      {/* Confirmation Modal */}\r\n      <ConfirmationModal />\r\n\r\n      {/* PDF Report Modal */}\r\n      <Modal show={showPdfModal} onHide={closePdfModal} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"text-danger me-2\" />\r\n            Generate Product Report\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Are you sure you want to generate a PDF report for this product?</p>\r\n          <div className=\"bg-light p-3 rounded\">\r\n            <p className=\"mb-1\"><strong>Product:</strong> {productName || (productDetails && productDetails.fabric_definition_data ?\r\n              productDetails.fabric_definition_data.fabric_name :\r\n              `Batch ID: ${id}`)}</p>\r\n            <p className=\"mb-1\"><strong>Manufacture Price:</strong> LKR {manufacturePrice}</p>\r\n            <p className=\"mb-1\"><strong>Selling Price:</strong> LKR {sellingPrice}</p>\r\n            <p className=\"mb-0\"><strong>Profit Margin:</strong> {profitMargin}%</p>\r\n          </div>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={closePdfModal}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={generateProductReport}\r\n            disabled={pdfLoading}\r\n          >\r\n            {pdfLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Generating...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaDownload className=\"me-2\" />\r\n                Generate PDF\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ApproveFinishedProduct;\r\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,GAAG,CAAEC,GAAG,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAK,CAAEC,WAAW,CAAEC,KAAK,CAAEC,IAAI,CAAEC,GAAG,CAAEC,KAAK,CAAEC,cAAc,CAAEC,OAAO,KAAQ,iBAAiB,CAC3J,OACEC,OAAO,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,MAAM,CAAEC,YAAY,CAAEC,eAAe,CACjEC,YAAY,CAAEC,YAAY,CAAEC,SAAS,CAAEC,eAAe,CACtDC,OAAO,CAAEC,MAAM,CAAEC,qBAAqB,CAAEC,SAAS,CAAEC,UAAU,KACxD,gBAAgB,CACvB,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,8BAA8B,CACrC,OAASC,KAAK,KAAQ,OAAO,CAC7B;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEA,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAEC,EAAG,CAAC,CAAG3C,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAA4C,QAAQ,CAAG3C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4C,YAAY,CAAGhD,MAAM,CAAC,IAAI,CAAC,CAEjC;AACA,KAAM,CAACiD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqD,YAAY,CAAEC,eAAe,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACuD,YAAY,CAAEC,eAAe,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACyD,KAAK,CAAEC,QAAQ,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC2D,UAAU,CAAEC,aAAa,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6D,UAAU,CAAEC,aAAa,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+D,OAAO,CAAEC,UAAU,CAAC,CAAGhE,QAAQ,CAAC,IAAI,CAAC,CAE5C;AACA,KAAM,CAACiE,aAAa,CAAEC,gBAAgB,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACqE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACuE,UAAU,CAAEC,aAAa,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACyE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1E,QAAQ,CAAC,CAAC,CAAC,CAC3D,KAAM,CAAC2E,cAAc,CAAEC,iBAAiB,CAAC,CAAG5E,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAAC6E,WAAW,CAAEC,cAAc,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAErD;AACA,KAAM,CAAC+E,cAAc,CAAEC,iBAAiB,CAAC,CAAGhF,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACiF,aAAa,CAAEC,gBAAgB,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmF,cAAc,CAAEC,iBAAiB,CAAC,CAAGpF,QAAQ,CAAC,CACnDqF,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAC/B,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG3F,QAAQ,CAAC,SAAS,CAAC,CACrD,KAAM,CAAC4F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC8F,YAAY,CAAEC,eAAe,CAAC,CAAG/F,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgG,UAAU,CAAEC,aAAa,CAAC,CAAGjG,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACkG,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACoG,YAAY,CAAEC,eAAe,CAAC,CAAGrG,QAAQ,CAAC,CAAC,CAAC,CAEnD;AACA,KAAM,CAACsG,WAAW,CAAEC,cAAc,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACwG,aAAa,CAAEC,gBAAgB,CAAC,CAAGzG,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC0G,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAE5D;AACA,KAAM,CAAC4G,eAAe,CAAEC,kBAAkB,CAAC,CAAG7G,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC8G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CACpE,KAAM,CAACgH,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACkH,eAAe,CAAEC,kBAAkB,CAAC,CAAGnH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACoH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrH,QAAQ,CAAC,IAAI,CAAC,CAEhE;AACAC,SAAS,CAAC,IAAM,CACd,GAAIkD,gBAAgB,EAAIE,YAAY,CAAE,CACpC,KAAM,CAAAiE,MAAM,CAAGC,UAAU,CAACpE,gBAAgB,CAAC,CAC3C,KAAM,CAAAqE,MAAM,CAAGD,UAAU,CAAClE,YAAY,CAAC,CAEvC,GAAIiE,MAAM,CAAG,CAAC,EAAIE,MAAM,CAAG,CAAC,CAAE,CAC5B,KAAM,CAAAC,MAAM,CAAI,CAACD,MAAM,CAAGF,MAAM,EAAIE,MAAM,CAAI,GAAG,CACjDnB,eAAe,CAACoB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CACpC,CACF,CACF,CAAC,CAAE,CAACvE,gBAAgB,CAAEE,YAAY,CAAC,CAAC,CAEpC;AACA,KAAM,CAACsE,UAAU,CAAEC,aAAa,CAAC,CAAG5H,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAA6H,UAAU,CAAG,CAAC,CAEpB;AACA,KAAM,CAAAC,gBAAgB,CAAG3H,WAAW,CAAC,SAAY,CAC/C,GAAI,CACF6D,UAAU,CAAC,IAAI,CAAC,CAChBN,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,KAAM,CAAAqE,WAAW,CAAG,KAAM,CAAA3H,KAAK,CAAC4H,GAAG,CAAC,qDAAqDhF,EAAE,GAAG,CAAC,CAE/F;AACA,KAAM,CAAAiF,UAAU,CAAG,KAAM,CAAA7H,KAAK,CAAC4H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC,CAEtF,GAAI+E,WAAW,CAACG,IAAI,EAAIH,WAAW,CAACG,IAAI,CAACC,WAAW,CAAE,CACpDrE,aAAa,CAAC,IAAI,CAAC,CACnBV,mBAAmB,CAAC2E,WAAW,CAACG,IAAI,CAACE,iBAAiB,CAAC,CACvD9E,eAAe,CAACyE,WAAW,CAACG,IAAI,CAACG,aAAa,CAAC,CAC/ChB,oBAAoB,CAACU,WAAW,CAACG,IAAI,CAACI,mBAAmB,CAAC,CAE1D;AACA,GAAIP,WAAW,CAACG,IAAI,CAACK,cAAc,EAAIC,KAAK,CAACC,OAAO,CAACV,WAAW,CAACG,IAAI,CAACK,cAAc,CAAC,CAAE,CACrFjE,oBAAoB,CAACyD,WAAW,CAACG,IAAI,CAACK,cAAc,CAAC,CACvD,CAAC,IAAM,IAAIR,WAAW,CAACG,IAAI,CAACQ,aAAa,CAAE,CACzC;AACApE,oBAAoB,CAAC,CAACyD,WAAW,CAACG,IAAI,CAACQ,aAAa,CAAC,CAAC,CACxD,CAEA,GAAIX,WAAW,CAACG,IAAI,CAACS,KAAK,CAAE,CAC1BnF,eAAe,CAACuE,WAAW,CAACG,IAAI,CAACS,KAAK,CAAC,CACzC,CACF,CAEA,GAAIV,UAAU,CAACC,IAAI,CAAE,CACnBlD,iBAAiB,CAACiD,UAAU,CAACC,IAAI,CAAC,CAElC;AACA,GAAID,UAAU,CAACC,IAAI,CAACU,YAAY,CAAE,CAChCrC,cAAc,CAAC0B,UAAU,CAACC,IAAI,CAACU,YAAY,CAAC,CAC9C,CAAC,IAAM,IAAIX,UAAU,CAACC,IAAI,CAACW,sBAAsB,CAAE,CACjD;AACAtC,cAAc,CAAC0B,UAAU,CAACC,IAAI,CAACW,sBAAsB,CAACC,WAAW,CAAC,CACpE,CAEA;AACA,GAAIb,UAAU,CAACC,IAAI,CAACa,OAAO,EAAId,UAAU,CAACC,IAAI,CAACa,OAAO,CAACC,MAAM,CAAG,CAAC,CAAE,CACjE;AACAC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEjB,UAAU,CAACC,IAAI,CAACa,OAAO,CAAC,CAEhE7D,gBAAgB,CAAC+C,UAAU,CAACC,IAAI,CAACa,OAAO,CAAC,CAEzC;AACA,KAAM,CAAAI,KAAK,CAAG,CAAC9D,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAC,CAC9CwC,UAAU,CAACC,IAAI,CAACa,OAAO,CAACK,OAAO,CAACC,MAAM,EAAI,CACxCF,KAAK,CAAC9D,EAAE,EAAIgE,MAAM,CAAChE,EAAE,EAAI,CAAC,CAC1B8D,KAAK,CAAC7D,CAAC,EAAI+D,MAAM,CAAC/D,CAAC,EAAI,CAAC,CACxB6D,KAAK,CAAC5D,CAAC,EAAI8D,MAAM,CAAC9D,CAAC,EAAI,CAAC,CACxB4D,KAAK,CAAC3D,CAAC,EAAI6D,MAAM,CAAC7D,CAAC,EAAI,CAAC,CACxB2D,KAAK,CAAC1D,EAAE,EAAI4D,MAAM,CAAC5D,EAAE,EAAI,CAAC,CAC5B,CAAC,CAAC,CACFL,iBAAiB,CAAC+D,KAAK,CAAC,CAC1B,CACF,CAEA;AACAvB,aAAa,CAAC,CAAC,CAAC,CAClB,CAAE,MAAO0B,GAAG,CAAE,CACZL,OAAO,CAACxF,KAAK,CAAC,+BAA+B,CAAE6F,GAAG,CAAC,CAEnD;AACA,KAAM,CAAAC,YAAY,CAAGD,GAAG,CAACE,QAAQ,CAC7B,UAAUF,GAAG,CAACE,QAAQ,CAACC,MAAM,MAAMH,GAAG,CAACE,QAAQ,CAACE,UAAU,EAAE,CAC5DJ,GAAG,CAACK,OAAO,CACT,oEAAoE,CACpE,wDAAwD,CAE9DjG,QAAQ,CAAC,iCAAiC6F,YAAY,EAAE,CAAC,CAEzD;AACA,GAAI5B,UAAU,CAAGE,UAAU,CAAE,CAC3BD,aAAa,CAACgC,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAC/BC,UAAU,CAAC,IAAM,CACf/B,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,IAAI,CAAC,CAAE;AACZ,CACF,CAAC,OAAS,CACR9D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAAChB,EAAE,CAAE2E,UAAU,CAAC,CAAC,CAEpB;AACA1H,SAAS,CAAC,IAAM,CACd6H,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAACA,gBAAgB,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAgC,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAAC,KAAK,CAAGxB,KAAK,CAACyB,IAAI,CAACF,CAAC,CAACG,MAAM,CAACF,KAAK,CAAC,CACxC,GAAIA,KAAK,CAAChB,MAAM,CAAG,CAAC,CAAE,CACpBmB,iBAAiB,CAACH,KAAK,CAAC,CAC1B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,iBAAiB,CAAGhK,WAAW,CAAE6J,KAAK,EAAK,CAC/C;AACA,GAAI/F,aAAa,CAAC+E,MAAM,CAAGgB,KAAK,CAAChB,MAAM,CAAG,EAAE,CAAE,CAC5CtF,QAAQ,CAAC,yDAAyDO,aAAa,CAAC+E,MAAM,UAAU,CAAC,CACjG,OACF,CAEA,KAAM,CAAAoB,SAAS,CAAG,EAAE,CACpB,KAAM,CAAAC,cAAc,CAAG,CAAC,GAAGlG,gBAAgB,CAAC,CAE5C6F,KAAK,CAACZ,OAAO,CAACkB,IAAI,EAAI,CACpB;AACA,GAAI,CAACA,IAAI,CAACC,IAAI,CAACC,KAAK,CAAC,SAAS,CAAC,CAAE,CAC/B9G,QAAQ,CAAC,kDAAkD,CAAC,CAC5D,OACF,CAEA;AACA,GAAI4G,IAAI,CAACG,IAAI,CAAG,CAAC,CAAG,IAAI,CAAG,IAAI,CAAE,CAC/B/G,QAAQ,CAAC,yCAAyC,CAAC,CACnD,OACF,CAEA0G,SAAS,CAACM,IAAI,CAACJ,IAAI,CAAC,CAEpB;AACA,KAAM,CAAAK,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACvBR,cAAc,CAACK,IAAI,CAACC,MAAM,CAACG,MAAM,CAAC,CAClC1G,mBAAmB,CAAC,CAAC,GAAGiG,cAAc,CAAC,CAAC,CAC1C,CAAC,CACDM,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC,CAC5B,CAAC,CAAC,CAEFpG,gBAAgB,CAAC,CAAC,GAAGD,aAAa,CAAE,GAAGmG,SAAS,CAAC,CAAC,CACpD,CAAC,CAAE,CAACnG,aAAa,CAAEE,gBAAgB,CAAC,CAAC,CAErC;AACA,KAAM,CAAA6G,gBAAgB,CAAGA,CAAA,GAAM,CAC7B9H,YAAY,CAAC+H,OAAO,CAACC,KAAK,CAAC,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAC,MAAM,CAAGhL,WAAW,CAAEiL,aAAa,EAAK,CAC5C,GAAIA,aAAa,EAAIA,aAAa,CAACpC,MAAM,CAAG,CAAC,CAAE,CAC7CmB,iBAAiB,CAACiB,aAAa,CAAC,CAClC,CACF,CAAC,CAAE,CAACjB,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAEkB,YAAY,CAAEC,aAAa,CAAEC,YAAa,CAAC,CAAGjJ,WAAW,CAAC,CAChE6I,MAAM,CACNK,MAAM,CAAE,CACN,SAAS,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAC7C,CAAC,CACDC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,WAAW,CAAIC,KAAK,EAAK,CAC7B,KAAM,CAAAvB,SAAS,CAAG,CAAC,GAAGnG,aAAa,CAAC,CACpC,KAAM,CAAAoG,cAAc,CAAG,CAAC,GAAGlG,gBAAgB,CAAC,CAE5CiG,SAAS,CAACwB,MAAM,CAACD,KAAK,CAAE,CAAC,CAAC,CAC1BtB,cAAc,CAACuB,MAAM,CAACD,KAAK,CAAE,CAAC,CAAC,CAE/BzH,gBAAgB,CAACkG,SAAS,CAAC,CAC3BhG,mBAAmB,CAACiG,cAAc,CAAC,CAEnC;AACA,GAAIsB,KAAK,GAAKlH,gBAAgB,CAAE,CAC9BC,mBAAmB,CAACmH,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEH,KAAK,CAAG,CAAC,CAAC,CAAC,CAC7C,CAAC,IAAM,IAAIA,KAAK,CAAGlH,gBAAgB,CAAE,CACnCC,mBAAmB,CAACD,gBAAgB,CAAG,CAAC,CAAC,CAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAAsH,cAAc,CAAIJ,KAAK,EAAK,CAChCjH,mBAAmB,CAACiH,KAAK,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,MAAM,CAAG,CAAC,CAAC,CAEjB;AACA,GAAI,CAAC9I,gBAAgB,EAAIA,gBAAgB,CAAC+I,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvDD,MAAM,CAAC9I,gBAAgB,CAAG,+BAA+B,CAC3D,CAAC,IAAM,IAAIoE,UAAU,CAACpE,gBAAgB,CAAC,EAAI,CAAC,CAAE,CAC5C8I,MAAM,CAAC9I,gBAAgB,CAAG,6CAA6C,CACzE,CAAC,IAAM,IAAIgJ,KAAK,CAAC5E,UAAU,CAACpE,gBAAgB,CAAC,CAAC,CAAE,CAC9C8I,MAAM,CAAC9I,gBAAgB,CAAG,0CAA0C,CACtE,CAEA;AACA,GAAI,CAACE,YAAY,EAAIA,YAAY,CAAC6I,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC/CD,MAAM,CAAC5I,YAAY,CAAG,2BAA2B,CACnD,CAAC,IAAM,IAAIkE,UAAU,CAAClE,YAAY,CAAC,EAAI,CAAC,CAAE,CACxC4I,MAAM,CAAC5I,YAAY,CAAG,yCAAyC,CACjE,CAAC,IAAM,IAAI8I,KAAK,CAAC5E,UAAU,CAAClE,YAAY,CAAC,CAAC,CAAE,CAC1C4I,MAAM,CAAC5I,YAAY,CAAG,sCAAsC,CAC9D,CAAC,IAAM,IAAIkE,UAAU,CAAClE,YAAY,CAAC,CAAGkE,UAAU,CAACpE,gBAAgB,CAAC,CAAE,CAClE8I,MAAM,CAAC5I,YAAY,CAAG,oEAAoE,CAC5F,CAEA;AACA,GAAIE,YAAY,EAAIA,YAAY,CAACyF,MAAM,CAAG,GAAG,CAAE,CAC7CiD,MAAM,CAAC1I,YAAY,CAAG,0CAA0C,CAClE,CAEA4C,mBAAmB,CAAC8F,MAAM,CAAC,CAC3B,MAAO,CAAAG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACjD,MAAM,GAAK,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAsD,gBAAgB,CAAIvC,CAAC,EAAK,CAC9BA,CAAC,CAACwC,cAAc,CAAC,CAAC,CAElB;AACA,GAAI,CAACP,YAAY,CAAC,CAAC,CAAE,CACnB,OACF,CAEA;AACAnG,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAA2G,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B9I,QAAQ,CAAC,EAAE,CAAC,CACZE,aAAa,CAAC,EAAE,CAAC,CACjBiC,mBAAmB,CAAC,KAAK,CAAC,CAE1B,GAAI,CACF;AACA7B,UAAU,CAAC,IAAI,CAAC,CAChBc,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,KAAM,CAAA2H,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAE3J,EAAE,CAAC,CACrCyJ,QAAQ,CAACE,MAAM,CAAC,mBAAmB,CAAEpF,UAAU,CAACpE,gBAAgB,CAAC,CAAC,CAClEsJ,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEpF,UAAU,CAAClE,YAAY,CAAC,CAAC,CAE1D,GAAIE,YAAY,CAAE,CAChBkJ,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAEpJ,YAAY,CAAC,CACxC,CAEA;AACA,GAAIU,aAAa,EAAIA,aAAa,CAAC+E,MAAM,CAAG,CAAC,CAAE,CAC7CpE,iBAAiB,CAAC,CAAC,CAAC,CAEpB;AACAX,aAAa,CAACmF,OAAO,CAACwD,KAAK,EAAI,CAC7BH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEC,KAAK,CAAC,CAC1C,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAApD,QAAQ,CAAG,KAAM,CAAApJ,KAAK,CAACyM,IAAI,CAC/B,qDAAqD,CACrDJ,QAAQ,CACR,CACEK,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CAAC,CACDC,gBAAgB,CAAGC,aAAa,EAAK,CACnC,KAAM,CAAAC,gBAAgB,CAAGpB,IAAI,CAACqB,KAAK,CAChCF,aAAa,CAACG,MAAM,CAAG,GAAG,CAAIH,aAAa,CAACI,KAC/C,CAAC,CACDxI,iBAAiB,CAACqI,gBAAgB,CAAC,CACrC,CACF,CACF,CAAC,CAEDrJ,aAAa,CAAC4F,QAAQ,CAACtB,IAAI,CAACmF,OAAO,EAAI,gCAAgC,CAAC,CACxEvI,cAAc,CAAC,KAAK,CAAC,CAErB;AACA+E,UAAU,CAAC,IAAM,CACf5G,QAAQ,CAAC,sBAAsB,CAAC,CAClC,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOqG,GAAG,CAAE,CACZL,OAAO,CAACxF,KAAK,CAAC,mCAAmC,CAAE6F,GAAG,CAAC,CACvD,KAAM,CAAAgE,MAAM,CAAGhE,GAAG,CAACE,QAAQ,EAAIF,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAC5C,MAAO,CAAAoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,GAAK,QAAQ,CACnCqF,IAAI,CAACC,SAAS,CAAClE,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAAC,CACjCoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,CACnB,uDAAuD,CAC3DxE,QAAQ,CAAC4J,MAAM,CAAC,CAChBxI,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyJ,wBAAwB,CAAGA,CAAA,GAAM,CACrC5H,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAA6H,YAAY,CAAGA,CAAA,GAAM,CACzB3H,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAA4H,aAAa,CAAGA,CAAA,GAAM,CAC1B5H,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAA6H,gBAAgB,CAAGA,CAAA,GAAM,CAC7BnH,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAoH,iBAAiB,CAAGA,CAAA,GAAM,CAC9BpH,gBAAgB,CAAC,KAAK,CAAC,CACvBE,mBAAmB,CAAC,EAAE,CAAC,CACvB;AACA,GAAI5B,cAAc,EAAIA,cAAc,CAAC6D,YAAY,CAAE,CACjDrC,cAAc,CAACxB,cAAc,CAAC6D,YAAY,CAAC,CAC7C,CAAC,IAAM,IAAI7D,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CAAE,CAClEtC,cAAc,CAACxB,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CAAC,CACnE,CACF,CAAC,CAED;AACA,KAAM,CAAAgF,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC;AACA,GAAI,CAACxH,WAAW,CAAC4F,IAAI,CAAC,CAAC,CAAE,CACvBvF,mBAAmB,CAAC,8BAA8B,CAAC,CACnD,OACF,CAEAA,mBAAmB,CAAC,EAAE,CAAC,CACvB3C,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF;AACA,KAAM,CAAA+J,aAAa,CAAG,KAAM,CAAA3N,KAAK,CAAC4H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC,CAEzF;AACA,KAAM,CAAAgL,OAAO,CAAG,CACdC,iBAAiB,CAAEF,aAAa,CAAC7F,IAAI,CAAC+F,iBAAiB,CACvDC,YAAY,CAAEH,aAAa,CAAC7F,IAAI,CAACgG,YAAY,CAC7CtF,YAAY,CAAEtC,WAAW,CACzByC,OAAO,CAAEgF,aAAa,CAAC7F,IAAI,CAACa,OAAS;AACvC,CAAC,CAED;AACA,KAAM,CAAA3I,KAAK,CAAC+N,GAAG,CAAC,qDAAqDnL,EAAE,GAAG,CAAEgL,OAAO,CAAC,CAEpF;AACAhJ,iBAAiB,CAAC,CAChB,GAAGD,cAAc,CACjB6D,YAAY,CAAEtC,WAChB,CAAC,CAAC,CAEFG,gBAAgB,CAAC,KAAK,CAAC,CACvB7C,aAAa,CAAC,mCAAmC,CAAC,CAElD;AACAiG,UAAU,CAAC,IAAM,CACfjG,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAO0F,GAAG,CAAE,CACZL,OAAO,CAACxF,KAAK,CAAC,8BAA8B,CAAE6F,GAAG,CAAC,CAElD;AACA,GAAI,CACF,KAAM,CAAA8E,aAAa,CAAG,KAAM,CAAAhO,KAAK,CAAC4H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC,CACzF,GAAIoL,aAAa,CAAClG,IAAI,CAACU,YAAY,GAAKtC,WAAW,CAAE,CACnD;AACAtB,iBAAiB,CAAC,CAChB,GAAGD,cAAc,CACjB6D,YAAY,CAAEtC,WAChB,CAAC,CAAC,CACFG,gBAAgB,CAAC,KAAK,CAAC,CACvB7C,aAAa,CAAC,mCAAmC,CAAC,CAElD;AACAiG,UAAU,CAAC,IAAM,CACfjG,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACR,OACF,CACF,CAAE,MAAOyK,QAAQ,CAAE,CACjB;AACApF,OAAO,CAACxF,KAAK,CAAC,qCAAqC,CAAE4K,QAAQ,CAAC,CAChE,CAEA;AACA3K,QAAQ,CAAC,kDAAkD,CAAC,CAC9D,CAAC,OAAS,CACRM,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAsK,kBAAkB,CAAGA,CAAA,GAAM,CAC/BvH,uBAAuB,CAAC5D,gBAAgB,CAAC,CACzC8D,mBAAmB,CAAC5D,YAAY,CAAC,CACjCwD,kBAAkB,CAAC,IAAI,CAAC,CACxBM,kBAAkB,CAAC,CAAC,CAAC,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAoH,mBAAmB,CAAGA,CAAA,GAAM,CAChC1H,kBAAkB,CAAC,KAAK,CAAC,CACzBE,uBAAuB,CAAC,EAAE,CAAC,CAC3BE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAqH,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAvC,MAAM,CAAG,CAAC,CAAC,CAEjB;AACA,GAAI,CAACnF,oBAAoB,EAAIA,oBAAoB,CAACoF,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC/DD,MAAM,CAACnF,oBAAoB,CAAG,+BAA+B,CAC/D,CAAC,IAAM,IAAIS,UAAU,CAACT,oBAAoB,CAAC,EAAI,CAAC,CAAE,CAChDmF,MAAM,CAACnF,oBAAoB,CAAG,6CAA6C,CAC7E,CAAC,IAAM,IAAIqF,KAAK,CAAC5E,UAAU,CAACT,oBAAoB,CAAC,CAAC,CAAE,CAClDmF,MAAM,CAACnF,oBAAoB,CAAG,0CAA0C,CAC1E,CAEA;AACA,GAAI,CAACE,gBAAgB,EAAIA,gBAAgB,CAACkF,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvDD,MAAM,CAACjF,gBAAgB,CAAG,2BAA2B,CACvD,CAAC,IAAM,IAAIO,UAAU,CAACP,gBAAgB,CAAC,EAAI,CAAC,CAAE,CAC5CiF,MAAM,CAACjF,gBAAgB,CAAG,yCAAyC,CACrE,CAAC,IAAM,IAAImF,KAAK,CAAC5E,UAAU,CAACP,gBAAgB,CAAC,CAAC,CAAE,CAC9CiF,MAAM,CAACjF,gBAAgB,CAAG,sCAAsC,CAClE,CAAC,IAAM,IAAIO,UAAU,CAACP,gBAAgB,CAAC,CAAGO,UAAU,CAACT,oBAAoB,CAAC,CAAE,CAC1EmF,MAAM,CAACjF,gBAAgB,CAAG,oEAAoE,CAChG,CAEAG,kBAAkB,CAAC8E,MAAM,CAAC,CAC1B,MAAO,CAAAG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACjD,MAAM,GAAK,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAyF,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnCC,KAAK,CAAC,sBAAsB,CAAC,CAAE;AAC/BzF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACtCD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEpC,oBAAoB,CAAC,CAC1DmC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAElC,gBAAgB,CAAC,CAClDiC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE9B,iBAAiB,CAAC,CAEpD,GAAI,CAACoH,iBAAiB,CAAC,CAAC,CAAE,CACxBvF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChC,OACF,CAEA,GAAI,CAAC9B,iBAAiB,CAAE,CACtB6B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACrCxF,QAAQ,CAAC,wCAAwC,CAAC,CAClD,OACF,CAEAM,UAAU,CAAC,IAAI,CAAC,CAChBN,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF;AACA,KAAM,CAAAiL,UAAU,CAAG,CACjBvG,iBAAiB,CAAEb,UAAU,CAACT,oBAAoB,CAAC,CACnDuB,aAAa,CAAEd,UAAU,CAACP,gBAAgB,CAC5C,CAAC,CAEDiC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEyF,UAAU,CAAC,CAC/C,KAAM,CAAAvO,KAAK,CAACwO,KAAK,CAAC,qDAAqDxH,iBAAiB,GAAG,CAAEuH,UAAU,CAAC,CAExG;AACAvL,mBAAmB,CAAC0D,oBAAoB,CAAC,CACzCxD,eAAe,CAAC0D,gBAAgB,CAAC,CACjCH,kBAAkB,CAAC,KAAK,CAAC,CACzBjD,aAAa,CAAC,6BAA6B,CAAC,CAE5C;AACAiG,UAAU,CAAC,IAAM,CACfjG,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAO0F,GAAG,CAAE,CACZL,OAAO,CAACxF,KAAK,CAAC,wBAAwB,CAAE6F,GAAG,CAAC,CAC5C,KAAM,CAAAgE,MAAM,CAAGhE,GAAG,CAACE,QAAQ,EAAIF,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAC5C,MAAO,CAAAoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,GAAK,QAAQ,CACnCqF,IAAI,CAACC,SAAS,CAAClE,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAAC,CACjCoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,CACnB,4CAA4C,CAChDxE,QAAQ,CAAC4J,MAAM,CAAC,CAClB,CAAC,OAAS,CACRtJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA6K,qBAAqB,CAAGA,CAAA,GAAM,CAClC5I,aAAa,CAAC,IAAI,CAAC,CAEnB,GAAI,CACF;AACA,KAAM,CAAA6I,GAAG,CAAG,GAAI,CAAAtM,KAAK,CAAC,CACpBuM,WAAW,CAAE,UAAU,CACvBC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,eAAe,CAAG,EAAE,CAC1B,KAAM,CAAAC,cAAc,CAAG,EAAE,CACzB,KAAM,CAAAC,aAAa,CAAG,CAAC,CAEvB;AACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC,CAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,CAAE,GAAG,CAAE,EAAE,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAExD;AACA,KAAM,CAAAC,cAAc,CAAGpJ,WAAW,GAAKvB,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CAC1F9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CACjD,aAAa9F,EAAE,EAAE,CAAC,CACtB8L,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACU,IAAI,CAACE,cAAc,CAAE,GAAG,CAAE,EAAE,CAAE,CAAED,KAAK,CAAE,QAAS,CAAC,CAAC,CAEtD;AACAX,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAAC,kBAAkB,GAAI,CAAAG,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,CAAE,GAAG,CAAE,EAAE,CAAE,CAAEH,KAAK,CAAE,QAAS,CAAC,CAAC,CAE3F;AACAX,GAAG,CAACe,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/Bf,GAAG,CAACgB,IAAI,CAAC,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,EAAE,CAAC,CAEzB;AACA,GAAI,CAAAC,IAAI,CAAG,EAAE,CAEb;AACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,qBAAqB,CAAE,EAAE,CAAEO,IAAI,CAAC,CACzCA,IAAI,EAAI,EAAE,CAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAAC,0BAA0BrM,gBAAgB,EAAE,CAAE,EAAE,CAAE4M,IAAI,CAAC,CAChEA,IAAI,EAAI,CAAC,CACTjB,GAAG,CAACU,IAAI,CAAC,sBAAsBnM,YAAY,EAAE,CAAE,EAAE,CAAE0M,IAAI,CAAC,CACxDA,IAAI,EAAI,CAAC,CACTjB,GAAG,CAACU,IAAI,CAAC,kBAAkBpJ,YAAY,GAAG,CAAE,EAAE,CAAE2J,IAAI,CAAC,CACrDA,IAAI,EAAI,EAAE,CAEV;AACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,mBAAmB,CAAE,EAAE,CAAEO,IAAI,CAAC,CACvCA,IAAI,EAAI,EAAE,CAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAElC;AACA,KAAM,CAAApG,KAAK,CAAGiD,MAAM,CAAC4D,OAAO,CAAC7K,cAAc,CAAC,CAC5C,KAAM,CAAA8K,WAAW,CAAG,CAAC,MAAM,CAAE,UAAU,CAAE,YAAY,CAAC,CACtD,KAAM,CAAAC,aAAa,CAAG/G,KAAK,CAACgH,MAAM,CAAC,CAACC,GAAG,CAAAC,IAAA,OAAE,CAACC,CAAC,CAAEC,GAAG,CAAC,CAAAF,IAAA,OAAK,CAAAD,GAAG,CAAGG,GAAG,GAAE,CAAC,CAAC,CAEnE;AACAzB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,CAAE,EAAE,CAAEF,IAAI,CAAC,CAClCjB,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,CAAE,EAAE,CAAEF,IAAI,CAAC,CAClCjB,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,CAAE,EAAE,CAAEF,IAAI,CAAC,CAClCA,IAAI,EAAI,CAAC,CAET;AACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCpG,KAAK,CAACC,OAAO,CAACoH,KAAA,EAAsB,IAArB,CAAC/F,IAAI,CAAEgG,QAAQ,CAAC,CAAAD,KAAA,CAC7B,KAAM,CAAAE,UAAU,CAAGR,aAAa,CAAG,CAAC,CAAG,CAAEO,QAAQ,CAAGP,aAAa,CAAI,GAAG,EAAExI,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5FoH,GAAG,CAACU,IAAI,CAAC/E,IAAI,CAACkG,WAAW,CAAC,CAAC,CAAE,EAAE,CAAEZ,IAAI,CAAC,CACtCjB,GAAG,CAACU,IAAI,CAACiB,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAE,EAAE,CAAEb,IAAI,CAAC,CACvCjB,GAAG,CAACU,IAAI,CAAC,GAAGkB,UAAU,GAAG,CAAE,EAAE,CAAEX,IAAI,CAAC,CACpCA,IAAI,EAAI,CAAC,CACX,CAAC,CAAC,CAEF;AACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,CAAE,EAAE,CAAEO,IAAI,CAAC,CAC3BjB,GAAG,CAACU,IAAI,CAACU,aAAa,CAACU,QAAQ,CAAC,CAAC,CAAE,EAAE,CAAEb,IAAI,CAAC,CAC5CjB,GAAG,CAACU,IAAI,CAAC,QAAQ,CAAE,EAAE,CAAEO,IAAI,CAAC,CAC5BA,IAAI,EAAI,EAAE,CAEV;AACA,GAAI9K,aAAa,CAAC+D,MAAM,CAAG,CAAC,CAAE,CAC5B8F,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,mBAAmB,CAAE,EAAE,CAAEO,IAAI,CAAC,CACvCA,IAAI,EAAI,EAAE,CAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAElCtK,aAAa,CAACmE,OAAO,CAAC,CAACC,MAAM,CAAEsC,KAAK,GAAK,KAAAkF,qBAAA,CACvC,KAAM,CAAAC,SAAS,CAAG,EAAAD,qBAAA,CAAAxH,MAAM,CAAC0H,mBAAmB,UAAAF,qBAAA,iBAA1BA,qBAAA,CAA4BG,UAAU,GAAI3H,MAAM,CAAC4H,KAAK,EAAI,KAAK,CACjFnC,GAAG,CAACU,IAAI,CAAC,SAAS7D,KAAK,CAAG,CAAC,KAAKmF,SAAS,EAAE,CAAE,EAAE,CAAEf,IAAI,CAAC,CACtDA,IAAI,EAAI,CAAC,CACX,CAAC,CAAC,CAEFA,IAAI,EAAI,CAAC,CACX,CAEA;AACA,GAAIxM,YAAY,CAAE,CAChBuL,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,eAAe,CAAE,EAAE,CAAEO,IAAI,CAAC,CACnCA,IAAI,EAAI,EAAE,CAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAElC;AACA,KAAM,CAAA2B,UAAU,CAAGpC,GAAG,CAACqC,eAAe,CAAC5N,YAAY,CAAE,GAAG,CAAC,CACzDuL,GAAG,CAACU,IAAI,CAAC0B,UAAU,CAAE,EAAE,CAAEnB,IAAI,CAAC,CAC9BA,IAAI,EAAImB,UAAU,CAAClI,MAAM,CAAG,CAAC,CAAG,CAAC,CACnC,CAEA;AACA,GAAI3E,iBAAiB,EAAIA,iBAAiB,CAAC2E,MAAM,CAAG,CAAC,CAAE,CACrD8F,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC,CAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,CAAE,EAAE,CAAEO,IAAI,CAAC,CACpCA,IAAI,EAAI,EAAE,CAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC,CAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAAC,qBAAqBnL,iBAAiB,CAAC2E,MAAM,EAAE,CAAE,EAAE,CAAE+G,IAAI,CAAC,CACnEA,IAAI,EAAI,CAAC,CACTjB,GAAG,CAACU,IAAI,CAAC,0CAA0C,CAAE,EAAE,CAAEO,IAAI,CAAC,CAC9DA,IAAI,EAAI,EAAE,CACZ,CAEA;AACAjB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC,CAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,CAAE,QAAQ,CAAC,CAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,GAAI,CAAAG,IAAI,CAAC,CAAC,CAACyB,cAAc,CAAC,CAAC,EAAE,CAAE,GAAG,CAAE,GAAG,CAAE,CAAE3B,KAAK,CAAE,QAAS,CAAC,CAAC,CACvFX,GAAG,CAACU,IAAI,CAAC,uCAAuC,CAAE,GAAG,CAAE,GAAG,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAEhF;AACA,KAAM,CAAA4B,gBAAgB,CAAG/K,WAAW,CAACgL,OAAO,CAAC,eAAe,CAAE,GAAG,CAAC,CAClExC,GAAG,CAACyC,IAAI,CAAC,kBAAkBF,gBAAgB,IAAI,GAAI,CAAA1B,IAAI,CAAC,CAAC,CAAC6B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,MAAM,CAAC,CAE3FxL,aAAa,CAAC,KAAK,CAAC,CACpBF,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,MAAOtC,KAAK,CAAE,CACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CC,QAAQ,CAAC,2BAA2BD,KAAK,CAAC4J,OAAO,EAAE,CAAC,CACpDpH,aAAa,CAAC,KAAK,CAAC,CACpBF,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,GAAIhC,OAAO,CAAE,mBACXrB,IAAA,QAAKgP,SAAS,CAAC,kDAAkD,CAACC,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAAC,QAAA,cAC3FnP,IAAA,CAAC7B,OAAO,EAACiR,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAAH,QAAA,cACzDnP,IAAA,SAAMgP,SAAS,CAAC,iBAAiB,CAAAG,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,CACP,CAAC,CAGR;AACA,KAAM,CAAAI,iBAAiB,CAAIhB,KAAK,EAAK,CACnC,GAAI,CAACA,KAAK,CAAE,MAAO,KAAI,CAEvB;AACAhI,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE+H,KAAK,CAAC,CAE3C;AACA,GAAI,CAAAiB,OAAO,CAEX;AACA,GAAIjB,KAAK,CAACkB,UAAU,CAAC,GAAG,CAAC,CAAE,CACzBD,OAAO,CAAGjB,KAAK,CACjB,CACA;AAAA,IACK,IAAI,kBAAkB,CAACmB,IAAI,CAACnB,KAAK,CAAC,EAAI,kBAAkB,CAACmB,IAAI,CAACnB,KAAK,CAAC,CAAE,CACzEiB,OAAO,CAAG,IAAIjB,KAAK,EAAE,CACvB,CACA;AAAA,IACK,CACH;AACA,KAAM,CAAAoB,QAAQ,CAAG,CACf,KAAK,CAAE,SAAS,CAChB,MAAM,CAAE,SAAS,CACjB,OAAO,CAAE,SAAS,CAClB,QAAQ,CAAE,SAAS,CACnB,OAAO,CAAE,SAAS,CAClB,OAAO,CAAE,SAAS,CAClB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,SAAS,CACnB,MAAM,CAAE,SAAS,CACjB,OAAO,CAAE,SAAS,CAClB,MAAM,CAAE,SACV,CAAC,CAED;AACAH,OAAO,CAAGG,QAAQ,CAACpB,KAAK,CAACqB,WAAW,CAAC,CAAC,CAAC,EAAIrB,KAAK,CAClD,CAEA,mBACEvO,IAAA,CAACrB,cAAc,EACbkR,SAAS,CAAC,KAAK,CACfC,OAAO,cAAE9P,IAAA,CAACpB,OAAO,EAACoQ,SAAS,CAAC,gBAAgB,CAAAG,QAAA,CAAEZ,KAAK,CAAU,CAAE,CAAAY,QAAA,cAE/DnP,IAAA,QACEgP,SAAS,CAAC,cAAc,CACxBC,KAAK,CAAE,CAAEc,eAAe,CAAEP,OAAQ,CAAE,CACpC,aAAYjB,KAAM,CACnB,CAAC,CACY,CAAC,CAErB,CAAC,CAED;AACA,KAAM,CAAAyB,sBAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAvJ,KAAK,CAAGiD,MAAM,CAAC4D,OAAO,CAAC7K,cAAc,CAAC,CAC5C,KAAM,CAAAwN,WAAW,CAAG9G,IAAI,CAACC,GAAG,CAAC,GAAG3C,KAAK,CAACyJ,GAAG,CAACC,KAAA,MAAC,CAACvC,CAAC,CAAEC,GAAG,CAAC,CAAAsC,KAAA,OAAK,CAAAtC,GAAG,GAAC,CAAC,CAE7D,MAAO,CAAApH,KAAK,CAACyJ,GAAG,CAACE,KAAA,MAAC,CAACrI,IAAI,CAAEgG,QAAQ,CAAC,CAAAqC,KAAA,oBAChClQ,KAAA,QAAgB8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAC9BjP,KAAA,QAAK8O,SAAS,CAAC,qCAAqC,CAAAG,QAAA,eAClDnP,IAAA,SAAAmP,QAAA,cAAMnP,IAAA,WAAAmP,QAAA,CAASpH,IAAI,CAACkG,WAAW,CAAC,CAAC,CAAS,CAAC,CAAM,CAAC,cAClD/N,KAAA,SAAAiP,QAAA,EAAOpB,QAAQ,CAAC,MAAI,EAAM,CAAC,EACxB,CAAC,cACN/N,IAAA,CAAC1B,WAAW,EACV+R,GAAG,CAAEJ,WAAW,CAAIlC,QAAQ,CAAGkC,WAAW,CAAI,GAAG,CAAG,CAAE,CACtDX,OAAO,CAAEvB,QAAQ,CAAG,CAAC,CAAG,MAAM,CAAG,OAAQ,CACzCiB,SAAS,CAAC,mBAAmB,CAC9B,CAAC,GATMjH,IAUL,CAAC,EACP,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAuI,iBAAiB,CAAGA,CAAA,gBACxBpQ,KAAA,CAAC7B,KAAK,EACJkS,IAAI,CAAErN,gBAAiB,CACvBsN,MAAM,CAAEzF,wBAAyB,CACjC0F,QAAQ,MACRzB,SAAS,CAAC,oBAAoB,CAAAG,QAAA,eAE9BnP,IAAA,CAAC3B,KAAK,CAACqS,MAAM,EAACC,WAAW,MAAAxB,QAAA,cACvBnP,IAAA,CAAC3B,KAAK,CAACuS,KAAK,EAAAzB,QAAA,CAAC,0BAAwB,CAAa,CAAC,CACvC,CAAC,cACfjP,KAAA,CAAC7B,KAAK,CAACwS,IAAI,EAAA1B,QAAA,eACTnP,IAAA,MAAAmP,QAAA,CAAG,2EAAyE,CAAG,CAAC,cAChFnP,IAAA,CAACtB,KAAK,EAACoS,QAAQ,MAACC,KAAK,MAAChJ,IAAI,CAAC,IAAI,CAACiH,SAAS,CAAC,MAAM,CAAAG,QAAA,cAC9CjP,KAAA,UAAAiP,QAAA,eACEjP,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,eAAa,CAAQ,CAAC,CAAI,CAAC,cACvCnP,IAAA,OAAAmP,QAAA,CAAKvL,WAAW,GAAKvB,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CAC1E9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CACjD,aAAa9F,EAAE,EAAE,CAAC,CAAK,CAAC,EACxB,CAAC,cACLJ,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,CAAI,CAAC,cAC5CjP,KAAA,OAAAiP,QAAA,EAAI,MAAI,CAAC1O,gBAAgB,EAAK,CAAC,EAC7B,CAAC,cACLP,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAI,CAAC,cACxCjP,KAAA,OAAAiP,QAAA,EAAI,MAAI,CAACxO,YAAY,EAAK,CAAC,EACzB,CAAC,cACLT,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAI,CAAC,cACxCjP,KAAA,OAAAiP,QAAA,EAAKzL,YAAY,CAAC,GAAC,EAAI,CAAC,EACtB,CAAC,CACJ7C,YAAY,eACXX,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,QAAM,CAAQ,CAAC,CAAI,CAAC,cAChCnP,IAAA,OAAAmP,QAAA,CAAKtO,YAAY,CAAK,CAAC,EACrB,CACL,EACI,CAAC,CACH,CAAC,CACPY,gBAAgB,CAAC6E,MAAM,CAAG,CAAC,eAC1BpG,KAAA,QAAK8O,SAAS,CAAC,kBAAkB,CAAAG,QAAA,eAC/BnP,IAAA,MAAAmP,QAAA,cAAGnP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAG,CAAC,cACtCnP,IAAA,CAAC5B,KAAK,EACJ4S,GAAG,CAAEvP,gBAAgB,CAAC,CAAC,CAAE,CACzBwP,GAAG,CAAC,SAAS,CACbC,SAAS,MACTjC,KAAK,CAAE,CAAEkC,SAAS,CAAE,OAAQ,CAAE,CAC/B,CAAC,EACC,CACN,EACS,CAAC,cACbjR,KAAA,CAAC7B,KAAK,CAAC+S,MAAM,EAAAjC,QAAA,eACXnP,IAAA,CAACjC,MAAM,EAACuR,OAAO,CAAC,WAAW,CAAC+B,OAAO,CAAEtG,wBAAyB,CAAAoE,QAAA,CAAC,QAE/D,CAAQ,CAAC,cACTjP,KAAA,CAACnC,MAAM,EAACuR,OAAO,CAAC,SAAS,CAAC+B,OAAO,CAAEvH,YAAa,CAAAqF,QAAA,eAC9CnP,IAAA,CAACnB,OAAO,EAACmQ,SAAS,CAAC,MAAM,CAAE,CAAC,mBAE9B,EAAQ,CAAC,EACG,CAAC,EACV,CACR,CAED,mBACE9O,KAAA,CAAAE,SAAA,EAAA+O,QAAA,eACEnP,IAAA,CAACH,eAAe,GAAE,CAAC,cACnBG,IAAA,QAAKgP,SAAS,CAAC,cAAc,CAAAG,QAAA,cAC3BnP,IAAA,CAAC/B,GAAG,EAAC+Q,SAAS,CAAC,wBAAwB,CAAAG,QAAA,cACrCnP,IAAA,CAAC9B,GAAG,EAACoT,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAApC,QAAA,cACjBnP,IAAA,CAACnC,IAAI,EAACmR,SAAS,CAAC,8BAA8B,CAACC,KAAK,CAAE,CAAEc,eAAe,CAAE,SAAS,CAAEyB,YAAY,CAAE,MAAO,CAAE,CAAArC,QAAA,cACzGjP,KAAA,CAACrC,IAAI,CAACgT,IAAI,EAAA1B,QAAA,eACRnP,IAAA,OAAIgP,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAG9DnP,IAAA,QAAKgP,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAC9BrL,aAAa,cACZ5D,KAAA,QAAK8O,SAAS,CAAC,kDAAkD,CAAAG,QAAA,eAC/DjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,uBAAuB,CAACC,KAAK,CAAE,CAAEyC,QAAQ,CAAE,OAAQ,CAAE,CAAAvC,QAAA,eACzEnP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,MAAM,CACX+J,KAAK,CAAEhO,WAAY,CACnBiO,QAAQ,CAAGxK,CAAC,EAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CAChDE,SAAS,CAAE,CAAC,CAAC9N,gBAAiB,CAC9B+N,WAAW,CAAC,oBAAoB,CACjC,CAAC,cACF/R,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClCnL,gBAAgB,CACI,CAAC,EACd,CAAC,cACbhE,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,SAAS,CACjBvH,IAAI,CAAC,IAAI,CACTiH,SAAS,CAAC,MAAM,CAChBqC,OAAO,CAAEjG,eAAgB,CAAA+D,QAAA,cAEzBnP,IAAA,CAACnB,OAAO,GAAE,CAAC,CACL,CAAC,cACTmB,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,WAAW,CACnBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAElG,iBAAkB,CAAAgE,QAAA,cAE3BnP,IAAA,CAACR,MAAM,GAAE,CAAC,CACJ,CAAC,EACN,CAAC,cAENU,KAAA,QAAK8O,SAAS,CAAC,kDAAkD,CAAAG,QAAA,eAC/DnP,IAAA,MAAGgP,SAAS,CAAC,kCAAkC,CAAAG,QAAA,CAC5CvL,WAAW,GAAKvB,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CACjD,aAAa9F,EAAE,EAAE,CAAC,CACnB,CAAC,cACJN,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,iBAAiB,CACzBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAEnG,gBAAiB,CAAAiE,QAAA,CAC3B,WAED,CAAQ,CAAC,EACN,CACN,CACE,CAAC,CAELpO,KAAK,eACJf,IAAA,CAAChC,KAAK,EAACsR,OAAO,CAAC,QAAQ,CAACN,SAAS,CAAC,cAAc,CAAAG,QAAA,cAC9CjP,KAAA,QAAK8O,SAAS,CAAC,mDAAmD,CAAAG,QAAA,eAChEjP,KAAA,QAAAiP,QAAA,eACEnP,IAAA,CAACP,qBAAqB,EAACuP,SAAS,CAAC,MAAM,CAAE,CAAC,CACzCjO,KAAK,EACH,CAAC,cACNf,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,gBAAgB,CACxBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAEjM,gBAAiB,CAAA+J,QAAA,CAC3B,OAED,CAAQ,CAAC,EACN,CAAC,CACD,CACR,CAEAlO,UAAU,eACTf,KAAA,CAAClC,KAAK,EAACsR,OAAO,CAAC,SAAS,CAACN,SAAS,CAAC,cAAc,CAAAG,QAAA,eAC/CnP,IAAA,CAACnB,OAAO,EAACmQ,SAAS,CAAC,MAAM,CAAE,CAAC,CAC3B/N,UAAU,EACN,CACR,CAEAE,UAAU,cACTjB,KAAA,QAAK8O,SAAS,CAAC,oCAAoC,CAAAG,QAAA,eACjDjP,KAAA,OAAI8O,SAAS,CAAC,+BAA+B,CAAAG,QAAA,eAC3CnP,IAAA,CAACnB,OAAO,EAACmQ,SAAS,CAAC,MAAM,CAAE,CAAC,2BAE9B,EAAI,CAAC,cAELhP,IAAA,QAAKgP,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAC9BrL,aAAa,cACZ5D,KAAA,QAAK8O,SAAS,CAAC,kDAAkD,CAAAG,QAAA,eAC/DjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,uBAAuB,CAACC,KAAK,CAAE,CAAEyC,QAAQ,CAAE,OAAQ,CAAE,CAAAvC,QAAA,eACzEnP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,MAAM,CACX+J,KAAK,CAAEhO,WAAY,CACnBiO,QAAQ,CAAGxK,CAAC,EAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CAChDE,SAAS,CAAE,CAAC,CAAC9N,gBAAiB,CAC9B+N,WAAW,CAAC,oBAAoB,CACjC,CAAC,cACF/R,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClCnL,gBAAgB,CACI,CAAC,EACd,CAAC,cACbhE,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,SAAS,CACjBvH,IAAI,CAAC,IAAI,CACTiH,SAAS,CAAC,MAAM,CAChBqC,OAAO,CAAEjG,eAAgB,CAAA+D,QAAA,cAEzBnP,IAAA,CAACnB,OAAO,GAAE,CAAC,CACL,CAAC,cACTmB,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,WAAW,CACnBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAElG,iBAAkB,CAAAgE,QAAA,cAE3BnP,IAAA,CAACR,MAAM,GAAE,CAAC,CACJ,CAAC,EACN,CAAC,cAENU,KAAA,QAAK8O,SAAS,CAAC,kDAAkD,CAAAG,QAAA,eAC/DnP,IAAA,OAAIgP,SAAS,CAAC,WAAW,CAAAG,QAAA,CACtBvL,WAAW,GAAKvB,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CACjD,aAAa9F,EAAE,EAAE,CAAC,CAClB,CAAC,cACLN,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,iBAAiB,CACzBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAEnG,gBAAiB,CAAAiE,QAAA,CAC3B,WAED,CAAQ,CAAC,EACN,CACN,CACE,CAAC,cAENnP,IAAA,QAAKgP,SAAS,CAAC,2BAA2B,CAAAG,QAAA,cACtCjP,KAAA,CAACjC,GAAG,EAAC+Q,SAAS,CAAC,MAAM,CAAAG,QAAA,eACnBjP,KAAA,CAAChC,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,eACTjP,KAAA,QAAK8O,SAAS,CAAC,wDAAwD,CAAAG,QAAA,eACrEjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACd,eAAe,EAAC8P,SAAS,CAAC,MAAM,CAAE,CAAC,sBAAmB,EAAI,CAAC,CAChF,CAAC9K,eAAe,eACflE,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,iBAAiB,CACzBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAEzF,kBAAmB,CAAAuD,QAAA,CAC7B,aAED,CAAQ,CACT,EACE,CAAC,CAELjL,eAAe,cACdlE,IAAA,QAAKgP,SAAS,CAAC,sBAAsB,CAAAG,QAAA,cACnCjP,KAAA,CAACpC,IAAI,EAACmU,QAAQ,CAAG5K,CAAC,EAAKA,CAAC,CAACwC,cAAc,CAAC,CAAE,CAAAsF,QAAA,eACxCjP,KAAA,CAACjC,GAAG,EAAAkR,QAAA,eACFnP,IAAA,CAAC9B,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACTjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,MAAM,CAAAG,QAAA,eAC1BnP,IAAA,CAAClC,IAAI,CAACoU,KAAK,EAAA/C,QAAA,cAACnP,IAAA,WAAAmP,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,CAAY,CAAC,cAClEnP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,QAAQ,CACbsK,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPR,KAAK,CAAExN,oBAAqB,CAC5ByN,QAAQ,CAAGxK,CAAC,EAAKhD,uBAAuB,CAACgD,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CACzDE,SAAS,CAAE,CAAC,CAACtN,eAAe,CAACJ,oBAAqB,CACnD,CAAC,cACFpE,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClC3K,eAAe,CAACJ,oBAAoB,CAChB,CAAC,EACd,CAAC,CACV,CAAC,cACNpE,IAAA,CAAC9B,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACTjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,MAAM,CAAAG,QAAA,eAC1BnP,IAAA,CAAClC,IAAI,CAACoU,KAAK,EAAA/C,QAAA,cAACnP,IAAA,WAAAmP,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,CAAY,CAAC,cAC9DnP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,QAAQ,CACbsK,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPR,KAAK,CAAEtN,gBAAiB,CACxBuN,QAAQ,CAAGxK,CAAC,EAAK9C,mBAAmB,CAAC8C,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CACrDE,SAAS,CAAE,CAAC,CAACtN,eAAe,CAACF,gBAAiB,CAC/C,CAAC,cACFtE,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClC3K,eAAe,CAACF,gBAAgB,CACZ,CAAC,EACd,CAAC,CACV,CAAC,EACH,CAAC,CAELF,oBAAoB,EAAIE,gBAAgB,EAAIO,UAAU,CAACT,oBAAoB,CAAC,CAAG,CAAC,EAAIS,UAAU,CAACP,gBAAgB,CAAC,CAAG,CAAC,eACnHpE,KAAA,QAAK8O,SAAS,CAAC,wCAAwC,CAAAG,QAAA,eACrDnP,IAAA,WAAAmP,QAAA,CAAQ,qBAAmB,CAAQ,CAAC,CACnC,CAAE,CAACtK,UAAU,CAACP,gBAAgB,CAAC,CAAGO,UAAU,CAACT,oBAAoB,CAAC,EAAIS,UAAU,CAACP,gBAAgB,CAAC,CAAI,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,CAAC,GACzH,EAAK,CACN,cAED9E,KAAA,QAAK8O,SAAS,CAAC,cAAc,CAAAG,QAAA,eAC3BjP,KAAA,CAACnC,MAAM,EACL8J,IAAI,CAAC,QAAQ,CACbyH,OAAO,CAAC,SAAS,CACjBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAEtF,gBAAiB,CAC1BsG,QAAQ,CAAEhR,OAAQ,CAAA8N,QAAA,eAElBnP,IAAA,CAACnB,OAAO,EAACmQ,SAAS,CAAC,MAAM,CAAE,CAAC,eAE9B,EAAQ,CAAC,cACT9O,KAAA,CAACnC,MAAM,EACL8J,IAAI,CAAC,QAAQ,CACbyH,OAAO,CAAC,WAAW,CACnBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAExF,mBAAoB,CAAAsD,QAAA,eAE7BnP,IAAA,CAACR,MAAM,EAACwP,SAAS,CAAC,MAAM,CAAE,CAAC,SAE7B,EAAQ,CAAC,EACN,CAAC,EACF,CAAC,CACJ,CAAC,cAENhP,IAAA,CAACtB,KAAK,EAACoS,QAAQ,MAACC,KAAK,MAAA5B,QAAA,cACnBjP,KAAA,UAAAiP,QAAA,eACEjP,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,CAAI,CAAC,cAC5CjP,KAAA,OAAAiP,QAAA,EAAI,MAAI,CAAC1O,gBAAgB,EAAK,CAAC,EAC7B,CAAC,cACLP,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAI,CAAC,cACxCjP,KAAA,OAAAiP,QAAA,EAAI,MAAI,CAACxO,YAAY,EAAK,CAAC,EACzB,CAAC,cACLT,KAAA,OAAAiP,QAAA,eACEnP,IAAA,OAAAmP,QAAA,cAAInP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAI,CAAC,cACxCjP,KAAA,OAAAiP,QAAA,EAAKzL,YAAY,CAAC,GAAC,EAAI,CAAC,EACtB,CAAC,EACA,CAAC,CACH,CACR,CAEA7C,YAAY,eACXX,KAAA,QAAK8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eACnBjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACV,eAAe,EAAC0P,SAAS,CAAC,MAAM,CAAE,CAAC,QAAK,EAAI,CAAC,cACnEhP,IAAA,QAAKgP,SAAS,CAAC,sBAAsB,CAAAG,QAAA,CAClCtO,YAAY,CACV,CAAC,EACH,CACN,cAEDX,KAAA,OAAI8O,SAAS,CAAC,WAAW,CAAAG,QAAA,eAACnP,IAAA,CAAChB,MAAM,EAACgQ,SAAS,CAAC,MAAM,CAAE,CAAC,SAAM,EAAI,CAAC,cAChEhP,IAAA,QAAKgP,SAAS,CAAC,MAAM,CAAAG,QAAA,CAClB5M,aAAa,CAAC+D,MAAM,CAAG,CAAC,CACvB/D,aAAa,CAAC2N,GAAG,CAAC,CAACvJ,MAAM,CAAEsC,KAAK,QAAAqJ,sBAAA,CAAAC,sBAAA,oBAC9BrS,KAAA,QAAiB8O,SAAS,CAAC,MAAM,CAAAG,QAAA,EAC9BI,iBAAiB,CAAC,EAAA+C,sBAAA,CAAA3L,MAAM,CAAC0H,mBAAmB,UAAAiE,sBAAA,iBAA1BA,sBAAA,CAA4B/D,KAAK,GAAI5H,MAAM,CAAC4H,KAAK,EAAI,MAAM,CAAC,cAC/EvO,IAAA,SAAMgP,SAAS,CAAC,MAAM,CAAAG,QAAA,CAAE,EAAAoD,sBAAA,CAAA5L,MAAM,CAAC0H,mBAAmB,UAAAkE,sBAAA,iBAA1BA,sBAAA,CAA4BjE,UAAU,GAAI3H,MAAM,CAAC4H,KAAK,CAAO,CAAC,GAF9EtF,KAGL,CAAC,EACP,CAAC,cAEFjJ,IAAA,MAAGgP,SAAS,CAAC,YAAY,CAAAG,QAAA,CAAC,gCAA8B,CAAG,CAC5D,CACE,CAAC,cAENjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACX,SAAS,EAAC2P,SAAS,CAAC,MAAM,CAAE,CAAC,oBAAiB,EAAI,CAAC,CACxEgB,sBAAsB,CAAC,CAAC,cAEzBhQ,IAAA,QAAKgP,SAAS,CAAC,MAAM,CAAAG,QAAA,cACnBjP,KAAA,CAACnC,MAAM,EACLuR,OAAO,CAAC,iBAAiB,CACzBN,SAAS,CAAC,OAAO,CACjBqC,OAAO,CAAErG,YAAa,CAAAmE,QAAA,eAEtBnP,IAAA,CAACN,SAAS,EAACsP,SAAS,CAAC,MAAM,CAAE,CAAC,0BAEhC,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAEN9O,KAAA,CAAChC,GAAG,EAACoT,EAAE,CAAE,CAAE,CAACtC,SAAS,CAAC,aAAa,CAAAG,QAAA,eACjCjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACjB,OAAO,EAACiQ,SAAS,CAAC,MAAM,CAAE,CAAC,iBAAc,EAAI,CAAC,CACnErN,iBAAiB,EAAIA,iBAAiB,CAAC2E,MAAM,CAAG,CAAC,cAChDpG,KAAA,QAAK8O,SAAS,CAAC,0BAA0B,CAAAG,QAAA,eACvCnP,IAAA,QAAKgP,SAAS,CAAC,qBAAqB,CAAAG,QAAA,CACjCxN,iBAAiB,CAACuO,GAAG,CAAC,CAACsC,QAAQ,CAAEvJ,KAAK,gBACrC/I,KAAA,QAEE8O,SAAS,CAAE,sBAAsB/F,KAAK,GAAKlH,gBAAgB,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC9EsP,OAAO,CAAEA,CAAA,GAAMrP,mBAAmB,CAACiH,KAAK,CAAE,CAAAkG,QAAA,eAE1CnP,IAAA,CAAC5B,KAAK,EACJ4S,GAAG,CAAEwB,QAAS,CACdvB,GAAG,CAAE,WAAWhI,KAAK,CAAG,CAAC,EAAG,CAC5BiI,SAAS,MACTlC,SAAS,CAAC,eAAe,CAC1B,CAAC,cACFhP,IAAA,SAAMgP,SAAS,CAAC,cAAc,CAAAG,QAAA,CAAElG,KAAK,CAAG,CAAC,CAAO,CAAC,GAV5CA,KAWF,CACN,CAAC,CACC,CAAC,cACNjJ,IAAA,QAAKgP,SAAS,CAAC,2BAA2B,CAAAG,QAAA,cACxCnP,IAAA,CAAC5B,KAAK,EACJ4S,GAAG,CAAErP,iBAAiB,CAACI,gBAAgB,CAAE,CACzCkP,GAAG,CAAC,SAAS,CACbC,SAAS,MACTlC,SAAS,CAAC,oBAAoB,CAC9BC,KAAK,CAAE,CAAEkC,SAAS,CAAE,OAAQ,CAAE,CAC/B,CAAC,CACC,CAAC,EACH,CAAC,cAENjR,KAAA,QAAK8O,SAAS,CAAC,sBAAsB,CAAAG,QAAA,eACnCnP,IAAA,CAACjB,OAAO,EAACgJ,IAAI,CAAE,EAAG,CAACiH,SAAS,CAAC,gBAAgB,CAAE,CAAC,cAChDhP,IAAA,MAAGgP,SAAS,CAAC,iBAAiB,CAAAG,QAAA,CAAC,qBAAmB,CAAG,CAAC,EACnD,CACN,EACE,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,cAENnP,IAAA,QAAKgP,SAAS,CAAC,UAAU,CAAAG,QAAA,cACvBjP,KAAA,CAAC1B,IAAI,EACH8B,EAAE,CAAC,uBAAuB,CAC1BmS,SAAS,CAAEzP,SAAU,CACrB0P,QAAQ,CAAGC,CAAC,EAAK1P,YAAY,CAAC0P,CAAC,CAAE,CACjC3D,SAAS,CAAC,MAAM,CAAAG,QAAA,eAEhBnP,IAAA,CAACvB,GAAG,EAACmU,QAAQ,CAAC,SAAS,CAACC,KAAK,cAAE3S,KAAA,SAAAiP,QAAA,eAAMnP,IAAA,CAACf,YAAY,EAAC+P,SAAS,CAAC,MAAM,CAAE,CAAC,kBAAe,EAAM,CAAE,CAAAG,QAAA,cAC3FjP,KAAA,CAACjC,GAAG,EAAC+Q,SAAS,CAAC,MAAM,CAAAG,QAAA,eACnBjP,KAAA,CAAChC,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,eACTjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACf,YAAY,EAAC+P,SAAS,CAAC,MAAM,CAAE,CAAC,eAAY,EAAI,CAAC,CACtElL,aAAa,cACZ5D,KAAA,QAAK8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eACnBjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAAAtC,QAAA,eACTnP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,MAAM,CACX+J,KAAK,CAAEhO,WAAY,CACnBiO,QAAQ,CAAGxK,CAAC,EAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CAChDE,SAAS,CAAE,CAAC,CAAC9N,gBAAiB,CAC9B+N,WAAW,CAAC,oBAAoB,CACjC,CAAC,cACF/R,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClCnL,gBAAgB,CACI,CAAC,EACd,CAAC,cACb9D,KAAA,QAAK8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eACnBjP,KAAA,CAACnC,MAAM,EACLuR,OAAO,CAAC,SAAS,CACjBvH,IAAI,CAAC,IAAI,CACTiH,SAAS,CAAC,MAAM,CAChBqC,OAAO,CAAEjG,eAAgB,CAAA+D,QAAA,eAEzBnP,IAAA,CAACnB,OAAO,EAACmQ,SAAS,CAAC,MAAM,CAAE,CAAC,QAC9B,EAAQ,CAAC,cACT9O,KAAA,CAACnC,MAAM,EACLuR,OAAO,CAAC,WAAW,CACnBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAElG,iBAAkB,CAAAgE,QAAA,eAE3BnP,IAAA,CAACR,MAAM,EAACwP,SAAS,CAAC,MAAM,CAAE,CAAC,UAC7B,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN9O,KAAA,QAAK8O,SAAS,CAAC,6EAA6E,CAAAG,QAAA,eAC1FnP,IAAA,WAAAmP,QAAA,CACGvL,WAAW,GAAKvB,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CACjD,aAAa9F,EAAE,EAAE,CAAC,CACd,CAAC,cACTN,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,iBAAiB,CACzBvH,IAAI,CAAC,IAAI,CACTsJ,OAAO,CAAEnG,gBAAiB,CAAAiE,QAAA,CAC3B,WAED,CAAQ,CAAC,EACN,CACN,cAEDjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAAChB,MAAM,EAACgQ,SAAS,CAAC,MAAM,CAAE,CAAC,SAAM,EAAI,CAAC,cAC3DhP,IAAA,QAAKgP,SAAS,CAAC,MAAM,CAAAG,QAAA,CAClB5M,aAAa,CAAC+D,MAAM,CAAG,CAAC,CACvB/D,aAAa,CAAC2N,GAAG,CAAC,CAACvJ,MAAM,CAAEsC,KAAK,QAAA6J,sBAAA,CAAAC,sBAAA,oBAC9B7S,KAAA,QAAiB8O,SAAS,CAAC,MAAM,CAAAG,QAAA,EAC9BI,iBAAiB,CAAC,EAAAuD,sBAAA,CAAAnM,MAAM,CAAC0H,mBAAmB,UAAAyE,sBAAA,iBAA1BA,sBAAA,CAA4BvE,KAAK,GAAI5H,MAAM,CAAC4H,KAAK,EAAI,MAAM,CAAC,cAC/EvO,IAAA,SAAMgP,SAAS,CAAC,MAAM,CAAAG,QAAA,CAAE,EAAA4D,sBAAA,CAAApM,MAAM,CAAC0H,mBAAmB,UAAA0E,sBAAA,iBAA1BA,sBAAA,CAA4BzE,UAAU,GAAI3H,MAAM,CAAC4H,KAAK,CAAO,CAAC,GAF9EtF,KAGL,CAAC,EACP,CAAC,cAEFjJ,IAAA,MAAGgP,SAAS,CAAC,YAAY,CAAAG,QAAA,CAAC,gCAA8B,CAAG,CAC5D,CACE,CAAC,cAENjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACX,SAAS,EAAC2P,SAAS,CAAC,MAAM,CAAE,CAAC,oBAAiB,EAAI,CAAC,CACxEgB,sBAAsB,CAAC,CAAC,EACtB,CAAC,cAENhQ,IAAA,CAAC9B,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACTjP,KAAA,QAAK8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eACnBjP,KAAA,OAAI8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,CAACjB,OAAO,EAACiQ,SAAS,CAAC,MAAM,CAAE,CAAC,iBAAc,EAAI,CAAC,cACpE9O,KAAA,MAAG8O,SAAS,CAAC,iBAAiB,CAAAG,QAAA,EAAC,kDAAgD,CAAC5N,aAAa,CAAC+E,MAAM,CAAC,MAAI,EAAG,CAAC,CAG5G7E,gBAAgB,CAAC6E,MAAM,CAAG,CAAC,eAC1BpG,KAAA,QAAK8O,SAAS,CAAC,+BAA+B,CAAAG,QAAA,eAC5CnP,IAAA,QAAKgP,SAAS,CAAC,qBAAqB,CAAAG,QAAA,CACjC1N,gBAAgB,CAACyO,GAAG,CAAC,CAAC8C,UAAU,CAAE/J,KAAK,gBACtC/I,KAAA,QAEE8O,SAAS,CAAE,sBAAsB/F,KAAK,GAAKlH,gBAAgB,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC9EsP,OAAO,CAAEA,CAAA,GAAMrP,mBAAmB,CAACiH,KAAK,CAAE,CAAAkG,QAAA,eAE1CnP,IAAA,QAAKgP,SAAS,CAAC,eAAe,CAAAG,QAAA,cAC5BnP,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,QAAQ,CAChBvH,IAAI,CAAC,IAAI,CACTiH,SAAS,CAAC,kBAAkB,CAC5BqC,OAAO,CAAGhK,CAAC,EAAK,CACdA,CAAC,CAAC4L,eAAe,CAAC,CAAC,CACnBjK,WAAW,CAACC,KAAK,CAAC,CACpB,CAAE,CAAAkG,QAAA,cAEFnP,IAAA,CAACT,OAAO,GAAE,CAAC,CACL,CAAC,CACN,CAAC,cACNS,IAAA,CAAC5B,KAAK,EACJ4S,GAAG,CAAEgC,UAAW,CAChB/B,GAAG,CAAE,WAAWhI,KAAK,CAAG,CAAC,EAAG,CAC5BiI,SAAS,MACTlC,SAAS,CAAC,eAAe,CAC1B,CAAC,cACFhP,IAAA,SAAMgP,SAAS,CAAC,cAAc,CAAAG,QAAA,CAAElG,KAAK,CAAG,CAAC,CAAO,CAAC,GAvB5CA,KAwBF,CACN,CAAC,CACC,CAAC,CAELxH,gBAAgB,CAAC6E,MAAM,CAAG,CAAC,eAC1BtG,IAAA,QAAKgP,SAAS,CAAC,2BAA2B,CAAAG,QAAA,cACxCnP,IAAA,CAAC5B,KAAK,EACJ4S,GAAG,CAAEvP,gBAAgB,CAACM,gBAAgB,CAAE,CACxCkP,GAAG,CAAC,iBAAiB,CACrBC,SAAS,MACTlC,SAAS,CAAC,oBAAoB,CAC9BC,KAAK,CAAE,CAAEkC,SAAS,CAAE,OAAQ,CAAE,CAC/B,CAAC,CACC,CACN,EACE,CACN,CAGAhP,WAAW,cACVjC,KAAA,QAAK8O,SAAS,CAAC,kCAAkC,CAAAG,QAAA,eAC/CnP,IAAA,OAAIgP,SAAS,CAAC,MAAM,CAAAG,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC1CnP,IAAA,CAAC1B,WAAW,EACV+R,GAAG,CAAEpO,cAAe,CACpBiR,KAAK,CAAE,GAAG/J,IAAI,CAACqB,KAAK,CAACvI,cAAc,CAAC,GAAI,CACxCqN,OAAO,CAAC,MAAM,CACd6D,QAAQ,MACRnE,SAAS,CAAC,MAAM,CACjB,CAAC,cACF9O,KAAA,MAAG8O,SAAS,CAAC,YAAY,CAAAG,QAAA,eACvBnP,IAAA,CAAClB,QAAQ,EAACkQ,SAAS,CAAC,mBAAmB,CAAE,CAAC,aAChC,CAACzN,aAAa,CAAC+E,MAAM,CAAC,YAClC,EAAG,CAAC,EACD,CAAC,CAEN/E,aAAa,CAAC+E,MAAM,CAAG,EAAE,eACvBpG,KAAA,WAASyI,YAAY,CAAC,CAAC,CAAEqG,SAAS,CAAE,0BAA0BnG,YAAY,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAAsG,QAAA,eAC3FnP,IAAA,aAAW4I,aAAa,CAAC,CAAC,CAAEwK,QAAQ,MAAE,CAAC,cACvClT,KAAA,QAAK8O,SAAS,CAAC,aAAa,CAAAG,QAAA,eAC1BnP,IAAA,CAAClB,QAAQ,EAACiJ,IAAI,CAAE,EAAG,CAACiH,SAAS,CAAC,mBAAmB,CAAE,CAAC,cACpDhP,IAAA,MAAAmP,QAAA,CAAG,qDAAmD,CAAG,CAAC,cAC1DnP,IAAA,MAAGgP,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,mDAAiD,CAAG,CAAC,cACrFnP,IAAA,MAAGgP,SAAS,CAAC,kBAAkB,CAAAG,QAAA,CAAC,kDAAgD,CAAG,CAAC,EACjF,CAAC,EACH,CAER,cAEDnP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,MAAM,CACXwL,GAAG,CAAE7S,YAAa,CAClBqR,QAAQ,CAAEzK,iBAAkB,CAC5B0B,MAAM,CAAC,SAAS,CAChBsK,QAAQ,MACRnE,KAAK,CAAE,CAAEqE,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,EACC,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAENtT,IAAA,CAACvB,GAAG,EAACmU,QAAQ,CAAC,SAAS,CAACC,KAAK,cAAE3S,KAAA,SAAAiP,QAAA,eAAMnP,IAAA,CAACd,eAAe,EAAC8P,SAAS,CAAC,MAAM,CAAE,CAAC,UAAO,EAAM,CAAE,CAAAG,QAAA,cACtFjP,KAAA,CAACpC,IAAI,EAACmU,QAAQ,CAAErI,gBAAiB,CAACoF,SAAS,CAAC,MAAM,CAAAG,QAAA,eAChDjP,KAAA,CAACjC,GAAG,EAAAkR,QAAA,eACFjP,KAAA,CAAChC,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,eACTjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,MAAM,CAAAG,QAAA,eAC1BjP,KAAA,CAACpC,IAAI,CAACoU,KAAK,EAAA/C,QAAA,eACTnP,IAAA,WAAAmP,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,cACzCnP,IAAA,CAACrB,cAAc,EACbkR,SAAS,CAAC,KAAK,CACfC,OAAO,cAAE9P,IAAA,CAACpB,OAAO,EAAAuQ,QAAA,CAAC,sCAAoC,CAAS,CAAE,CAAAA,QAAA,cAEjEnP,IAAA,CAACf,YAAY,EAAC+P,SAAS,CAAC,iBAAiB,CAAE,CAAC,CAC9B,CAAC,EACP,CAAC,cACbhP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,QAAQ,CACbsK,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPR,KAAK,CAAEnR,gBAAiB,CACxBoR,QAAQ,CAAGxK,CAAC,EAAK3G,mBAAmB,CAAC2G,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CACrDE,SAAS,CAAE,CAAC,CAACtO,gBAAgB,CAAC/C,gBAAiB,CAC/C8S,QAAQ,MACT,CAAC,cACFvT,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClC3L,gBAAgB,CAAC/C,gBAAgB,CACb,CAAC,EACd,CAAC,cAEbP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,MAAM,CAAAG,QAAA,eAC1BjP,KAAA,CAACpC,IAAI,CAACoU,KAAK,EAAA/C,QAAA,eACTnP,IAAA,WAAAmP,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,cACrCnP,IAAA,CAACrB,cAAc,EACbkR,SAAS,CAAC,KAAK,CACfC,OAAO,cAAE9P,IAAA,CAACpB,OAAO,EAAAuQ,QAAA,CAAC,8CAA4C,CAAS,CAAE,CAAAA,QAAA,cAEzEnP,IAAA,CAACf,YAAY,EAAC+P,SAAS,CAAC,iBAAiB,CAAE,CAAC,CAC9B,CAAC,EACP,CAAC,cACbhP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX9J,IAAI,CAAC,QAAQ,CACbsK,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPR,KAAK,CAAEjR,YAAa,CACpBkR,QAAQ,CAAGxK,CAAC,EAAKzG,eAAe,CAACyG,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CACjDE,SAAS,CAAE,CAAC,CAACtO,gBAAgB,CAAC7C,YAAa,CAC3C4S,QAAQ,MACT,CAAC,cACFvT,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClC3L,gBAAgB,CAAC7C,YAAY,CACT,CAAC,EACd,CAAC,CAEZF,gBAAgB,EAAIE,YAAY,EAAIkE,UAAU,CAACpE,gBAAgB,CAAC,CAAG,CAAC,EAAIoE,UAAU,CAAClE,YAAY,CAAC,CAAG,CAAC,eACnGT,KAAA,QAAK8O,SAAS,CAAC,2BAA2B,CAAAG,QAAA,eACxCjP,KAAA,QAAK8O,SAAS,CAAC,wDAAwD,CAAAG,QAAA,eACrEnP,IAAA,SAAAmP,QAAA,cAAMnP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAM,CAAC,cAC5CjP,KAAA,CAAC3B,KAAK,EAACiV,EAAE,CACP9P,YAAY,CAAG,EAAE,CAAG,QAAQ,CAC5BA,YAAY,CAAG,EAAE,CAAG,SAAS,CAC7B,SACD,CAAAyL,QAAA,eACCnP,IAAA,CAACZ,YAAY,EAAC4P,SAAS,CAAC,MAAM,CAAE,CAAC,CAChCtL,YAAY,CAAC,GAChB,EAAO,CAAC,EACL,CAAC,cACN1D,IAAA,QAAKgP,SAAS,CAAC,yBAAyB,CAAE,CAAC,cAC3C9O,KAAA,QAAK8O,SAAS,CAAC,qCAAqC,CAAAG,QAAA,eAClDnP,IAAA,UAAAmP,QAAA,CAAO,KAAG,CAAO,CAAC,cAClBnP,IAAA,UAAAmP,QAAA,CAAO,MAAI,CAAO,CAAC,EAChB,CAAC,EACH,CACN,EACE,CAAC,cAENnP,IAAA,CAAC9B,GAAG,EAACoT,EAAE,CAAE,CAAE,CAAAnC,QAAA,cACTjP,KAAA,CAACpC,IAAI,CAAC2T,KAAK,EAACzC,SAAS,CAAC,MAAM,CAAAG,QAAA,eAC1BjP,KAAA,CAACpC,IAAI,CAACoU,KAAK,EAAA/C,QAAA,eACTnP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,cAC/BnP,IAAA,CAACrB,cAAc,EACbkR,SAAS,CAAC,KAAK,CACfC,OAAO,cAAE9P,IAAA,CAACpB,OAAO,EAAAuQ,QAAA,CAAC,2CAAyC,CAAS,CAAE,CAAAA,QAAA,cAEtEnP,IAAA,CAACf,YAAY,EAAC+P,SAAS,CAAC,iBAAiB,CAAE,CAAC,CAC9B,CAAC,EACP,CAAC,cACbhP,IAAA,CAAClC,IAAI,CAAC6T,OAAO,EACX8B,EAAE,CAAC,UAAU,CACbC,IAAI,CAAE,CAAE,CACR9B,KAAK,CAAE/Q,YAAa,CACpBgR,QAAQ,CAAGxK,CAAC,EAAKvG,eAAe,CAACuG,CAAC,CAACG,MAAM,CAACoK,KAAK,CAAE,CACjDE,SAAS,CAAE,CAAC,CAACtO,gBAAgB,CAAC3C,YAAa,CAC3CkR,WAAW,CAAC,kDAAkD,CAC/D,CAAC,cACF/R,IAAA,CAAClC,IAAI,CAAC6T,OAAO,CAACK,QAAQ,EAACnK,IAAI,CAAC,SAAS,CAAAsH,QAAA,CAClC3L,gBAAgB,CAAC3C,YAAY,CACT,CAAC,cACxBX,KAAA,CAACpC,IAAI,CAAC6V,IAAI,EAAC3E,SAAS,CAAC,YAAY,CAAAG,QAAA,EAC9BtO,YAAY,CAAG,GAAG,CAAGA,YAAY,CAACyF,MAAM,CAAG,GAAG,CAAC,uBAClD,EAAW,CAAC,EACF,CAAC,CACV,CAAC,EACH,CAAC,cAENtG,IAAA,QAAKgP,SAAS,CAAC,mBAAmB,CAAAG,QAAA,cAChCjP,KAAA,CAACnC,MAAM,EAAC8J,IAAI,CAAC,QAAQ,CAACmH,SAAS,CAAC,aAAa,CAACjH,IAAI,CAAC,IAAI,CAAAoH,QAAA,eACrDnP,IAAA,CAACnB,OAAO,EAACmQ,SAAS,CAAC,MAAM,CAAE,CAAC,kBAE9B,EAAQ,CAAC,CACN,CAAC,EACF,CAAC,CACJ,CAAC,EACF,CAAC,CACJ,CACN,EACQ,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CAAC,CACH,CAAC,cAGNhP,IAAA,CAACsQ,iBAAiB,GAAE,CAAC,cAGrBpQ,KAAA,CAAC7B,KAAK,EAACkS,IAAI,CAAEnN,YAAa,CAACoN,MAAM,CAAEvF,aAAc,CAACwF,QAAQ,MAAAtB,QAAA,eACxDnP,IAAA,CAAC3B,KAAK,CAACqS,MAAM,EAACC,WAAW,MAAAxB,QAAA,cACvBjP,KAAA,CAAC7B,KAAK,CAACuS,KAAK,EAAAzB,QAAA,eACVnP,IAAA,CAACN,SAAS,EAACsP,SAAS,CAAC,kBAAkB,CAAE,CAAC,0BAE5C,EAAa,CAAC,CACF,CAAC,cACf9O,KAAA,CAAC7B,KAAK,CAACwS,IAAI,EAAA1B,QAAA,eACTnP,IAAA,MAAAmP,QAAA,CAAG,kEAAgE,CAAG,CAAC,cACvEjP,KAAA,QAAK8O,SAAS,CAAC,sBAAsB,CAAAG,QAAA,eACnCjP,KAAA,MAAG8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,WAAAmP,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACvL,WAAW,GAAKvB,cAAc,EAAIA,cAAc,CAAC8D,sBAAsB,CACpH9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CACjD,aAAa9F,EAAE,EAAE,CAAC,EAAI,CAAC,cACzBJ,KAAA,MAAG8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,WAAAmP,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,QAAK,CAAC1O,gBAAgB,EAAI,CAAC,cAClFP,KAAA,MAAG8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,QAAK,CAACxO,YAAY,EAAI,CAAC,cAC1ET,KAAA,MAAG8O,SAAS,CAAC,MAAM,CAAAG,QAAA,eAACnP,IAAA,WAAAmP,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAACzL,YAAY,CAAC,GAAC,EAAG,CAAC,EACpE,CAAC,EACI,CAAC,cACbxD,KAAA,CAAC7B,KAAK,CAAC+S,MAAM,EAAAjC,QAAA,eACXnP,IAAA,CAACjC,MAAM,EAACuR,OAAO,CAAC,WAAW,CAAC+B,OAAO,CAAEpG,aAAc,CAAAkE,QAAA,CAAC,QAEpD,CAAQ,CAAC,cACTnP,IAAA,CAACjC,MAAM,EACLuR,OAAO,CAAC,SAAS,CACjB+B,OAAO,CAAElF,qBAAsB,CAC/BkG,QAAQ,CAAE/O,UAAW,CAAA6L,QAAA,CAEpB7L,UAAU,cACTpD,KAAA,CAAAE,SAAA,EAAA+O,QAAA,eACEnP,IAAA,CAAC7B,OAAO,EACNsV,EAAE,CAAC,MAAM,CACTrE,SAAS,CAAC,QAAQ,CAClBrH,IAAI,CAAC,IAAI,CACTsH,IAAI,CAAC,QAAQ,CACb,cAAY,MAAM,CAClBL,SAAS,CAAC,MAAM,CACjB,CAAC,gBAEJ,EAAE,CAAC,cAEH9O,KAAA,CAAAE,SAAA,EAAA+O,QAAA,eACEnP,IAAA,CAACL,UAAU,EAACqP,SAAS,CAAC,MAAM,CAAE,CAAC,eAEjC,EAAE,CACH,CACK,CAAC,EACG,CAAC,EACV,CAAC,EACR,CAAC,CAEP,CAAC,CAED,cAAe,CAAA3O,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}