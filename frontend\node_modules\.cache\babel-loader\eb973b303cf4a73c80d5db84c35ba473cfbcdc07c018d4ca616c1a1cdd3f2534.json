{"ast": null, "code": "import{ref,uploadBytesResumable,getDownloadURL}from\"firebase/storage\";import{storage}from\"./config\";/**\r\n * Upload a single image to Firebase Storage\r\n * @param {File} file - The file to upload\r\n * @param {string} path - The path in Firebase Storage to upload to\r\n * @param {Function} progressCallback - Callback function for upload progress\r\n * @returns {Promise<string>} - A promise that resolves to the download URL\r\n */export const uploadImage=async function(file,path){let progressCallback=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;try{// Create a unique filename\nconst timestamp=new Date().getTime();const fileName=`${timestamp}_${file.name}`;const fullPath=`${path}/${fileName}`;// Create a storage reference\nconst storageRef=ref(storage,fullPath);// Upload the file\nconst uploadTask=uploadBytesResumable(storageRef,file);// Return a promise that resolves with the download URL\nreturn new Promise((resolve,reject)=>{uploadTask.on(\"state_changed\",snapshot=>{// Calculate progress\nconst progress=snapshot.bytesTransferred/snapshot.totalBytes*100;if(progressCallback){progressCallback(progress);}},error=>{// Handle errors\nconsole.error(\"Upload error:\",error);reject(error);},async()=>{// Upload completed successfully, get the download URL\nconst downloadURL=await getDownloadURL(uploadTask.snapshot.ref);resolve(downloadURL);});});}catch(error){console.error(\"Error in uploadImage:\",error);throw error;}};/**\r\n * Upload multiple images to Firebase Storage\r\n * @param {File[]} files - Array of files to upload\r\n * @param {string} path - The path in Firebase Storage to upload to\r\n * @param {Function} progressCallback - Callback function for overall upload progress\r\n * @returns {Promise<string[]>} - A promise that resolves to an array of download URLs\r\n */export const uploadMultipleImages=async function(files,path){let progressCallback=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;try{const uploadPromises=[];const totalFiles=files.length;let completedFiles=0;// Create upload promises for each file\nfor(const file of files){const uploadPromise=uploadImage(file,path,fileProgress=>{// Calculate overall progress\nif(progressCallback){const overallProgress=completedFiles/totalFiles*100+fileProgress/totalFiles;progressCallback(overallProgress);}});// When a file completes, increment the counter\nuploadPromise.then(()=>{completedFiles++;});uploadPromises.push(uploadPromise);}// Wait for all uploads to complete\nconst downloadURLs=await Promise.all(uploadPromises);return downloadURLs;}catch(error){console.error(\"Error in uploadMultipleImages:\",error);throw error;}};", "map": {"version": 3, "names": ["ref", "uploadBytesResumable", "getDownloadURL", "storage", "uploadImage", "file", "path", "progressCallback", "arguments", "length", "undefined", "timestamp", "Date", "getTime", "fileName", "name", "fullPath", "storageRef", "uploadTask", "Promise", "resolve", "reject", "on", "snapshot", "progress", "bytesTransferred", "totalBytes", "error", "console", "downloadURL", "uploadMultipleImages", "files", "uploadPromises", "totalFiles", "completedFiles", "uploadPromise", "fileProgress", "overallProgress", "then", "push", "downloadURLs", "all"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/firebase/imageUpload.js"], "sourcesContent": ["import { ref, uploadBytesResumable, getDownloadURL } from \"firebase/storage\";\r\nimport { storage } from \"./config\";\r\n\r\n/**\r\n * Upload a single image to Firebase Storage\r\n * @param {File} file - The file to upload\r\n * @param {string} path - The path in Firebase Storage to upload to\r\n * @param {Function} progressCallback - Callback function for upload progress\r\n * @returns {Promise<string>} - A promise that resolves to the download URL\r\n */\r\nexport const uploadImage = async (file, path, progressCallback = null) => {\r\n  try {\r\n    // Create a unique filename\r\n    const timestamp = new Date().getTime();\r\n    const fileName = `${timestamp}_${file.name}`;\r\n    const fullPath = `${path}/${fileName}`;\r\n    \r\n    // Create a storage reference\r\n    const storageRef = ref(storage, fullPath);\r\n    \r\n    // Upload the file\r\n    const uploadTask = uploadBytesResumable(storageRef, file);\r\n    \r\n    // Return a promise that resolves with the download URL\r\n    return new Promise((resolve, reject) => {\r\n      uploadTask.on(\r\n        \"state_changed\",\r\n        (snapshot) => {\r\n          // Calculate progress\r\n          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;\r\n          if (progressCallback) {\r\n            progressCallback(progress);\r\n          }\r\n        },\r\n        (error) => {\r\n          // Handle errors\r\n          console.error(\"Upload error:\", error);\r\n          reject(error);\r\n        },\r\n        async () => {\r\n          // Upload completed successfully, get the download URL\r\n          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);\r\n          resolve(downloadURL);\r\n        }\r\n      );\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error in uploadImage:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Upload multiple images to Firebase Storage\r\n * @param {File[]} files - Array of files to upload\r\n * @param {string} path - The path in Firebase Storage to upload to\r\n * @param {Function} progressCallback - Callback function for overall upload progress\r\n * @returns {Promise<string[]>} - A promise that resolves to an array of download URLs\r\n */\r\nexport const uploadMultipleImages = async (files, path, progressCallback = null) => {\r\n  try {\r\n    const uploadPromises = [];\r\n    const totalFiles = files.length;\r\n    let completedFiles = 0;\r\n    \r\n    // Create upload promises for each file\r\n    for (const file of files) {\r\n      const uploadPromise = uploadImage(\r\n        file, \r\n        path, \r\n        (fileProgress) => {\r\n          // Calculate overall progress\r\n          if (progressCallback) {\r\n            const overallProgress = \r\n              ((completedFiles / totalFiles) * 100) + \r\n              (fileProgress / totalFiles);\r\n            progressCallback(overallProgress);\r\n          }\r\n        }\r\n      );\r\n      \r\n      // When a file completes, increment the counter\r\n      uploadPromise.then(() => {\r\n        completedFiles++;\r\n      });\r\n      \r\n      uploadPromises.push(uploadPromise);\r\n    }\r\n    \r\n    // Wait for all uploads to complete\r\n    const downloadURLs = await Promise.all(uploadPromises);\r\n    return downloadURLs;\r\n  } catch (error) {\r\n    console.error(\"Error in uploadMultipleImages:\", error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAASA,GAAG,CAAEC,oBAAoB,CAAEC,cAAc,KAAQ,kBAAkB,CAC5E,OAASC,OAAO,KAAQ,UAAU,CAElC;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,WAAW,CAAG,cAAAA,CAAOC,IAAI,CAAEC,IAAI,CAA8B,IAA5B,CAAAC,gBAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACnE,GAAI,CACF;AACA,KAAM,CAAAG,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CACtC,KAAM,CAAAC,QAAQ,CAAG,GAAGH,SAAS,IAAIN,IAAI,CAACU,IAAI,EAAE,CAC5C,KAAM,CAAAC,QAAQ,CAAG,GAAGV,IAAI,IAAIQ,QAAQ,EAAE,CAEtC;AACA,KAAM,CAAAG,UAAU,CAAGjB,GAAG,CAACG,OAAO,CAAEa,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAE,UAAU,CAAGjB,oBAAoB,CAACgB,UAAU,CAAEZ,IAAI,CAAC,CAEzD;AACA,MAAO,IAAI,CAAAc,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtCH,UAAU,CAACI,EAAE,CACX,eAAe,CACdC,QAAQ,EAAK,CACZ;AACA,KAAM,CAAAC,QAAQ,CAAID,QAAQ,CAACE,gBAAgB,CAAGF,QAAQ,CAACG,UAAU,CAAI,GAAG,CACxE,GAAInB,gBAAgB,CAAE,CACpBA,gBAAgB,CAACiB,QAAQ,CAAC,CAC5B,CACF,CAAC,CACAG,KAAK,EAAK,CACT;AACAC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCN,MAAM,CAACM,KAAK,CAAC,CACf,CAAC,CACD,SAAY,CACV;AACA,KAAM,CAAAE,WAAW,CAAG,KAAM,CAAA3B,cAAc,CAACgB,UAAU,CAACK,QAAQ,CAACvB,GAAG,CAAC,CACjEoB,OAAO,CAACS,WAAW,CAAC,CACtB,CACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAG,oBAAoB,CAAG,cAAAA,CAAOC,KAAK,CAAEzB,IAAI,CAA8B,IAA5B,CAAAC,gBAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC7E,GAAI,CACF,KAAM,CAAAwB,cAAc,CAAG,EAAE,CACzB,KAAM,CAAAC,UAAU,CAAGF,KAAK,CAACtB,MAAM,CAC/B,GAAI,CAAAyB,cAAc,CAAG,CAAC,CAEtB;AACA,IAAK,KAAM,CAAA7B,IAAI,GAAI,CAAA0B,KAAK,CAAE,CACxB,KAAM,CAAAI,aAAa,CAAG/B,WAAW,CAC/BC,IAAI,CACJC,IAAI,CACH8B,YAAY,EAAK,CAChB;AACA,GAAI7B,gBAAgB,CAAE,CACpB,KAAM,CAAA8B,eAAe,CACjBH,cAAc,CAAGD,UAAU,CAAI,GAAG,CACnCG,YAAY,CAAGH,UAAW,CAC7B1B,gBAAgB,CAAC8B,eAAe,CAAC,CACnC,CACF,CACF,CAAC,CAED;AACAF,aAAa,CAACG,IAAI,CAAC,IAAM,CACvBJ,cAAc,EAAE,CAClB,CAAC,CAAC,CAEFF,cAAc,CAACO,IAAI,CAACJ,aAAa,CAAC,CACpC,CAEA;AACA,KAAM,CAAAK,YAAY,CAAG,KAAM,CAAArB,OAAO,CAACsB,GAAG,CAACT,cAAc,CAAC,CACtD,MAAO,CAAAQ,YAAY,CACrB,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}