{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import axios from\"axios\";import{useNavigate}from'react-router-dom';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import{Container,Row,Col,Card,Table,Button,Form,InputGroup,Badge,Spinner,<PERSON><PERSON>,Modal}from'react-bootstrap';import{FaSearch,FaSort,FaSortUp,FaSortDown,FaTshirt,FaCut,FaCalendarAlt,FaFilter,FaPlus,FaInfoCircle,FaTrash,FaFileDownload,FaFileCsv,FaTable,FaEdit}from'react-icons/fa';// Add global CSS for hover effect\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const addGlobalStyle=()=>{const style=document.createElement('style');style.innerHTML=`\n    .hover-row:hover {\n      background-color: #f0f8ff !important;\n    }\n  `;document.head.appendChild(style);};// Add the global style once when component loads\naddGlobalStyle();const ViewCutting=()=>{const navigate=useNavigate();const[cuttingRecords,setCuttingRecords]=useState([]);const[filteredRecords,setFilteredRecords]=useState([]);const[error,setError]=useState(\"\");const[loading,setLoading]=useState(true);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);// Track sidebar state\nconst[searchTerm,setSearchTerm]=useState(\"\");const[sortConfig,setSortConfig]=useState({key:'cutting_date',direction:'desc'});const[dateFilter,setDateFilter]=useState({startDate:'',endDate:''});const[showDeleteModal,setShowDeleteModal]=useState(false);const[recordToDelete,setRecordToDelete]=useState(null);const[showCsvModal,setShowCsvModal]=useState(false);const[csvFilters,setCsvFilters]=useState({startDate:'',endDate:'',fabricFilter:'',includeAllRecords:true});const[uniqueFabrics,setUniqueFabrics]=useState([]);const[csvLoading,setCsvLoading]=useState(false);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Fetch cutting records on mount\nuseEffect(()=>{setLoading(true);axios.get(\"http://localhost:8000/api/cutting/cutting-records/\").then(res=>{console.log(\"Fetched cutting records:\",res.data);setCuttingRecords(res.data);setFilteredRecords(res.data);// Extract unique fabric names for the dropdown\n// First, create a more detailed log of the fabric data\nconsole.log(\"Detailed fabric data:\",res.data.map(record=>{var _record$fabric_defini;return{id:record.id,product_name:record.product_name,fabric_definition_id:record.fabric_definition,fabric_definition_data:record.fabric_definition_data,fabric_name:(_record$fabric_defini=record.fabric_definition_data)===null||_record$fabric_defini===void 0?void 0:_record$fabric_defini.fabric_name};}));// Create a list of all fabric names from the records\nlet allFabricNames=[];// Loop through each record to extract fabric names\nres.data.forEach(record=>{if(record.fabric_definition_data&&record.fabric_definition_data.fabric_name){allFabricNames.push(record.fabric_definition_data.fabric_name);}});console.log(\"All fabric names (before deduplication):\",allFabricNames);// Remove duplicates and sort alphabetically\nconst uniqueFabricNames=[...new Set(allFabricNames)].sort();console.log(\"Unique fabric names (after deduplication):\",uniqueFabricNames);// Set the unique fabric names in state\nsetUniqueFabrics(uniqueFabricNames);setLoading(false);}).catch(err=>{console.error(\"Error fetching cutting records:\",err);setError(\"Failed to fetch cutting records.\");setLoading(false);});},[]);// Filter records based on search term and date range\nuseEffect(()=>{let results=cuttingRecords;// Apply search filter\nif(searchTerm){const lowercasedSearch=searchTerm.toLowerCase();results=results.filter(record=>{var _record$fabric_defini2;return record.product_name&&record.product_name.toLowerCase().includes(lowercasedSearch)||((_record$fabric_defini2=record.fabric_definition_data)===null||_record$fabric_defini2===void 0?void 0:_record$fabric_defini2.fabric_name)&&record.fabric_definition_data.fabric_name.toLowerCase().includes(lowercasedSearch);});}// Apply date filter\nif(dateFilter.startDate&&dateFilter.endDate){results=results.filter(record=>{const recordDate=new Date(record.cutting_date);const startDate=new Date(dateFilter.startDate);const endDate=new Date(dateFilter.endDate);endDate.setHours(23,59,59);// Include the entire end date\nreturn recordDate>=startDate&&recordDate<=endDate;});}// Apply sorting\nif(sortConfig.key){results=[...results].sort((a,b)=>{let aValue,bValue;if(sortConfig.key==='product_name'){aValue=a.product_name||'';bValue=b.product_name||'';}else if(sortConfig.key==='fabric_name'){var _a$fabric_definition_,_b$fabric_definition_;aValue=((_a$fabric_definition_=a.fabric_definition_data)===null||_a$fabric_definition_===void 0?void 0:_a$fabric_definition_.fabric_name)||'';bValue=((_b$fabric_definition_=b.fabric_definition_data)===null||_b$fabric_definition_===void 0?void 0:_b$fabric_definition_.fabric_name)||'';}else if(sortConfig.key==='cutting_date'){aValue=new Date(a.cutting_date);bValue=new Date(b.cutting_date);}else if(sortConfig.key==='total_quantity'||sortConfig.key==='total_yard'||sortConfig.key==='total_variants'){const aAggregates=getAggregates(a);const bAggregates=getAggregates(b);if(sortConfig.key==='total_quantity'){aValue=aAggregates.totalQuantity;bValue=bAggregates.totalQuantity;}else if(sortConfig.key==='total_yard'){aValue=aAggregates.totalYard;bValue=bAggregates.totalYard;}else{aValue=aAggregates.totalVariants;bValue=bAggregates.totalVariants;}}if(aValue<bValue){return sortConfig.direction==='asc'?-1:1;}if(aValue>bValue){return sortConfig.direction==='asc'?1:-1;}return 0;});}setFilteredRecords(results);},[cuttingRecords,searchTerm,dateFilter,sortConfig]);// Helper to calculate aggregates for each record\nconst getAggregates=record=>{let totalYard=0;let totalQuantity=0;const variantSet=new Set();if(record.details){record.details.forEach(detail=>{totalYard+=parseFloat(detail.yard_usage||0);const sizesSum=(detail.xs||0)+(detail.s||0)+(detail.m||0)+(detail.l||0)+(detail.xl||0);totalQuantity+=sizesSum;// Use nested data if available, otherwise fallback to raw ID\nconst variantId=detail.fabric_variant_data?detail.fabric_variant_data.id:detail.fabric_variant;variantSet.add(variantId);});}return{totalYard,totalQuantity,totalVariants:variantSet.size};};// Handle sorting\nconst requestSort=key=>{let direction='asc';if(sortConfig.key===key&&sortConfig.direction==='asc'){direction='desc';}setSortConfig({key,direction});};// Get sort icon\nconst getSortIcon=columnName=>{if(sortConfig.key!==columnName){return/*#__PURE__*/_jsx(FaSort,{className:\"ms-1 text-muted\"});}return sortConfig.direction==='asc'?/*#__PURE__*/_jsx(FaSortUp,{className:\"ms-1 text-primary\"}):/*#__PURE__*/_jsx(FaSortDown,{className:\"ms-1 text-primary\"});};// Reset all filters\nconst resetFilters=()=>{setSearchTerm('');setDateFilter({startDate:'',endDate:''});setSortConfig({key:'cutting_date',direction:'desc'});};// Edit functionality removed\n// State for tracking if a record has sewing records\nconst[hasSewingRecords,setHasSewingRecords]=useState(false);// Handle delete confirmation\nconst handleDeleteClick=async(e,record)=>{e.stopPropagation();// Prevent row click event\nsetRecordToDelete(record);setLoading(true);setError(\"\");// Clear any previous errors\ntry{// Check if the cutting record has any sewing records\nconst response=await axios.get(`http://localhost:8000/api/cutting/cutting-records/${record.id}/check_sewing_records/`);setHasSewingRecords(response.data.has_sewing_records);setShowDeleteModal(true);}catch(error){console.error(\"Error checking sewing records:\",error);// If we can't check, assume there might be sewing records to prevent accidental deletion\nsetHasSewingRecords(true);setShowDeleteModal(true);}finally{setLoading(false);}};// Handle actual deletion\nconst handleDeleteConfirm=()=>{if(!recordToDelete)return;// Don't allow deletion if there are sewing records\nif(hasSewingRecords){setError(\"Cannot delete this cutting record because it has associated sewing records.\");setShowDeleteModal(false);setRecordToDelete(null);return;}setLoading(true);setError(\"\");// Clear any previous errors\n// Log the request for debugging\nconsole.log(`Attempting to delete cutting record with ID: ${recordToDelete.id}`);axios.delete(`http://localhost:8000/api/cutting/cutting-records/${recordToDelete.id}/`).then(response=>{console.log(\"Delete response:\",response);// Remove the deleted record from state\nconst updatedRecords=cuttingRecords.filter(record=>record.id!==recordToDelete.id);setCuttingRecords(updatedRecords);setFilteredRecords(updatedRecords);setShowDeleteModal(false);setRecordToDelete(null);setLoading(false);// Show success message\nsetError(\"\");// Clear any previous errors\nalert(\"Cutting record deleted successfully!\");}).catch(err=>{console.error(\"Error deleting cutting record:\",err);let errorMessage=\"Failed to delete cutting record.\";// Check for specific error messages from the server\nif(err.response){console.log(\"Error response:\",err.response);if(err.response.data&&typeof err.response.data==='object'){errorMessage=JSON.stringify(err.response.data);}else if(err.response.data){errorMessage=err.response.data;}else if(err.response.status===403){errorMessage=\"You don't have permission to delete this record.\";}else if(err.response.status===409||err.response.status===400){errorMessage=\"This record cannot be deleted because it is referenced by other records.\";}}setError(errorMessage);setShowDeleteModal(false);setRecordToDelete(null);setLoading(false);});};// Open CSV export modal\nconst openCsvModal=()=>{// Initialize CSV filters with current date filters\nsetCsvFilters({...csvFilters,startDate:dateFilter.startDate,endDate:dateFilter.endDate});console.log(\"Opening CSV modal, unique fabrics:\",uniqueFabrics);// Debug log\nsetShowCsvModal(true);};// Generate and download CSV file\nconst generateCSV=()=>{setCsvLoading(true);// Apply filters to get the records for CSV\nlet recordsToExport=cuttingRecords;// Apply date filter if provided\nif(csvFilters.startDate&&csvFilters.endDate){recordsToExport=recordsToExport.filter(record=>{const recordDate=new Date(record.cutting_date);const startDate=new Date(csvFilters.startDate);const endDate=new Date(csvFilters.endDate);endDate.setHours(23,59,59);// Include the entire end date\nreturn recordDate>=startDate&&recordDate<=endDate;});}// Apply fabric filter if provided\nif(csvFilters.fabricFilter){recordsToExport=recordsToExport.filter(record=>{var _record$fabric_defini3;return((_record$fabric_defini3=record.fabric_definition_data)===null||_record$fabric_defini3===void 0?void 0:_record$fabric_defini3.fabric_name)===csvFilters.fabricFilter;});}// If not including all records, use the current filtered records\nif(!csvFilters.includeAllRecords){recordsToExport=filteredRecords;}// Create CSV content\nlet csvContent=\"Product Name,Cutting Date,Fabric Name,Total Yard Usage,Total Amount (Rs.),Number of Colors,Color Codes\\n\";recordsToExport.forEach(record=>{var _record$fabric_defini4;const{totalYard,totalVariants}=getAggregates(record);// Calculate total amount (fabric cost)\nlet totalAmount=0;// Collect color information\nconst colorCodes=[];if(record.details){record.details.forEach(detail=>{if(detail.fabric_variant_data){const yardUsage=parseFloat(detail.yard_usage||0);const pricePerYard=parseFloat(detail.fabric_variant_data.price_per_yard||0);totalAmount+=yardUsage*pricePerYard;// Add color code if available\nif(detail.fabric_variant_data.color){colorCodes.push(detail.fabric_variant_data.color);}}});}// Format the row data\nconst productName=record.product_name||\"N/A\";const cuttingDate=record.cutting_date;const fabricName=((_record$fabric_defini4=record.fabric_definition_data)===null||_record$fabric_defini4===void 0?void 0:_record$fabric_defini4.fabric_name)||\"N/A\";const numberOfColors=totalVariants;const colorCodesStr=colorCodes.join(' | ');// Escape commas in text fields\nconst escapedProductName=productName.includes(',')?`\"${productName}\"`:productName;const escapedFabricName=fabricName.includes(',')?`\"${fabricName}\"`:fabricName;const escapedColorCodes=colorCodesStr.includes(',')?`\"${colorCodesStr}\"`:colorCodesStr;// Add row to CSV\ncsvContent+=`${escapedProductName},${cuttingDate},${escapedFabricName},${totalYard.toFixed(2)},${totalAmount.toFixed(2)},${numberOfColors},${escapedColorCodes}\\n`;});// Create a Blob and download link\nconst blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const url=URL.createObjectURL(blob);const link=document.createElement('a');link.setAttribute('href',url);link.setAttribute('download',`cutting_records_export_${new Date().toISOString().slice(0,10)}.csv`);document.body.appendChild(link);// Trigger download and cleanup\nlink.click();document.body.removeChild(link);setCsvLoading(false);setShowCsvModal(false);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(Container,{fluid:true,style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-4\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(FaCut,{className:\"me-2 text-primary\"}),\"Cutting Records\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",onClick:openCsvModal,children:[/*#__PURE__*/_jsx(FaFileCsv,{className:\"me-1\"}),\" Export CSV\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",onClick:()=>navigate('/addcutting'),children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-1\"}),\" Add New Cutting\"]})]})]}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"text-center\",children:error}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(InputGroup,{className:\"mb-3 mb-md-0\",children:[/*#__PURE__*/_jsx(InputGroup.Text,{children:/*#__PURE__*/_jsx(FaSearch,{})}),/*#__PURE__*/_jsx(Form.Control,{placeholder:\"Search by product or fabric name...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:5,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3 mb-md-0\",children:/*#__PURE__*/_jsx(Form.Control,{type:\"date\",placeholder:\"Start Date\",value:dateFilter.startDate,onChange:e=>setDateFilter({...dateFilter,startDate:e.target.value})})})}),/*#__PURE__*/_jsx(Col,{md:5,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3 mb-md-0\",children:/*#__PURE__*/_jsx(Form.Control,{type:\"date\",placeholder:\"End Date\",value:dateFilter.endDate,onChange:e=>setDateFilter({...dateFilter,endDate:e.target.value})})})}),/*#__PURE__*/_jsxs(Col,{md:2,className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"text-primary me-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"d-none d-md-inline\",children:\"Date Range\"})]})]})}),/*#__PURE__*/_jsx(Col,{md:2,className:\"d-flex justify-content-end\",children:/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",onClick:resetFilters,className:\"w-100\",children:[/*#__PURE__*/_jsx(FaFilter,{className:\"me-1\"}),\" Reset Filters\"]})})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"info\",className:\"p-2\",children:[filteredRecords.length,\" \",filteredRecords.length===1?'Record':'Records',\" Found\"]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(FaInfoCircle,{className:\"me-1\"}),\" Click on any row for detailed view\"]})})]}),/*#__PURE__*/_jsx(Card,{className:\"shadow-sm\",children:/*#__PURE__*/_jsx(Card.Body,{className:\"p-0\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",variant:\"primary\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:\"Loading cutting records...\"})]}):filteredRecords.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(FaCut,{className:\"text-muted mb-3\",style:{fontSize:'2rem'}}),/*#__PURE__*/_jsx(\"h5\",{children:\"No cutting records found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:\"Try adjusting your search or filter criteria\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(Table,{hover:true,className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-light\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"cursor-pointer\",onClick:()=>requestSort('product_name'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[\"Product Name \",getSortIcon('product_name')]})}),/*#__PURE__*/_jsx(\"th\",{className:\"cursor-pointer\",onClick:()=>requestSort('fabric_name'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[\"Fabric Name \",getSortIcon('fabric_name')]})}),/*#__PURE__*/_jsx(\"th\",{className:\"cursor-pointer\",onClick:()=>requestSort('cutting_date'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[\"Cutting Date \",getSortIcon('cutting_date')]})}),/*#__PURE__*/_jsx(\"th\",{className:\"cursor-pointer\",onClick:()=>requestSort('total_quantity'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[\"Total Quantity \",getSortIcon('total_quantity')]})}),/*#__PURE__*/_jsx(\"th\",{className:\"cursor-pointer\",onClick:()=>requestSort('total_yard'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[\"Yard Usage \",getSortIcon('total_yard')]})}),/*#__PURE__*/_jsx(\"th\",{className:\"cursor-pointer\",onClick:()=>requestSort('total_variants'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[\"Variants Used \",getSortIcon('total_variants')]})}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredRecords.map(record=>{var _record$details;const{totalYard,totalQuantity,totalVariants}=getAggregates(record);// Get product name; fallback to fabric name if product_name is empty\nconst productName=record.product_name||\"N/A\";// Get fabric names from details\nconst fabricNames=new Set();(_record$details=record.details)===null||_record$details===void 0?void 0:_record$details.forEach(detail=>{var _detail$fabric_varian,_detail$fabric_varian2;if((_detail$fabric_varian=detail.fabric_variant_data)!==null&&_detail$fabric_varian!==void 0&&(_detail$fabric_varian2=_detail$fabric_varian.fabric_definition_data)!==null&&_detail$fabric_varian2!==void 0&&_detail$fabric_varian2.fabric_name){fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);}});const fabricName=Array.from(fabricNames).join(', ')||\"N/A\";return/*#__PURE__*/_jsx(React.Fragment,{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"hover-row\",style:{cursor:'pointer'},onClick:e=>{// Prevent navigation if clicking on the action buttons\nif(e.target.closest('.action-buttons'))return;navigate(`/cutting-record/${record.id}`);},children:[/*#__PURE__*/_jsx(\"td\",{children:productName}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaTshirt,{className:\"text-primary me-2\"}),fabricName]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"light\",text:\"dark\",className:\"p-2\",children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"me-1\"}),record.cutting_date]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:\"info\",className:\"p-2\",children:totalQuantity})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"warning\",text:\"dark\",className:\"p-2\",children:[totalYard,\" yards\"]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Badge,{bg:\"secondary\",className:\"p-2\",children:totalVariants})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex action-buttons gap-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-info\",size:\"sm\",onClick:e=>{e.stopPropagation();// Prevent row click event\nnavigate(`/cutting-record/${record.id}`);},children:[/*#__PURE__*/_jsx(FaInfoCircle,{className:\"me-1\"}),\" Details\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",size:\"sm\",onClick:e=>{e.stopPropagation();// Prevent row click event\nnavigate(`/edit-cutting/${record.id}`);},children:[/*#__PURE__*/_jsx(FaEdit,{className:\"me-1\"}),\" Edit\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"outline-danger\",size:\"sm\",onClick:e=>handleDeleteClick(e,record),children:[/*#__PURE__*/_jsx(FaTrash,{className:\"me-1\"}),\" Delete\"]})]})})]})},record.id);})})]})})})})]}),/*#__PURE__*/_jsxs(Modal,{show:showDeleteModal,onHide:()=>setShowDeleteModal(false),children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsx(Modal.Title,{children:\"Confirm Deletion\"})}),/*#__PURE__*/_jsx(Modal.Body,{children:hasSewingRecords?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cannot Delete:\"}),\" This cutting record has associated sewing records.\",/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:/*#__PURE__*/_jsx(\"small\",{children:\"To delete this cutting record, you must first delete all sewing records that use it. You can view these records in the Daily Sewing History page.\"})})]}),recordToDelete&&/*#__PURE__*/_jsxs(\"p\",{className:\"mt-3\",children:[\"Cutting record for \",/*#__PURE__*/_jsx(\"strong\",{children:recordToDelete.product_name||\"Unnamed Product\"}),\" from\",\" \",/*#__PURE__*/_jsx(\"strong\",{children:recordToDelete.cutting_date}),\" cannot be deleted.\"]})]}):/*#__PURE__*/_jsxs(\"div\",{children:[recordToDelete&&/*#__PURE__*/_jsxs(\"p\",{children:[\"Are you sure you want to delete the cutting record for\",\" \",/*#__PURE__*/_jsx(\"strong\",{children:recordToDelete.product_name||\"Unnamed Product\"}),\" from\",\" \",/*#__PURE__*/_jsx(\"strong\",{children:recordToDelete.cutting_date}),\"?\"]}),/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Warning:\"}),\" This action cannot be undone. Deleting this record may affect related data.\"]})]})}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>setShowDeleteModal(false),children:hasSewingRecords?\"Close\":\"Cancel\"}),!hasSewingRecords&&/*#__PURE__*/_jsx(Button,{variant:\"danger\",onClick:handleDeleteConfirm,disabled:loading,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Deleting...\"]}):\"Delete\"})]})]}),/*#__PURE__*/_jsxs(Modal,{show:showCsvModal,onHide:()=>setShowCsvModal(false),children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsxs(Modal.Title,{children:[/*#__PURE__*/_jsx(FaFileCsv,{className:\"me-2 text-primary\"}),\"Export Cutting Records to CSV\"]})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-muted mb-3\",children:\"Configure the filters below to customize your CSV export. The CSV will include product name, cutting date, fabric name, total yard usage, total amount, number of colors, and color codes.\"}),/*#__PURE__*/_jsxs(Form,{children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Date Range\"}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Form.Control,{type:\"date\",placeholder:\"Start Date\",value:csvFilters.startDate,onChange:e=>setCsvFilters({...csvFilters,startDate:e.target.value})})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Form.Control,{type:\"date\",placeholder:\"End Date\",value:csvFilters.endDate,onChange:e=>setCsvFilters({...csvFilters,endDate:e.target.value})})})]}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Leave dates empty to include all dates\"})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Fabric Filter\"}),/*#__PURE__*/_jsxs(Form.Select,{value:csvFilters.fabricFilter,onChange:e=>setCsvFilters({...csvFilters,fabricFilter:e.target.value}),className:\"form-select mb-2\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Fabrics\"}),uniqueFabrics&&uniqueFabrics.length>0?uniqueFabrics.map((fabric,index)=>/*#__PURE__*/_jsx(\"option\",{value:fabric,children:fabric},`fabric-${index}`)):/*#__PURE__*/_jsx(\"option\",{disabled:true,children:\"No fabrics available\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"small text-muted mb-2\",children:[\"Available fabrics: \",uniqueFabrics&&uniqueFabrics.length>0?uniqueFabrics.join(', '):'None found']}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Select a fabric from the dropdown or choose \\\"All Fabrics\\\" to include all\"})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Check,{type:\"checkbox\",label:\"Include only currently filtered records\",checked:!csvFilters.includeAllRecords,onChange:e=>setCsvFilters({...csvFilters,includeAllRecords:!e.target.checked})}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"When checked, only the records currently visible in the table will be exported\"})]})]}),/*#__PURE__*/_jsxs(Alert,{variant:\"info\",children:[/*#__PURE__*/_jsx(FaTable,{className:\"me-2\"}),/*#__PURE__*/_jsx(\"strong\",{children:\"Note:\"}),\" The CSV file will include color information with the number of colors used and their color codes.\"]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>setShowCsvModal(false),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:generateCSV,disabled:csvLoading,children:csvLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Generating CSV...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaFileDownload,{className:\"me-1\"}),\" Download CSV\"]})})]})]})]});};export default ViewCutting;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useNavigate", "RoleBasedNavBar", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Form", "InputGroup", "Badge", "Spinner", "<PERSON><PERSON>", "Modal", "FaSearch", "FaSort", "FaSortUp", "FaSortDown", "FaTshirt", "FaCut", "FaCalendarAlt", "FaFilter", "FaPlus", "FaInfoCircle", "FaTrash", "FaFileDownload", "FaFileCsv", "FaTable", "FaEdit", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "addGlobalStyle", "style", "document", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "ViewCutting", "navigate", "cuttingRecords", "setCuttingRecords", "filteredRecords", "setFilteredRecords", "error", "setError", "loading", "setLoading", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "searchTerm", "setSearchTerm", "sortConfig", "setSortConfig", "key", "direction", "dateFilter", "setDateFilter", "startDate", "endDate", "showDeleteModal", "setShowDeleteModal", "recordToDelete", "setRecordToDelete", "showCsvModal", "setShowCsvModal", "csvFilters", "setCsvFilters", "fabricFilter", "includeAllRecords", "uniqueFabrics", "setUniqueFabrics", "csvLoading", "setCsvLoading", "handleResize", "addEventListener", "removeEventListener", "get", "then", "res", "console", "log", "data", "map", "record", "_record$fabric_defini", "id", "product_name", "fabric_definition_id", "fabric_definition", "fabric_definition_data", "fabric_name", "allFabricNames", "for<PERSON>ach", "push", "uniqueFabricNames", "Set", "sort", "catch", "err", "results", "lowercasedSearch", "toLowerCase", "filter", "_record$fabric_defini2", "includes", "recordDate", "Date", "cutting_date", "setHours", "a", "b", "aValue", "bValue", "_a$fabric_definition_", "_b$fabric_definition_", "aAggregates", "getAggregates", "bAggregates", "totalQuantity", "totalYard", "totalVariants", "variantSet", "details", "detail", "parseFloat", "yard_usage", "sizesSum", "xs", "s", "m", "l", "xl", "variantId", "fabric_variant_data", "fabric_variant", "add", "size", "requestSort", "getSortIcon", "columnName", "className", "resetFilters", "hasSewingRecords", "setHasSewingRecords", "handleDeleteClick", "e", "stopPropagation", "response", "has_sewing_records", "handleDeleteConfirm", "delete", "updatedRecords", "alert", "errorMessage", "JSON", "stringify", "status", "openCsvModal", "generateCSV", "recordsToExport", "_record$fabric_defini3", "csv<PERSON><PERSON>nt", "_record$fabric_defini4", "totalAmount", "colorCodes", "yardUsage", "pricePerYard", "price_per_yard", "color", "productName", "cuttingDate", "fabricName", "numberOfColors", "colorCodesStr", "join", "escapedProductName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapedColorCodes", "toFixed", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "setAttribute", "toISOString", "slice", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "children", "fluid", "marginLeft", "width", "transition", "padding", "variant", "onClick", "Body", "md", "Text", "Control", "placeholder", "value", "onChange", "target", "Group", "bg", "length", "animation", "fontSize", "hover", "_record$details", "fabricNames", "_detail$fabric_varian", "_detail$fabric_varian2", "Array", "from", "cursor", "closest", "text", "show", "onHide", "Header", "closeButton", "Title", "Footer", "disabled", "as", "role", "Label", "Select", "fabric", "index", "Check", "label", "checked"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/ViewCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { useNavigate } from 'react-router-dom';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport {\r\n  Container, Row, Col, Card, Table, Button,\r\n  Form, InputGroup, Badge, Spinner, <PERSON><PERSON>, Modal\r\n} from 'react-bootstrap';\r\nimport {\r\n  FaSearch, FaSort, FaSortUp, FaSortDown,\r\n  FaTshirt, FaCut, FaCalendarAlt, FaFilter,\r\n  FaPlus, FaInfoCircle, FaTrash, FaFileDownload,\r\n  FaFileCsv, FaTable, FaEdit\r\n} from 'react-icons/fa';\r\n\r\n// Add global CSS for hover effect\r\nconst addGlobalStyle = () => {\r\n  const style = document.createElement('style');\r\n  style.innerHTML = `\r\n    .hover-row:hover {\r\n      background-color: #f0f8ff !important;\r\n    }\r\n  `;\r\n  document.head.appendChild(style);\r\n};\r\n\r\n// Add the global style once when component loads\r\naddGlobalStyle();\r\n\r\nconst ViewCutting = () => {\r\n  const navigate = useNavigate();\r\n  const [cuttingRecords, setCuttingRecords] = useState([]);\r\n  const [filteredRecords, setFilteredRecords] = useState([]);\r\n  const [error, setError] = useState(\"\");\r\n  const [loading, setLoading] = useState(true);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768); // Track sidebar state\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortConfig, setSortConfig] = useState({ key: 'cutting_date', direction: 'desc' });\r\n  const [dateFilter, setDateFilter] = useState({ startDate: '', endDate: '' });\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [recordToDelete, setRecordToDelete] = useState(null);\r\n  const [showCsvModal, setShowCsvModal] = useState(false);\r\n  const [csvFilters, setCsvFilters] = useState({\r\n    startDate: '',\r\n    endDate: '',\r\n    fabricFilter: '',\r\n    includeAllRecords: true\r\n  });\r\n  const [uniqueFabrics, setUniqueFabrics] = useState([]);\r\n  const [csvLoading, setCsvLoading] = useState(false);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch cutting records on mount\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios\r\n      .get(\"http://localhost:8000/api/cutting/cutting-records/\")\r\n      .then((res) => {\r\n        console.log(\"Fetched cutting records:\", res.data);\r\n        setCuttingRecords(res.data);\r\n        setFilteredRecords(res.data);\r\n\r\n        // Extract unique fabric names for the dropdown\r\n        // First, create a more detailed log of the fabric data\r\n        console.log(\"Detailed fabric data:\", res.data.map(record => {\r\n          return {\r\n            id: record.id,\r\n            product_name: record.product_name,\r\n            fabric_definition_id: record.fabric_definition,\r\n            fabric_definition_data: record.fabric_definition_data,\r\n            fabric_name: record.fabric_definition_data?.fabric_name\r\n          };\r\n        }));\r\n\r\n        // Create a list of all fabric names from the records\r\n        let allFabricNames = [];\r\n\r\n        // Loop through each record to extract fabric names\r\n        res.data.forEach(record => {\r\n          if (record.fabric_definition_data && record.fabric_definition_data.fabric_name) {\r\n            allFabricNames.push(record.fabric_definition_data.fabric_name);\r\n          }\r\n        });\r\n\r\n        console.log(\"All fabric names (before deduplication):\", allFabricNames);\r\n\r\n        // Remove duplicates and sort alphabetically\r\n        const uniqueFabricNames = [...new Set(allFabricNames)].sort();\r\n        console.log(\"Unique fabric names (after deduplication):\", uniqueFabricNames);\r\n\r\n        // Set the unique fabric names in state\r\n        setUniqueFabrics(uniqueFabricNames);\r\n\r\n        setLoading(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Error fetching cutting records:\", err);\r\n        setError(\"Failed to fetch cutting records.\");\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  // Filter records based on search term and date range\r\n  useEffect(() => {\r\n    let results = cuttingRecords;\r\n\r\n    // Apply search filter\r\n    if (searchTerm) {\r\n      const lowercasedSearch = searchTerm.toLowerCase();\r\n      results = results.filter(record =>\r\n        (record.product_name && record.product_name.toLowerCase().includes(lowercasedSearch)) ||\r\n        (record.fabric_definition_data?.fabric_name &&\r\n         record.fabric_definition_data.fabric_name.toLowerCase().includes(lowercasedSearch))\r\n      );\r\n    }\r\n\r\n    // Apply date filter\r\n    if (dateFilter.startDate && dateFilter.endDate) {\r\n      results = results.filter(record => {\r\n        const recordDate = new Date(record.cutting_date);\r\n        const startDate = new Date(dateFilter.startDate);\r\n        const endDate = new Date(dateFilter.endDate);\r\n        endDate.setHours(23, 59, 59); // Include the entire end date\r\n        return recordDate >= startDate && recordDate <= endDate;\r\n      });\r\n    }\r\n\r\n    // Apply sorting\r\n    if (sortConfig.key) {\r\n      results = [...results].sort((a, b) => {\r\n        let aValue, bValue;\r\n\r\n        if (sortConfig.key === 'product_name') {\r\n          aValue = a.product_name || '';\r\n          bValue = b.product_name || '';\r\n        } else if (sortConfig.key === 'fabric_name') {\r\n          aValue = a.fabric_definition_data?.fabric_name || '';\r\n          bValue = b.fabric_definition_data?.fabric_name || '';\r\n        } else if (sortConfig.key === 'cutting_date') {\r\n          aValue = new Date(a.cutting_date);\r\n          bValue = new Date(b.cutting_date);\r\n        } else if (sortConfig.key === 'total_quantity' || sortConfig.key === 'total_yard' || sortConfig.key === 'total_variants') {\r\n          const aAggregates = getAggregates(a);\r\n          const bAggregates = getAggregates(b);\r\n\r\n          if (sortConfig.key === 'total_quantity') {\r\n            aValue = aAggregates.totalQuantity;\r\n            bValue = bAggregates.totalQuantity;\r\n          } else if (sortConfig.key === 'total_yard') {\r\n            aValue = aAggregates.totalYard;\r\n            bValue = bAggregates.totalYard;\r\n          } else {\r\n            aValue = aAggregates.totalVariants;\r\n            bValue = bAggregates.totalVariants;\r\n          }\r\n        }\r\n\r\n        if (aValue < bValue) {\r\n          return sortConfig.direction === 'asc' ? -1 : 1;\r\n        }\r\n        if (aValue > bValue) {\r\n          return sortConfig.direction === 'asc' ? 1 : -1;\r\n        }\r\n        return 0;\r\n      });\r\n    }\r\n\r\n    setFilteredRecords(results);\r\n  }, [cuttingRecords, searchTerm, dateFilter, sortConfig]);\r\n\r\n  // Helper to calculate aggregates for each record\r\n  const getAggregates = (record) => {\r\n    let totalYard = 0;\r\n    let totalQuantity = 0;\r\n    const variantSet = new Set();\r\n\r\n    if (record.details) {\r\n      record.details.forEach((detail) => {\r\n        totalYard += parseFloat(detail.yard_usage || 0);\r\n        const sizesSum =\r\n          (detail.xs || 0) +\r\n          (detail.s || 0) +\r\n          (detail.m || 0) +\r\n          (detail.l || 0) +\r\n          (detail.xl || 0);\r\n        totalQuantity += sizesSum;\r\n        // Use nested data if available, otherwise fallback to raw ID\r\n        const variantId = detail.fabric_variant_data\r\n          ? detail.fabric_variant_data.id\r\n          : detail.fabric_variant;\r\n        variantSet.add(variantId);\r\n      });\r\n    }\r\n\r\n    return { totalYard, totalQuantity, totalVariants: variantSet.size };\r\n  };\r\n\r\n\r\n\r\n  // Handle sorting\r\n  const requestSort = (key) => {\r\n    let direction = 'asc';\r\n    if (sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  // Get sort icon\r\n  const getSortIcon = (columnName) => {\r\n    if (sortConfig.key !== columnName) {\r\n      return <FaSort className=\"ms-1 text-muted\" />;\r\n    }\r\n    return sortConfig.direction === 'asc' ?\r\n      <FaSortUp className=\"ms-1 text-primary\" /> :\r\n      <FaSortDown className=\"ms-1 text-primary\" />;\r\n  };\r\n\r\n  // Reset all filters\r\n  const resetFilters = () => {\r\n    setSearchTerm('');\r\n    setDateFilter({ startDate: '', endDate: '' });\r\n    setSortConfig({ key: 'cutting_date', direction: 'desc' });\r\n  };\r\n\r\n  // Edit functionality removed\r\n\r\n  // State for tracking if a record has sewing records\r\n  const [hasSewingRecords, setHasSewingRecords] = useState(false);\r\n\r\n  // Handle delete confirmation\r\n  const handleDeleteClick = async (e, record) => {\r\n    e.stopPropagation(); // Prevent row click event\r\n    setRecordToDelete(record);\r\n    setLoading(true);\r\n    setError(\"\"); // Clear any previous errors\r\n\r\n    try {\r\n      // Check if the cutting record has any sewing records\r\n      const response = await axios.get(`http://localhost:8000/api/cutting/cutting-records/${record.id}/check_sewing_records/`);\r\n      setHasSewingRecords(response.data.has_sewing_records);\r\n      setShowDeleteModal(true);\r\n    } catch (error) {\r\n      console.error(\"Error checking sewing records:\", error);\r\n      // If we can't check, assume there might be sewing records to prevent accidental deletion\r\n      setHasSewingRecords(true);\r\n      setShowDeleteModal(true);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle actual deletion\r\n  const handleDeleteConfirm = () => {\r\n    if (!recordToDelete) return;\r\n\r\n    // Don't allow deletion if there are sewing records\r\n    if (hasSewingRecords) {\r\n      setError(\"Cannot delete this cutting record because it has associated sewing records.\");\r\n      setShowDeleteModal(false);\r\n      setRecordToDelete(null);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(\"\"); // Clear any previous errors\r\n\r\n    // Log the request for debugging\r\n    console.log(`Attempting to delete cutting record with ID: ${recordToDelete.id}`);\r\n\r\n    axios\r\n      .delete(`http://localhost:8000/api/cutting/cutting-records/${recordToDelete.id}/`)\r\n      .then((response) => {\r\n        console.log(\"Delete response:\", response);\r\n        // Remove the deleted record from state\r\n        const updatedRecords = cuttingRecords.filter(record => record.id !== recordToDelete.id);\r\n        setCuttingRecords(updatedRecords);\r\n        setFilteredRecords(updatedRecords);\r\n        setShowDeleteModal(false);\r\n        setRecordToDelete(null);\r\n        setLoading(false);\r\n\r\n        // Show success message\r\n        setError(\"\"); // Clear any previous errors\r\n        alert(\"Cutting record deleted successfully!\");\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Error deleting cutting record:\", err);\r\n        let errorMessage = \"Failed to delete cutting record.\";\r\n\r\n        // Check for specific error messages from the server\r\n        if (err.response) {\r\n          console.log(\"Error response:\", err.response);\r\n          if (err.response.data && typeof err.response.data === 'object') {\r\n            errorMessage = JSON.stringify(err.response.data);\r\n          } else if (err.response.data) {\r\n            errorMessage = err.response.data;\r\n          } else if (err.response.status === 403) {\r\n            errorMessage = \"You don't have permission to delete this record.\";\r\n          } else if (err.response.status === 409 || err.response.status === 400) {\r\n            errorMessage = \"This record cannot be deleted because it is referenced by other records.\";\r\n          }\r\n        }\r\n\r\n        setError(errorMessage);\r\n        setShowDeleteModal(false);\r\n        setRecordToDelete(null);\r\n        setLoading(false);\r\n      });\r\n  };\r\n\r\n  // Open CSV export modal\r\n  const openCsvModal = () => {\r\n    // Initialize CSV filters with current date filters\r\n    setCsvFilters({\r\n      ...csvFilters,\r\n      startDate: dateFilter.startDate,\r\n      endDate: dateFilter.endDate\r\n    });\r\n    console.log(\"Opening CSV modal, unique fabrics:\", uniqueFabrics); // Debug log\r\n    setShowCsvModal(true);\r\n  };\r\n\r\n  // Generate and download CSV file\r\n  const generateCSV = () => {\r\n    setCsvLoading(true);\r\n\r\n    // Apply filters to get the records for CSV\r\n    let recordsToExport = cuttingRecords;\r\n\r\n    // Apply date filter if provided\r\n    if (csvFilters.startDate && csvFilters.endDate) {\r\n      recordsToExport = recordsToExport.filter(record => {\r\n        const recordDate = new Date(record.cutting_date);\r\n        const startDate = new Date(csvFilters.startDate);\r\n        const endDate = new Date(csvFilters.endDate);\r\n        endDate.setHours(23, 59, 59); // Include the entire end date\r\n        return recordDate >= startDate && recordDate <= endDate;\r\n      });\r\n    }\r\n\r\n    // Apply fabric filter if provided\r\n    if (csvFilters.fabricFilter) {\r\n      recordsToExport = recordsToExport.filter(record =>\r\n        record.fabric_definition_data?.fabric_name === csvFilters.fabricFilter\r\n      );\r\n    }\r\n\r\n    // If not including all records, use the current filtered records\r\n    if (!csvFilters.includeAllRecords) {\r\n      recordsToExport = filteredRecords;\r\n    }\r\n\r\n    // Create CSV content\r\n    let csvContent = \"Product Name,Cutting Date,Fabric Name,Total Yard Usage,Total Amount (Rs.),Number of Colors,Color Codes\\n\";\r\n\r\n    recordsToExport.forEach(record => {\r\n      const { totalYard, totalVariants } = getAggregates(record);\r\n\r\n      // Calculate total amount (fabric cost)\r\n      let totalAmount = 0;\r\n\r\n      // Collect color information\r\n      const colorCodes = [];\r\n\r\n      if (record.details) {\r\n        record.details.forEach(detail => {\r\n          if (detail.fabric_variant_data) {\r\n            const yardUsage = parseFloat(detail.yard_usage || 0);\r\n            const pricePerYard = parseFloat(detail.fabric_variant_data.price_per_yard || 0);\r\n            totalAmount += yardUsage * pricePerYard;\r\n\r\n            // Add color code if available\r\n            if (detail.fabric_variant_data.color) {\r\n              colorCodes.push(detail.fabric_variant_data.color);\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      // Format the row data\r\n      const productName = record.product_name || \"N/A\";\r\n      const cuttingDate = record.cutting_date;\r\n      const fabricName = record.fabric_definition_data?.fabric_name || \"N/A\";\r\n      const numberOfColors = totalVariants;\r\n      const colorCodesStr = colorCodes.join(' | ');\r\n\r\n      // Escape commas in text fields\r\n      const escapedProductName = productName.includes(',') ? `\"${productName}\"` : productName;\r\n      const escapedFabricName = fabricName.includes(',') ? `\"${fabricName}\"` : fabricName;\r\n      const escapedColorCodes = colorCodesStr.includes(',') ? `\"${colorCodesStr}\"` : colorCodesStr;\r\n\r\n      // Add row to CSV\r\n      csvContent += `${escapedProductName},${cuttingDate},${escapedFabricName},${totalYard.toFixed(2)},${totalAmount.toFixed(2)},${numberOfColors},${escapedColorCodes}\\n`;\r\n    });\r\n\r\n    // Create a Blob and download link\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `cutting_records_export_${new Date().toISOString().slice(0, 10)}.csv`);\r\n    document.body.appendChild(link);\r\n\r\n    // Trigger download and cleanup\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    setCsvLoading(false);\r\n    setShowCsvModal(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <Container fluid\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n          <h2 className=\"mb-0\">\r\n            <FaCut className=\"me-2 text-primary\" />\r\n            Cutting Records\r\n          </h2>\r\n          <div className=\"d-flex gap-2\">\r\n            <Button\r\n              variant=\"outline-primary\"\r\n              onClick={openCsvModal}\r\n            >\r\n              <FaFileCsv className=\"me-1\" /> Export CSV\r\n            </Button>\r\n            <Button\r\n              variant=\"success\"\r\n              onClick={() => navigate('/addcutting')}\r\n            >\r\n              <FaPlus className=\"me-1\" /> Add New Cutting\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {error && <Alert variant=\"danger\" className=\"text-center\">{error}</Alert>}\r\n\r\n        {/* Search and Filter Section */}\r\n        <Card className=\"mb-4 shadow-sm\">\r\n          <Card.Body>\r\n            <Row>\r\n              <Col md={4}>\r\n                <InputGroup className=\"mb-3 mb-md-0\">\r\n                  <InputGroup.Text>\r\n                    <FaSearch />\r\n                  </InputGroup.Text>\r\n                  <Form.Control\r\n                    placeholder=\"Search by product or fabric name...\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                  />\r\n                </InputGroup>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Row>\r\n                  <Col md={5}>\r\n                    <Form.Group className=\"mb-3 mb-md-0\">\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        placeholder=\"Start Date\"\r\n                        value={dateFilter.startDate}\r\n                        onChange={(e) => setDateFilter({...dateFilter, startDate: e.target.value})}\r\n                      />\r\n                    </Form.Group>\r\n                  </Col>\r\n                  <Col md={5}>\r\n                    <Form.Group className=\"mb-3 mb-md-0\">\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        placeholder=\"End Date\"\r\n                        value={dateFilter.endDate}\r\n                        onChange={(e) => setDateFilter({...dateFilter, endDate: e.target.value})}\r\n                      />\r\n                    </Form.Group>\r\n                  </Col>\r\n                  <Col md={2} className=\"d-flex align-items-center\">\r\n                    <FaCalendarAlt className=\"text-primary me-2\" />\r\n                    <span className=\"d-none d-md-inline\">Date Range</span>\r\n                  </Col>\r\n                </Row>\r\n              </Col>\r\n              <Col md={2} className=\"d-flex justify-content-end\">\r\n                <Button\r\n                  variant=\"outline-secondary\"\r\n                  onClick={resetFilters}\r\n                  className=\"w-100\"\r\n                >\r\n                  <FaFilter className=\"me-1\" /> Reset Filters\r\n                </Button>\r\n              </Col>\r\n            </Row>\r\n          </Card.Body>\r\n        </Card>\r\n\r\n        {/* Results Count */}\r\n        <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n          <div>\r\n            <Badge bg=\"info\" className=\"p-2\">\r\n              {filteredRecords.length} {filteredRecords.length === 1 ? 'Record' : 'Records'} Found\r\n            </Badge>\r\n          </div>\r\n          <div>\r\n            <small className=\"text-muted\">\r\n              <FaInfoCircle className=\"me-1\" /> Click on any row for detailed view\r\n            </small>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Main Table */}\r\n        <Card className=\"shadow-sm\">\r\n          <Card.Body className=\"p-0\">\r\n            {loading ? (\r\n              <div className=\"text-center py-5\">\r\n                <Spinner animation=\"border\" variant=\"primary\" />\r\n                <p className=\"mt-2\">Loading cutting records...</p>\r\n              </div>\r\n            ) : filteredRecords.length === 0 ? (\r\n              <div className=\"text-center py-5\">\r\n                <FaCut className=\"text-muted mb-3\" style={{ fontSize: '2rem' }} />\r\n                <h5>No cutting records found</h5>\r\n                <p className=\"text-muted\">Try adjusting your search or filter criteria</p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"table-responsive\">\r\n                <Table hover className=\"mb-0\">\r\n                  <thead className=\"bg-light\">\r\n                    <tr>\r\n                      <th className=\"cursor-pointer\" onClick={() => requestSort('product_name')}>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          Product Name {getSortIcon('product_name')}\r\n                        </div>\r\n                      </th>\r\n                      <th className=\"cursor-pointer\" onClick={() => requestSort('fabric_name')}>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          Fabric Name {getSortIcon('fabric_name')}\r\n                        </div>\r\n                      </th>\r\n                      <th className=\"cursor-pointer\" onClick={() => requestSort('cutting_date')}>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          Cutting Date {getSortIcon('cutting_date')}\r\n                        </div>\r\n                      </th>\r\n                      <th className=\"cursor-pointer\" onClick={() => requestSort('total_quantity')}>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          Total Quantity {getSortIcon('total_quantity')}\r\n                        </div>\r\n                      </th>\r\n                      <th className=\"cursor-pointer\" onClick={() => requestSort('total_yard')}>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          Yard Usage {getSortIcon('total_yard')}\r\n                        </div>\r\n                      </th>\r\n                      <th className=\"cursor-pointer\" onClick={() => requestSort('total_variants')}>\r\n                        <div className=\"d-flex align-items-center\">\r\n                          Variants Used {getSortIcon('total_variants')}\r\n                        </div>\r\n                      </th>\r\n                      <th>Actions</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {filteredRecords.map((record) => {\r\n                      const { totalYard, totalQuantity, totalVariants } = getAggregates(record);\r\n                      // Get product name; fallback to fabric name if product_name is empty\r\n                      const productName = record.product_name || \"N/A\";\r\n                      // Get fabric names from details\r\n                      const fabricNames = new Set();\r\n                      record.details?.forEach(detail => {\r\n                        if (detail.fabric_variant_data?.fabric_definition_data?.fabric_name) {\r\n                          fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\r\n                        }\r\n                      });\r\n                      const fabricName = Array.from(fabricNames).join(', ') || \"N/A\";\r\n\r\n                      return (\r\n                        <React.Fragment key={record.id}>\r\n                          <tr\r\n                            className=\"hover-row\"\r\n                            style={{ cursor: 'pointer' }}\r\n                            onClick={(e) => {\r\n                              // Prevent navigation if clicking on the action buttons\r\n                              if (e.target.closest('.action-buttons')) return;\r\n                              navigate(`/cutting-record/${record.id}`);\r\n                            }}\r\n                          >\r\n                            <td>{productName}</td>\r\n                            <td>\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <FaTshirt className=\"text-primary me-2\" />\r\n                                {fabricName}\r\n                              </div>\r\n                            </td>\r\n                            <td>\r\n                              <Badge bg=\"light\" text=\"dark\" className=\"p-2\">\r\n                                <FaCalendarAlt className=\"me-1\" />\r\n                                {record.cutting_date}\r\n                              </Badge>\r\n                            </td>\r\n                            <td>\r\n                              <Badge bg=\"info\" className=\"p-2\">\r\n                                {totalQuantity}\r\n                              </Badge>\r\n                            </td>\r\n                            <td>\r\n                              <Badge bg=\"warning\" text=\"dark\" className=\"p-2\">\r\n                                {totalYard} yards\r\n                              </Badge>\r\n                            </td>\r\n                            <td>\r\n                              <Badge bg=\"secondary\" className=\"p-2\">\r\n                                {totalVariants}\r\n                              </Badge>\r\n                            </td>\r\n                            <td>\r\n                              <div className=\"d-flex action-buttons gap-2\">\r\n                                <Button\r\n                                  variant=\"outline-info\"\r\n                                  size=\"sm\"\r\n                                  onClick={(e) => {\r\n                                    e.stopPropagation(); // Prevent row click event\r\n                                    navigate(`/cutting-record/${record.id}`);\r\n                                  }}\r\n                                >\r\n                                  <FaInfoCircle className=\"me-1\" /> Details\r\n                                </Button>\r\n\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={(e) => {\r\n                                    e.stopPropagation(); // Prevent row click event\r\n                                    navigate(`/edit-cutting/${record.id}`);\r\n                                  }}\r\n                                >\r\n                                  <FaEdit className=\"me-1\" /> Edit\r\n                                </Button>\r\n\r\n                                <Button\r\n                                  variant=\"outline-danger\"\r\n                                  size=\"sm\"\r\n                                  onClick={(e) => handleDeleteClick(e, record)}\r\n                                >\r\n                                  <FaTrash className=\"me-1\" /> Delete\r\n                                </Button>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        </React.Fragment>\r\n                      );\r\n                    })}\r\n                  </tbody>\r\n                </Table>\r\n              </div>\r\n            )}\r\n          </Card.Body>\r\n        </Card>\r\n      </Container>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Confirm Deletion</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {hasSewingRecords ? (\r\n            <div>\r\n              <Alert variant=\"danger\">\r\n                <strong>Cannot Delete:</strong> This cutting record has associated sewing records.\r\n                <div className=\"mt-2\">\r\n                  <small>\r\n                    To delete this cutting record, you must first delete all sewing records that use it.\r\n                    You can view these records in the Daily Sewing History page.\r\n                  </small>\r\n                </div>\r\n              </Alert>\r\n              {recordToDelete && (\r\n                <p className=\"mt-3\">\r\n                  Cutting record for <strong>{recordToDelete.product_name || \"Unnamed Product\"}</strong> from{\" \"}\r\n                  <strong>{recordToDelete.cutting_date}</strong> cannot be deleted.\r\n                </p>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              {recordToDelete && (\r\n                <p>\r\n                  Are you sure you want to delete the cutting record for{\" \"}\r\n                  <strong>{recordToDelete.product_name || \"Unnamed Product\"}</strong> from{\" \"}\r\n                  <strong>{recordToDelete.cutting_date}</strong>?\r\n                </p>\r\n              )}\r\n              <Alert variant=\"warning\">\r\n                <strong>Warning:</strong> This action cannot be undone. Deleting this record may affect related data.\r\n              </Alert>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowDeleteModal(false)}>\r\n            {hasSewingRecords ? \"Close\" : \"Cancel\"}\r\n          </Button>\r\n          {!hasSewingRecords && (\r\n            <Button\r\n              variant=\"danger\"\r\n              onClick={handleDeleteConfirm}\r\n              disabled={loading}\r\n            >\r\n              {loading ? (\r\n                <>\r\n                  <Spinner\r\n                    as=\"span\"\r\n                    animation=\"border\"\r\n                    size=\"sm\"\r\n                    role=\"status\"\r\n                    aria-hidden=\"true\"\r\n                    className=\"me-2\"\r\n                  />\r\n                  Deleting...\r\n                </>\r\n              ) : (\r\n                \"Delete\"\r\n              )}\r\n            </Button>\r\n          )}\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* CSV Export Modal */}\r\n      <Modal show={showCsvModal} onHide={() => setShowCsvModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFileCsv className=\"me-2 text-primary\" />\r\n            Export Cutting Records to CSV\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p className=\"text-muted mb-3\">\r\n            Configure the filters below to customize your CSV export. The CSV will include product name,\r\n            cutting date, fabric name, total yard usage, total amount, number of colors, and color codes.\r\n          </p>\r\n\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Date Range</Form.Label>\r\n              <Row>\r\n                <Col>\r\n                  <Form.Control\r\n                    type=\"date\"\r\n                    placeholder=\"Start Date\"\r\n                    value={csvFilters.startDate}\r\n                    onChange={(e) => setCsvFilters({...csvFilters, startDate: e.target.value})}\r\n                  />\r\n                </Col>\r\n                <Col>\r\n                  <Form.Control\r\n                    type=\"date\"\r\n                    placeholder=\"End Date\"\r\n                    value={csvFilters.endDate}\r\n                    onChange={(e) => setCsvFilters({...csvFilters, endDate: e.target.value})}\r\n                  />\r\n                </Col>\r\n              </Row>\r\n              <Form.Text className=\"text-muted\">\r\n                Leave dates empty to include all dates\r\n              </Form.Text>\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Fabric Filter</Form.Label>\r\n              <Form.Select\r\n                value={csvFilters.fabricFilter}\r\n                onChange={(e) => setCsvFilters({...csvFilters, fabricFilter: e.target.value})}\r\n                className=\"form-select mb-2\"\r\n              >\r\n                <option value=\"\">All Fabrics</option>\r\n                {uniqueFabrics && uniqueFabrics.length > 0 ?\r\n                  uniqueFabrics.map((fabric, index) => (\r\n                    <option key={`fabric-${index}`} value={fabric}>\r\n                      {fabric}\r\n                    </option>\r\n                  ))\r\n                :\r\n                  <option disabled>No fabrics available</option>\r\n                }\r\n              </Form.Select>\r\n\r\n              {/* Debug information - will show what fabrics are available */}\r\n              <div className=\"small text-muted mb-2\">\r\n                Available fabrics: {uniqueFabrics && uniqueFabrics.length > 0 ?\r\n                  uniqueFabrics.join(', ') : 'None found'}\r\n              </div>\r\n\r\n              <Form.Text className=\"text-muted\">\r\n                Select a fabric from the dropdown or choose \"All Fabrics\" to include all\r\n              </Form.Text>\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Check\r\n                type=\"checkbox\"\r\n                label=\"Include only currently filtered records\"\r\n                checked={!csvFilters.includeAllRecords}\r\n                onChange={(e) => setCsvFilters({...csvFilters, includeAllRecords: !e.target.checked})}\r\n              />\r\n              <Form.Text className=\"text-muted\">\r\n                When checked, only the records currently visible in the table will be exported\r\n              </Form.Text>\r\n            </Form.Group>\r\n          </Form>\r\n\r\n          <Alert variant=\"info\">\r\n            <FaTable className=\"me-2\" />\r\n            <strong>Note:</strong> The CSV file will include color information with the number of colors used and their color codes.\r\n          </Alert>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowCsvModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={generateCSV}\r\n            disabled={csvLoading}\r\n          >\r\n            {csvLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Generating CSV...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaFileDownload className=\"me-1\" /> Download CSV\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ViewCutting;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,OACEC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CACxCC,IAAI,CAAEC,UAAU,CAAEC,KAAK,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAK,KACzC,iBAAiB,CACxB,OACEC,QAAQ,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,UAAU,CACtCC,QAAQ,CAAEC,KAAK,CAAEC,aAAa,CAAEC,QAAQ,CACxCC,MAAM,CAAEC,YAAY,CAAEC,OAAO,CAAEC,cAAc,CAC7CC,SAAS,CAAEC,OAAO,CAAEC,MAAM,KACrB,gBAAgB,CAEvB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,CAC7CF,KAAK,CAACG,SAAS,CAAG;AACpB;AACA;AACA;AACA,GAAG,CACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC,CAClC,CAAC,CAED;AACAD,cAAc,CAAC,CAAC,CAEhB,KAAM,CAAAO,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,QAAQ,CAAG3C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC4C,cAAc,CAAEC,iBAAiB,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACiD,eAAe,CAAEC,kBAAkB,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACmD,KAAK,CAAEC,QAAQ,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqD,OAAO,CAAEC,UAAU,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuD,aAAa,CAAEC,gBAAgB,CAAC,CAAGxD,QAAQ,CAACyD,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAAE;AAC9E,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6D,UAAU,CAAEC,aAAa,CAAC,CAAG9D,QAAQ,CAAC,CAAE+D,GAAG,CAAE,cAAc,CAAEC,SAAS,CAAE,MAAO,CAAC,CAAC,CACxF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGlE,QAAQ,CAAC,CAAEmE,SAAS,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAC5E,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACuE,cAAc,CAAEC,iBAAiB,CAAC,CAAGxE,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACyE,YAAY,CAAEC,eAAe,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC2E,UAAU,CAAEC,aAAa,CAAC,CAAG5E,QAAQ,CAAC,CAC3CmE,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,EAAE,CACXS,YAAY,CAAE,EAAE,CAChBC,iBAAiB,CAAE,IACrB,CAAC,CAAC,CACF,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiF,UAAU,CAAEC,aAAa,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkF,YAAY,CAAGA,CAAA,GAAM,CACzB3B,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAAC2B,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAM1B,MAAM,CAAC4B,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACAlF,SAAS,CAAC,IAAM,CACdqD,UAAU,CAAC,IAAI,CAAC,CAChBpD,KAAK,CACFoF,GAAG,CAAC,oDAAoD,CAAC,CACzDC,IAAI,CAAEC,GAAG,EAAK,CACbC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEF,GAAG,CAACG,IAAI,CAAC,CACjD3C,iBAAiB,CAACwC,GAAG,CAACG,IAAI,CAAC,CAC3BzC,kBAAkB,CAACsC,GAAG,CAACG,IAAI,CAAC,CAE5B;AACA;AACAF,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEF,GAAG,CAACG,IAAI,CAACC,GAAG,CAACC,MAAM,EAAI,KAAAC,qBAAA,CAC1D,MAAO,CACLC,EAAE,CAAEF,MAAM,CAACE,EAAE,CACbC,YAAY,CAAEH,MAAM,CAACG,YAAY,CACjCC,oBAAoB,CAAEJ,MAAM,CAACK,iBAAiB,CAC9CC,sBAAsB,CAAEN,MAAM,CAACM,sBAAsB,CACrDC,WAAW,EAAAN,qBAAA,CAAED,MAAM,CAACM,sBAAsB,UAAAL,qBAAA,iBAA7BA,qBAAA,CAA+BM,WAC9C,CAAC,CACH,CAAC,CAAC,CAAC,CAEH;AACA,GAAI,CAAAC,cAAc,CAAG,EAAE,CAEvB;AACAb,GAAG,CAACG,IAAI,CAACW,OAAO,CAACT,MAAM,EAAI,CACzB,GAAIA,MAAM,CAACM,sBAAsB,EAAIN,MAAM,CAACM,sBAAsB,CAACC,WAAW,CAAE,CAC9EC,cAAc,CAACE,IAAI,CAACV,MAAM,CAACM,sBAAsB,CAACC,WAAW,CAAC,CAChE,CACF,CAAC,CAAC,CAEFX,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAEW,cAAc,CAAC,CAEvE;AACA,KAAM,CAAAG,iBAAiB,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAACJ,cAAc,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,CAC7DjB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEc,iBAAiB,CAAC,CAE5E;AACAxB,gBAAgB,CAACwB,iBAAiB,CAAC,CAEnClD,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACDqD,KAAK,CAAEC,GAAG,EAAK,CACdnB,OAAO,CAACtC,KAAK,CAAC,iCAAiC,CAAEyD,GAAG,CAAC,CACrDxD,QAAQ,CAAC,kCAAkC,CAAC,CAC5CE,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAEN;AACArD,SAAS,CAAC,IAAM,CACd,GAAI,CAAA4G,OAAO,CAAG9D,cAAc,CAE5B;AACA,GAAIY,UAAU,CAAE,CACd,KAAM,CAAAmD,gBAAgB,CAAGnD,UAAU,CAACoD,WAAW,CAAC,CAAC,CACjDF,OAAO,CAAGA,OAAO,CAACG,MAAM,CAACnB,MAAM,OAAAoB,sBAAA,OAC5B,CAAApB,MAAM,CAACG,YAAY,EAAIH,MAAM,CAACG,YAAY,CAACe,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,gBAAgB,CAAC,EACnF,EAAAG,sBAAA,CAAApB,MAAM,CAACM,sBAAsB,UAAAc,sBAAA,iBAA7BA,sBAAA,CAA+Bb,WAAW,GAC1CP,MAAM,CAACM,sBAAsB,CAACC,WAAW,CAACW,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,gBAAgB,CAAE,EACtF,CAAC,CACH,CAEA;AACA,GAAI7C,UAAU,CAACE,SAAS,EAAIF,UAAU,CAACG,OAAO,CAAE,CAC9CyC,OAAO,CAAGA,OAAO,CAACG,MAAM,CAACnB,MAAM,EAAI,CACjC,KAAM,CAAAsB,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACvB,MAAM,CAACwB,YAAY,CAAC,CAChD,KAAM,CAAAlD,SAAS,CAAG,GAAI,CAAAiD,IAAI,CAACnD,UAAU,CAACE,SAAS,CAAC,CAChD,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAgD,IAAI,CAACnD,UAAU,CAACG,OAAO,CAAC,CAC5CA,OAAO,CAACkD,QAAQ,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAE;AAC9B,MAAO,CAAAH,UAAU,EAAIhD,SAAS,EAAIgD,UAAU,EAAI/C,OAAO,CACzD,CAAC,CAAC,CACJ,CAEA;AACA,GAAIP,UAAU,CAACE,GAAG,CAAE,CAClB8C,OAAO,CAAG,CAAC,GAAGA,OAAO,CAAC,CAACH,IAAI,CAAC,CAACa,CAAC,CAAEC,CAAC,GAAK,CACpC,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAElB,GAAI7D,UAAU,CAACE,GAAG,GAAK,cAAc,CAAE,CACrC0D,MAAM,CAAGF,CAAC,CAACvB,YAAY,EAAI,EAAE,CAC7B0B,MAAM,CAAGF,CAAC,CAACxB,YAAY,EAAI,EAAE,CAC/B,CAAC,IAAM,IAAInC,UAAU,CAACE,GAAG,GAAK,aAAa,CAAE,KAAA4D,qBAAA,CAAAC,qBAAA,CAC3CH,MAAM,CAAG,EAAAE,qBAAA,CAAAJ,CAAC,CAACpB,sBAAsB,UAAAwB,qBAAA,iBAAxBA,qBAAA,CAA0BvB,WAAW,GAAI,EAAE,CACpDsB,MAAM,CAAG,EAAAE,qBAAA,CAAAJ,CAAC,CAACrB,sBAAsB,UAAAyB,qBAAA,iBAAxBA,qBAAA,CAA0BxB,WAAW,GAAI,EAAE,CACtD,CAAC,IAAM,IAAIvC,UAAU,CAACE,GAAG,GAAK,cAAc,CAAE,CAC5C0D,MAAM,CAAG,GAAI,CAAAL,IAAI,CAACG,CAAC,CAACF,YAAY,CAAC,CACjCK,MAAM,CAAG,GAAI,CAAAN,IAAI,CAACI,CAAC,CAACH,YAAY,CAAC,CACnC,CAAC,IAAM,IAAIxD,UAAU,CAACE,GAAG,GAAK,gBAAgB,EAAIF,UAAU,CAACE,GAAG,GAAK,YAAY,EAAIF,UAAU,CAACE,GAAG,GAAK,gBAAgB,CAAE,CACxH,KAAM,CAAA8D,WAAW,CAAGC,aAAa,CAACP,CAAC,CAAC,CACpC,KAAM,CAAAQ,WAAW,CAAGD,aAAa,CAACN,CAAC,CAAC,CAEpC,GAAI3D,UAAU,CAACE,GAAG,GAAK,gBAAgB,CAAE,CACvC0D,MAAM,CAAGI,WAAW,CAACG,aAAa,CAClCN,MAAM,CAAGK,WAAW,CAACC,aAAa,CACpC,CAAC,IAAM,IAAInE,UAAU,CAACE,GAAG,GAAK,YAAY,CAAE,CAC1C0D,MAAM,CAAGI,WAAW,CAACI,SAAS,CAC9BP,MAAM,CAAGK,WAAW,CAACE,SAAS,CAChC,CAAC,IAAM,CACLR,MAAM,CAAGI,WAAW,CAACK,aAAa,CAClCR,MAAM,CAAGK,WAAW,CAACG,aAAa,CACpC,CACF,CAEA,GAAIT,MAAM,CAAGC,MAAM,CAAE,CACnB,MAAO,CAAA7D,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,CAAC,CAAC,CAAG,CAAC,CAChD,CACA,GAAIyD,MAAM,CAAGC,MAAM,CAAE,CACnB,MAAO,CAAA7D,UAAU,CAACG,SAAS,GAAK,KAAK,CAAG,CAAC,CAAG,CAAC,CAAC,CAChD,CACA,MAAO,EAAC,CACV,CAAC,CAAC,CACJ,CAEAd,kBAAkB,CAAC2D,OAAO,CAAC,CAC7B,CAAC,CAAE,CAAC9D,cAAc,CAAEY,UAAU,CAAEM,UAAU,CAAEJ,UAAU,CAAC,CAAC,CAExD;AACA,KAAM,CAAAiE,aAAa,CAAIjC,MAAM,EAAK,CAChC,GAAI,CAAAoC,SAAS,CAAG,CAAC,CACjB,GAAI,CAAAD,aAAa,CAAG,CAAC,CACrB,KAAM,CAAAG,UAAU,CAAG,GAAI,CAAA1B,GAAG,CAAC,CAAC,CAE5B,GAAIZ,MAAM,CAACuC,OAAO,CAAE,CAClBvC,MAAM,CAACuC,OAAO,CAAC9B,OAAO,CAAE+B,MAAM,EAAK,CACjCJ,SAAS,EAAIK,UAAU,CAACD,MAAM,CAACE,UAAU,EAAI,CAAC,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CACZ,CAACH,MAAM,CAACI,EAAE,EAAI,CAAC,GACdJ,MAAM,CAACK,CAAC,EAAI,CAAC,CAAC,EACdL,MAAM,CAACM,CAAC,EAAI,CAAC,CAAC,EACdN,MAAM,CAACO,CAAC,EAAI,CAAC,CAAC,EACdP,MAAM,CAACQ,EAAE,EAAI,CAAC,CAAC,CAClBb,aAAa,EAAIQ,QAAQ,CACzB;AACA,KAAM,CAAAM,SAAS,CAAGT,MAAM,CAACU,mBAAmB,CACxCV,MAAM,CAACU,mBAAmB,CAAChD,EAAE,CAC7BsC,MAAM,CAACW,cAAc,CACzBb,UAAU,CAACc,GAAG,CAACH,SAAS,CAAC,CAC3B,CAAC,CAAC,CACJ,CAEA,MAAO,CAAEb,SAAS,CAAED,aAAa,CAAEE,aAAa,CAAEC,UAAU,CAACe,IAAK,CAAC,CACrE,CAAC,CAID;AACA,KAAM,CAAAC,WAAW,CAAIpF,GAAG,EAAK,CAC3B,GAAI,CAAAC,SAAS,CAAG,KAAK,CACrB,GAAIH,UAAU,CAACE,GAAG,GAAKA,GAAG,EAAIF,UAAU,CAACG,SAAS,GAAK,KAAK,CAAE,CAC5DA,SAAS,CAAG,MAAM,CACpB,CACAF,aAAa,CAAC,CAAEC,GAAG,CAAEC,SAAU,CAAC,CAAC,CACnC,CAAC,CAED;AACA,KAAM,CAAAoF,WAAW,CAAIC,UAAU,EAAK,CAClC,GAAIxF,UAAU,CAACE,GAAG,GAAKsF,UAAU,CAAE,CACjC,mBAAOpH,IAAA,CAACf,MAAM,EAACoI,SAAS,CAAC,iBAAiB,CAAE,CAAC,CAC/C,CACA,MAAO,CAAAzF,UAAU,CAACG,SAAS,GAAK,KAAK,cACnC/B,IAAA,CAACd,QAAQ,EAACmI,SAAS,CAAC,mBAAmB,CAAE,CAAC,cAC1CrH,IAAA,CAACb,UAAU,EAACkI,SAAS,CAAC,mBAAmB,CAAE,CAAC,CAChD,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB3F,aAAa,CAAC,EAAE,CAAC,CACjBM,aAAa,CAAC,CAAEC,SAAS,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAC7CN,aAAa,CAAC,CAAEC,GAAG,CAAE,cAAc,CAAEC,SAAS,CAAE,MAAO,CAAC,CAAC,CAC3D,CAAC,CAED;AAEA;AACA,KAAM,CAACwF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzJ,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACA,KAAM,CAAA0J,iBAAiB,CAAG,KAAAA,CAAOC,CAAC,CAAE9D,MAAM,GAAK,CAC7C8D,CAAC,CAACC,eAAe,CAAC,CAAC,CAAE;AACrBpF,iBAAiB,CAACqB,MAAM,CAAC,CACzBvC,UAAU,CAAC,IAAI,CAAC,CAChBF,QAAQ,CAAC,EAAE,CAAC,CAAE;AAEd,GAAI,CACF;AACA,KAAM,CAAAyG,QAAQ,CAAG,KAAM,CAAA3J,KAAK,CAACoF,GAAG,CAAC,qDAAqDO,MAAM,CAACE,EAAE,wBAAwB,CAAC,CACxH0D,mBAAmB,CAACI,QAAQ,CAAClE,IAAI,CAACmE,kBAAkB,CAAC,CACrDxF,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,MAAOnB,KAAK,CAAE,CACdsC,OAAO,CAACtC,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD;AACAsG,mBAAmB,CAAC,IAAI,CAAC,CACzBnF,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,OAAS,CACRhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyG,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAACxF,cAAc,CAAE,OAErB;AACA,GAAIiF,gBAAgB,CAAE,CACpBpG,QAAQ,CAAC,6EAA6E,CAAC,CACvFkB,kBAAkB,CAAC,KAAK,CAAC,CACzBE,iBAAiB,CAAC,IAAI,CAAC,CACvB,OACF,CAEAlB,UAAU,CAAC,IAAI,CAAC,CAChBF,QAAQ,CAAC,EAAE,CAAC,CAAE;AAEd;AACAqC,OAAO,CAACC,GAAG,CAAC,gDAAgDnB,cAAc,CAACwB,EAAE,EAAE,CAAC,CAEhF7F,KAAK,CACF8J,MAAM,CAAC,qDAAqDzF,cAAc,CAACwB,EAAE,GAAG,CAAC,CACjFR,IAAI,CAAEsE,QAAQ,EAAK,CAClBpE,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEmE,QAAQ,CAAC,CACzC;AACA,KAAM,CAAAI,cAAc,CAAGlH,cAAc,CAACiE,MAAM,CAACnB,MAAM,EAAIA,MAAM,CAACE,EAAE,GAAKxB,cAAc,CAACwB,EAAE,CAAC,CACvF/C,iBAAiB,CAACiH,cAAc,CAAC,CACjC/G,kBAAkB,CAAC+G,cAAc,CAAC,CAClC3F,kBAAkB,CAAC,KAAK,CAAC,CACzBE,iBAAiB,CAAC,IAAI,CAAC,CACvBlB,UAAU,CAAC,KAAK,CAAC,CAEjB;AACAF,QAAQ,CAAC,EAAE,CAAC,CAAE;AACd8G,KAAK,CAAC,sCAAsC,CAAC,CAC/C,CAAC,CAAC,CACDvD,KAAK,CAAEC,GAAG,EAAK,CACdnB,OAAO,CAACtC,KAAK,CAAC,gCAAgC,CAAEyD,GAAG,CAAC,CACpD,GAAI,CAAAuD,YAAY,CAAG,kCAAkC,CAErD;AACA,GAAIvD,GAAG,CAACiD,QAAQ,CAAE,CAChBpE,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEkB,GAAG,CAACiD,QAAQ,CAAC,CAC5C,GAAIjD,GAAG,CAACiD,QAAQ,CAAClE,IAAI,EAAI,MAAO,CAAAiB,GAAG,CAACiD,QAAQ,CAAClE,IAAI,GAAK,QAAQ,CAAE,CAC9DwE,YAAY,CAAGC,IAAI,CAACC,SAAS,CAACzD,GAAG,CAACiD,QAAQ,CAAClE,IAAI,CAAC,CAClD,CAAC,IAAM,IAAIiB,GAAG,CAACiD,QAAQ,CAAClE,IAAI,CAAE,CAC5BwE,YAAY,CAAGvD,GAAG,CAACiD,QAAQ,CAAClE,IAAI,CAClC,CAAC,IAAM,IAAIiB,GAAG,CAACiD,QAAQ,CAACS,MAAM,GAAK,GAAG,CAAE,CACtCH,YAAY,CAAG,kDAAkD,CACnE,CAAC,IAAM,IAAIvD,GAAG,CAACiD,QAAQ,CAACS,MAAM,GAAK,GAAG,EAAI1D,GAAG,CAACiD,QAAQ,CAACS,MAAM,GAAK,GAAG,CAAE,CACrEH,YAAY,CAAG,0EAA0E,CAC3F,CACF,CAEA/G,QAAQ,CAAC+G,YAAY,CAAC,CACtB7F,kBAAkB,CAAC,KAAK,CAAC,CACzBE,iBAAiB,CAAC,IAAI,CAAC,CACvBlB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAED;AACA,KAAM,CAAAiH,YAAY,CAAGA,CAAA,GAAM,CACzB;AACA3F,aAAa,CAAC,CACZ,GAAGD,UAAU,CACbR,SAAS,CAAEF,UAAU,CAACE,SAAS,CAC/BC,OAAO,CAAEH,UAAU,CAACG,OACtB,CAAC,CAAC,CACFqB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEX,aAAa,CAAC,CAAE;AAClEL,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAA8F,WAAW,CAAGA,CAAA,GAAM,CACxBtF,aAAa,CAAC,IAAI,CAAC,CAEnB;AACA,GAAI,CAAAuF,eAAe,CAAG1H,cAAc,CAEpC;AACA,GAAI4B,UAAU,CAACR,SAAS,EAAIQ,UAAU,CAACP,OAAO,CAAE,CAC9CqG,eAAe,CAAGA,eAAe,CAACzD,MAAM,CAACnB,MAAM,EAAI,CACjD,KAAM,CAAAsB,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACvB,MAAM,CAACwB,YAAY,CAAC,CAChD,KAAM,CAAAlD,SAAS,CAAG,GAAI,CAAAiD,IAAI,CAACzC,UAAU,CAACR,SAAS,CAAC,CAChD,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAgD,IAAI,CAACzC,UAAU,CAACP,OAAO,CAAC,CAC5CA,OAAO,CAACkD,QAAQ,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAE;AAC9B,MAAO,CAAAH,UAAU,EAAIhD,SAAS,EAAIgD,UAAU,EAAI/C,OAAO,CACzD,CAAC,CAAC,CACJ,CAEA;AACA,GAAIO,UAAU,CAACE,YAAY,CAAE,CAC3B4F,eAAe,CAAGA,eAAe,CAACzD,MAAM,CAACnB,MAAM,OAAA6E,sBAAA,OAC7C,EAAAA,sBAAA,CAAA7E,MAAM,CAACM,sBAAsB,UAAAuE,sBAAA,iBAA7BA,sBAAA,CAA+BtE,WAAW,IAAKzB,UAAU,CAACE,YAAY,EACxE,CAAC,CACH,CAEA;AACA,GAAI,CAACF,UAAU,CAACG,iBAAiB,CAAE,CACjC2F,eAAe,CAAGxH,eAAe,CACnC,CAEA;AACA,GAAI,CAAA0H,UAAU,CAAG,0GAA0G,CAE3HF,eAAe,CAACnE,OAAO,CAACT,MAAM,EAAI,KAAA+E,sBAAA,CAChC,KAAM,CAAE3C,SAAS,CAAEC,aAAc,CAAC,CAAGJ,aAAa,CAACjC,MAAM,CAAC,CAE1D;AACA,GAAI,CAAAgF,WAAW,CAAG,CAAC,CAEnB;AACA,KAAM,CAAAC,UAAU,CAAG,EAAE,CAErB,GAAIjF,MAAM,CAACuC,OAAO,CAAE,CAClBvC,MAAM,CAACuC,OAAO,CAAC9B,OAAO,CAAC+B,MAAM,EAAI,CAC/B,GAAIA,MAAM,CAACU,mBAAmB,CAAE,CAC9B,KAAM,CAAAgC,SAAS,CAAGzC,UAAU,CAACD,MAAM,CAACE,UAAU,EAAI,CAAC,CAAC,CACpD,KAAM,CAAAyC,YAAY,CAAG1C,UAAU,CAACD,MAAM,CAACU,mBAAmB,CAACkC,cAAc,EAAI,CAAC,CAAC,CAC/EJ,WAAW,EAAIE,SAAS,CAAGC,YAAY,CAEvC;AACA,GAAI3C,MAAM,CAACU,mBAAmB,CAACmC,KAAK,CAAE,CACpCJ,UAAU,CAACvE,IAAI,CAAC8B,MAAM,CAACU,mBAAmB,CAACmC,KAAK,CAAC,CACnD,CACF,CACF,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,WAAW,CAAGtF,MAAM,CAACG,YAAY,EAAI,KAAK,CAChD,KAAM,CAAAoF,WAAW,CAAGvF,MAAM,CAACwB,YAAY,CACvC,KAAM,CAAAgE,UAAU,CAAG,EAAAT,sBAAA,CAAA/E,MAAM,CAACM,sBAAsB,UAAAyE,sBAAA,iBAA7BA,sBAAA,CAA+BxE,WAAW,GAAI,KAAK,CACtE,KAAM,CAAAkF,cAAc,CAAGpD,aAAa,CACpC,KAAM,CAAAqD,aAAa,CAAGT,UAAU,CAACU,IAAI,CAAC,KAAK,CAAC,CAE5C;AACA,KAAM,CAAAC,kBAAkB,CAAGN,WAAW,CAACjE,QAAQ,CAAC,GAAG,CAAC,CAAG,IAAIiE,WAAW,GAAG,CAAGA,WAAW,CACvF,KAAM,CAAAO,iBAAiB,CAAGL,UAAU,CAACnE,QAAQ,CAAC,GAAG,CAAC,CAAG,IAAImE,UAAU,GAAG,CAAGA,UAAU,CACnF,KAAM,CAAAM,iBAAiB,CAAGJ,aAAa,CAACrE,QAAQ,CAAC,GAAG,CAAC,CAAG,IAAIqE,aAAa,GAAG,CAAGA,aAAa,CAE5F;AACAZ,UAAU,EAAI,GAAGc,kBAAkB,IAAIL,WAAW,IAAIM,iBAAiB,IAAIzD,SAAS,CAAC2D,OAAO,CAAC,CAAC,CAAC,IAAIf,WAAW,CAACe,OAAO,CAAC,CAAC,CAAC,IAAIN,cAAc,IAAIK,iBAAiB,IAAI,CACtK,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACnB,UAAU,CAAC,CAAE,CAAEoB,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CACrC,KAAM,CAAAM,IAAI,CAAG3J,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxC0J,IAAI,CAACC,YAAY,CAAC,MAAM,CAAEJ,GAAG,CAAC,CAC9BG,IAAI,CAACC,YAAY,CAAC,UAAU,CAAE,0BAA0B,GAAI,CAAAhF,IAAI,CAAC,CAAC,CAACiF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,MAAM,CAAC,CACpG9J,QAAQ,CAAC+J,IAAI,CAAC3J,WAAW,CAACuJ,IAAI,CAAC,CAE/B;AACAA,IAAI,CAACK,KAAK,CAAC,CAAC,CACZhK,QAAQ,CAAC+J,IAAI,CAACE,WAAW,CAACN,IAAI,CAAC,CAE/BjH,aAAa,CAAC,KAAK,CAAC,CACpBR,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED,mBACEvC,KAAA,CAAAE,SAAA,EAAAqK,QAAA,eACEzK,IAAA,CAAC7B,eAAe,GAAE,CAAC,cACnB+B,KAAA,CAAC9B,SAAS,EAACsM,KAAK,MACdpK,KAAK,CAAE,CACLqK,UAAU,CAAErJ,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5CsJ,KAAK,CAAE,eAAetJ,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDuJ,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAL,QAAA,eAEFvK,KAAA,QAAKmH,SAAS,CAAC,wDAAwD,CAAAoD,QAAA,eACrEvK,KAAA,OAAImH,SAAS,CAAC,MAAM,CAAAoD,QAAA,eAClBzK,IAAA,CAACX,KAAK,EAACgI,SAAS,CAAC,mBAAmB,CAAE,CAAC,kBAEzC,EAAI,CAAC,cACLnH,KAAA,QAAKmH,SAAS,CAAC,cAAc,CAAAoD,QAAA,eAC3BvK,KAAA,CAACzB,MAAM,EACLsM,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAE1C,YAAa,CAAAmC,QAAA,eAEtBzK,IAAA,CAACJ,SAAS,EAACyH,SAAS,CAAC,MAAM,CAAE,CAAC,cAChC,EAAQ,CAAC,cACTnH,KAAA,CAACzB,MAAM,EACLsM,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,CAAC,aAAa,CAAE,CAAA4J,QAAA,eAEvCzK,IAAA,CAACR,MAAM,EAAC6H,SAAS,CAAC,MAAM,CAAE,CAAC,mBAC7B,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAELnG,KAAK,eAAIlB,IAAA,CAAClB,KAAK,EAACiM,OAAO,CAAC,QAAQ,CAAC1D,SAAS,CAAC,aAAa,CAAAoD,QAAA,CAAEvJ,KAAK,CAAQ,CAAC,cAGzElB,IAAA,CAACzB,IAAI,EAAC8I,SAAS,CAAC,gBAAgB,CAAAoD,QAAA,cAC9BzK,IAAA,CAACzB,IAAI,CAAC0M,IAAI,EAAAR,QAAA,cACRvK,KAAA,CAAC7B,GAAG,EAAAoM,QAAA,eACFzK,IAAA,CAAC1B,GAAG,EAAC4M,EAAE,CAAE,CAAE,CAAAT,QAAA,cACTvK,KAAA,CAACvB,UAAU,EAAC0I,SAAS,CAAC,cAAc,CAAAoD,QAAA,eAClCzK,IAAA,CAACrB,UAAU,CAACwM,IAAI,EAAAV,QAAA,cACdzK,IAAA,CAAChB,QAAQ,GAAE,CAAC,CACG,CAAC,cAClBgB,IAAA,CAACtB,IAAI,CAAC0M,OAAO,EACXC,WAAW,CAAC,qCAAqC,CACjDC,KAAK,CAAE5J,UAAW,CAClB6J,QAAQ,CAAG7D,CAAC,EAAK/F,aAAa,CAAC+F,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAE,CAChD,CAAC,EACQ,CAAC,CACV,CAAC,cACNtL,IAAA,CAAC1B,GAAG,EAAC4M,EAAE,CAAE,CAAE,CAAAT,QAAA,cACTvK,KAAA,CAAC7B,GAAG,EAAAoM,QAAA,eACFzK,IAAA,CAAC1B,GAAG,EAAC4M,EAAE,CAAE,CAAE,CAAAT,QAAA,cACTzK,IAAA,CAACtB,IAAI,CAAC+M,KAAK,EAACpE,SAAS,CAAC,cAAc,CAAAoD,QAAA,cAClCzK,IAAA,CAACtB,IAAI,CAAC0M,OAAO,EACXtB,IAAI,CAAC,MAAM,CACXuB,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEtJ,UAAU,CAACE,SAAU,CAC5BqJ,QAAQ,CAAG7D,CAAC,EAAKzF,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEE,SAAS,CAAEwF,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAC,CAAE,CAC5E,CAAC,CACQ,CAAC,CACV,CAAC,cACNtL,IAAA,CAAC1B,GAAG,EAAC4M,EAAE,CAAE,CAAE,CAAAT,QAAA,cACTzK,IAAA,CAACtB,IAAI,CAAC+M,KAAK,EAACpE,SAAS,CAAC,cAAc,CAAAoD,QAAA,cAClCzK,IAAA,CAACtB,IAAI,CAAC0M,OAAO,EACXtB,IAAI,CAAC,MAAM,CACXuB,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEtJ,UAAU,CAACG,OAAQ,CAC1BoJ,QAAQ,CAAG7D,CAAC,EAAKzF,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEG,OAAO,CAAEuF,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAC,CAAE,CAC1E,CAAC,CACQ,CAAC,CACV,CAAC,cACNpL,KAAA,CAAC5B,GAAG,EAAC4M,EAAE,CAAE,CAAE,CAAC7D,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,eAC/CzK,IAAA,CAACV,aAAa,EAAC+H,SAAS,CAAC,mBAAmB,CAAE,CAAC,cAC/CrH,IAAA,SAAMqH,SAAS,CAAC,oBAAoB,CAAAoD,QAAA,CAAC,YAAU,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,CACH,CAAC,cACNzK,IAAA,CAAC1B,GAAG,EAAC4M,EAAE,CAAE,CAAE,CAAC7D,SAAS,CAAC,4BAA4B,CAAAoD,QAAA,cAChDvK,KAAA,CAACzB,MAAM,EACLsM,OAAO,CAAC,mBAAmB,CAC3BC,OAAO,CAAE1D,YAAa,CACtBD,SAAS,CAAC,OAAO,CAAAoD,QAAA,eAEjBzK,IAAA,CAACT,QAAQ,EAAC8H,SAAS,CAAC,MAAM,CAAE,CAAC,iBAC/B,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACG,CAAC,CACR,CAAC,cAGPnH,KAAA,QAAKmH,SAAS,CAAC,wDAAwD,CAAAoD,QAAA,eACrEzK,IAAA,QAAAyK,QAAA,cACEvK,KAAA,CAACtB,KAAK,EAAC8M,EAAE,CAAC,MAAM,CAACrE,SAAS,CAAC,KAAK,CAAAoD,QAAA,EAC7BzJ,eAAe,CAAC2K,MAAM,CAAC,GAAC,CAAC3K,eAAe,CAAC2K,MAAM,GAAK,CAAC,CAAG,QAAQ,CAAG,SAAS,CAAC,QAChF,EAAO,CAAC,CACL,CAAC,cACN3L,IAAA,QAAAyK,QAAA,cACEvK,KAAA,UAAOmH,SAAS,CAAC,YAAY,CAAAoD,QAAA,eAC3BzK,IAAA,CAACP,YAAY,EAAC4H,SAAS,CAAC,MAAM,CAAE,CAAC,sCACnC,EAAO,CAAC,CACL,CAAC,EACH,CAAC,cAGNrH,IAAA,CAACzB,IAAI,EAAC8I,SAAS,CAAC,WAAW,CAAAoD,QAAA,cACzBzK,IAAA,CAACzB,IAAI,CAAC0M,IAAI,EAAC5D,SAAS,CAAC,KAAK,CAAAoD,QAAA,CACvBrJ,OAAO,cACNlB,KAAA,QAAKmH,SAAS,CAAC,kBAAkB,CAAAoD,QAAA,eAC/BzK,IAAA,CAACnB,OAAO,EAAC+M,SAAS,CAAC,QAAQ,CAACb,OAAO,CAAC,SAAS,CAAE,CAAC,cAChD/K,IAAA,MAAGqH,SAAS,CAAC,MAAM,CAAAoD,QAAA,CAAC,4BAA0B,CAAG,CAAC,EAC/C,CAAC,CACJzJ,eAAe,CAAC2K,MAAM,GAAK,CAAC,cAC9BzL,KAAA,QAAKmH,SAAS,CAAC,kBAAkB,CAAAoD,QAAA,eAC/BzK,IAAA,CAACX,KAAK,EAACgI,SAAS,CAAC,iBAAiB,CAAC/G,KAAK,CAAE,CAAEuL,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAC,cAClE7L,IAAA,OAAAyK,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCzK,IAAA,MAAGqH,SAAS,CAAC,YAAY,CAAAoD,QAAA,CAAC,8CAA4C,CAAG,CAAC,EACvE,CAAC,cAENzK,IAAA,QAAKqH,SAAS,CAAC,kBAAkB,CAAAoD,QAAA,cAC/BvK,KAAA,CAAC1B,KAAK,EAACsN,KAAK,MAACzE,SAAS,CAAC,MAAM,CAAAoD,QAAA,eAC3BzK,IAAA,UAAOqH,SAAS,CAAC,UAAU,CAAAoD,QAAA,cACzBvK,KAAA,OAAAuK,QAAA,eACEzK,IAAA,OAAIqH,SAAS,CAAC,gBAAgB,CAAC2D,OAAO,CAAEA,CAAA,GAAM9D,WAAW,CAAC,cAAc,CAAE,CAAAuD,QAAA,cACxEvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,EAAC,eAC5B,CAACtD,WAAW,CAAC,cAAc,CAAC,EACtC,CAAC,CACJ,CAAC,cACLnH,IAAA,OAAIqH,SAAS,CAAC,gBAAgB,CAAC2D,OAAO,CAAEA,CAAA,GAAM9D,WAAW,CAAC,aAAa,CAAE,CAAAuD,QAAA,cACvEvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,EAAC,cAC7B,CAACtD,WAAW,CAAC,aAAa,CAAC,EACpC,CAAC,CACJ,CAAC,cACLnH,IAAA,OAAIqH,SAAS,CAAC,gBAAgB,CAAC2D,OAAO,CAAEA,CAAA,GAAM9D,WAAW,CAAC,cAAc,CAAE,CAAAuD,QAAA,cACxEvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,EAAC,eAC5B,CAACtD,WAAW,CAAC,cAAc,CAAC,EACtC,CAAC,CACJ,CAAC,cACLnH,IAAA,OAAIqH,SAAS,CAAC,gBAAgB,CAAC2D,OAAO,CAAEA,CAAA,GAAM9D,WAAW,CAAC,gBAAgB,CAAE,CAAAuD,QAAA,cAC1EvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,EAAC,iBAC1B,CAACtD,WAAW,CAAC,gBAAgB,CAAC,EAC1C,CAAC,CACJ,CAAC,cACLnH,IAAA,OAAIqH,SAAS,CAAC,gBAAgB,CAAC2D,OAAO,CAAEA,CAAA,GAAM9D,WAAW,CAAC,YAAY,CAAE,CAAAuD,QAAA,cACtEvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,EAAC,aAC9B,CAACtD,WAAW,CAAC,YAAY,CAAC,EAClC,CAAC,CACJ,CAAC,cACLnH,IAAA,OAAIqH,SAAS,CAAC,gBAAgB,CAAC2D,OAAO,CAAEA,CAAA,GAAM9D,WAAW,CAAC,gBAAgB,CAAE,CAAAuD,QAAA,cAC1EvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,EAAC,gBAC3B,CAACtD,WAAW,CAAC,gBAAgB,CAAC,EACzC,CAAC,CACJ,CAAC,cACLnH,IAAA,OAAAyK,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACRzK,IAAA,UAAAyK,QAAA,CACGzJ,eAAe,CAAC2C,GAAG,CAAEC,MAAM,EAAK,KAAAmI,eAAA,CAC/B,KAAM,CAAE/F,SAAS,CAAED,aAAa,CAAEE,aAAc,CAAC,CAAGJ,aAAa,CAACjC,MAAM,CAAC,CACzE;AACA,KAAM,CAAAsF,WAAW,CAAGtF,MAAM,CAACG,YAAY,EAAI,KAAK,CAChD;AACA,KAAM,CAAAiI,WAAW,CAAG,GAAI,CAAAxH,GAAG,CAAC,CAAC,CAC7B,CAAAuH,eAAA,CAAAnI,MAAM,CAACuC,OAAO,UAAA4F,eAAA,iBAAdA,eAAA,CAAgB1H,OAAO,CAAC+B,MAAM,EAAI,KAAA6F,qBAAA,CAAAC,sBAAA,CAChC,IAAAD,qBAAA,CAAI7F,MAAM,CAACU,mBAAmB,UAAAmF,qBAAA,YAAAC,sBAAA,CAA1BD,qBAAA,CAA4B/H,sBAAsB,UAAAgI,sBAAA,WAAlDA,sBAAA,CAAoD/H,WAAW,CAAE,CACnE6H,WAAW,CAAChF,GAAG,CAACZ,MAAM,CAACU,mBAAmB,CAAC5C,sBAAsB,CAACC,WAAW,CAAC,CAChF,CACF,CAAC,CAAC,CACF,KAAM,CAAAiF,UAAU,CAAG+C,KAAK,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACzC,IAAI,CAAC,IAAI,CAAC,EAAI,KAAK,CAE9D,mBACEvJ,IAAA,CAAClC,KAAK,CAACqC,QAAQ,EAAAsK,QAAA,cACbvK,KAAA,OACEmH,SAAS,CAAC,WAAW,CACrB/G,KAAK,CAAE,CAAE+L,MAAM,CAAE,SAAU,CAAE,CAC7BrB,OAAO,CAAGtD,CAAC,EAAK,CACd;AACA,GAAIA,CAAC,CAAC8D,MAAM,CAACc,OAAO,CAAC,iBAAiB,CAAC,CAAE,OACzCzL,QAAQ,CAAC,mBAAmB+C,MAAM,CAACE,EAAE,EAAE,CAAC,CAC1C,CAAE,CAAA2G,QAAA,eAEFzK,IAAA,OAAAyK,QAAA,CAAKvB,WAAW,CAAK,CAAC,cACtBlJ,IAAA,OAAAyK,QAAA,cACEvK,KAAA,QAAKmH,SAAS,CAAC,2BAA2B,CAAAoD,QAAA,eACxCzK,IAAA,CAACZ,QAAQ,EAACiI,SAAS,CAAC,mBAAmB,CAAE,CAAC,CACzC+B,UAAU,EACR,CAAC,CACJ,CAAC,cACLpJ,IAAA,OAAAyK,QAAA,cACEvK,KAAA,CAACtB,KAAK,EAAC8M,EAAE,CAAC,OAAO,CAACa,IAAI,CAAC,MAAM,CAAClF,SAAS,CAAC,KAAK,CAAAoD,QAAA,eAC3CzK,IAAA,CAACV,aAAa,EAAC+H,SAAS,CAAC,MAAM,CAAE,CAAC,CACjCzD,MAAM,CAACwB,YAAY,EACf,CAAC,CACN,CAAC,cACLpF,IAAA,OAAAyK,QAAA,cACEzK,IAAA,CAACpB,KAAK,EAAC8M,EAAE,CAAC,MAAM,CAACrE,SAAS,CAAC,KAAK,CAAAoD,QAAA,CAC7B1E,aAAa,CACT,CAAC,CACN,CAAC,cACL/F,IAAA,OAAAyK,QAAA,cACEvK,KAAA,CAACtB,KAAK,EAAC8M,EAAE,CAAC,SAAS,CAACa,IAAI,CAAC,MAAM,CAAClF,SAAS,CAAC,KAAK,CAAAoD,QAAA,EAC5CzE,SAAS,CAAC,QACb,EAAO,CAAC,CACN,CAAC,cACLhG,IAAA,OAAAyK,QAAA,cACEzK,IAAA,CAACpB,KAAK,EAAC8M,EAAE,CAAC,WAAW,CAACrE,SAAS,CAAC,KAAK,CAAAoD,QAAA,CAClCxE,aAAa,CACT,CAAC,CACN,CAAC,cACLjG,IAAA,OAAAyK,QAAA,cACEvK,KAAA,QAAKmH,SAAS,CAAC,6BAA6B,CAAAoD,QAAA,eAC1CvK,KAAA,CAACzB,MAAM,EACLsM,OAAO,CAAC,cAAc,CACtB9D,IAAI,CAAC,IAAI,CACT+D,OAAO,CAAGtD,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAE;AACrB9G,QAAQ,CAAC,mBAAmB+C,MAAM,CAACE,EAAE,EAAE,CAAC,CAC1C,CAAE,CAAA2G,QAAA,eAEFzK,IAAA,CAACP,YAAY,EAAC4H,SAAS,CAAC,MAAM,CAAE,CAAC,WACnC,EAAQ,CAAC,cAETnH,KAAA,CAACzB,MAAM,EACLsM,OAAO,CAAC,iBAAiB,CACzB9D,IAAI,CAAC,IAAI,CACT+D,OAAO,CAAGtD,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAE;AACrB9G,QAAQ,CAAC,iBAAiB+C,MAAM,CAACE,EAAE,EAAE,CAAC,CACxC,CAAE,CAAA2G,QAAA,eAEFzK,IAAA,CAACF,MAAM,EAACuH,SAAS,CAAC,MAAM,CAAE,CAAC,QAC7B,EAAQ,CAAC,cAETnH,KAAA,CAACzB,MAAM,EACLsM,OAAO,CAAC,gBAAgB,CACxB9D,IAAI,CAAC,IAAI,CACT+D,OAAO,CAAGtD,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAE9D,MAAM,CAAE,CAAA6G,QAAA,eAE7CzK,IAAA,CAACN,OAAO,EAAC2H,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CAAC,EACN,CAAC,CACJ,CAAC,EACH,CAAC,EAvEczD,MAAM,CAACE,EAwEZ,CAAC,CAErB,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,CACQ,CAAC,CACR,CAAC,EACE,CAAC,cAGZ5D,KAAA,CAACnB,KAAK,EAACyN,IAAI,CAAEpK,eAAgB,CAACqK,MAAM,CAAEA,CAAA,GAAMpK,kBAAkB,CAAC,KAAK,CAAE,CAAAoI,QAAA,eACpEzK,IAAA,CAACjB,KAAK,CAAC2N,MAAM,EAACC,WAAW,MAAAlC,QAAA,cACvBzK,IAAA,CAACjB,KAAK,CAAC6N,KAAK,EAAAnC,QAAA,CAAC,kBAAgB,CAAa,CAAC,CAC/B,CAAC,cACfzK,IAAA,CAACjB,KAAK,CAACkM,IAAI,EAAAR,QAAA,CACRlD,gBAAgB,cACfrH,KAAA,QAAAuK,QAAA,eACEvK,KAAA,CAACpB,KAAK,EAACiM,OAAO,CAAC,QAAQ,CAAAN,QAAA,eACrBzK,IAAA,WAAAyK,QAAA,CAAQ,gBAAc,CAAQ,CAAC,sDAC/B,cAAAzK,IAAA,QAAKqH,SAAS,CAAC,MAAM,CAAAoD,QAAA,cACnBzK,IAAA,UAAAyK,QAAA,CAAO,mJAGP,CAAO,CAAC,CACL,CAAC,EACD,CAAC,CACPnI,cAAc,eACbpC,KAAA,MAAGmH,SAAS,CAAC,MAAM,CAAAoD,QAAA,EAAC,qBACC,cAAAzK,IAAA,WAAAyK,QAAA,CAASnI,cAAc,CAACyB,YAAY,EAAI,iBAAiB,CAAS,CAAC,QAAK,CAAC,GAAG,cAC/F/D,IAAA,WAAAyK,QAAA,CAASnI,cAAc,CAAC8C,YAAY,CAAS,CAAC,sBAChD,EAAG,CACJ,EACE,CAAC,cAENlF,KAAA,QAAAuK,QAAA,EACGnI,cAAc,eACbpC,KAAA,MAAAuK,QAAA,EAAG,wDACqD,CAAC,GAAG,cAC1DzK,IAAA,WAAAyK,QAAA,CAASnI,cAAc,CAACyB,YAAY,EAAI,iBAAiB,CAAS,CAAC,QAAK,CAAC,GAAG,cAC5E/D,IAAA,WAAAyK,QAAA,CAASnI,cAAc,CAAC8C,YAAY,CAAS,CAAC,IAChD,EAAG,CACJ,cACDlF,KAAA,CAACpB,KAAK,EAACiM,OAAO,CAAC,SAAS,CAAAN,QAAA,eACtBzK,IAAA,WAAAyK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,+EAC3B,EAAO,CAAC,EACL,CACN,CACS,CAAC,cACbvK,KAAA,CAACnB,KAAK,CAAC8N,MAAM,EAAApC,QAAA,eACXzK,IAAA,CAACvB,MAAM,EAACsM,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAM3I,kBAAkB,CAAC,KAAK,CAAE,CAAAoI,QAAA,CAClElD,gBAAgB,CAAG,OAAO,CAAG,QAAQ,CAChC,CAAC,CACR,CAACA,gBAAgB,eAChBvH,IAAA,CAACvB,MAAM,EACLsM,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAElD,mBAAoB,CAC7BgF,QAAQ,CAAE1L,OAAQ,CAAAqJ,QAAA,CAEjBrJ,OAAO,cACNlB,KAAA,CAAAE,SAAA,EAAAqK,QAAA,eACEzK,IAAA,CAACnB,OAAO,EACNkO,EAAE,CAAC,MAAM,CACTnB,SAAS,CAAC,QAAQ,CAClB3E,IAAI,CAAC,IAAI,CACT+F,IAAI,CAAC,QAAQ,CACb,cAAY,MAAM,CAClB3F,SAAS,CAAC,MAAM,CACjB,CAAC,cAEJ,EAAE,CAAC,CAEH,QACD,CACK,CACT,EACW,CAAC,EACV,CAAC,cAGRnH,KAAA,CAACnB,KAAK,EAACyN,IAAI,CAAEhK,YAAa,CAACiK,MAAM,CAAEA,CAAA,GAAMhK,eAAe,CAAC,KAAK,CAAE,CAAAgI,QAAA,eAC9DzK,IAAA,CAACjB,KAAK,CAAC2N,MAAM,EAACC,WAAW,MAAAlC,QAAA,cACvBvK,KAAA,CAACnB,KAAK,CAAC6N,KAAK,EAAAnC,QAAA,eACVzK,IAAA,CAACJ,SAAS,EAACyH,SAAS,CAAC,mBAAmB,CAAE,CAAC,gCAE7C,EAAa,CAAC,CACF,CAAC,cACfnH,KAAA,CAACnB,KAAK,CAACkM,IAAI,EAAAR,QAAA,eACTzK,IAAA,MAAGqH,SAAS,CAAC,iBAAiB,CAAAoD,QAAA,CAAC,4LAG/B,CAAG,CAAC,cAEJvK,KAAA,CAACxB,IAAI,EAAA+L,QAAA,eACHvK,KAAA,CAACxB,IAAI,CAAC+M,KAAK,EAACpE,SAAS,CAAC,MAAM,CAAAoD,QAAA,eAC1BzK,IAAA,CAACtB,IAAI,CAACuO,KAAK,EAAAxC,QAAA,CAAC,YAAU,CAAY,CAAC,cACnCvK,KAAA,CAAC7B,GAAG,EAAAoM,QAAA,eACFzK,IAAA,CAAC1B,GAAG,EAAAmM,QAAA,cACFzK,IAAA,CAACtB,IAAI,CAAC0M,OAAO,EACXtB,IAAI,CAAC,MAAM,CACXuB,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAE5I,UAAU,CAACR,SAAU,CAC5BqJ,QAAQ,CAAG7D,CAAC,EAAK/E,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAER,SAAS,CAAEwF,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAC,CAAE,CAC5E,CAAC,CACC,CAAC,cACNtL,IAAA,CAAC1B,GAAG,EAAAmM,QAAA,cACFzK,IAAA,CAACtB,IAAI,CAAC0M,OAAO,EACXtB,IAAI,CAAC,MAAM,CACXuB,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAE5I,UAAU,CAACP,OAAQ,CAC1BoJ,QAAQ,CAAG7D,CAAC,EAAK/E,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEP,OAAO,CAAEuF,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAC,CAAE,CAC1E,CAAC,CACC,CAAC,EACH,CAAC,cACNtL,IAAA,CAACtB,IAAI,CAACyM,IAAI,EAAC9D,SAAS,CAAC,YAAY,CAAAoD,QAAA,CAAC,wCAElC,CAAW,CAAC,EACF,CAAC,cAEbvK,KAAA,CAACxB,IAAI,CAAC+M,KAAK,EAACpE,SAAS,CAAC,MAAM,CAAAoD,QAAA,eAC1BzK,IAAA,CAACtB,IAAI,CAACuO,KAAK,EAAAxC,QAAA,CAAC,eAAa,CAAY,CAAC,cACtCvK,KAAA,CAACxB,IAAI,CAACwO,MAAM,EACV5B,KAAK,CAAE5I,UAAU,CAACE,YAAa,CAC/B2I,QAAQ,CAAG7D,CAAC,EAAK/E,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEE,YAAY,CAAE8E,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAC,CAAE,CAC9EjE,SAAS,CAAC,kBAAkB,CAAAoD,QAAA,eAE5BzK,IAAA,WAAQsL,KAAK,CAAC,EAAE,CAAAb,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpC3H,aAAa,EAAIA,aAAa,CAAC6I,MAAM,CAAG,CAAC,CACxC7I,aAAa,CAACa,GAAG,CAAC,CAACwJ,MAAM,CAAEC,KAAK,gBAC9BpN,IAAA,WAAgCsL,KAAK,CAAE6B,MAAO,CAAA1C,QAAA,CAC3C0C,MAAM,EADI,UAAUC,KAAK,EAEpB,CACT,CAAC,cAEFpN,IAAA,WAAQ8M,QAAQ,MAAArC,QAAA,CAAC,sBAAoB,CAAQ,CAAC,EAErC,CAAC,cAGdvK,KAAA,QAAKmH,SAAS,CAAC,uBAAuB,CAAAoD,QAAA,EAAC,qBAClB,CAAC3H,aAAa,EAAIA,aAAa,CAAC6I,MAAM,CAAG,CAAC,CAC3D7I,aAAa,CAACyG,IAAI,CAAC,IAAI,CAAC,CAAG,YAAY,EACtC,CAAC,cAENvJ,IAAA,CAACtB,IAAI,CAACyM,IAAI,EAAC9D,SAAS,CAAC,YAAY,CAAAoD,QAAA,CAAC,4EAElC,CAAW,CAAC,EACF,CAAC,cAEbvK,KAAA,CAACxB,IAAI,CAAC+M,KAAK,EAACpE,SAAS,CAAC,MAAM,CAAAoD,QAAA,eAC1BzK,IAAA,CAACtB,IAAI,CAAC2O,KAAK,EACTvD,IAAI,CAAC,UAAU,CACfwD,KAAK,CAAC,yCAAyC,CAC/CC,OAAO,CAAE,CAAC7K,UAAU,CAACG,iBAAkB,CACvC0I,QAAQ,CAAG7D,CAAC,EAAK/E,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEG,iBAAiB,CAAE,CAAC6E,CAAC,CAAC8D,MAAM,CAAC+B,OAAO,CAAC,CAAE,CACvF,CAAC,cACFvN,IAAA,CAACtB,IAAI,CAACyM,IAAI,EAAC9D,SAAS,CAAC,YAAY,CAAAoD,QAAA,CAAC,gFAElC,CAAW,CAAC,EACF,CAAC,EACT,CAAC,cAEPvK,KAAA,CAACpB,KAAK,EAACiM,OAAO,CAAC,MAAM,CAAAN,QAAA,eACnBzK,IAAA,CAACH,OAAO,EAACwH,SAAS,CAAC,MAAM,CAAE,CAAC,cAC5BrH,IAAA,WAAAyK,QAAA,CAAQ,OAAK,CAAQ,CAAC,qGACxB,EAAO,CAAC,EACE,CAAC,cACbvK,KAAA,CAACnB,KAAK,CAAC8N,MAAM,EAAApC,QAAA,eACXzK,IAAA,CAACvB,MAAM,EAACsM,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAMvI,eAAe,CAAC,KAAK,CAAE,CAAAgI,QAAA,CAAC,QAEnE,CAAQ,CAAC,cACTzK,IAAA,CAACvB,MAAM,EACLsM,OAAO,CAAC,SAAS,CACjBC,OAAO,CAAEzC,WAAY,CACrBuE,QAAQ,CAAE9J,UAAW,CAAAyH,QAAA,CAEpBzH,UAAU,cACT9C,KAAA,CAAAE,SAAA,EAAAqK,QAAA,eACEzK,IAAA,CAACnB,OAAO,EACNkO,EAAE,CAAC,MAAM,CACTnB,SAAS,CAAC,QAAQ,CAClB3E,IAAI,CAAC,IAAI,CACT+F,IAAI,CAAC,QAAQ,CACb,cAAY,MAAM,CAClB3F,SAAS,CAAC,MAAM,CACjB,CAAC,oBAEJ,EAAE,CAAC,cAEHnH,KAAA,CAAAE,SAAA,EAAAqK,QAAA,eACEzK,IAAA,CAACL,cAAc,EAAC0H,SAAS,CAAC,MAAM,CAAE,CAAC,gBACrC,EAAE,CACH,CACK,CAAC,EACG,CAAC,EACV,CAAC,EACR,CAAC,CAEP,CAAC,CAED,cAAe,CAAAzG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}