{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import axios from\"axios\";import{Link}from\"react-router-dom\";import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import{format}from\"date-fns\";// Import date-fns for formatting\nimport{FaSearch,FaPlus,FaTshirt,FaExclamationTriangle}from\"react-icons/fa\";import{Card,Row,Col,Badge,Button,Form,InputGroup,Spinner,Alert}from\"react-bootstrap\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ViewDailySewingHistory=()=>{const[records,setRecords]=useState([]);const[error,setError]=useState(\"\");const[searchTerm,setSearchTerm]=useState(\"\");const[sortField,setSortField]=useState(\"date\");// default sort by date\nconst[sortOrder,setSortOrder]=useState(\"desc\");// 'asc' or 'desc'\nconst[loading,setLoading]=useState(true);// Loading state\nconst[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);// Track sidebar state\nconst[currentPage,setCurrentPage]=useState(1);const[recordsPerPage]=useState(10);const[totalItems,setTotalItems]=useState(0);useEffect(()=>{// Fetch records on component mount\nsetLoading(true);axios.get(\"http://localhost:8000/api/sewing/history/daily/\").then(res=>{setRecords(res.data);setTotalItems(res.data.length);setLoading(false);}).catch(err=>{console.error(\"Error fetching daily sewing history:\",err);setError(\"Failed to load daily sewing history.\");setLoading(false);});},[]);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Filter records based on search term (product_name search)\nconst filteredRecords=records.filter(record=>record.product_name.toLowerCase().includes(searchTerm.toLowerCase()));// Sort records based on sortField and sortOrder\nconst sortedRecords=filteredRecords.sort((a,b)=>{let aVal=a[sortField];let bVal=b[sortField];// If sorting by date, convert to Date objects\nif(sortField===\"date\"){aVal=new Date(aVal);bVal=new Date(bVal);}else{aVal=aVal.toString().toLowerCase();bVal=bVal.toString().toLowerCase();}if(aVal<bVal)return sortOrder===\"asc\"?-1:1;if(aVal>bVal)return sortOrder===\"asc\"?1:-1;return 0;});// Pagination logic\nconst indexOfLastRecord=currentPage*recordsPerPage;const indexOfFirstRecord=indexOfLastRecord-recordsPerPage;const currentRecords=sortedRecords.slice(indexOfFirstRecord,indexOfLastRecord);// Change page\nconst paginate=pageNumber=>setCurrentPage(pageNumber);// Calculate total pages\nconst totalPages=Math.ceil(sortedRecords.length/recordsPerPage);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"main-content\",style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsx(Row,{className:\"mb-4\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{className:\"shadow-sm\",style:{backgroundColor:\"#D9EDFB\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-0\",children:\"Daily Sewing History\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted mb-0\",children:\"View and manage daily sewing production records\"})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Link,{to:\"/adddailysewing\",children:/*#__PURE__*/_jsxs(Button,{variant:\"primary\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-2\"}),\" Add New Record\"]})})})]})})})})}),error&&/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",className:\"mb-3 d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaExclamationTriangle,{className:\"me-2\"}),\" \",error]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"mb-3 mb-md-0\",children:/*#__PURE__*/_jsx(Link,{to:\"/viewproductlist\",children:/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaTshirt,{className:\"me-2\"}),\" View Product List\"]})})}),/*#__PURE__*/_jsx(Col,{md:6,className:\"text-md-end\",children:/*#__PURE__*/_jsxs(Badge,{bg:\"info\",className:\"p-2 me-2\",children:[\"Total Records: \",filteredRecords.length]})})]}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,className:\"mb-3 mb-md-0\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Search Products\"}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputGroup.Text,{children:/*#__PURE__*/_jsx(FaSearch,{})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:\"Search by Product Name...\",value:searchTerm,onChange:e=>{setSearchTerm(e.target.value);setCurrentPage(1);// Reset to first page on search\n}})]})]})}),/*#__PURE__*/_jsx(Col,{md:3,className:\"mb-3 mb-md-0\",children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Sort by\"}),/*#__PURE__*/_jsxs(Form.Select,{value:sortField,onChange:e=>setSortField(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"date\",children:\"Date\"}),/*#__PURE__*/_jsx(\"option\",{value:\"product_name\",children:\"Product Name\"}),/*#__PURE__*/_jsx(\"option\",{value:\"color\",children:\"Color\"})]})]})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(Form.Group,{children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Order\"}),/*#__PURE__*/_jsxs(Form.Select,{value:sortOrder,onChange:e=>setSortOrder(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"asc\",children:\"Ascending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"desc\",children:\"Descending\"})]})]})})]})})}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center my-5\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",variant:\"primary\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:\"Loading records...\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Card,{className:\"shadow-sm\",children:/*#__PURE__*/_jsx(Card.Body,{className:\"p-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table table-hover mb-0\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{style:{backgroundColor:\"#f8f9fa\"},children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"Product Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"Color\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"XS\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"S\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"M\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"L\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"XL\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-3 py-3\",children:\"Damage\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentRecords.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsxs(\"td\",{colSpan:\"10\",className:\"text-center py-4\",children:[/*#__PURE__*/_jsx(FaSearch,{className:\"me-2 text-muted\",size:20}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-0 text-muted\",children:\"No records found.\"})]})}):currentRecords.map(record=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:format(new Date(record.date),\"dd/MM/yyyy\")}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3 fw-bold\",children:record.product_name}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:\"24px\",height:\"24px\",backgroundColor:record.color_hex||\"#CCCCCC\",border:\"1px solid #ccc\",marginRight:\"8px\",borderRadius:\"4px\"}}),/*#__PURE__*/_jsx(\"span\",{children:record.color})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:record.xs}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:record.s}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:record.m}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:record.l}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:record.xl}),/*#__PURE__*/_jsx(\"td\",{className:\"px-3 py-3\",children:record.damage_count>0?/*#__PURE__*/_jsx(Badge,{bg:\"warning\",pill:true,children:record.damage_count}):/*#__PURE__*/_jsx(\"span\",{children:\"0\"})})]},record.id))})]})})})}),totalPages>1&&/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center mt-4\",children:/*#__PURE__*/_jsxs(\"ul\",{className:\"pagination\",children:[/*#__PURE__*/_jsx(\"li\",{className:`page-item ${currentPage===1?'disabled':''}`,children:/*#__PURE__*/_jsx(Button,{variant:\"light\",className:\"page-link\",onClick:()=>paginate(currentPage-1),disabled:currentPage===1,children:\"Previous\"})}),[...Array(totalPages).keys()].map(number=>/*#__PURE__*/_jsx(\"li\",{className:`page-item ${currentPage===number+1?'active':''}`,children:/*#__PURE__*/_jsx(Button,{variant:currentPage===number+1?\"primary\":\"light\",className:\"page-link\",onClick:()=>paginate(number+1),children:number+1})},number+1)),/*#__PURE__*/_jsx(\"li\",{className:`page-item ${currentPage===totalPages?'disabled':''}`,children:/*#__PURE__*/_jsx(Button,{variant:\"light\",className:\"page-link\",onClick:()=>paginate(currentPage+1),disabled:currentPage===totalPages,children:\"Next\"})})]})})]})]})]});};export default ViewDailySewingHistory;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Link", "RoleBasedNavBar", "format", "FaSearch", "FaPlus", "FaTshirt", "FaExclamationTriangle", "Card", "Row", "Col", "Badge", "<PERSON><PERSON>", "Form", "InputGroup", "Spinner", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ViewDailySewingHistory", "records", "setRecords", "error", "setError", "searchTerm", "setSearchTerm", "sortField", "setSortField", "sortOrder", "setSortOrder", "loading", "setLoading", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "currentPage", "setCurrentPage", "recordsPerPage", "totalItems", "setTotalItems", "get", "then", "res", "data", "length", "catch", "err", "console", "handleResize", "addEventListener", "removeEventListener", "filteredRecords", "filter", "record", "product_name", "toLowerCase", "includes", "sortedRecords", "sort", "a", "b", "aVal", "bVal", "Date", "toString", "indexOfLastRecord", "indexOfFirstRecord", "currentRecords", "slice", "paginate", "pageNumber", "totalPages", "Math", "ceil", "children", "className", "style", "marginLeft", "width", "transition", "padding", "backgroundColor", "Body", "to", "variant", "md", "bg", "Group", "Label", "Text", "Control", "type", "placeholder", "value", "onChange", "e", "target", "Select", "animation", "colSpan", "size", "map", "date", "height", "color_hex", "border", "marginRight", "borderRadius", "color", "xs", "s", "m", "l", "xl", "damage_count", "pill", "id", "onClick", "disabled", "Array", "keys", "number"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/ViewDailySewingHistory.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { Link } from \"react-router-dom\";\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport { format } from \"date-fns\"; // Import date-fns for formatting\r\nimport { FaSearch, FaPlus, FaTshirt, FaExclamationTriangle } from \"react-icons/fa\";\r\nimport { Card, Row, Col, Badge, Button, Form, InputGroup, Spinner, Alert } from \"react-bootstrap\";\r\n\r\nconst ViewDailySewingHistory = () => {\r\n  const [records, setRecords] = useState([]);\r\n  const [error, setError] = useState(\"\");\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortField, setSortField] = useState(\"date\"); // default sort by date\r\n  const [sortOrder, setSortOrder] = useState(\"desc\"); // 'asc' or 'desc'\r\n  const [loading, setLoading] = useState(true); // Loading state\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768); // Track sidebar state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [recordsPerPage] = useState(10);\r\n  const [totalItems, setTotalItems] = useState(0);\r\n\r\n  useEffect(() => {\r\n    // Fetch records on component mount\r\n    setLoading(true);\r\n    axios\r\n      .get(\"http://localhost:8000/api/sewing/history/daily/\")\r\n      .then((res) => {\r\n        setRecords(res.data);\r\n        setTotalItems(res.data.length);\r\n        setLoading(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Error fetching daily sewing history:\", err);\r\n        setError(\"Failed to load daily sewing history.\");\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Filter records based on search term (product_name search)\r\n  const filteredRecords = records.filter((record) =>\r\n    record.product_name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  // Sort records based on sortField and sortOrder\r\n  const sortedRecords = filteredRecords.sort((a, b) => {\r\n    let aVal = a[sortField];\r\n    let bVal = b[sortField];\r\n\r\n    // If sorting by date, convert to Date objects\r\n    if (sortField === \"date\") {\r\n      aVal = new Date(aVal);\r\n      bVal = new Date(bVal);\r\n    } else {\r\n      aVal = aVal.toString().toLowerCase();\r\n      bVal = bVal.toString().toLowerCase();\r\n    }\r\n\r\n    if (aVal < bVal) return sortOrder === \"asc\" ? -1 : 1;\r\n    if (aVal > bVal) return sortOrder === \"asc\" ? 1 : -1;\r\n    return 0;\r\n  });\r\n\r\n  // Pagination logic\r\n  const indexOfLastRecord = currentPage * recordsPerPage;\r\n  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;\r\n  const currentRecords = sortedRecords.slice(indexOfFirstRecord, indexOfLastRecord);\r\n\r\n  // Change page\r\n  const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n  // Calculate total pages\r\n  const totalPages = Math.ceil(sortedRecords.length / recordsPerPage);\r\n\r\n\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        className=\"main-content\"\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        {/* Header Section with Cards */}\r\n        <Row className=\"mb-4\">\r\n          <Col>\r\n            <Card className=\"shadow-sm\" style={{ backgroundColor: \"#D9EDFB\" }}>\r\n              <Card.Body>\r\n                <div className=\"d-flex justify-content-between align-items-center\">\r\n                  <div>\r\n                    <h2 className=\"mb-0\">Daily Sewing History</h2>\r\n                    <p className=\"text-muted mb-0\">\r\n                      View and manage daily sewing production records\r\n                    </p>\r\n                  </div>\r\n                  <div>\r\n                    <Link to=\"/adddailysewing\">\r\n                      <Button variant=\"primary\" className=\"d-flex align-items-center\">\r\n                        <FaPlus className=\"me-2\" /> Add New Record\r\n                      </Button>\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n\r\n        {/* Error Message */}\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"mb-3 d-flex align-items-center\">\r\n            <FaExclamationTriangle className=\"me-2\" /> {error}\r\n          </Alert>\r\n        )}\r\n\r\n        {/* Action Buttons Row */}\r\n        <Row className=\"mb-4\">\r\n          <Col md={6} className=\"mb-3 mb-md-0\">\r\n            <Link to=\"/viewproductlist\">\r\n              <Button variant=\"outline-primary\" className=\"d-flex align-items-center\">\r\n                <FaTshirt className=\"me-2\" /> View Product List\r\n              </Button>\r\n            </Link>\r\n          </Col>\r\n          <Col md={6} className=\"text-md-end\">\r\n            <Badge bg=\"info\" className=\"p-2 me-2\">\r\n              Total Records: {filteredRecords.length}\r\n            </Badge>\r\n          </Col>\r\n        </Row>\r\n\r\n        {/* Search and Filter Card */}\r\n        <Card className=\"mb-4 shadow-sm\">\r\n          <Card.Body>\r\n            <Row>\r\n              {/* Search Bar */}\r\n              <Col md={6} className=\"mb-3 mb-md-0\">\r\n                <Form.Group>\r\n                  <Form.Label>Search Products</Form.Label>\r\n                  <InputGroup>\r\n                    <InputGroup.Text>\r\n                      <FaSearch />\r\n                    </InputGroup.Text>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      placeholder=\"Search by Product Name...\"\r\n                      value={searchTerm}\r\n                      onChange={(e) => {\r\n                        setSearchTerm(e.target.value);\r\n                        setCurrentPage(1); // Reset to first page on search\r\n                      }}\r\n                    />\r\n                  </InputGroup>\r\n                </Form.Group>\r\n              </Col>\r\n\r\n              {/* Sorting Controls */}\r\n              <Col md={3} className=\"mb-3 mb-md-0\">\r\n                <Form.Group>\r\n                  <Form.Label>Sort by</Form.Label>\r\n                  <Form.Select\r\n                    value={sortField}\r\n                    onChange={(e) => setSortField(e.target.value)}\r\n                  >\r\n                    <option value=\"date\">Date</option>\r\n                    <option value=\"product_name\">Product Name</option>\r\n                    <option value=\"color\">Color</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={3}>\r\n                <Form.Group>\r\n                  <Form.Label>Order</Form.Label>\r\n                  <Form.Select\r\n                    value={sortOrder}\r\n                    onChange={(e) => setSortOrder(e.target.value)}\r\n                  >\r\n                    <option value=\"asc\">Ascending</option>\r\n                    <option value=\"desc\">Descending</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n          </Card.Body>\r\n        </Card>\r\n\r\n        {/* Loading Spinner */}\r\n        {loading ? (\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" variant=\"primary\" />\r\n            <p className=\"mt-2\">Loading records...</p>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Table of Records */}\r\n            <Card className=\"shadow-sm\">\r\n              <Card.Body className=\"p-0\">\r\n                <div className=\"table-responsive\">\r\n                  <table className=\"table table-hover mb-0\">\r\n                    <thead>\r\n                      <tr style={{ backgroundColor: \"#f8f9fa\" }}>\r\n                        <th className=\"px-3 py-3\">Date</th>\r\n                        <th className=\"px-3 py-3\">Product Name</th>\r\n                        <th className=\"px-3 py-3\">Color</th>\r\n                        <th className=\"px-3 py-3\">XS</th>\r\n                        <th className=\"px-3 py-3\">S</th>\r\n                        <th className=\"px-3 py-3\">M</th>\r\n                        <th className=\"px-3 py-3\">L</th>\r\n                        <th className=\"px-3 py-3\">XL</th>\r\n                        <th className=\"px-3 py-3\">Damage</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      {currentRecords.length === 0 ? (\r\n                        <tr>\r\n                          <td colSpan=\"10\" className=\"text-center py-4\">\r\n                            <FaSearch className=\"me-2 text-muted\" size={20} />\r\n                            <p className=\"mb-0 text-muted\">No records found.</p>\r\n                          </td>\r\n                        </tr>\r\n                      ) : (\r\n                        currentRecords.map((record) => (\r\n                          <tr key={record.id}>\r\n                            <td className=\"px-3 py-3\">{format(new Date(record.date), \"dd/MM/yyyy\")}</td>\r\n                            <td className=\"px-3 py-3 fw-bold\">{record.product_name}</td>\r\n                            <td className=\"px-3 py-3\">\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <div\r\n                                  style={{\r\n                                    width: \"24px\",\r\n                                    height: \"24px\",\r\n                                    backgroundColor: record.color_hex || \"#CCCCCC\",\r\n                                    border: \"1px solid #ccc\",\r\n                                    marginRight: \"8px\",\r\n                                    borderRadius: \"4px\"\r\n                                  }}\r\n                                />\r\n                                <span>{record.color}</span>\r\n                              </div>\r\n                            </td>\r\n                            <td className=\"px-3 py-3\">{record.xs}</td>\r\n                            <td className=\"px-3 py-3\">{record.s}</td>\r\n                            <td className=\"px-3 py-3\">{record.m}</td>\r\n                            <td className=\"px-3 py-3\">{record.l}</td>\r\n                            <td className=\"px-3 py-3\">{record.xl}</td>\r\n                            <td className=\"px-3 py-3\">\r\n                              {record.damage_count > 0 ? (\r\n                                <Badge bg=\"warning\" pill>\r\n                                  {record.damage_count}\r\n                                </Badge>\r\n                              ) : (\r\n                                <span>0</span>\r\n                              )}\r\n                            </td>\r\n\r\n                          </tr>\r\n                        ))\r\n                      )}\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </Card.Body>\r\n            </Card>\r\n\r\n            {/* Pagination */}\r\n            {totalPages > 1 && (\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <ul className=\"pagination\">\r\n                  <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>\r\n                    <Button\r\n                      variant=\"light\"\r\n                      className=\"page-link\"\r\n                      onClick={() => paginate(currentPage - 1)}\r\n                      disabled={currentPage === 1}\r\n                    >\r\n                      Previous\r\n                    </Button>\r\n                  </li>\r\n\r\n                  {[...Array(totalPages).keys()].map(number => (\r\n                    <li key={number + 1} className={`page-item ${currentPage === number + 1 ? 'active' : ''}`}>\r\n                      <Button\r\n                        variant={currentPage === number + 1 ? \"primary\" : \"light\"}\r\n                        className=\"page-link\"\r\n                        onClick={() => paginate(number + 1)}\r\n                      >\r\n                        {number + 1}\r\n                      </Button>\r\n                    </li>\r\n                  ))}\r\n\r\n                  <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>\r\n                    <Button\r\n                      variant=\"light\"\r\n                      className=\"page-link\"\r\n                      onClick={() => paginate(currentPage + 1)}\r\n                      disabled={currentPage === totalPages}\r\n                    >\r\n                      Next\r\n                    </Button>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ViewDailySewingHistory;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,OAASC,MAAM,KAAQ,UAAU,CAAE;AACnC,OAASC,QAAQ,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,qBAAqB,KAAQ,gBAAgB,CAClF,OAASC,IAAI,CAAEC,GAAG,CAAEC,GAAG,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,OAAO,CAAEC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAElG,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8B,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACgC,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,MAAM,CAAC,CAAE;AACpD,KAAM,CAACkC,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,MAAM,CAAC,CAAE;AACpD,KAAM,CAACoC,OAAO,CAAEC,UAAU,CAAC,CAAGrC,QAAQ,CAAC,IAAI,CAAC,CAAE;AAC9C,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAACwC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAAE;AAC9E,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC4C,cAAc,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CACrC,KAAM,CAAC6C,UAAU,CAAEC,aAAa,CAAC,CAAG9C,QAAQ,CAAC,CAAC,CAAC,CAE/CC,SAAS,CAAC,IAAM,CACd;AACAoC,UAAU,CAAC,IAAI,CAAC,CAChBnC,KAAK,CACF6C,GAAG,CAAC,iDAAiD,CAAC,CACtDC,IAAI,CAAEC,GAAG,EAAK,CACbtB,UAAU,CAACsB,GAAG,CAACC,IAAI,CAAC,CACpBJ,aAAa,CAACG,GAAG,CAACC,IAAI,CAACC,MAAM,CAAC,CAC9Bd,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACDe,KAAK,CAAEC,GAAG,EAAK,CACdC,OAAO,CAAC1B,KAAK,CAAC,sCAAsC,CAAEyB,GAAG,CAAC,CAC1DxB,QAAQ,CAAC,sCAAsC,CAAC,CAChDQ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAEN;AACApC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsD,YAAY,CAAGA,CAAA,GAAM,CACzBhB,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACgB,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMf,MAAM,CAACiB,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,eAAe,CAAGhC,OAAO,CAACiC,MAAM,CAAEC,MAAM,EAC5CA,MAAM,CAACC,YAAY,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjC,UAAU,CAACgC,WAAW,CAAC,CAAC,CACrE,CAAC,CAED;AACA,KAAM,CAAAE,aAAa,CAAGN,eAAe,CAACO,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACnD,GAAI,CAAAC,IAAI,CAAGF,CAAC,CAAClC,SAAS,CAAC,CACvB,GAAI,CAAAqC,IAAI,CAAGF,CAAC,CAACnC,SAAS,CAAC,CAEvB;AACA,GAAIA,SAAS,GAAK,MAAM,CAAE,CACxBoC,IAAI,CAAG,GAAI,CAAAE,IAAI,CAACF,IAAI,CAAC,CACrBC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACD,IAAI,CAAC,CACvB,CAAC,IAAM,CACLD,IAAI,CAAGA,IAAI,CAACG,QAAQ,CAAC,CAAC,CAACT,WAAW,CAAC,CAAC,CACpCO,IAAI,CAAGA,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACT,WAAW,CAAC,CAAC,CACtC,CAEA,GAAIM,IAAI,CAAGC,IAAI,CAAE,MAAO,CAAAnC,SAAS,GAAK,KAAK,CAAG,CAAC,CAAC,CAAG,CAAC,CACpD,GAAIkC,IAAI,CAAGC,IAAI,CAAE,MAAO,CAAAnC,SAAS,GAAK,KAAK,CAAG,CAAC,CAAG,CAAC,CAAC,CACpD,MAAO,EAAC,CACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsC,iBAAiB,CAAG9B,WAAW,CAAGE,cAAc,CACtD,KAAM,CAAA6B,kBAAkB,CAAGD,iBAAiB,CAAG5B,cAAc,CAC7D,KAAM,CAAA8B,cAAc,CAAGV,aAAa,CAACW,KAAK,CAACF,kBAAkB,CAAED,iBAAiB,CAAC,CAEjF;AACA,KAAM,CAAAI,QAAQ,CAAIC,UAAU,EAAKlC,cAAc,CAACkC,UAAU,CAAC,CAE3D;AACA,KAAM,CAAAC,UAAU,CAAGC,IAAI,CAACC,IAAI,CAAChB,aAAa,CAACb,MAAM,CAAGP,cAAc,CAAC,CAInE,mBACEtB,KAAA,CAAAE,SAAA,EAAAyD,QAAA,eACE7D,IAAA,CAAChB,eAAe,GAAE,CAAC,cACnBkB,KAAA,QACE4D,SAAS,CAAC,cAAc,CACxBC,KAAK,CAAE,CACLC,UAAU,CAAE9C,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5C+C,KAAK,CAAE,eAAe/C,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDgD,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAN,QAAA,eAGF7D,IAAA,CAACT,GAAG,EAACuE,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7D,IAAA,CAACR,GAAG,EAAAqE,QAAA,cACF7D,IAAA,CAACV,IAAI,EAACwE,SAAS,CAAC,WAAW,CAACC,KAAK,CAAE,CAAEK,eAAe,CAAE,SAAU,CAAE,CAAAP,QAAA,cAChE7D,IAAA,CAACV,IAAI,CAAC+E,IAAI,EAAAR,QAAA,cACR3D,KAAA,QAAK4D,SAAS,CAAC,mDAAmD,CAAAD,QAAA,eAChE3D,KAAA,QAAA2D,QAAA,eACE7D,IAAA,OAAI8D,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC9C7D,IAAA,MAAG8D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAAC,iDAE/B,CAAG,CAAC,EACD,CAAC,cACN7D,IAAA,QAAA6D,QAAA,cACE7D,IAAA,CAACjB,IAAI,EAACuF,EAAE,CAAC,iBAAiB,CAAAT,QAAA,cACxB3D,KAAA,CAACR,MAAM,EAAC6E,OAAO,CAAC,SAAS,CAACT,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eAC7D7D,IAAA,CAACb,MAAM,EAAC2E,SAAS,CAAC,MAAM,CAAE,CAAC,kBAC7B,EAAQ,CAAC,CACL,CAAC,CACJ,CAAC,EACH,CAAC,CACG,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CAAC,CAGLtD,KAAK,eACJN,KAAA,CAACJ,KAAK,EAACyE,OAAO,CAAC,QAAQ,CAACT,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAChE7D,IAAA,CAACX,qBAAqB,EAACyE,SAAS,CAAC,MAAM,CAAE,CAAC,IAAC,CAACtD,KAAK,EAC5C,CACR,cAGDN,KAAA,CAACX,GAAG,EAACuE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnB7D,IAAA,CAACR,GAAG,EAACgF,EAAE,CAAE,CAAE,CAACV,SAAS,CAAC,cAAc,CAAAD,QAAA,cAClC7D,IAAA,CAACjB,IAAI,EAACuF,EAAE,CAAC,kBAAkB,CAAAT,QAAA,cACzB3D,KAAA,CAACR,MAAM,EAAC6E,OAAO,CAAC,iBAAiB,CAACT,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACrE7D,IAAA,CAACZ,QAAQ,EAAC0E,SAAS,CAAC,MAAM,CAAE,CAAC,qBAC/B,EAAQ,CAAC,CACL,CAAC,CACJ,CAAC,cACN9D,IAAA,CAACR,GAAG,EAACgF,EAAE,CAAE,CAAE,CAACV,SAAS,CAAC,aAAa,CAAAD,QAAA,cACjC3D,KAAA,CAACT,KAAK,EAACgF,EAAE,CAAC,MAAM,CAACX,SAAS,CAAC,UAAU,CAAAD,QAAA,EAAC,iBACrB,CAACvB,eAAe,CAACP,MAAM,EACjC,CAAC,CACL,CAAC,EACH,CAAC,cAGN/B,IAAA,CAACV,IAAI,EAACwE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,cAC9B7D,IAAA,CAACV,IAAI,CAAC+E,IAAI,EAAAR,QAAA,cACR3D,KAAA,CAACX,GAAG,EAAAsE,QAAA,eAEF7D,IAAA,CAACR,GAAG,EAACgF,EAAE,CAAE,CAAE,CAACV,SAAS,CAAC,cAAc,CAAAD,QAAA,cAClC3D,KAAA,CAACP,IAAI,CAAC+E,KAAK,EAAAb,QAAA,eACT7D,IAAA,CAACL,IAAI,CAACgF,KAAK,EAAAd,QAAA,CAAC,iBAAe,CAAY,CAAC,cACxC3D,KAAA,CAACN,UAAU,EAAAiE,QAAA,eACT7D,IAAA,CAACJ,UAAU,CAACgF,IAAI,EAAAf,QAAA,cACd7D,IAAA,CAACd,QAAQ,GAAE,CAAC,CACG,CAAC,cAClBc,IAAA,CAACL,IAAI,CAACkF,OAAO,EACXC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAEtE,UAAW,CAClBuE,QAAQ,CAAGC,CAAC,EAAK,CACfvE,aAAa,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAC7BzD,cAAc,CAAC,CAAC,CAAC,CAAE;AACrB,CAAE,CACH,CAAC,EACQ,CAAC,EACH,CAAC,CACV,CAAC,cAGNvB,IAAA,CAACR,GAAG,EAACgF,EAAE,CAAE,CAAE,CAACV,SAAS,CAAC,cAAc,CAAAD,QAAA,cAClC3D,KAAA,CAACP,IAAI,CAAC+E,KAAK,EAAAb,QAAA,eACT7D,IAAA,CAACL,IAAI,CAACgF,KAAK,EAAAd,QAAA,CAAC,SAAO,CAAY,CAAC,cAChC3D,KAAA,CAACP,IAAI,CAACyF,MAAM,EACVJ,KAAK,CAAEpE,SAAU,CACjBqE,QAAQ,CAAGC,CAAC,EAAKrE,YAAY,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAnB,QAAA,eAE9C7D,IAAA,WAAQgF,KAAK,CAAC,MAAM,CAAAnB,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC7D,IAAA,WAAQgF,KAAK,CAAC,cAAc,CAAAnB,QAAA,CAAC,cAAY,CAAQ,CAAC,cAClD7D,IAAA,WAAQgF,KAAK,CAAC,OAAO,CAAAnB,QAAA,CAAC,OAAK,CAAQ,CAAC,EACzB,CAAC,EACJ,CAAC,CACV,CAAC,cACN7D,IAAA,CAACR,GAAG,EAACgF,EAAE,CAAE,CAAE,CAAAX,QAAA,cACT3D,KAAA,CAACP,IAAI,CAAC+E,KAAK,EAAAb,QAAA,eACT7D,IAAA,CAACL,IAAI,CAACgF,KAAK,EAAAd,QAAA,CAAC,OAAK,CAAY,CAAC,cAC9B3D,KAAA,CAACP,IAAI,CAACyF,MAAM,EACVJ,KAAK,CAAElE,SAAU,CACjBmE,QAAQ,CAAGC,CAAC,EAAKnE,YAAY,CAACmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAnB,QAAA,eAE9C7D,IAAA,WAAQgF,KAAK,CAAC,KAAK,CAAAnB,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtC7D,IAAA,WAAQgF,KAAK,CAAC,MAAM,CAAAnB,QAAA,CAAC,YAAU,CAAQ,CAAC,EAC7B,CAAC,EACJ,CAAC,CACV,CAAC,EACH,CAAC,CACG,CAAC,CACR,CAAC,CAGN7C,OAAO,cACNd,KAAA,QAAK4D,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B7D,IAAA,CAACH,OAAO,EAACwF,SAAS,CAAC,QAAQ,CAACd,OAAO,CAAC,SAAS,CAAE,CAAC,cAChDvE,IAAA,MAAG8D,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,oBAAkB,CAAG,CAAC,EACvC,CAAC,cAEN3D,KAAA,CAAAE,SAAA,EAAAyD,QAAA,eAEE7D,IAAA,CAACV,IAAI,EAACwE,SAAS,CAAC,WAAW,CAAAD,QAAA,cACzB7D,IAAA,CAACV,IAAI,CAAC+E,IAAI,EAACP,SAAS,CAAC,KAAK,CAAAD,QAAA,cACxB7D,IAAA,QAAK8D,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/B3D,KAAA,UAAO4D,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACvC7D,IAAA,UAAA6D,QAAA,cACE3D,KAAA,OAAI6D,KAAK,CAAE,CAAEK,eAAe,CAAE,SAAU,CAAE,CAAAP,QAAA,eACxC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,MAAI,CAAI,CAAC,cACnC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,cAAY,CAAI,CAAC,cAC3C7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,OAAK,CAAI,CAAC,cACpC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,IAAE,CAAI,CAAC,cACjC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,GAAC,CAAI,CAAC,cAChC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,GAAC,CAAI,CAAC,cAChC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,GAAC,CAAI,CAAC,cAChC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,IAAE,CAAI,CAAC,cACjC7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,QAAM,CAAI,CAAC,EACnC,CAAC,CACA,CAAC,cACR7D,IAAA,UAAA6D,QAAA,CACGP,cAAc,CAACvB,MAAM,GAAK,CAAC,cAC1B/B,IAAA,OAAA6D,QAAA,cACE3D,KAAA,OAAIoF,OAAO,CAAC,IAAI,CAACxB,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC3C7D,IAAA,CAACd,QAAQ,EAAC4E,SAAS,CAAC,iBAAiB,CAACyB,IAAI,CAAE,EAAG,CAAE,CAAC,cAClDvF,IAAA,MAAG8D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,CAAC,mBAAiB,CAAG,CAAC,EAClD,CAAC,CACH,CAAC,CAELP,cAAc,CAACkC,GAAG,CAAEhD,MAAM,eACxBtC,KAAA,OAAA2D,QAAA,eACE7D,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAE5E,MAAM,CAAC,GAAI,CAAAiE,IAAI,CAACV,MAAM,CAACiD,IAAI,CAAC,CAAE,YAAY,CAAC,CAAK,CAAC,cAC5EzF,IAAA,OAAI8D,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAErB,MAAM,CAACC,YAAY,CAAK,CAAC,cAC5DzC,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,cACvB3D,KAAA,QAAK4D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7D,IAAA,QACE+D,KAAK,CAAE,CACLE,KAAK,CAAE,MAAM,CACbyB,MAAM,CAAE,MAAM,CACdtB,eAAe,CAAE5B,MAAM,CAACmD,SAAS,EAAI,SAAS,CAC9CC,MAAM,CAAE,gBAAgB,CACxBC,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,cACF9F,IAAA,SAAA6D,QAAA,CAAOrB,MAAM,CAACuD,KAAK,CAAO,CAAC,EACxB,CAAC,CACJ,CAAC,cACL/F,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAErB,MAAM,CAACwD,EAAE,CAAK,CAAC,cAC1ChG,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAErB,MAAM,CAACyD,CAAC,CAAK,CAAC,cACzCjG,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAErB,MAAM,CAAC0D,CAAC,CAAK,CAAC,cACzClG,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAErB,MAAM,CAAC2D,CAAC,CAAK,CAAC,cACzCnG,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAErB,MAAM,CAAC4D,EAAE,CAAK,CAAC,cAC1CpG,IAAA,OAAI8D,SAAS,CAAC,WAAW,CAAAD,QAAA,CACtBrB,MAAM,CAAC6D,YAAY,CAAG,CAAC,cACtBrG,IAAA,CAACP,KAAK,EAACgF,EAAE,CAAC,SAAS,CAAC6B,IAAI,MAAAzC,QAAA,CACrBrB,MAAM,CAAC6D,YAAY,CACf,CAAC,cAERrG,IAAA,SAAA6D,QAAA,CAAM,GAAC,CAAM,CACd,CACC,CAAC,GA/BErB,MAAM,CAAC+D,EAiCZ,CACL,CACF,CACI,CAAC,EACH,CAAC,CACL,CAAC,CACG,CAAC,CACR,CAAC,CAGN7C,UAAU,CAAG,CAAC,eACb1D,IAAA,QAAK8D,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3D,KAAA,OAAI4D,SAAS,CAAC,YAAY,CAAAD,QAAA,eACxB7D,IAAA,OAAI8D,SAAS,CAAE,aAAaxC,WAAW,GAAK,CAAC,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAuC,QAAA,cAChE7D,IAAA,CAACN,MAAM,EACL6E,OAAO,CAAC,OAAO,CACfT,SAAS,CAAC,WAAW,CACrB0C,OAAO,CAAEA,CAAA,GAAMhD,QAAQ,CAAClC,WAAW,CAAG,CAAC,CAAE,CACzCmF,QAAQ,CAAEnF,WAAW,GAAK,CAAE,CAAAuC,QAAA,CAC7B,UAED,CAAQ,CAAC,CACP,CAAC,CAEJ,CAAC,GAAG6C,KAAK,CAAChD,UAAU,CAAC,CAACiD,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,CAACoB,MAAM,eACvC5G,IAAA,OAAqB8D,SAAS,CAAE,aAAaxC,WAAW,GAAKsF,MAAM,CAAG,CAAC,CAAG,QAAQ,CAAG,EAAE,EAAG,CAAA/C,QAAA,cACxF7D,IAAA,CAACN,MAAM,EACL6E,OAAO,CAAEjD,WAAW,GAAKsF,MAAM,CAAG,CAAC,CAAG,SAAS,CAAG,OAAQ,CAC1D9C,SAAS,CAAC,WAAW,CACrB0C,OAAO,CAAEA,CAAA,GAAMhD,QAAQ,CAACoD,MAAM,CAAG,CAAC,CAAE,CAAA/C,QAAA,CAEnC+C,MAAM,CAAG,CAAC,CACL,CAAC,EAPFA,MAAM,CAAG,CAQd,CACL,CAAC,cAEF5G,IAAA,OAAI8D,SAAS,CAAE,aAAaxC,WAAW,GAAKoC,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CAAAG,QAAA,cACzE7D,IAAA,CAACN,MAAM,EACL6E,OAAO,CAAC,OAAO,CACfT,SAAS,CAAC,WAAW,CACrB0C,OAAO,CAAEA,CAAA,GAAMhD,QAAQ,CAAClC,WAAW,CAAG,CAAC,CAAE,CACzCmF,QAAQ,CAAEnF,WAAW,GAAKoC,UAAW,CAAAG,QAAA,CACtC,MAED,CAAQ,CAAC,CACP,CAAC,EACH,CAAC,CACF,CACN,EACD,CACH,EACE,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAAxD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}