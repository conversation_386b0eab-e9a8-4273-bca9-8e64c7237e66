{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import axios from\"axios\";import Select from\"react-select\";import{FaPlus,FaTrash}from\"react-icons/fa\";import{<PERSON>,Col,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>ert,Spinner}from'react-bootstrap';import'bootstrap/dist/css/bootstrap.min.css';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";// Common color presets with names\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const COLOR_PRESETS=[{color:\"#000000\",name:\"Black\"},{color:\"#FFFFFF\",name:\"White\"},{color:\"#FF0000\",name:\"Red\"},{color:\"#0000FF\",name:\"Blue\"},{color:\"#008000\",name:\"Green\"},{color:\"#FFFF00\",name:\"Yellow\"},{color:\"#FFA500\",name:\"Orange\"},{color:\"#800080\",name:\"<PERSON>\"},{color:\"#FFC0CB\",name:\"<PERSON>\"},{color:\"#A52A2A\",name:\"<PERSON>\"},{color:\"#808080\",name:\"Gray\"}];const AddFabric=()=>{// State variables\nconst[fabricName,setFabricName]=useState(\"\");const[selectedSupplier,setSelectedSupplier]=useState(null);const[dateAdded,setDateAdded]=useState(\"\");const[variants,setVariants]=useState([{color:\"#000000\",colorName:\"Black\",totalYard:\"\",pricePerYard:\"\"}]);const[suppliers,setSuppliers]=useState([]);const[message,setMessage]=useState(\"\");const[isSubmitting,setIsSubmitting]=useState(false);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[loading,setLoading]=useState(false);// Effect to handle sidebar state based on window size\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Fetch suppliers\nuseEffect(()=>{setLoading(true);axios.get(\"http://localhost:8000/api/suppliers/\").then(response=>{const supplierOptions=response.data.map(sup=>({value:sup.supplier_id,label:sup.name}));setSuppliers(supplierOptions);setLoading(false);}).catch(error=>{console.error(\"Error fetching suppliers:\",error);setMessage(\"Failed to load suppliers\");setLoading(false);});},[]);// Handle adding a variant\nconst handleAddVariant=()=>{setVariants([...variants,{color:\"#000000\",colorName:\"Black\",totalYard:\"\",pricePerYard:\"\"}]);};// Handle removing a variant\nconst handleRemoveVariant=index=>{const updated=variants.filter((_,i)=>i!==index);setVariants(updated);};// Handle variant input changes\nconst handleVariantChange=(index,field,value)=>{const updated=[...variants];updated[index][field]=value;// If color is changed, suggest a color name only if current name is empty or matches a preset\nif(field===\"color\"){const colorPreset=COLOR_PRESETS.find(preset=>preset.color===value);const currentColorName=updated[index].colorName;// Only auto-update if the field is empty or contains a basic preset name\nconst isBasicPresetName=COLOR_PRESETS.some(preset=>preset.name===currentColorName);if(!currentColorName||isBasicPresetName){updated[index].colorName=colorPreset?colorPreset.name:\"\";}}setVariants(updated);};// Function to check if two colors are similar\nconst areColorsSimilar=(color1,color2)=>{// Convert hex to RGB\nconst hexToRgb=hex=>{const result=/^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);return result?{r:parseInt(result[1],16),g:parseInt(result[2],16),b:parseInt(result[3],16)}:null;};const rgb1=hexToRgb(color1);const rgb2=hexToRgb(color2);if(!rgb1||!rgb2)return false;// Calculate color difference using Euclidean distance\nconst distance=Math.sqrt(Math.pow(rgb1.r-rgb2.r,2)+Math.pow(rgb1.g-rgb2.g,2)+Math.pow(rgb1.b-rgb2.b,2));// Colors are similar if distance is less than 50 (adjustable threshold)\nreturn distance<50;};// Submit handler\nconst handleSubmit=async e=>{e.preventDefault();setMessage(\"\");setIsSubmitting(true);// Basic validation\nif(!fabricName.trim()){setMessage(\"Please enter a fabric name\");setIsSubmitting(false);return;}if(!selectedSupplier){setMessage(\"Please select a supplier\");setIsSubmitting(false);return;}if(!dateAdded){setMessage(\"Please select a date\");setIsSubmitting(false);return;}// Validate variants\nfor(let i=0;i<variants.length;i++){const variant=variants[i];if(variant.totalYard&&parseFloat(variant.totalYard)<0){setMessage(`Variant ${i+1}: Total yard cannot be negative`);setIsSubmitting(false);return;}if(variant.pricePerYard&&parseFloat(variant.pricePerYard)<0){setMessage(`Variant ${i+1}: Price per yard cannot be negative`);setIsSubmitting(false);return;}}// Check for similar colors without descriptive names\nfor(let i=0;i<variants.length;i++){for(let j=i+1;j<variants.length;j++){const variant1=variants[i];const variant2=variants[j];if(areColorsSimilar(variant1.color,variant2.color)){const name1=variant1.colorName||\"Unnamed\";const name2=variant2.colorName||\"Unnamed\";// Check if names are too generic or similar\nif(name1===name2||name1===\"Unnamed\"||name2===\"Unnamed\"||COLOR_PRESETS.some(preset=>preset.name===name1||preset.name===name2)){const proceed=window.confirm(`⚠️ Warning: Variants ${i+1} and ${j+1} have similar colors but generic names.\\n\\n`+`This might cause confusion during cutting and production.\\n\\n`+`Consider using more descriptive names like:\\n`+`• \"Black Line\" vs \"Black Circle\"\\n`+`• \"Navy Stripe\" vs \"Navy Solid\"\\n\\n`+`Do you want to continue anyway?`);if(!proceed){setIsSubmitting(false);return;}}}}}try{const defResponse=await axios.post(\"http://localhost:8000/api/fabric-definitions/\",{fabric_name:fabricName,supplier:selectedSupplier.value,date_added:dateAdded});if(defResponse.status===201){const definitionId=defResponse.data.id;for(let variant of variants){await axios.post(\"http://localhost:8000/api/fabric-variants/\",{fabric_definition:definitionId,color:variant.color,color_name:variant.colorName,total_yard:parseFloat(variant.totalYard)||0,price_per_yard:parseFloat(variant.pricePerYard)||0});}setMessage(\"✅ Fabric and variants created successfully!\");setFabricName(\"\");setSelectedSupplier(null);setDateAdded(\"\");setVariants([{color:\"#000000\",colorName:\"Black\",totalYard:\"\",pricePerYard:\"\"}]);}}catch(error){console.error(\"Error creating fabric or variants:\",error);setMessage(\"Error creating fabric or variants.\");}finally{setIsSubmitting(false);}};// Function to select a preset color\nconst selectPresetColor=(index,preset)=>{const updated=[...variants];updated[index].color=preset.color;updated[index].colorName=preset.name;setVariants(updated);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"mb-4\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-2\"}),\"Add Fabric\"]}),message&&/*#__PURE__*/_jsx(Alert,{variant:message.includes(\"✅\")?\"success\":\"danger\",className:\"d-flex align-items-center\",children:message}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric Name\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:fabricName,onChange:e=>setFabricName(e.target.value),required:true,placeholder:\"Enter fabric name\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Supplier\"})}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading suppliers...\"})]}):/*#__PURE__*/_jsx(Select,{options:suppliers,value:selectedSupplier,onChange:setSelectedSupplier,placeholder:\"Select a supplier...\",isSearchable:true})]})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Date Added\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:dateAdded,onChange:e=>setDateAdded(e.target.value),required:true})]})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"mt-4 mb-3 border-bottom pb-2\",children:\"Fabric Variants\"}),variants.map((variant,index)=>/*#__PURE__*/_jsxs(Card,{className:\"mb-3 border\",style:{borderLeft:`5px solid ${variant.color}`,borderRadius:\"8px\"},children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"d-flex justify-content-between align-items-center bg-light\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[\"Variant #\",index+1]}),variants.length>1&&/*#__PURE__*/_jsxs(Button,{variant:\"outline-danger\",size:\"sm\",onClick:()=>handleRemoveVariant(index),children:[/*#__PURE__*/_jsx(FaTrash,{className:\"me-1\"}),\" Remove\"]})]}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Color\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mb-2\",children:[/*#__PURE__*/_jsx(Form.Control,{type:\"color\",value:variant.color,onChange:e=>handleVariantChange(index,\"color\",e.target.value),className:\"me-2\",style:{width:'38px',height:'38px'}}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:\"e.g., Black Line, Black Circle, Navy Stripe\",value:variant.colorName,onChange:e=>handleVariantChange(index,\"colorName\",e.target.value)})]}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted mb-2 d-block\",children:[\"\\uD83D\\uDCA1 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Tip:\"}),\" Use descriptive names for similar colors (e.g., \\\"Black Line\\\", \\\"Black Circle\\\") to avoid confusion during cutting\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"color-presets d-flex flex-wrap gap-1 mt-1\",children:COLOR_PRESETS.map((preset,presetIndex)=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>selectPresetColor(index,preset),style:{width:'20px',height:'20px',backgroundColor:preset.color,border:variant.color===preset.color?'2px solid #000':'1px solid #ccc',borderRadius:'4px',cursor:'pointer'},title:preset.name},presetIndex))})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Total Yard\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",step:\"0.01\",value:variant.totalYard,onChange:e=>{const value=e.target.value;// Only allow positive numbers\nif(value===''||parseFloat(value)>=0){handleVariantChange(index,\"totalYard\",value);}},placeholder:\"Enter total yards\"})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Price per Yard\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",step:\"0.01\",value:variant.pricePerYard,onChange:e=>{const value=e.target.value;// Only allow positive numbers\nif(value===''||parseFloat(value)>=0){handleVariantChange(index,\"pricePerYard\",value);}},placeholder:\"Enter price per yard\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2 p-2 bg-light rounded d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'20px',height:'20px',backgroundColor:variant.color,border:'1px solid #ccc',borderRadius:'4px',marginRight:'8px'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Preview:\"}),/*#__PURE__*/_jsx(\"span\",{className:variant.colorName?\"text-success fw-bold\":\"text-warning\",children:variant.colorName||\"⚠️ Unnamed Color\"}),variant.totalYard?` - ${variant.totalYard} yards`:\" - No yards specified\",variant.pricePerYard?` - Rs. ${variant.pricePerYard}/yard`:\" - No price specified\"]})]})]})]},index)),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center mb-4\",children:/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",onClick:handleAddVariant,className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-2\"}),\" Add Another Variant\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center mt-4\",children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"primary\",size:\"lg\",disabled:isSubmitting,className:\"px-5\",children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Submitting...\"]}):'Submit Fabric'})})]})})})]})]});};export default AddFabric;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Select", "FaPlus", "FaTrash", "Row", "Col", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "Spinner", "RoleBasedNavBar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "COLOR_PRESETS", "color", "name", "AddFabric", "fabricName", "setFabricName", "selectedSupplier", "setSelectedSupplier", "dateAdded", "setDateAdded", "variants", "setVariants", "colorName", "totalYard", "pricePerYard", "suppliers", "setSuppliers", "message", "setMessage", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "loading", "setLoading", "handleResize", "addEventListener", "removeEventListener", "get", "then", "response", "supplierOptions", "data", "map", "sup", "value", "supplier_id", "label", "catch", "error", "console", "handleAddVariant", "handleRemoveVariant", "index", "updated", "filter", "_", "i", "handleVariantChange", "field", "colorPreset", "find", "preset", "currentColorName", "isBasicPresetName", "some", "areColorsSimilar", "color1", "color2", "hexToRgb", "hex", "result", "exec", "r", "parseInt", "g", "b", "rgb1", "rgb2", "distance", "Math", "sqrt", "pow", "handleSubmit", "e", "preventDefault", "trim", "length", "variant", "parseFloat", "j", "variant1", "variant2", "name1", "name2", "proceed", "confirm", "defResponse", "post", "fabric_name", "supplier", "date_added", "status", "definitionId", "id", "fabric_definition", "color_name", "total_yard", "price_per_yard", "selectPresetColor", "children", "style", "marginLeft", "width", "transition", "padding", "className", "includes", "backgroundColor", "borderRadius", "Body", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "required", "placeholder", "animation", "size", "options", "isSearchable", "borderLeft", "Header", "onClick", "height", "presetIndex", "border", "cursor", "title", "min", "step", "marginRight", "disabled", "as", "role"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/AddFabric.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport Select from \"react-select\";\r\nimport { FaPlus, FaTrash } from \"react-icons/fa\";\r\nimport { Row, Col, Form, <PERSON><PERSON>, Card, Alert, Spinner } from 'react-bootstrap';\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\n\r\n// Common color presets with names\r\nconst COLOR_PRESETS = [\r\n  { color: \"#000000\", name: \"Black\" },\r\n  { color: \"#FFFFFF\", name: \"White\" },\r\n  { color: \"#FF0000\", name: \"Red\" },\r\n  { color: \"#0000FF\", name: \"Blue\" },\r\n  { color: \"#008000\", name: \"Green\" },\r\n  { color: \"#FFFF00\", name: \"Yellow\" },\r\n  { color: \"#FFA500\", name: \"Orange\" },\r\n  { color: \"#800080\", name: \"Purple\" },\r\n  { color: \"#FFC0CB\", name: \"Pink\" },\r\n  { color: \"#A52A2A\", name: \"<PERSON>\" },\r\n  { color: \"#808080\", name: \"<PERSON>\" },\r\n];\r\n\r\nconst AddFabric = () => {\r\n  // State variables\r\n  const [fabricName, setFabricName] = useState(\"\");\r\n  const [selectedSupplier, setSelectedSupplier] = useState(null);\r\n  const [dateAdded, setDateAdded] = useState(\"\");\r\n  const [variants, setVariants] = useState([{ color: \"#000000\", colorName: \"Black\", totalYard: \"\", pricePerYard: \"\" }]);\r\n  const [suppliers, setSuppliers] = useState([]);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // Effect to handle sidebar state based on window size\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch suppliers\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios.get(\"http://localhost:8000/api/suppliers/\")\r\n      .then((response) => {\r\n        const supplierOptions = response.data.map((sup) => ({\r\n          value: sup.supplier_id,\r\n          label: sup.name,\r\n        }));\r\n        setSuppliers(supplierOptions);\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching suppliers:\", error);\r\n        setMessage(\"Failed to load suppliers\");\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  // Handle adding a variant\r\n  const handleAddVariant = () => {\r\n    setVariants([...variants, { color: \"#000000\", colorName: \"Black\", totalYard: \"\", pricePerYard: \"\" }]);\r\n  };\r\n\r\n  // Handle removing a variant\r\n  const handleRemoveVariant = (index) => {\r\n    const updated = variants.filter((_, i) => i !== index);\r\n    setVariants(updated);\r\n  };\r\n\r\n  // Handle variant input changes\r\n  const handleVariantChange = (index, field, value) => {\r\n    const updated = [...variants];\r\n    updated[index][field] = value;\r\n\r\n    // If color is changed, suggest a color name only if current name is empty or matches a preset\r\n    if (field === \"color\") {\r\n      const colorPreset = COLOR_PRESETS.find(preset => preset.color === value);\r\n      const currentColorName = updated[index].colorName;\r\n\r\n      // Only auto-update if the field is empty or contains a basic preset name\r\n      const isBasicPresetName = COLOR_PRESETS.some(preset => preset.name === currentColorName);\r\n\r\n      if (!currentColorName || isBasicPresetName) {\r\n        updated[index].colorName = colorPreset ? colorPreset.name : \"\";\r\n      }\r\n    }\r\n\r\n    setVariants(updated);\r\n  };\r\n\r\n  // Function to check if two colors are similar\r\n  const areColorsSimilar = (color1, color2) => {\r\n    // Convert hex to RGB\r\n    const hexToRgb = (hex) => {\r\n      const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\r\n      return result ? {\r\n        r: parseInt(result[1], 16),\r\n        g: parseInt(result[2], 16),\r\n        b: parseInt(result[3], 16)\r\n      } : null;\r\n    };\r\n\r\n    const rgb1 = hexToRgb(color1);\r\n    const rgb2 = hexToRgb(color2);\r\n\r\n    if (!rgb1 || !rgb2) return false;\r\n\r\n    // Calculate color difference using Euclidean distance\r\n    const distance = Math.sqrt(\r\n      Math.pow(rgb1.r - rgb2.r, 2) +\r\n      Math.pow(rgb1.g - rgb2.g, 2) +\r\n      Math.pow(rgb1.b - rgb2.b, 2)\r\n    );\r\n\r\n    // Colors are similar if distance is less than 50 (adjustable threshold)\r\n    return distance < 50;\r\n  };\r\n\r\n  // Submit handler\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setMessage(\"\");\r\n    setIsSubmitting(true);\r\n\r\n    // Basic validation\r\n    if (!fabricName.trim()) {\r\n      setMessage(\"Please enter a fabric name\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    if (!selectedSupplier) {\r\n      setMessage(\"Please select a supplier\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    if (!dateAdded) {\r\n      setMessage(\"Please select a date\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    // Validate variants\r\n    for (let i = 0; i < variants.length; i++) {\r\n      const variant = variants[i];\r\n\r\n      if (variant.totalYard && parseFloat(variant.totalYard) < 0) {\r\n        setMessage(`Variant ${i+1}: Total yard cannot be negative`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (variant.pricePerYard && parseFloat(variant.pricePerYard) < 0) {\r\n        setMessage(`Variant ${i+1}: Price per yard cannot be negative`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Check for similar colors without descriptive names\r\n    for (let i = 0; i < variants.length; i++) {\r\n      for (let j = i + 1; j < variants.length; j++) {\r\n        const variant1 = variants[i];\r\n        const variant2 = variants[j];\r\n\r\n        if (areColorsSimilar(variant1.color, variant2.color)) {\r\n          const name1 = variant1.colorName || \"Unnamed\";\r\n          const name2 = variant2.colorName || \"Unnamed\";\r\n\r\n          // Check if names are too generic or similar\r\n          if (name1 === name2 ||\r\n              name1 === \"Unnamed\" || name2 === \"Unnamed\" ||\r\n              COLOR_PRESETS.some(preset => preset.name === name1 || preset.name === name2)) {\r\n\r\n            const proceed = window.confirm(\r\n              `⚠️ Warning: Variants ${i+1} and ${j+1} have similar colors but generic names.\\n\\n` +\r\n              `This might cause confusion during cutting and production.\\n\\n` +\r\n              `Consider using more descriptive names like:\\n` +\r\n              `• \"Black Line\" vs \"Black Circle\"\\n` +\r\n              `• \"Navy Stripe\" vs \"Navy Solid\"\\n\\n` +\r\n              `Do you want to continue anyway?`\r\n            );\r\n\r\n            if (!proceed) {\r\n              setIsSubmitting(false);\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    try {\r\n      const defResponse = await axios.post(\"http://localhost:8000/api/fabric-definitions/\", {\r\n        fabric_name: fabricName,\r\n        supplier: selectedSupplier.value,\r\n        date_added: dateAdded,\r\n      });\r\n\r\n      if (defResponse.status === 201) {\r\n        const definitionId = defResponse.data.id;\r\n\r\n        for (let variant of variants) {\r\n          await axios.post(\"http://localhost:8000/api/fabric-variants/\", {\r\n            fabric_definition: definitionId,\r\n            color: variant.color,\r\n            color_name: variant.colorName,\r\n            total_yard: parseFloat(variant.totalYard) || 0,\r\n            price_per_yard: parseFloat(variant.pricePerYard) || 0,\r\n          });\r\n        }\r\n\r\n        setMessage(\"✅ Fabric and variants created successfully!\");\r\n        setFabricName(\"\");\r\n        setSelectedSupplier(null);\r\n        setDateAdded(\"\");\r\n        setVariants([{ color: \"#000000\", colorName: \"Black\", totalYard: \"\", pricePerYard: \"\" }]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating fabric or variants:\", error);\r\n      setMessage(\"Error creating fabric or variants.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to select a preset color\r\n  const selectPresetColor = (index, preset) => {\r\n    const updated = [...variants];\r\n    updated[index].color = preset.color;\r\n    updated[index].colorName = preset.name;\r\n    setVariants(updated);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <FaPlus className=\"me-2\" />\r\n          Add Fabric\r\n        </h2>\r\n\r\n        {message && (\r\n          <Alert\r\n            variant={message.includes(\"✅\") ? \"success\" : \"danger\"}\r\n            className=\"d-flex align-items-center\"\r\n          >\r\n            {message}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Fabric Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={fabricName}\r\n                      onChange={(e) => setFabricName(e.target.value)}\r\n                      required\r\n                      placeholder=\"Enter fabric name\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Supplier</strong></Form.Label>\r\n                    {loading ? (\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                        <span>Loading suppliers...</span>\r\n                      </div>\r\n                    ) : (\r\n                      <Select\r\n                        options={suppliers}\r\n                        value={selectedSupplier}\r\n                        onChange={setSelectedSupplier}\r\n                        placeholder=\"Select a supplier...\"\r\n                        isSearchable\r\n                      />\r\n                    )}\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Date Added</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={dateAdded}\r\n                      onChange={(e) => setDateAdded(e.target.value)}\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <h4 className=\"mt-4 mb-3 border-bottom pb-2\">Fabric Variants</h4>\r\n\r\n              {variants.map((variant, index) => (\r\n                <Card\r\n                  key={index}\r\n                  className=\"mb-3 border\"\r\n                  style={{\r\n                    borderLeft: `5px solid ${variant.color}`,\r\n                    borderRadius: \"8px\"\r\n                  }}\r\n                >\r\n                  <Card.Header className=\"d-flex justify-content-between align-items-center bg-light\">\r\n                    <h5 className=\"mb-0\">Variant #{index + 1}</h5>\r\n                    {variants.length > 1 && (\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={() => handleRemoveVariant(index)}\r\n                      >\r\n                        <FaTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    )}\r\n                  </Card.Header>\r\n                  <Card.Body>\r\n                    <Row>\r\n                      <Col md={4}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label><strong>Color</strong></Form.Label>\r\n                          <div className=\"d-flex align-items-center mb-2\">\r\n                            <Form.Control\r\n                              type=\"color\"\r\n                              value={variant.color}\r\n                              onChange={(e) => handleVariantChange(index, \"color\", e.target.value)}\r\n                              className=\"me-2\"\r\n                              style={{ width: '38px', height: '38px' }}\r\n                            />\r\n                            <Form.Control\r\n                              type=\"text\"\r\n                              placeholder=\"e.g., Black Line, Black Circle, Navy Stripe\"\r\n                              value={variant.colorName}\r\n                              onChange={(e) => handleVariantChange(index, \"colorName\", e.target.value)}\r\n                            />\r\n                          </div>\r\n                          <small className=\"text-muted mb-2 d-block\">\r\n                            💡 <strong>Tip:</strong> Use descriptive names for similar colors (e.g., \"Black Line\", \"Black Circle\") to avoid confusion during cutting\r\n                          </small>\r\n                          <div className=\"color-presets d-flex flex-wrap gap-1 mt-1\">\r\n                            {COLOR_PRESETS.map((preset, presetIndex) => (\r\n                              <div\r\n                                key={presetIndex}\r\n                                onClick={() => selectPresetColor(index, preset)}\r\n                                style={{\r\n                                  width: '20px',\r\n                                  height: '20px',\r\n                                  backgroundColor: preset.color,\r\n                                  border: variant.color === preset.color ? '2px solid #000' : '1px solid #ccc',\r\n                                  borderRadius: '4px',\r\n                                  cursor: 'pointer'\r\n                                }}\r\n                                title={preset.name}\r\n                              />\r\n                            ))}\r\n                          </div>\r\n                        </Form.Group>\r\n                      </Col>\r\n\r\n                      <Col md={4}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label><strong>Total Yard</strong></Form.Label>\r\n                          <Form.Control\r\n                            type=\"number\"\r\n                            min=\"0\"\r\n                            step=\"0.01\"\r\n                            value={variant.totalYard}\r\n                            onChange={(e) => {\r\n                              const value = e.target.value;\r\n                              // Only allow positive numbers\r\n                              if (value === '' || parseFloat(value) >= 0) {\r\n                                handleVariantChange(index, \"totalYard\", value);\r\n                              }\r\n                            }}\r\n                            placeholder=\"Enter total yards\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n\r\n                      <Col md={4}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label><strong>Price per Yard</strong></Form.Label>\r\n                          <Form.Control\r\n                            type=\"number\"\r\n                            min=\"0\"\r\n                            step=\"0.01\"\r\n                            value={variant.pricePerYard}\r\n                            onChange={(e) => {\r\n                              const value = e.target.value;\r\n                              // Only allow positive numbers\r\n                              if (value === '' || parseFloat(value) >= 0) {\r\n                                handleVariantChange(index, \"pricePerYard\", value);\r\n                              }\r\n                            }}\r\n                            placeholder=\"Enter price per yard\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    <div className=\"mt-2 p-2 bg-light rounded d-flex align-items-center\">\r\n                      <div\r\n                        style={{\r\n                          width: '20px',\r\n                          height: '20px',\r\n                          backgroundColor: variant.color,\r\n                          border: '1px solid #ccc',\r\n                          borderRadius: '4px',\r\n                          marginRight: '8px'\r\n                        }}\r\n                      ></div>\r\n                      <div>\r\n                        <strong>Preview:</strong>\r\n                        <span className={variant.colorName ? \"text-success fw-bold\" : \"text-warning\"}>\r\n                          {variant.colorName || \"⚠️ Unnamed Color\"}\r\n                        </span>\r\n                        {variant.totalYard ? ` - ${variant.totalYard} yards` : \" - No yards specified\"}\r\n                        {variant.pricePerYard ? ` - Rs. ${variant.pricePerYard}/yard` : \" - No price specified\"}\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              ))}\r\n\r\n              <div className=\"d-flex justify-content-center mb-4\">\r\n                <Button\r\n                  variant=\"outline-primary\"\r\n                  onClick={handleAddVariant}\r\n                  className=\"d-flex align-items-center\"\r\n                >\r\n                  <FaPlus className=\"me-2\" /> Add Another Variant\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Fabric'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n\r\n\r\nexport default AddFabric;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OAASC,MAAM,CAAEC,OAAO,KAAQ,gBAAgB,CAChD,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,KAAQ,iBAAiB,CAC9E,MAAO,sCAAsC,CAC7C,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAE3D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,aAAa,CAAG,CACpB,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,KAAM,CAAC,CACjC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,MAAO,CAAC,CAClC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,MAAO,CAAC,CAClC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,MAAO,CAAC,CACnC,CAED,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB;AACA,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC0B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8B,QAAQ,CAAEC,WAAW,CAAC,CAAG/B,QAAQ,CAAC,CAAC,CAAEqB,KAAK,CAAE,SAAS,CAAEW,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,EAAE,CAAEC,YAAY,CAAE,EAAG,CAAC,CAAC,CAAC,CACrH,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACuC,YAAY,CAAEC,eAAe,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACyC,aAAa,CAAEC,gBAAgB,CAAC,CAAG1C,QAAQ,CAAC2C,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAE7C;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8C,YAAY,CAAGA,CAAA,GAAM,CACzBL,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACK,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMJ,MAAM,CAACM,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA9C,SAAS,CAAC,IAAM,CACd6C,UAAU,CAAC,IAAI,CAAC,CAChB5C,KAAK,CAACgD,GAAG,CAAC,sCAAsC,CAAC,CAC9CC,IAAI,CAAEC,QAAQ,EAAK,CAClB,KAAM,CAAAC,eAAe,CAAGD,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAEC,GAAG,GAAM,CAClDC,KAAK,CAAED,GAAG,CAACE,WAAW,CACtBC,KAAK,CAAEH,GAAG,CAAClC,IACb,CAAC,CAAC,CAAC,CACHc,YAAY,CAACiB,eAAe,CAAC,CAC7BP,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACDc,KAAK,CAAEC,KAAK,EAAK,CAChBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDvB,UAAU,CAAC,0BAA0B,CAAC,CACtCQ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAiB,gBAAgB,CAAGA,CAAA,GAAM,CAC7BhC,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAE,CAAET,KAAK,CAAE,SAAS,CAAEW,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,EAAE,CAAEC,YAAY,CAAE,EAAG,CAAC,CAAC,CAAC,CACvG,CAAC,CAED;AACA,KAAM,CAAA8B,mBAAmB,CAAIC,KAAK,EAAK,CACrC,KAAM,CAAAC,OAAO,CAAGpC,QAAQ,CAACqC,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKJ,KAAK,CAAC,CACtDlC,WAAW,CAACmC,OAAO,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAI,mBAAmB,CAAGA,CAACL,KAAK,CAAEM,KAAK,CAAEd,KAAK,GAAK,CACnD,KAAM,CAAAS,OAAO,CAAG,CAAC,GAAGpC,QAAQ,CAAC,CAC7BoC,OAAO,CAACD,KAAK,CAAC,CAACM,KAAK,CAAC,CAAGd,KAAK,CAE7B;AACA,GAAIc,KAAK,GAAK,OAAO,CAAE,CACrB,KAAM,CAAAC,WAAW,CAAGpD,aAAa,CAACqD,IAAI,CAACC,MAAM,EAAIA,MAAM,CAACrD,KAAK,GAAKoC,KAAK,CAAC,CACxE,KAAM,CAAAkB,gBAAgB,CAAGT,OAAO,CAACD,KAAK,CAAC,CAACjC,SAAS,CAEjD;AACA,KAAM,CAAA4C,iBAAiB,CAAGxD,aAAa,CAACyD,IAAI,CAACH,MAAM,EAAIA,MAAM,CAACpD,IAAI,GAAKqD,gBAAgB,CAAC,CAExF,GAAI,CAACA,gBAAgB,EAAIC,iBAAiB,CAAE,CAC1CV,OAAO,CAACD,KAAK,CAAC,CAACjC,SAAS,CAAGwC,WAAW,CAAGA,WAAW,CAAClD,IAAI,CAAG,EAAE,CAChE,CACF,CAEAS,WAAW,CAACmC,OAAO,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAY,gBAAgB,CAAGA,CAACC,MAAM,CAAEC,MAAM,GAAK,CAC3C;AACA,KAAM,CAAAC,QAAQ,CAAIC,GAAG,EAAK,CACxB,KAAM,CAAAC,MAAM,CAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC,CACpE,MAAO,CAAAC,MAAM,CAAG,CACdE,CAAC,CAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAE,EAAE,CAAC,CAC1BI,CAAC,CAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAE,EAAE,CAAC,CAC1BK,CAAC,CAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAE,EAAE,CAC3B,CAAC,CAAG,IAAI,CACV,CAAC,CAED,KAAM,CAAAM,IAAI,CAAGR,QAAQ,CAACF,MAAM,CAAC,CAC7B,KAAM,CAAAW,IAAI,CAAGT,QAAQ,CAACD,MAAM,CAAC,CAE7B,GAAI,CAACS,IAAI,EAAI,CAACC,IAAI,CAAE,MAAO,MAAK,CAEhC;AACA,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,IAAI,CACxBD,IAAI,CAACE,GAAG,CAACL,IAAI,CAACJ,CAAC,CAAGK,IAAI,CAACL,CAAC,CAAE,CAAC,CAAC,CAC5BO,IAAI,CAACE,GAAG,CAACL,IAAI,CAACF,CAAC,CAAGG,IAAI,CAACH,CAAC,CAAE,CAAC,CAAC,CAC5BK,IAAI,CAACE,GAAG,CAACL,IAAI,CAACD,CAAC,CAAGE,IAAI,CAACF,CAAC,CAAE,CAAC,CAC7B,CAAC,CAED;AACA,MAAO,CAAAG,QAAQ,CAAG,EAAE,CACtB,CAAC,CAED;AACA,KAAM,CAAAI,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB3D,UAAU,CAAC,EAAE,CAAC,CACdE,eAAe,CAAC,IAAI,CAAC,CAErB;AACA,GAAI,CAAChB,UAAU,CAAC0E,IAAI,CAAC,CAAC,CAAE,CACtB5D,UAAU,CAAC,4BAA4B,CAAC,CACxCE,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CAEA,GAAI,CAACd,gBAAgB,CAAE,CACrBY,UAAU,CAAC,0BAA0B,CAAC,CACtCE,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CAEA,GAAI,CAACZ,SAAS,CAAE,CACdU,UAAU,CAAC,sBAAsB,CAAC,CAClCE,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CAEA;AACA,IAAK,GAAI,CAAA6B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGvC,QAAQ,CAACqE,MAAM,CAAE9B,CAAC,EAAE,CAAE,CACxC,KAAM,CAAA+B,OAAO,CAAGtE,QAAQ,CAACuC,CAAC,CAAC,CAE3B,GAAI+B,OAAO,CAACnE,SAAS,EAAIoE,UAAU,CAACD,OAAO,CAACnE,SAAS,CAAC,CAAG,CAAC,CAAE,CAC1DK,UAAU,CAAC,WAAW+B,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAC3D7B,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CAEA,GAAI4D,OAAO,CAAClE,YAAY,EAAImE,UAAU,CAACD,OAAO,CAAClE,YAAY,CAAC,CAAG,CAAC,CAAE,CAChEI,UAAU,CAAC,WAAW+B,CAAC,CAAC,CAAC,qCAAqC,CAAC,CAC/D7B,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CACF,CAEA;AACA,IAAK,GAAI,CAAA6B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGvC,QAAQ,CAACqE,MAAM,CAAE9B,CAAC,EAAE,CAAE,CACxC,IAAK,GAAI,CAAAiC,CAAC,CAAGjC,CAAC,CAAG,CAAC,CAAEiC,CAAC,CAAGxE,QAAQ,CAACqE,MAAM,CAAEG,CAAC,EAAE,CAAE,CAC5C,KAAM,CAAAC,QAAQ,CAAGzE,QAAQ,CAACuC,CAAC,CAAC,CAC5B,KAAM,CAAAmC,QAAQ,CAAG1E,QAAQ,CAACwE,CAAC,CAAC,CAE5B,GAAIxB,gBAAgB,CAACyB,QAAQ,CAAClF,KAAK,CAAEmF,QAAQ,CAACnF,KAAK,CAAC,CAAE,CACpD,KAAM,CAAAoF,KAAK,CAAGF,QAAQ,CAACvE,SAAS,EAAI,SAAS,CAC7C,KAAM,CAAA0E,KAAK,CAAGF,QAAQ,CAACxE,SAAS,EAAI,SAAS,CAE7C;AACA,GAAIyE,KAAK,GAAKC,KAAK,EACfD,KAAK,GAAK,SAAS,EAAIC,KAAK,GAAK,SAAS,EAC1CtF,aAAa,CAACyD,IAAI,CAACH,MAAM,EAAIA,MAAM,CAACpD,IAAI,GAAKmF,KAAK,EAAI/B,MAAM,CAACpD,IAAI,GAAKoF,KAAK,CAAC,CAAE,CAEhF,KAAM,CAAAC,OAAO,CAAGhE,MAAM,CAACiE,OAAO,CAC5B,wBAAwBvC,CAAC,CAAC,CAAC,QAAQiC,CAAC,CAAC,CAAC,6CAA6C,CACnF,+DAA+D,CAC/D,+CAA+C,CAC/C,oCAAoC,CACpC,qCAAqC,CACrC,iCACF,CAAC,CAED,GAAI,CAACK,OAAO,CAAE,CACZnE,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CACF,CACF,CACF,CACF,CAEA,GAAI,CACF,KAAM,CAAAqE,WAAW,CAAG,KAAM,CAAA3G,KAAK,CAAC4G,IAAI,CAAC,+CAA+C,CAAE,CACpFC,WAAW,CAAEvF,UAAU,CACvBwF,QAAQ,CAAEtF,gBAAgB,CAAC+B,KAAK,CAChCwD,UAAU,CAAErF,SACd,CAAC,CAAC,CAEF,GAAIiF,WAAW,CAACK,MAAM,GAAK,GAAG,CAAE,CAC9B,KAAM,CAAAC,YAAY,CAAGN,WAAW,CAACvD,IAAI,CAAC8D,EAAE,CAExC,IAAK,GAAI,CAAAhB,OAAO,GAAI,CAAAtE,QAAQ,CAAE,CAC5B,KAAM,CAAA5B,KAAK,CAAC4G,IAAI,CAAC,4CAA4C,CAAE,CAC7DO,iBAAiB,CAAEF,YAAY,CAC/B9F,KAAK,CAAE+E,OAAO,CAAC/E,KAAK,CACpBiG,UAAU,CAAElB,OAAO,CAACpE,SAAS,CAC7BuF,UAAU,CAAElB,UAAU,CAACD,OAAO,CAACnE,SAAS,CAAC,EAAI,CAAC,CAC9CuF,cAAc,CAAEnB,UAAU,CAACD,OAAO,CAAClE,YAAY,CAAC,EAAI,CACtD,CAAC,CAAC,CACJ,CAEAI,UAAU,CAAC,6CAA6C,CAAC,CACzDb,aAAa,CAAC,EAAE,CAAC,CACjBE,mBAAmB,CAAC,IAAI,CAAC,CACzBE,YAAY,CAAC,EAAE,CAAC,CAChBE,WAAW,CAAC,CAAC,CAAEV,KAAK,CAAE,SAAS,CAAEW,SAAS,CAAE,OAAO,CAAEC,SAAS,CAAE,EAAE,CAAEC,YAAY,CAAE,EAAG,CAAC,CAAC,CAAC,CAC1F,CACF,CAAE,MAAO2B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1DvB,UAAU,CAAC,oCAAoC,CAAC,CAClD,CAAC,OAAS,CACRE,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAAiF,iBAAiB,CAAGA,CAACxD,KAAK,CAAES,MAAM,GAAK,CAC3C,KAAM,CAAAR,OAAO,CAAG,CAAC,GAAGpC,QAAQ,CAAC,CAC7BoC,OAAO,CAACD,KAAK,CAAC,CAAC5C,KAAK,CAAGqD,MAAM,CAACrD,KAAK,CACnC6C,OAAO,CAACD,KAAK,CAAC,CAACjC,SAAS,CAAG0C,MAAM,CAACpD,IAAI,CACtCS,WAAW,CAACmC,OAAO,CAAC,CACtB,CAAC,CAED,mBACEjD,KAAA,CAAAE,SAAA,EAAAuG,QAAA,eACE3G,IAAA,CAACF,eAAe,GAAE,CAAC,cACnBI,KAAA,QACE0G,KAAK,CAAE,CACLC,UAAU,CAAEnF,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5CoF,KAAK,CAAE,eAAepF,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDqF,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAL,QAAA,eAEFzG,KAAA,OAAI+G,SAAS,CAAC,MAAM,CAAAN,QAAA,eAClB3G,IAAA,CAACX,MAAM,EAAC4H,SAAS,CAAC,MAAM,CAAE,CAAC,aAE7B,EAAI,CAAC,CAEJ3F,OAAO,eACNtB,IAAA,CAACJ,KAAK,EACJyF,OAAO,CAAE/D,OAAO,CAAC4F,QAAQ,CAAC,GAAG,CAAC,CAAG,SAAS,CAAG,QAAS,CACtDD,SAAS,CAAC,2BAA2B,CAAAN,QAAA,CAEpCrF,OAAO,CACH,CACR,cAEDtB,IAAA,CAACL,IAAI,EAACsH,SAAS,CAAC,gBAAgB,CAACL,KAAK,CAAE,CAAEO,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAT,QAAA,cAC3F3G,IAAA,CAACL,IAAI,CAAC0H,IAAI,EAAAV,QAAA,cACRzG,KAAA,CAACT,IAAI,EAAC6H,QAAQ,CAAEtC,YAAa,CAAA2B,QAAA,eAC3BzG,KAAA,CAACX,GAAG,EAAAoH,QAAA,eACF3G,IAAA,CAACR,GAAG,EAAC+H,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACTzG,KAAA,CAACT,IAAI,CAAC+H,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1B3G,IAAA,CAACP,IAAI,CAACgI,KAAK,EAAAd,QAAA,cAAC3G,IAAA,WAAA2G,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAY,CAAC,cACrD3G,IAAA,CAACP,IAAI,CAACiI,OAAO,EACXC,IAAI,CAAC,MAAM,CACXjF,KAAK,CAAEjC,UAAW,CAClBmH,QAAQ,CAAG3C,CAAC,EAAKvE,aAAa,CAACuE,CAAC,CAAC4C,MAAM,CAACnF,KAAK,CAAE,CAC/CoF,QAAQ,MACRC,WAAW,CAAC,mBAAmB,CAChC,CAAC,EACQ,CAAC,CACV,CAAC,cAEN/H,IAAA,CAACR,GAAG,EAAC+H,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACTzG,KAAA,CAACT,IAAI,CAAC+H,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1B3G,IAAA,CAACP,IAAI,CAACgI,KAAK,EAAAd,QAAA,cAAC3G,IAAA,WAAA2G,QAAA,CAAQ,UAAQ,CAAQ,CAAC,CAAY,CAAC,CACjD7E,OAAO,cACN5B,KAAA,QAAK+G,SAAS,CAAC,2BAA2B,CAAAN,QAAA,eACxC3G,IAAA,CAACH,OAAO,EAACmI,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAChB,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDjH,IAAA,SAAA2G,QAAA,CAAM,sBAAoB,CAAM,CAAC,EAC9B,CAAC,cAEN3G,IAAA,CAACZ,MAAM,EACL8I,OAAO,CAAE9G,SAAU,CACnBsB,KAAK,CAAE/B,gBAAiB,CACxBiH,QAAQ,CAAEhH,mBAAoB,CAC9BmH,WAAW,CAAC,sBAAsB,CAClCI,YAAY,MACb,CACF,EACS,CAAC,CACV,CAAC,EACH,CAAC,cAENnI,IAAA,CAACT,GAAG,EAAAoH,QAAA,cACF3G,IAAA,CAACR,GAAG,EAAC+H,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACTzG,KAAA,CAACT,IAAI,CAAC+H,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1B3G,IAAA,CAACP,IAAI,CAACgI,KAAK,EAAAd,QAAA,cAAC3G,IAAA,WAAA2G,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,cACpD3G,IAAA,CAACP,IAAI,CAACiI,OAAO,EACXC,IAAI,CAAC,MAAM,CACXjF,KAAK,CAAE7B,SAAU,CACjB+G,QAAQ,CAAG3C,CAAC,EAAKnE,YAAY,CAACmE,CAAC,CAAC4C,MAAM,CAACnF,KAAK,CAAE,CAC9CoF,QAAQ,MACT,CAAC,EACQ,CAAC,CACV,CAAC,CACH,CAAC,cAEN9H,IAAA,OAAIiH,SAAS,CAAC,8BAA8B,CAAAN,QAAA,CAAC,iBAAe,CAAI,CAAC,CAEhE5F,QAAQ,CAACyB,GAAG,CAAC,CAAC6C,OAAO,CAAEnC,KAAK,gBAC3BhD,KAAA,CAACP,IAAI,EAEHsH,SAAS,CAAC,aAAa,CACvBL,KAAK,CAAE,CACLwB,UAAU,CAAE,aAAa/C,OAAO,CAAC/E,KAAK,EAAE,CACxC8G,YAAY,CAAE,KAChB,CAAE,CAAAT,QAAA,eAEFzG,KAAA,CAACP,IAAI,CAAC0I,MAAM,EAACpB,SAAS,CAAC,4DAA4D,CAAAN,QAAA,eACjFzG,KAAA,OAAI+G,SAAS,CAAC,MAAM,CAAAN,QAAA,EAAC,WAAS,CAACzD,KAAK,CAAG,CAAC,EAAK,CAAC,CAC7CnC,QAAQ,CAACqE,MAAM,CAAG,CAAC,eAClBlF,KAAA,CAACR,MAAM,EACL2F,OAAO,CAAC,gBAAgB,CACxB4C,IAAI,CAAC,IAAI,CACTK,OAAO,CAAEA,CAAA,GAAMrF,mBAAmB,CAACC,KAAK,CAAE,CAAAyD,QAAA,eAE1C3G,IAAA,CAACV,OAAO,EAAC2H,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CACT,EACU,CAAC,cACd/G,KAAA,CAACP,IAAI,CAAC0H,IAAI,EAAAV,QAAA,eACRzG,KAAA,CAACX,GAAG,EAAAoH,QAAA,eACF3G,IAAA,CAACR,GAAG,EAAC+H,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACTzG,KAAA,CAACT,IAAI,CAAC+H,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1B3G,IAAA,CAACP,IAAI,CAACgI,KAAK,EAAAd,QAAA,cAAC3G,IAAA,WAAA2G,QAAA,CAAQ,OAAK,CAAQ,CAAC,CAAY,CAAC,cAC/CzG,KAAA,QAAK+G,SAAS,CAAC,gCAAgC,CAAAN,QAAA,eAC7C3G,IAAA,CAACP,IAAI,CAACiI,OAAO,EACXC,IAAI,CAAC,OAAO,CACZjF,KAAK,CAAE2C,OAAO,CAAC/E,KAAM,CACrBsH,QAAQ,CAAG3C,CAAC,EAAK1B,mBAAmB,CAACL,KAAK,CAAE,OAAO,CAAE+B,CAAC,CAAC4C,MAAM,CAACnF,KAAK,CAAE,CACrEuE,SAAS,CAAC,MAAM,CAChBL,KAAK,CAAE,CAAEE,KAAK,CAAE,MAAM,CAAEyB,MAAM,CAAE,MAAO,CAAE,CAC1C,CAAC,cACFvI,IAAA,CAACP,IAAI,CAACiI,OAAO,EACXC,IAAI,CAAC,MAAM,CACXI,WAAW,CAAC,6CAA6C,CACzDrF,KAAK,CAAE2C,OAAO,CAACpE,SAAU,CACzB2G,QAAQ,CAAG3C,CAAC,EAAK1B,mBAAmB,CAACL,KAAK,CAAE,WAAW,CAAE+B,CAAC,CAAC4C,MAAM,CAACnF,KAAK,CAAE,CAC1E,CAAC,EACC,CAAC,cACNxC,KAAA,UAAO+G,SAAS,CAAC,yBAAyB,CAAAN,QAAA,EAAC,eACtC,cAAA3G,IAAA,WAAA2G,QAAA,CAAQ,MAAI,CAAQ,CAAC,uHAC1B,EAAO,CAAC,cACR3G,IAAA,QAAKiH,SAAS,CAAC,2CAA2C,CAAAN,QAAA,CACvDtG,aAAa,CAACmC,GAAG,CAAC,CAACmB,MAAM,CAAE6E,WAAW,gBACrCxI,IAAA,QAEEsI,OAAO,CAAEA,CAAA,GAAM5B,iBAAiB,CAACxD,KAAK,CAAES,MAAM,CAAE,CAChDiD,KAAK,CAAE,CACLE,KAAK,CAAE,MAAM,CACbyB,MAAM,CAAE,MAAM,CACdpB,eAAe,CAAExD,MAAM,CAACrD,KAAK,CAC7BmI,MAAM,CAAEpD,OAAO,CAAC/E,KAAK,GAAKqD,MAAM,CAACrD,KAAK,CAAG,gBAAgB,CAAG,gBAAgB,CAC5E8G,YAAY,CAAE,KAAK,CACnBsB,MAAM,CAAE,SACV,CAAE,CACFC,KAAK,CAAEhF,MAAM,CAACpD,IAAK,EAVdiI,WAWN,CACF,CAAC,CACC,CAAC,EACI,CAAC,CACV,CAAC,cAENxI,IAAA,CAACR,GAAG,EAAC+H,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACTzG,KAAA,CAACT,IAAI,CAAC+H,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1B3G,IAAA,CAACP,IAAI,CAACgI,KAAK,EAAAd,QAAA,cAAC3G,IAAA,WAAA2G,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,cACpD3G,IAAA,CAACP,IAAI,CAACiI,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbiB,GAAG,CAAC,GAAG,CACPC,IAAI,CAAC,MAAM,CACXnG,KAAK,CAAE2C,OAAO,CAACnE,SAAU,CACzB0G,QAAQ,CAAG3C,CAAC,EAAK,CACf,KAAM,CAAAvC,KAAK,CAAGuC,CAAC,CAAC4C,MAAM,CAACnF,KAAK,CAC5B;AACA,GAAIA,KAAK,GAAK,EAAE,EAAI4C,UAAU,CAAC5C,KAAK,CAAC,EAAI,CAAC,CAAE,CAC1Ca,mBAAmB,CAACL,KAAK,CAAE,WAAW,CAAER,KAAK,CAAC,CAChD,CACF,CAAE,CACFqF,WAAW,CAAC,mBAAmB,CAChC,CAAC,EACQ,CAAC,CACV,CAAC,cAEN/H,IAAA,CAACR,GAAG,EAAC+H,EAAE,CAAE,CAAE,CAAAZ,QAAA,cACTzG,KAAA,CAACT,IAAI,CAAC+H,KAAK,EAACP,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1B3G,IAAA,CAACP,IAAI,CAACgI,KAAK,EAAAd,QAAA,cAAC3G,IAAA,WAAA2G,QAAA,CAAQ,gBAAc,CAAQ,CAAC,CAAY,CAAC,cACxD3G,IAAA,CAACP,IAAI,CAACiI,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbiB,GAAG,CAAC,GAAG,CACPC,IAAI,CAAC,MAAM,CACXnG,KAAK,CAAE2C,OAAO,CAAClE,YAAa,CAC5ByG,QAAQ,CAAG3C,CAAC,EAAK,CACf,KAAM,CAAAvC,KAAK,CAAGuC,CAAC,CAAC4C,MAAM,CAACnF,KAAK,CAC5B;AACA,GAAIA,KAAK,GAAK,EAAE,EAAI4C,UAAU,CAAC5C,KAAK,CAAC,EAAI,CAAC,CAAE,CAC1Ca,mBAAmB,CAACL,KAAK,CAAE,cAAc,CAAER,KAAK,CAAC,CACnD,CACF,CAAE,CACFqF,WAAW,CAAC,sBAAsB,CACnC,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,cAEN7H,KAAA,QAAK+G,SAAS,CAAC,qDAAqD,CAAAN,QAAA,eAClE3G,IAAA,QACE4G,KAAK,CAAE,CACLE,KAAK,CAAE,MAAM,CACbyB,MAAM,CAAE,MAAM,CACdpB,eAAe,CAAE9B,OAAO,CAAC/E,KAAK,CAC9BmI,MAAM,CAAE,gBAAgB,CACxBrB,YAAY,CAAE,KAAK,CACnB0B,WAAW,CAAE,KACf,CAAE,CACE,CAAC,cACP5I,KAAA,QAAAyG,QAAA,eACE3G,IAAA,WAAA2G,QAAA,CAAQ,UAAQ,CAAQ,CAAC,cACzB3G,IAAA,SAAMiH,SAAS,CAAE5B,OAAO,CAACpE,SAAS,CAAG,sBAAsB,CAAG,cAAe,CAAA0F,QAAA,CAC1EtB,OAAO,CAACpE,SAAS,EAAI,kBAAkB,CACpC,CAAC,CACNoE,OAAO,CAACnE,SAAS,CAAG,MAAMmE,OAAO,CAACnE,SAAS,QAAQ,CAAG,uBAAuB,CAC7EmE,OAAO,CAAClE,YAAY,CAAG,UAAUkE,OAAO,CAAClE,YAAY,OAAO,CAAG,uBAAuB,EACpF,CAAC,EACH,CAAC,EACG,CAAC,GA3HP+B,KA4HD,CACP,CAAC,cAEFlD,IAAA,QAAKiH,SAAS,CAAC,oCAAoC,CAAAN,QAAA,cACjDzG,KAAA,CAACR,MAAM,EACL2F,OAAO,CAAC,iBAAiB,CACzBiD,OAAO,CAAEtF,gBAAiB,CAC1BiE,SAAS,CAAC,2BAA2B,CAAAN,QAAA,eAErC3G,IAAA,CAACX,MAAM,EAAC4H,SAAS,CAAC,MAAM,CAAE,CAAC,uBAC7B,EAAQ,CAAC,CACN,CAAC,cAENjH,IAAA,QAAKiH,SAAS,CAAC,oCAAoC,CAAAN,QAAA,cACjD3G,IAAA,CAACN,MAAM,EACLiI,IAAI,CAAC,QAAQ,CACbtC,OAAO,CAAC,SAAS,CACjB4C,IAAI,CAAC,IAAI,CACTc,QAAQ,CAAEvH,YAAa,CACvByF,SAAS,CAAC,MAAM,CAAAN,QAAA,CAEfnF,YAAY,cACXtB,KAAA,CAAAE,SAAA,EAAAuG,QAAA,eACE3G,IAAA,CAACH,OAAO,EAACmJ,EAAE,CAAC,MAAM,CAAChB,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACgB,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAAChC,SAAS,CAAC,MAAM,CAAE,CAAC,gBAEtG,EAAE,CAAC,CAEH,eACD,CACK,CAAC,CACN,CAAC,EACF,CAAC,CACE,CAAC,CACR,CAAC,EACJ,CAAC,EACN,CAAC,CAEP,CAAC,CAID,cAAe,CAAAzG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}