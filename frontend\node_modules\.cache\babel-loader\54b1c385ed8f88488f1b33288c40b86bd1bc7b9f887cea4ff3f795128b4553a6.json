{"ast": null, "code": "import { _isFirebaseServerApp, _getProvider, getApp, _registerComponent, registerVersion, SDK_VERSION } from '@firebase/app';\nimport { FirebaseError, createMockUserToken, getModularInstance, getDefaultEmulatorHostnameAndPort } from '@firebase/util';\nimport { Component } from '@firebase/component';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n/**\n * Domain name for firebase storage.\n */\nconst DEFAULT_HOST = 'firebasestorage.googleapis.com';\n/**\n * The key in Firebase config json for the storage bucket.\n */\nconst CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nconst DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nconst DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n/**\n * 1 second\n */\nconst DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nclass StorageError extends FirebaseError {\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code, message) {\n    let status_ = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    super(prependCode(code), `Firebase Storage: ${message} (${prependCode(code)})`);\n    this.status_ = status_;\n    /**\n     * Stores custom error data unique to the `StorageError`.\n     */\n    this.customData = {\n      serverResponse: null\n    };\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n  get status() {\n    return this.status_;\n  }\n  set status(status) {\n    this.status_ = status;\n  }\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code) {\n    return prependCode(code) === this.code;\n  }\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse() {\n    return this.customData.serverResponse;\n  }\n  set serverResponse(serverResponse) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nvar StorageErrorCode;\n(function (StorageErrorCode) {\n  // Shared between all platforms\n  StorageErrorCode[\"UNKNOWN\"] = \"unknown\";\n  StorageErrorCode[\"OBJECT_NOT_FOUND\"] = \"object-not-found\";\n  StorageErrorCode[\"BUCKET_NOT_FOUND\"] = \"bucket-not-found\";\n  StorageErrorCode[\"PROJECT_NOT_FOUND\"] = \"project-not-found\";\n  StorageErrorCode[\"QUOTA_EXCEEDED\"] = \"quota-exceeded\";\n  StorageErrorCode[\"UNAUTHENTICATED\"] = \"unauthenticated\";\n  StorageErrorCode[\"UNAUTHORIZED\"] = \"unauthorized\";\n  StorageErrorCode[\"UNAUTHORIZED_APP\"] = \"unauthorized-app\";\n  StorageErrorCode[\"RETRY_LIMIT_EXCEEDED\"] = \"retry-limit-exceeded\";\n  StorageErrorCode[\"INVALID_CHECKSUM\"] = \"invalid-checksum\";\n  StorageErrorCode[\"CANCELED\"] = \"canceled\";\n  // JS specific\n  StorageErrorCode[\"INVALID_EVENT_NAME\"] = \"invalid-event-name\";\n  StorageErrorCode[\"INVALID_URL\"] = \"invalid-url\";\n  StorageErrorCode[\"INVALID_DEFAULT_BUCKET\"] = \"invalid-default-bucket\";\n  StorageErrorCode[\"NO_DEFAULT_BUCKET\"] = \"no-default-bucket\";\n  StorageErrorCode[\"CANNOT_SLICE_BLOB\"] = \"cannot-slice-blob\";\n  StorageErrorCode[\"SERVER_FILE_WRONG_SIZE\"] = \"server-file-wrong-size\";\n  StorageErrorCode[\"NO_DOWNLOAD_URL\"] = \"no-download-url\";\n  StorageErrorCode[\"INVALID_ARGUMENT\"] = \"invalid-argument\";\n  StorageErrorCode[\"INVALID_ARGUMENT_COUNT\"] = \"invalid-argument-count\";\n  StorageErrorCode[\"APP_DELETED\"] = \"app-deleted\";\n  StorageErrorCode[\"INVALID_ROOT_OPERATION\"] = \"invalid-root-operation\";\n  StorageErrorCode[\"INVALID_FORMAT\"] = \"invalid-format\";\n  StorageErrorCode[\"INTERNAL_ERROR\"] = \"internal-error\";\n  StorageErrorCode[\"UNSUPPORTED_ENVIRONMENT\"] = \"unsupported-environment\";\n})(StorageErrorCode || (StorageErrorCode = {}));\nfunction prependCode(code) {\n  return 'storage/' + code;\n}\nfunction unknown() {\n  const message = 'An unknown error occurred, please check the error payload for ' + 'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\nfunction objectNotFound(path) {\n  return new StorageError(StorageErrorCode.OBJECT_NOT_FOUND, \"Object '\" + path + \"' does not exist.\");\n}\nfunction quotaExceeded(bucket) {\n  return new StorageError(StorageErrorCode.QUOTA_EXCEEDED, \"Quota for bucket '\" + bucket + \"' exceeded, please view quota on \" + 'https://firebase.google.com/pricing/.');\n}\nfunction unauthenticated() {\n  const message = 'User is not authenticated, please authenticate using Firebase ' + 'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\nfunction unauthorizedApp() {\n  return new StorageError(StorageErrorCode.UNAUTHORIZED_APP, 'This app does not have permission to access Firebase Storage on this project.');\n}\nfunction unauthorized(path) {\n  return new StorageError(StorageErrorCode.UNAUTHORIZED, \"User does not have permission to access '\" + path + \"'.\");\n}\nfunction retryLimitExceeded() {\n  return new StorageError(StorageErrorCode.RETRY_LIMIT_EXCEEDED, 'Max retry time for operation exceeded, please try again.');\n}\nfunction canceled() {\n  return new StorageError(StorageErrorCode.CANCELED, 'User canceled the upload/download.');\n}\nfunction invalidUrl(url) {\n  return new StorageError(StorageErrorCode.INVALID_URL, \"Invalid URL '\" + url + \"'.\");\n}\nfunction invalidDefaultBucket(bucket) {\n  return new StorageError(StorageErrorCode.INVALID_DEFAULT_BUCKET, \"Invalid default bucket '\" + bucket + \"'.\");\n}\nfunction noDefaultBucket() {\n  return new StorageError(StorageErrorCode.NO_DEFAULT_BUCKET, 'No default bucket ' + \"found. Did you set the '\" + CONFIG_STORAGE_BUCKET_KEY + \"' property when initializing the app?\");\n}\nfunction cannotSliceBlob() {\n  return new StorageError(StorageErrorCode.CANNOT_SLICE_BLOB, 'Cannot slice blob for upload. Please retry the upload.');\n}\nfunction serverFileWrongSize() {\n  return new StorageError(StorageErrorCode.SERVER_FILE_WRONG_SIZE, 'Server recorded incorrect upload file size, please retry the upload.');\n}\nfunction noDownloadURL() {\n  return new StorageError(StorageErrorCode.NO_DOWNLOAD_URL, 'The given file does not have any download URLs.');\n}\nfunction missingPolyFill(polyFill) {\n  return new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`);\n}\n/**\n * @internal\n */\nfunction invalidArgument(message) {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\nfunction appDeleted() {\n  return new StorageError(StorageErrorCode.APP_DELETED, 'The Firebase app was deleted.');\n}\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nfunction invalidRootOperation(name) {\n  return new StorageError(StorageErrorCode.INVALID_ROOT_OPERATION, \"The operation '\" + name + \"' cannot be performed on a root reference, create a non-root \" + \"reference using child, such as .child('file.png').\");\n}\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nfunction invalidFormat(format, message) {\n  return new StorageError(StorageErrorCode.INVALID_FORMAT, \"String does not match format '\" + format + \"': \" + message);\n}\n/**\n * @param message - A message describing the internal error.\n */\nfunction internalError(message) {\n  throw new StorageError(StorageErrorCode.INTERNAL_ERROR, 'Internal error: ' + message);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nclass Location {\n  constructor(bucket, path) {\n    this.bucket = bucket;\n    this.path_ = path;\n  }\n  get path() {\n    return this.path_;\n  }\n  get isRoot() {\n    return this.path.length === 0;\n  }\n  fullServerUrl() {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n  bucketOnlyServerUrl() {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n  static makeFromBucketSpec(bucketString, host) {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n  static makeFromUrl(url, host) {\n    let location = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n    function gsModify(loc) {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = {\n      bucket: 1,\n      path: 3\n    };\n    function httpModify(loc) {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(`^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`, 'i');\n    const firebaseStorageIndices = {\n      bucket: 1,\n      path: 3\n    };\n    const cloudStorageHost = host === DEFAULT_HOST ? '(?:storage.googleapis.com|storage.cloud.google.com)' : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(`^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`, 'i');\n    const cloudStorageIndices = {\n      bucket: 1,\n      path: 2\n    };\n    const groups = [{\n      regex: gsRegex,\n      indices: gsIndices,\n      postModify: gsModify\n    }, {\n      regex: firebaseStorageRegExp,\n      indices: firebaseStorageIndices,\n      postModify: httpModify\n    }, {\n      regex: cloudStorageRegExp,\n      indices: cloudStorageIndices,\n      postModify: httpModify\n    }];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n\n/**\n * A request whose promise always fails.\n */\nclass FailRequest {\n  constructor(error) {\n    this.promise_ = Promise.reject(error);\n  }\n  /** @inheritDoc */\n  getPromise() {\n    return this.promise_;\n  }\n  /** @inheritDoc */\n  cancel() {\n    let _appDelete = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nfunction start(doRequest,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nbackoffCompleteCb, timeout) {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n  function canceled() {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n  function triggerCallback() {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n  function callWithDelay(millis) {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n  function clearGlobalTimeout() {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n  function responseHandler(success) {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n  function stop(wasTimeout) {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nfunction stop(id) {\n  id(false);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isJustDef(p) {\n  return p !== void 0;\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isFunction(p) {\n  return typeof p === 'function';\n}\nfunction isNonArrayObject(p) {\n  return typeof p === 'object' && !Array.isArray(p);\n}\nfunction isString(p) {\n  return typeof p === 'string' || p instanceof String;\n}\nfunction isNativeBlob(p) {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\nfunction isNativeBlobDefined() {\n  return typeof Blob !== 'undefined';\n}\nfunction validateNumber(argument, minValue, maxValue, value) {\n  if (value < minValue) {\n    throw invalidArgument(`Invalid value for '${argument}'. Expected ${minValue} or greater.`);\n  }\n  if (value > maxValue) {\n    throw invalidArgument(`Invalid value for '${argument}'. Expected ${maxValue} or less.`);\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction makeUrl(urlPart, host, protocol) {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\nfunction makeQueryString(params) {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nvar ErrorCode;\n(function (ErrorCode) {\n  ErrorCode[ErrorCode[\"NO_ERROR\"] = 0] = \"NO_ERROR\";\n  ErrorCode[ErrorCode[\"NETWORK_ERROR\"] = 1] = \"NETWORK_ERROR\";\n  ErrorCode[ErrorCode[\"ABORT\"] = 2] = \"ABORT\";\n})(ErrorCode || (ErrorCode = {}));\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nfunction isRetryStatusCode(status, additionalRetryCodes) {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n  // Request Timeout: web server didn't receive full request in time.\n  408,\n  // Too Many Requests: you're getting rate-limited, basically.\n  429];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest {\n  constructor(url_, method_, headers_, body_, successCodes_, additionalRetryCodes_, callback_, errorCallback_, timeout_, progressCallback_, connectionFactory_) {\n    let retry = arguments.length > 11 && arguments[11] !== undefined ? arguments[11] : true;\n    this.url_ = url_;\n    this.method_ = method_;\n    this.headers_ = headers_;\n    this.body_ = body_;\n    this.successCodes_ = successCodes_;\n    this.additionalRetryCodes_ = additionalRetryCodes_;\n    this.callback_ = callback_;\n    this.errorCallback_ = errorCallback_;\n    this.timeout_ = timeout_;\n    this.progressCallback_ = progressCallback_;\n    this.connectionFactory_ = connectionFactory_;\n    this.retry = retry;\n    this.pendingConnection_ = null;\n    this.backoffId_ = null;\n    this.canceled_ = false;\n    this.appDelete_ = false;\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n  /**\n   * Actually starts the retry loop.\n   */\n  start_() {\n    const doTheRequest = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n      const progressListener = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection.send(this.url_, this.method_, this.body_, this.headers_).then(() => {\n        if (this.progressCallback_ !== null) {\n          connection.removeUploadProgressListener(progressListener);\n        }\n        this.pendingConnection_ = null;\n        const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n        const status = connection.getStatus();\n        if (!hitServer || isRetryStatusCode(status, this.additionalRetryCodes_) && this.retry) {\n          const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n          backoffCallback(false, new RequestEndStatus(false, null, wasCanceled));\n          return;\n        }\n        const successCode = this.successCodes_.indexOf(status) !== -1;\n        backoffCallback(true, new RequestEndStatus(successCode, connection));\n      });\n    };\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n  /** @inheritDoc */\n  getPromise() {\n    return this.promise_;\n  }\n  /** @inheritDoc */\n  cancel(appDelete) {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nclass RequestEndStatus {\n  constructor(wasSuccessCode, connection, canceled) {\n    this.wasSuccessCode = wasSuccessCode;\n    this.connection = connection;\n    this.canceled = !!canceled;\n  }\n}\nfunction addAuthHeader_(headers, authToken) {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\nfunction addVersionHeader_(headers, firebaseVersion) {\n  headers['X-Firebase-Storage-Version'] = 'webjs/' + (firebaseVersion !== null && firebaseVersion !== void 0 ? firebaseVersion : 'AppManager');\n}\nfunction addGmpidHeader_(headers, appId) {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\nfunction addAppCheckHeader_(headers, appCheckToken) {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\nfunction makeRequest(requestInfo, appId, authToken, appCheckToken, requestFactory, firebaseVersion) {\n  let retry = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : true;\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest(url, requestInfo.method, headers, requestInfo.body, requestInfo.successCodes, requestInfo.additionalRetryCodes, requestInfo.handler, requestInfo.errorHandler, requestInfo.timeout, requestInfo.progressCallback, requestFactory, retry);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getBlobBuilder() {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nfunction getBlob$1() {\n  const BlobBuilder = getBlobBuilder();\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, \"This browser doesn't seem to support creating Blobs\");\n    }\n  }\n}\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nfunction sliceBlob(blob, start, end) {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Converts a Base64 encoded string to a binary string. */\nfunction decodeBase64(encoded) {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nconst StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n};\nclass StringData {\n  constructor(data, contentType) {\n    this.data = data;\n    this.contentType = contentType || null;\n  }\n}\n/**\n * @internal\n */\nfunction dataFromString(format, stringData) {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(dataURLBytes_(stringData), dataURLContentType_(stringData));\n    // do nothing\n  }\n  // assert(false);\n  throw unknown();\n}\nfunction utf8Bytes_(value) {\n  const b = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | c >> 6, 128 | c & 63);\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid = i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | (hi & 1023) << 10 | lo & 1023;\n            b.push(240 | c >> 18, 128 | c >> 12 & 63, 128 | c >> 6 & 63, 128 | c & 63);\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | c >> 12, 128 | c >> 6 & 63, 128 | c & 63);\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\nfunction percentEncodedBytes_(value) {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\nfunction base64Bytes_(format, value) {\n  switch (format) {\n    case StringFormat.BASE64:\n      {\n        const hasMinus = value.indexOf('-') !== -1;\n        const hasUnder = value.indexOf('_') !== -1;\n        if (hasMinus || hasUnder) {\n          const invalidChar = hasMinus ? '-' : '_';\n          throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64url encoded?\");\n        }\n        break;\n      }\n    case StringFormat.BASE64URL:\n      {\n        const hasPlus = value.indexOf('+') !== -1;\n        const hasSlash = value.indexOf('/') !== -1;\n        if (hasPlus || hasSlash) {\n          const invalidChar = hasPlus ? '+' : '/';\n          throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\");\n        }\n        value = value.replace(/-/g, '+').replace(/_/g, '/');\n        break;\n      }\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if (e.message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\nclass DataURLParts {\n  constructor(dataURL) {\n    this.base64 = false;\n    this.contentType = null;\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(StringFormat.DATA_URL, \"Must be formatted 'data:[<mediatype>][;base64],<data>\");\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64 ? middle.substring(0, middle.length - ';base64'.length) : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\nfunction dataURLBytes_(dataUrl) {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\nfunction dataURLContentType_(dataUrl) {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\nfunction endsWith(s, end) {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n  return s.substring(s.length - end.length) === end;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nclass FbsBlob {\n  constructor(data, elideCopy) {\n    let size = 0;\n    let blobType = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data;\n      size = data.size;\n      blobType = data.type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n  size() {\n    return this.size_;\n  }\n  type() {\n    return this.type_;\n  }\n  slice(startByte, endByte) {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(this.data_.buffer, startByte, endByte - startByte);\n      return new FbsBlob(slice, true);\n    }\n  }\n  static getBlob() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    if (isNativeBlobDefined()) {\n      const blobby = args.map(val => {\n        if (val instanceof FbsBlob) {\n          return val.data_;\n        } else {\n          return val;\n        }\n      });\n      return new FbsBlob(getBlob$1.apply(null, blobby));\n    } else {\n      const uint8Arrays = args.map(val => {\n        if (isString(val)) {\n          return dataFromString(StringFormat.RAW, val).data;\n        } else {\n          // Blobs don't exist, so this has to be a Uint8Array.\n          return val.data_;\n        }\n      });\n      let finalLength = 0;\n      uint8Arrays.forEach(array => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach(array => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n  uploadData() {\n    return this.data_;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nfunction jsonObjectOrNull(s) {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n/**\n * @return Null if the path is already at the root.\n */\nfunction parent(path) {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\nfunction child(path, childPath) {\n  const canonicalChildPath = childPath.split('/').filter(component => component.length > 0).join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nfunction lastComponent(path) {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction noXform_(metadata, value) {\n  return value;\n}\nclass Mapping {\n  constructor(server, local, writable, xform) {\n    this.server = server;\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\nlet mappings_ = null;\nfunction xformPath(fullPath) {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\nfunction getMappings() {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings = [];\n  mappings.push(new Mapping('bucket'));\n  mappings.push(new Mapping('generation'));\n  mappings.push(new Mapping('metageneration'));\n  mappings.push(new Mapping('name', 'fullPath', true));\n  function mappingsXformPath(_metadata, fullPath) {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(_metadata, size) {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping('timeCreated'));\n  mappings.push(new Mapping('updated'));\n  mappings.push(new Mapping('md5Hash', null, true));\n  mappings.push(new Mapping('cacheControl', null, true));\n  mappings.push(new Mapping('contentDisposition', null, true));\n  mappings.push(new Mapping('contentEncoding', null, true));\n  mappings.push(new Mapping('contentLanguage', null, true));\n  mappings.push(new Mapping('contentType', null, true));\n  mappings.push(new Mapping('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\nfunction addRef(metadata, service) {\n  function generateRef() {\n    const bucket = metadata['bucket'];\n    const path = metadata['fullPath'];\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', {\n    get: generateRef\n  });\n}\nfunction fromResource(service, resource, mappings) {\n  const metadata = {};\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = mapping.xform(metadata, resource[mapping.server]);\n  }\n  addRef(metadata, service);\n  return metadata;\n}\nfunction fromResourceString(service, resourceString, mappings) {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj;\n  return fromResource(service, resource, mappings);\n}\nfunction downloadUrlFromResourceString(metadata, resourceString, host, protocol) {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens = obj['downloadTokens'];\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map(token => {\n    const bucket = metadata['bucket'];\n    const path = metadata['fullPath'];\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\nfunction toResourceString(metadata, mappings) {\n  const resource = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\nfunction fromBackendResponse(service, bucket, resource) {\n  const listResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(new Location(bucket, pathWithoutTrailingSlash));\n      listResult.prefixes.push(reference);\n    }\n  }\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(new Location(bucket, item['name']));\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\nfunction fromResponseString(service, bucket, resourceString) {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj;\n  return fromBackendResponse(service, bucket, resource);\n}\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nclass RequestInfo {\n  constructor(url, method,\n  /**\n   * Returns the value with which to resolve the request's promise. Only called\n   * if the request is successful. Throw from this function to reject the\n   * returned Request's promise with the thrown error.\n   * Note: The XhrIo passed to this function may be reused after this callback\n   * returns. Do not keep a reference to it in any way.\n   */\n  handler, timeout) {\n    this.url = url;\n    this.method = method;\n    this.handler = handler;\n    this.timeout = timeout;\n    this.urlParams = {};\n    this.headers = {};\n    this.body = null;\n    this.errorHandler = null;\n    /**\n     * Called with the current number of bytes uploaded and total size (-1 if not\n     * computable) of the request body (i.e. used to report upload progress).\n     */\n    this.progressCallback = null;\n    this.successCodes = [200];\n    this.additionalRetryCodes = [];\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nfunction handlerCheck(cndn) {\n  if (!cndn) {\n    throw unknown();\n  }\n}\nfunction metadataHandler(service, mappings) {\n  function handler(xhr, text) {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata;\n  }\n  return handler;\n}\nfunction listHandler(service, bucket) {\n  function handler(xhr, text) {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult;\n  }\n  return handler;\n}\nfunction downloadUrlHandler(service, mappings) {\n  function handler(xhr, text) {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(metadata, text, service.host, service._protocol);\n  }\n  return handler;\n}\nfunction sharedErrorHandler(location) {\n  function errorHandler(xhr, err) {\n    let newErr;\n    if (xhr.getStatus() === 401) {\n      if (\n      // This exact message string is the only consistent part of the\n      // server's error response that identifies it as an App Check error.\n      xhr.getErrorText().includes('Firebase App Check token is invalid')) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\nfunction objectErrorHandler(location) {\n  const shared = sharedErrorHandler(location);\n  function errorHandler(xhr, err) {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\nfunction getMetadata$2(service, location, mappings) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction list$2(service, location, delimiter, pageToken, maxResults) {\n  const urlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, listHandler(service, location.bucket), timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\nfunction getBytes$1(service, location, maxDownloadSizeBytes) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, (_, data) => data, timeout);\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\nfunction getDownloadUrl(service, location, mappings) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, downloadUrlHandler(service, mappings), timeout);\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction updateMetadata$2(service, location, metadata, mappings) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = {\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction deleteObject$2(service, location) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n  function handler(_xhr, _text) {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction determineContentType_(metadata, blob) {\n  return metadata && metadata['contentType'] || blob && blob.type() || 'application/octet-stream';\n}\nfunction metadataForUpload_(location, blob, metadata) {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nfunction multipartUpload(service, location, mappings, blob, metadata) {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n  function genBoundary() {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart = '--' + boundary + '\\r\\n' + 'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' + metadataString + '\\r\\n--' + boundary + '\\r\\n' + 'Content-Type: ' + metadata_['contentType'] + '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams = {\n    name: metadata_['fullPath']\n  };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nclass ResumableUploadStatus {\n  constructor(current, total, finalized, metadata) {\n    this.current = current;\n    this.total = total;\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\nfunction checkResumeHeader_(xhr, allowed) {\n  let status = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status;\n}\nfunction createResumableUpload(service, location, mappings, blob, metadata) {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams = {\n    name: metadataForUpload['fullPath']\n  };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType'],\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n  function handler(xhr) {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nfunction getResumableUploadStatus(service, location, url, blob) {\n  const headers = {\n    'X-Goog-Upload-Command': 'query'\n  };\n  function handler(xhr) {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nconst RESUMABLE_UPLOAD_CHUNK_SIZE = 256 * 1024;\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nfunction continueResumableUpload(location, service, url, blob, chunkSize, mappings, status, progressCallback) {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  function handler(xhr, text) {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(newCurrent, size, uploadStatus === 'final', metadata);\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An event that is triggered on a task.\n * @internal\n */\nconst TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nconst TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n  /** The task failed with an error. */\n  ERROR: 'error'\n};\nfunction taskStateFromInternalTaskState(state) {\n  switch (state) {\n    case \"running\" /* InternalTaskState.RUNNING */:\n    case \"pausing\" /* InternalTaskState.PAUSING */:\n    case \"canceling\" /* InternalTaskState.CANCELING */:\n      return TaskState.RUNNING;\n    case \"paused\" /* InternalTaskState.PAUSED */:\n      return TaskState.PAUSED;\n    case \"success\" /* InternalTaskState.SUCCESS */:\n      return TaskState.SUCCESS;\n    case \"canceled\" /* InternalTaskState.CANCELED */:\n      return TaskState.CANCELED;\n    case \"error\" /* InternalTaskState.ERROR */:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Observer {\n  constructor(nextOrObserver, error, complete) {\n    const asFunctions = isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver;\n      this.error = error !== null && error !== void 0 ? error : undefined;\n      this.complete = complete !== null && complete !== void 0 ? complete : undefined;\n    } else {\n      const observer = nextOrObserver;\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(f) {\n  return function () {\n    for (var _len5 = arguments.length, argsToForward = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      argsToForward[_key5] = arguments[_key5];\n    }\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride = null;\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nclass XhrConnection {\n  constructor() {\n    this.sent_ = false;\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n  send(url, method, body, headers) {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n  getErrorCode() {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n  getStatus() {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n  getResponse() {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n  getErrorText() {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n  /** Aborts the request. */\n  abort() {\n    this.xhr_.abort();\n  }\n  getResponseHeader(header) {\n    return this.xhr_.getResponseHeader(header);\n  }\n  addUploadProgressListener(listener) {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n  removeUploadProgressListener(listener) {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\nclass XhrTextConnection extends XhrConnection {\n  initXhr() {\n    this.xhr_.responseType = 'text';\n  }\n}\nfunction newTextConnection() {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\nclass XhrBytesConnection extends XhrConnection {\n  initXhr() {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\nfunction newBytesConnection() {\n  return new XhrBytesConnection();\n}\nclass XhrBlobConnection extends XhrConnection {\n  initXhr() {\n    this.xhr_.responseType = 'blob';\n  }\n}\nfunction newBlobConnection() {\n  return new XhrBlobConnection();\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nclass UploadTask {\n  isExponentialBackoffExpired() {\n    return this.sleepTime > this.maxSleepTime;\n  }\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref, blob) {\n    let metadata = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    /**\n     * Number of bytes transferred so far.\n     */\n    this._transferred = 0;\n    this._needToFetchStatus = false;\n    this._needToFetchMetadata = false;\n    this._observers = [];\n    this._error = undefined;\n    this._uploadUrl = undefined;\n    this._request = undefined;\n    this._chunkMultiplier = 1;\n    this._resolve = undefined;\n    this._reject = undefined;\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = \"running\" /* InternalTaskState.RUNNING */;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(this.sleepTime * 2, DEFAULT_MIN_SLEEP_TIME_MILLIS);\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(\"error\" /* InternalTaskState.ERROR */);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(\"error\" /* InternalTaskState.ERROR */);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n  _makeProgressCallback() {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n  _shouldDoResumable(blob) {\n    return blob.size() > 256 * 1024;\n  }\n  _start() {\n    if (this._state !== \"running\" /* InternalTaskState.RUNNING */) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n  _resolveToken(callback) {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([this._ref.storage._getAuthToken(), this._ref.storage._getAppCheckToken()]).then(_ref => {\n      let [authToken, appCheckToken] = _ref;\n      switch (this._state) {\n        case \"running\" /* InternalTaskState.RUNNING */:\n          callback(authToken, appCheckToken);\n          break;\n        case \"canceling\" /* InternalTaskState.CANCELING */:\n          this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n          break;\n        case \"pausing\" /* InternalTaskState.PAUSING */:\n          this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n          break;\n      }\n    });\n  }\n  // TODO(andysoto): assert false\n  _createResumable() {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n      const createRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = createRequest;\n      createRequest.getPromise().then(url => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n  _fetchStatus() {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(this._ref.storage, this._ref._location, url, this._blob);\n      const statusRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n  _continueUpload() {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(this._transferred, this._blob.size());\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(this._ref._location, this._ref.storage, url, this._blob, chunkSize, this._mappings, status, this._makeProgressCallback());\n      } catch (e) {\n        this._error = e;\n        this._transition(\"error\" /* InternalTaskState.ERROR */);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken, /*retry=*/false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then(newStatus => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n  _increaseMultiplier() {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n  _fetchMetadata() {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata$2(this._ref.storage, this._ref._location, this._mappings);\n      const metadataRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n      }, this._metadataErrorHandler);\n    });\n  }\n  _oneShotUpload() {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n      const multipartRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n      }, this._errorHandler);\n    });\n  }\n  _updateProgress(transferred) {\n    const old = this._transferred;\n    this._transferred = transferred;\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n  _transition(state) {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case \"canceling\" /* InternalTaskState.CANCELING */:\n      case \"pausing\" /* InternalTaskState.PAUSING */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case \"running\" /* InternalTaskState.RUNNING */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === \"paused\" /* InternalTaskState.PAUSED */;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case \"paused\" /* InternalTaskState.PAUSED */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case \"canceled\" /* InternalTaskState.CANCELED */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case \"error\" /* InternalTaskState.ERROR */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case \"success\" /* InternalTaskState.SUCCESS */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n    }\n  }\n  completeTransitions_() {\n    switch (this._state) {\n      case \"pausing\" /* InternalTaskState.PAUSING */:\n        this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n        break;\n      case \"canceling\" /* InternalTaskState.CANCELING */:\n        this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n        break;\n      case \"running\" /* InternalTaskState.RUNNING */:\n        this._start();\n        break;\n    }\n  }\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot() {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata,\n      task: this,\n      ref: this._ref\n    };\n  }\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(type, nextOrObserver, error, completed) {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(nextOrObserver || undefined, error || undefined, completed || undefined);\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then(onFulfilled, onRejected) {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then(onFulfilled, onRejected);\n  }\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch(onRejected) {\n    return this.then(null, onRejected);\n  }\n  /**\n   * Adds the given observer.\n   */\n  _addObserver(observer) {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n  /**\n   * Removes the given observer.\n   */\n  _removeObserver(observer) {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n  _notifyObservers() {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n  _finishPromise() {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          async(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject;\n          async(toCall.bind(null, this._error))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n  _notifyObserver(observer) {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          async(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          async(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          async(observer.error.bind(observer, this._error))();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          async(observer.error.bind(observer, this._error))();\n        }\n    }\n  }\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume() {\n    const valid = this._state === \"paused\" /* InternalTaskState.PAUSED */ || this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n    if (valid) {\n      this._transition(\"running\" /* InternalTaskState.RUNNING */);\n    }\n    return valid;\n  }\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause() {\n    const valid = this._state === \"running\" /* InternalTaskState.RUNNING */;\n    if (valid) {\n      this._transition(\"pausing\" /* InternalTaskState.PAUSING */);\n    }\n    return valid;\n  }\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel() {\n    const valid = this._state === \"running\" /* InternalTaskState.RUNNING */ || this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n    if (valid) {\n      this._transition(\"canceling\" /* InternalTaskState.CANCELING */);\n    }\n    return valid;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nclass Reference {\n  constructor(_service, location) {\n    this._service = _service;\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString() {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n  _newRef(service, location) {\n    return new Reference(service, location);\n  }\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root() {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket() {\n    return this._location.bucket;\n  }\n  /**\n   * The full path of this object.\n   */\n  get fullPath() {\n    return this._location.path;\n  }\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name() {\n    return lastComponent(this._location.path);\n  }\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage() {\n    return this._service;\n  }\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent() {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name) {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nfunction getBytesInternal(ref, maxDownloadSizeBytes) {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n  return ref.storage.makeRequestWithTokens(requestInfo, newBytesConnection).then(bytes => maxDownloadSizeBytes !== undefined ?\n  // GCS may not honor the Range header for small files\n  bytes.slice(0, maxDownloadSizeBytes) : bytes);\n}\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nfunction getBlobInternal(ref, maxDownloadSizeBytes) {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n  return ref.storage.makeRequestWithTokens(requestInfo, newBlobConnection).then(blob => maxDownloadSizeBytes !== undefined ?\n  // GCS may not honor the Range header for small files\n  blob.slice(0, maxDownloadSizeBytes) : blob);\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes$1(ref, data, metadata) {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(ref.storage, ref._location, getMappings(), new FbsBlob(data, true), metadata);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection).then(finalMetadata => {\n    return {\n      metadata: finalMetadata,\n      ref\n    };\n  });\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable$1(ref, data, metadata) {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString$1(ref, value) {\n  let format = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : StringFormat.RAW;\n  let metadata = arguments.length > 3 ? arguments[3] : undefined;\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = Object.assign({}, metadata);\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType;\n  }\n  return uploadBytes$1(ref, data.data, metadataClone);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll$1(ref) {\n  const accumulator = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(ref, accumulator, pageToken) {\n  const opt = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list$1(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list$1(ref, options) {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber('options.maxResults', /* minValue= */1, /* maxValue= */1000, options.maxResults);\n    }\n  }\n  const op = options || {};\n  const requestInfo = list$2(ref.storage, ref._location, /*delimiter= */'/', op.pageToken, op.maxResults);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nfunction getMetadata$1(ref) {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = getMetadata$2(ref.storage, ref._location, getMappings());\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nfunction updateMetadata$1(ref, metadata) {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = updateMetadata$2(ref.storage, ref._location, metadata, getMappings());\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL$1(ref) {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = getDownloadUrl(ref.storage, ref._location, getMappings());\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection).then(url => {\n    if (url === null) {\n      throw noDownloadURL();\n    }\n    return url;\n  });\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject$1(ref) {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = deleteObject$2(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nfunction _getChild$1(ref, childPath) {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isUrl(path) {\n  return /^[A-Za-z]+:\\/\\//.test(path);\n}\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service, url) {\n  return new Reference(service, url);\n}\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(ref, path) {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild$1(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\nfunction ref$1(serviceOrRef, pathOrUrl) {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument('To use ref(service, url), the first argument must be a Storage instance.');\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\nfunction extractBucket(host, config) {\n  const bucketString = config === null || config === void 0 ? void 0 : config[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\nfunction connectStorageEmulator$1(storage, host, port) {\n  let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  storage.host = `${host}:${port}`;\n  storage._protocol = 'http';\n  const {\n    mockUserToken\n  } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken = typeof mockUserToken === 'string' ? mockUserToken : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nclass FirebaseStorageImpl {\n  constructor(\n  /**\n   * FirebaseApp associated with this StorageService instance.\n   */\n  app, _authProvider,\n  /**\n   * @internal\n   */\n  _appCheckProvider,\n  /**\n   * @internal\n   */\n  _url, _firebaseVersion) {\n    this.app = app;\n    this._authProvider = _authProvider;\n    this._appCheckProvider = _appCheckProvider;\n    this._url = _url;\n    this._firebaseVersion = _firebaseVersion;\n    this._bucket = null;\n    /**\n     * This string can be in the formats:\n     * - host\n     * - host:port\n     */\n    this._host = DEFAULT_HOST;\n    this._protocol = 'https';\n    this._appId = null;\n    this._deleted = false;\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host() {\n    return this._host;\n  }\n  set host(host) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime() {\n    return this._maxUploadRetryTime;\n  }\n  set maxUploadRetryTime(time) {\n    validateNumber('time', /* minValue=*/0, /* maxValue= */Number.POSITIVE_INFINITY, time);\n    this._maxUploadRetryTime = time;\n  }\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime() {\n    return this._maxOperationRetryTime;\n  }\n  set maxOperationRetryTime(time) {\n    validateNumber('time', /* minValue=*/0, /* maxValue= */Number.POSITIVE_INFINITY, time);\n    this._maxOperationRetryTime = time;\n  }\n  async _getAuthToken() {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({\n      optional: true\n    });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n  async _getAppCheckToken() {\n    if (_isFirebaseServerApp(this.app) && this.app.settings.appCheckToken) {\n      return this.app.settings.appCheckToken;\n    }\n    const appCheck = this._appCheckProvider.getImmediate({\n      optional: true\n    });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete() {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc) {\n    return new Reference(this, loc);\n  }\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest(requestInfo, requestFactory, authToken, appCheckToken) {\n    let retry = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    if (!this._deleted) {\n      const request = makeRequest(requestInfo, this._appId, authToken, appCheckToken, requestFactory, this._firebaseVersion, retry);\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(() => this._requests.delete(request), () => this._requests.delete(request));\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n  async makeRequestWithTokens(requestInfo, requestFactory) {\n    const [authToken, appCheckToken] = await Promise.all([this._getAuthToken(), this._getAppCheckToken()]);\n    return this._makeRequest(requestInfo, requestFactory, authToken, appCheckToken).getPromise();\n  }\n}\nconst name = \"@firebase/storage\";\nconst version = \"0.13.7\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage';\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nfunction getBytes(ref, maxDownloadSizeBytes) {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref, maxDownloadSizeBytes);\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes(ref, data, metadata) {\n  ref = getModularInstance(ref);\n  return uploadBytes$1(ref, data, metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString(ref, value, format, metadata) {\n  ref = getModularInstance(ref);\n  return uploadString$1(ref, value, format, metadata);\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable(ref, data, metadata) {\n  ref = getModularInstance(ref);\n  return uploadBytesResumable$1(ref, data, metadata);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nfunction getMetadata(ref) {\n  ref = getModularInstance(ref);\n  return getMetadata$1(ref);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nfunction updateMetadata(ref, metadata) {\n  ref = getModularInstance(ref);\n  return updateMetadata$1(ref, metadata);\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list(ref, options) {\n  ref = getModularInstance(ref);\n  return list$1(ref, options);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll(ref) {\n  ref = getModularInstance(ref);\n  return listAll$1(ref);\n}\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL(ref) {\n  ref = getModularInstance(ref);\n  return getDownloadURL$1(ref);\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject(ref) {\n  ref = getModularInstance(ref);\n  return deleteObject$1(ref);\n}\nfunction ref(serviceOrRef, pathOrUrl) {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return ref$1(serviceOrRef, pathOrUrl);\n}\n/**\n * @internal\n */\nfunction _getChild(ref, childPath) {\n  return _getChild$1(ref, childPath);\n}\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nfunction getStorage() {\n  let app = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getApp();\n  let bucketUrl = arguments.length > 1 ? arguments[1] : undefined;\n  app = getModularInstance(app);\n  const storageProvider = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nfunction connectStorageEmulator(storage, host, port) {\n  let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  connectStorageEmulator$1(storage, host, port, options);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\nfunction getBlob(ref, maxDownloadSizeBytes) {\n  ref = getModularInstance(ref);\n  return getBlobInternal(ref, maxDownloadSizeBytes);\n}\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nfunction getStream(ref, maxDownloadSizeBytes) {\n  throw new Error('getStream() is only supported by NodeJS builds');\n}\n\n/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\nfunction factory(container, _ref2) {\n  let {\n    instanceIdentifier: url\n  } = _ref2;\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n  return new FirebaseStorageImpl(app, authProvider, appCheckProvider, url, SDK_VERSION);\n}\nfunction registerStorage() {\n  _registerComponent(new Component(STORAGE_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '');\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n}\nregisterStorage();\nexport { StorageError, StorageErrorCode, StringFormat, FbsBlob as _FbsBlob, Location as _Location, TaskEvent as _TaskEvent, TaskState as _TaskState, UploadTask as _UploadTask, dataFromString as _dataFromString, _getChild, invalidArgument as _invalidArgument, invalidRootOperation as _invalidRootOperation, connectStorageEmulator, deleteObject, getBlob, getBytes, getDownloadURL, getMetadata, getStorage, getStream, list, listAll, ref, updateMetadata, uploadBytes, uploadBytesResumable, uploadString };", "map": {"version": 3, "names": ["DEFAULT_HOST", "CONFIG_STORAGE_BUCKET_KEY", "DEFAULT_MAX_OPERATION_RETRY_TIME", "DEFAULT_MAX_UPLOAD_RETRY_TIME", "DEFAULT_MIN_SLEEP_TIME_MILLIS", "StorageError", "FirebaseError", "constructor", "code", "message", "status_", "arguments", "length", "undefined", "prependCode", "customData", "serverResponse", "_baseMessage", "Object", "setPrototypeOf", "prototype", "status", "_codeEquals", "StorageErrorCode", "unknown", "UNKNOWN", "objectNotFound", "path", "OBJECT_NOT_FOUND", "quotaExceeded", "bucket", "QUOTA_EXCEEDED", "unauthenticated", "UNAUTHENTICATED", "unauthorizedApp", "UNAUTHORIZED_APP", "unauthorized", "UNAUTHORIZED", "retryLimitExceeded", "RETRY_LIMIT_EXCEEDED", "canceled", "CANCELED", "invalidUrl", "url", "INVALID_URL", "invalidDefaultBucket", "INVALID_DEFAULT_BUCKET", "noDefaultBucket", "NO_DEFAULT_BUCKET", "cannotSliceBlob", "CANNOT_SLICE_BLOB", "serverFileWrongSize", "SERVER_FILE_WRONG_SIZE", "noDownloadURL", "NO_DOWNLOAD_URL", "missingPolyFill", "polyFill", "UNSUPPORTED_ENVIRONMENT", "invalidArgument", "INVALID_ARGUMENT", "appDeleted", "APP_DELETED", "invalidRootOperation", "name", "INVALID_ROOT_OPERATION", "invalidFormat", "format", "INVALID_FORMAT", "internalError", "INTERNAL_ERROR", "Location", "path_", "isRoot", "fullServerUrl", "encode", "encodeURIComponent", "bucketOnlyServerUrl", "makeFromBucketSpec", "bucketString", "host", "bucketLocation", "makeFromUrl", "e", "location", "bucketDomain", "gsModify", "loc", "char<PERSON>t", "slice", "gsPath", "gsRegex", "RegExp", "gsIndices", "httpModify", "decodeURIComponent", "version", "firebaseStorageHost", "replace", "firebaseStoragePath", "firebaseStorageRegExp", "firebaseStorageIndices", "cloudStorageHost", "cloudStoragePath", "cloudStorageRegExp", "cloudStorageIndices", "groups", "regex", "indices", "postModify", "i", "group", "captures", "exec", "bucketValue", "pathValue", "FailRequest", "error", "promise_", "Promise", "reject", "getPromise", "cancel", "_appDelete", "start", "doRequest", "backoffCompleteCb", "timeout", "waitSeconds", "retryTimeoutId", "globalTimeoutId", "hitTimeout", "cancelState", "triggeredCallback", "triggerCallback", "_len", "args", "Array", "_key", "apply", "callWithDelay", "millis", "setTimeout", "response<PERSON><PERSON>ler", "clearGlobalTimeout", "clearTimeout", "success", "_len2", "_key2", "call", "mustStop", "<PERSON><PERSON><PERSON><PERSON>", "Math", "random", "stopped", "stop", "wasTimeout", "id", "isJustDef", "p", "isFunction", "isNonArrayObject", "isArray", "isString", "String", "isNativeBlob", "isNativeBlobDefined", "Blob", "validateNumber", "argument", "minValue", "maxValue", "value", "makeUrl", "urlPart", "protocol", "origin", "makeQueryString", "params", "query<PERSON>art", "key", "hasOwnProperty", "nextPart", "ErrorCode", "isRetryStatusCode", "additionalRetryCodes", "isFiveHundredCode", "extraRetryCodes", "isExtraRetryCode", "indexOf", "isAdditionalRetryCode", "NetworkRequest", "url_", "method_", "headers_", "body_", "successCodes_", "additionalRetryCodes_", "callback_", "errorCallback_", "timeout_", "progressCallback_", "connectionFactory_", "retry", "pendingConnection_", "backoffId_", "canceled_", "appDelete_", "resolve", "resolve_", "reject_", "start_", "doTheRequest", "backoff<PERSON>allback", "RequestEndStatus", "connection", "progressListener", "progressEvent", "loaded", "total", "lengthComputable", "addUploadProgressListener", "send", "then", "removeUploadProgressListener", "hitServer", "getErrorCode", "NO_ERROR", "getStatus", "wasCanceled", "ABORT", "successCode", "backoffDone", "requestWentThrough", "wasSuccessCode", "result", "getResponse", "err", "getErrorText", "appDelete", "abort", "addAuthHeader_", "headers", "authToken", "addVersionHeader_", "firebaseVersion", "addGmpidHeader_", "appId", "addAppCheckHeader_", "appCheckToken", "makeRequest", "requestInfo", "requestFactory", "urlParams", "assign", "method", "body", "successCodes", "handler", "<PERSON><PERSON><PERSON><PERSON>", "progressCallback", "getBlobBuilder", "BlobBuilder", "WebKitBlobBuilder", "getBlob$1", "getBlob", "_len3", "_key3", "bb", "append", "sliceBlob", "blob", "end", "webkitSlice", "mozSlice", "decodeBase64", "encoded", "atob", "StringFormat", "RAW", "BASE64", "BASE64URL", "DATA_URL", "StringData", "data", "contentType", "dataFromString", "stringData", "utf8Bytes_", "base64Bytes_", "dataURLBytes_", "dataURLContentType_", "b", "c", "charCodeAt", "push", "valid", "hi", "lo", "Uint8Array", "percentEncodedBytes_", "decoded", "hasMinus", "hasUnder", "invalid<PERSON><PERSON>", "hasPlus", "hasSlash", "bytes", "includes", "array", "DataURLParts", "dataURL", "base64", "matches", "match", "middle", "endsWith", "substring", "rest", "dataUrl", "parts", "s", "long<PERSON><PERSON>ugh", "FbsBlob", "elideCopy", "size", "blobType", "data_", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "set", "size_", "type_", "startByte", "endByte", "realBlob", "sliced", "buffer", "_len4", "_key4", "blobby", "map", "val", "uint8Arrays", "finalLength", "for<PERSON>ach", "merged", "index", "uploadData", "jsonObjectOrNull", "obj", "JSON", "parse", "parent", "lastIndexOf", "newPath", "child", "child<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "filter", "component", "join", "lastComponent", "noXform_", "metadata", "Mapping", "server", "local", "writable", "xform", "mappings_", "xformPath", "fullPath", "getMappings", "mappings", "mappingsXformPath", "_metadata", "nameMapping", "xformSize", "Number", "sizeMapping", "addRef", "service", "generateRef", "_makeStorageReference", "defineProperty", "get", "fromResource", "resource", "len", "mapping", "fromResourceString", "resourceString", "downloadUrlFromResourceString", "tokens", "tokensList", "urls", "token", "base", "queryString", "alt", "toResourceString", "stringify", "PREFIXES_KEY", "ITEMS_KEY", "fromBackendResponse", "listResult", "prefixes", "items", "nextPageToken", "pathWithoutTrailingSlash", "reference", "item", "fromResponseString", "RequestInfo", "handler<PERSON><PERSON><PERSON>", "cndn", "metadataHandler", "xhr", "text", "<PERSON><PERSON><PERSON><PERSON>", "downloadUrlHandler", "_protocol", "sharedErrorHandler", "newErr", "objectErrorHandler", "shared", "getMetadata$2", "getMetadata", "maxOperationRetryTime", "list$2", "list", "delimiter", "pageToken", "maxResults", "getBytes$1", "getBytes", "maxDownloadSizeBytes", "_", "getDownloadUrl", "updateMetadata$2", "updateMetadata", "deleteObject$2", "deleteObject", "_xhr", "_text", "determineContentType_", "metadataForUpload_", "metadataClone", "multipartUpload", "genBoundary", "str", "toString", "boundary", "metadata_", "metadataString", "preBlobPart", "postBlobPart", "maxUploadRetryTime", "ResumableUploadStatus", "current", "finalized", "checkResumeHeader_", "allowed", "getResponseHeader", "allowedStatus", "createResumableUpload", "metadataForUpload", "getResumableUploadStatus", "sizeString", "isNaN", "RESUMABLE_UPLOAD_CHUNK_SIZE", "continueResumableUpload", "chunkSize", "bytesLeft", "bytesToUpload", "min", "uploadCommand", "uploadStatus", "newCurrent", "TaskEvent", "STATE_CHANGED", "TaskState", "RUNNING", "PAUSED", "SUCCESS", "ERROR", "taskStateFromInternalTaskState", "state", "Observer", "nextOrObserver", "complete", "asFunctions", "next", "observer", "async", "f", "_len5", "argsToForward", "_key5", "textFactoryOverride", "XhrConnection", "sent_", "xhr_", "XMLHttpRequest", "initXhr", "errorCode_", "sendPromise_", "addEventListener", "NETWORK_ERROR", "open", "setRequestHeader", "response", "statusText", "header", "listener", "upload", "removeEventListener", "XhrTextConnection", "responseType", "newTextConnection", "XhrBytesConnection", "newBytesConnection", "XhrBlobConnection", "newBlobConnection", "UploadTask", "isExponentialBackoffExpired", "sleepTime", "maxSleepTime", "ref", "_transferred", "_needToFetchStatus", "_needToFetchMetadata", "_observers", "_error", "_uploadUrl", "_request", "_chunkMultiplier", "_resolve", "_reject", "_ref", "_blob", "_mappings", "_resumable", "_shouldDoResumable", "_state", "_error<PERSON><PERSON><PERSON>", "completeTransitions_", "backoffExpired", "max", "_transition", "_metadataErrorHandler", "storage", "_promise", "_start", "_makeProgressCallback", "sizeBefore", "_updateProgress", "_createResumable", "_fetchStatus", "_fetchMetadata", "pendingTimeout", "_continueUpload", "_oneShotUpload", "_resolveToken", "callback", "all", "_getAuthToken", "_getAppCheckToken", "_location", "createRequest", "_makeRequest", "statusRequest", "uploadRequest", "newStatus", "_increaseMultiplier", "currentSize", "metadataRequest", "multipartRequest", "transferred", "old", "_notifyObservers", "wasPaused", "snapshot", "externalState", "bytesTransferred", "totalBytes", "task", "on", "completed", "_addObserver", "_removeObserver", "onFulfilled", "onRejected", "catch", "_notifyObserver", "splice", "_finishPromise", "observers", "triggered", "bind", "toCall", "resume", "pause", "Reference", "_service", "_newRef", "root", "_throwIfRoot", "getBytesInternal", "makeRequestWithTokens", "getBlobInternal", "uploadBytes$1", "uploadBytes", "finalMetadata", "uploadBytesResumable$1", "uploadBytesResumable", "uploadString$1", "uploadString", "listAll$1", "listAll", "accumulator", "listAllHelper", "opt", "nextPage", "list$1", "options", "op", "getMetadata$1", "updateMetadata$1", "getDownloadURL$1", "getDownloadURL", "deleteObject$1", "_getChild$1", "_get<PERSON><PERSON>d", "isUrl", "test", "refFromURL", "ref<PERSON><PERSON><PERSON><PERSON>", "FirebaseStorageImpl", "_bucket", "ref$1", "serviceOrRef", "pathOrUrl", "extractBucket", "config", "connectStorageEmulator$1", "connectStorageEmulator", "port", "mockUserToken", "_overrideAuthToken", "createMockUserToken", "app", "projectId", "_authProvider", "_appCheckProvider", "_url", "_firebaseVersion", "_host", "_appId", "_deleted", "_maxOperationRetryTime", "_maxUploadRetryTime", "_requests", "Set", "time", "POSITIVE_INFINITY", "auth", "getImmediate", "optional", "tokenData", "getToken", "accessToken", "_isFirebaseServerApp", "settings", "appCheck", "_delete", "request", "clear", "add", "delete", "STORAGE_TYPE", "getModularInstance", "getStorage", "getApp", "bucketUrl", "storageProvider", "_get<PERSON><PERSON><PERSON>", "storageInstance", "identifier", "emulator", "getDefaultEmulatorHostnameAndPort", "getStream", "Error", "factory", "container", "_ref2", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "authProvider", "appCheckProvider", "SDK_VERSION", "registerStorage", "_registerComponent", "Component", "setMultipleInstances", "registerVersion"], "sources": ["D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\constants.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\error.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\location.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\failrequest.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\backoff.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\type.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\url.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\connection.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\utils.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\request.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\fs.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\platform\\browser\\base64.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\string.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\blob.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\json.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\path.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\metadata.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\list.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\requestinfo.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\requests.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\taskenums.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\observer.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\implementation\\async.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\platform\\browser\\connection.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\task.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\reference.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\service.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\constants.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\api.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\api.browser.ts", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\storage\\src\\index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n\n/**\n * Domain name for firebase storage.\n */\nexport const DEFAULT_HOST = 'firebasestorage.googleapis.com';\n\n/**\n * The key in Firebase config json for the storage bucket.\n */\nexport const CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nexport const DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nexport const DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n\n/**\n * 1 second\n */\nexport const DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * This is the value of Number.MIN_SAFE_INTEGER, which is not well supported\n * enough for us to use it directly.\n */\nexport const MIN_SAFE_INTEGER = -9007199254740991;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport { CONFIG_STORAGE_BUCKET_KEY } from './constants';\n\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nexport class StorageError extends FirebaseError {\n  private readonly _baseMessage: string;\n  /**\n   * Stores custom error data unique to the `StorageError`.\n   */\n  customData: { serverResponse: string | null } = { serverResponse: null };\n\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code: StorageErrorCode, message: string, private status_ = 0) {\n    super(\n      prependCode(code),\n      `Firebase Storage: ${message} (${prependCode(code)})`\n    );\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n\n  get status(): number {\n    return this.status_;\n  }\n\n  set status(status: number) {\n    this.status_ = status;\n  }\n\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code: StorageErrorCode): boolean {\n    return prependCode(code) === this.code;\n  }\n\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse(): null | string {\n    return this.customData.serverResponse;\n  }\n\n  set serverResponse(serverResponse: string | null) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n\nexport const errors = {};\n\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nexport enum StorageErrorCode {\n  // Shared between all platforms\n  UNKNOWN = 'unknown',\n  OBJECT_NOT_FOUND = 'object-not-found',\n  BUCKET_NOT_FOUND = 'bucket-not-found',\n  PROJECT_NOT_FOUND = 'project-not-found',\n  QUOTA_EXCEEDED = 'quota-exceeded',\n  UNAUTHENTICATED = 'unauthenticated',\n  UNAUTHORIZED = 'unauthorized',\n  UNAUTHORIZED_APP = 'unauthorized-app',\n  RETRY_LIMIT_EXCEEDED = 'retry-limit-exceeded',\n  INVALID_CHECKSUM = 'invalid-checksum',\n  CANCELED = 'canceled',\n  // JS specific\n  INVALID_EVENT_NAME = 'invalid-event-name',\n  INVALID_URL = 'invalid-url',\n  INVALID_DEFAULT_BUCKET = 'invalid-default-bucket',\n  NO_DEFAULT_BUCKET = 'no-default-bucket',\n  CANNOT_SLICE_BLOB = 'cannot-slice-blob',\n  SERVER_FILE_WRONG_SIZE = 'server-file-wrong-size',\n  NO_DOWNLOAD_URL = 'no-download-url',\n  INVALID_ARGUMENT = 'invalid-argument',\n  INVALID_ARGUMENT_COUNT = 'invalid-argument-count',\n  APP_DELETED = 'app-deleted',\n  INVALID_ROOT_OPERATION = 'invalid-root-operation',\n  INVALID_FORMAT = 'invalid-format',\n  INTERNAL_ERROR = 'internal-error',\n  UNSUPPORTED_ENVIRONMENT = 'unsupported-environment'\n}\n\nexport function prependCode(code: StorageErrorCode): string {\n  return 'storage/' + code;\n}\n\nexport function unknown(): StorageError {\n  const message =\n    'An unknown error occurred, please check the error payload for ' +\n    'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\n\nexport function objectNotFound(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.OBJECT_NOT_FOUND,\n    \"Object '\" + path + \"' does not exist.\"\n  );\n}\n\nexport function bucketNotFound(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.BUCKET_NOT_FOUND,\n    \"Bucket '\" + bucket + \"' does not exist.\"\n  );\n}\n\nexport function projectNotFound(project: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.PROJECT_NOT_FOUND,\n    \"Project '\" + project + \"' does not exist.\"\n  );\n}\n\nexport function quotaExceeded(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.QUOTA_EXCEEDED,\n    \"Quota for bucket '\" +\n      bucket +\n      \"' exceeded, please view quota on \" +\n      'https://firebase.google.com/pricing/.'\n  );\n}\n\nexport function unauthenticated(): StorageError {\n  const message =\n    'User is not authenticated, please authenticate using Firebase ' +\n    'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\n\nexport function unauthorizedApp(): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED_APP,\n    'This app does not have permission to access Firebase Storage on this project.'\n  );\n}\n\nexport function unauthorized(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED,\n    \"User does not have permission to access '\" + path + \"'.\"\n  );\n}\n\nexport function retryLimitExceeded(): StorageError {\n  return new StorageError(\n    StorageErrorCode.RETRY_LIMIT_EXCEEDED,\n    'Max retry time for operation exceeded, please try again.'\n  );\n}\n\nexport function invalidChecksum(\n  path: string,\n  checksum: string,\n  calculated: string\n): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_CHECKSUM,\n    \"Uploaded/downloaded object '\" +\n      path +\n      \"' has checksum '\" +\n      checksum +\n      \"' which does not match '\" +\n      calculated +\n      \"'. Please retry the upload/download.\"\n  );\n}\n\nexport function canceled(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANCELED,\n    'User canceled the upload/download.'\n  );\n}\n\nexport function invalidEventName(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_EVENT_NAME,\n    \"Invalid event name '\" + name + \"'.\"\n  );\n}\n\nexport function invalidUrl(url: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_URL,\n    \"Invalid URL '\" + url + \"'.\"\n  );\n}\n\nexport function invalidDefaultBucket(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_DEFAULT_BUCKET,\n    \"Invalid default bucket '\" + bucket + \"'.\"\n  );\n}\n\nexport function noDefaultBucket(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DEFAULT_BUCKET,\n    'No default bucket ' +\n      \"found. Did you set the '\" +\n      CONFIG_STORAGE_BUCKET_KEY +\n      \"' property when initializing the app?\"\n  );\n}\n\nexport function cannotSliceBlob(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANNOT_SLICE_BLOB,\n    'Cannot slice blob for upload. Please retry the upload.'\n  );\n}\n\nexport function serverFileWrongSize(): StorageError {\n  return new StorageError(\n    StorageErrorCode.SERVER_FILE_WRONG_SIZE,\n    'Server recorded incorrect upload file size, please retry the upload.'\n  );\n}\n\nexport function noDownloadURL(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DOWNLOAD_URL,\n    'The given file does not have any download URLs.'\n  );\n}\n\nexport function missingPolyFill(polyFill: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n    `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`\n  );\n}\n\n/**\n * @internal\n */\nexport function invalidArgument(message: string): StorageError {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\n\nexport function invalidArgumentCount(\n  argMin: number,\n  argMax: number,\n  fnName: string,\n  real: number\n): StorageError {\n  let countPart;\n  let plural;\n  if (argMin === argMax) {\n    countPart = argMin;\n    plural = argMin === 1 ? 'argument' : 'arguments';\n  } else {\n    countPart = 'between ' + argMin + ' and ' + argMax;\n    plural = 'arguments';\n  }\n  return new StorageError(\n    StorageErrorCode.INVALID_ARGUMENT_COUNT,\n    'Invalid argument count in `' +\n      fnName +\n      '`: Expected ' +\n      countPart +\n      ' ' +\n      plural +\n      ', received ' +\n      real +\n      '.'\n  );\n}\n\nexport function appDeleted(): StorageError {\n  return new StorageError(\n    StorageErrorCode.APP_DELETED,\n    'The Firebase app was deleted.'\n  );\n}\n\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nexport function invalidRootOperation(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_ROOT_OPERATION,\n    \"The operation '\" +\n      name +\n      \"' cannot be performed on a root reference, create a non-root \" +\n      \"reference using child, such as .child('file.png').\"\n  );\n}\n\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nexport function invalidFormat(format: string, message: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_FORMAT,\n    \"String does not match format '\" + format + \"': \" + message\n  );\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function unsupportedEnvironment(message: string): StorageError {\n  throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, message);\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function internalError(message: string): StorageError {\n  throw new StorageError(\n    StorageErrorCode.INTERNAL_ERROR,\n    'Internal error: ' + message\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functionality related to the parsing/composition of bucket/\n * object location.\n */\n\nimport { invalidDefaultBucket, invalidUrl } from './error';\nimport { DEFAULT_HOST } from './constants';\n\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nexport class Location {\n  private path_: string;\n\n  constructor(public readonly bucket: string, path: string) {\n    this.path_ = path;\n  }\n\n  get path(): string {\n    return this.path_;\n  }\n\n  get isRoot(): boolean {\n    return this.path.length === 0;\n  }\n\n  fullServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n\n  bucketOnlyServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n\n  static makeFromBucketSpec(bucketString: string, host: string): Location {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n\n  static makeFromUrl(url: string, host: string): Location {\n    let location: Location | null = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n\n    function gsModify(loc: Location): void {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = { bucket: 1, path: 3 };\n\n    function httpModify(loc: Location): void {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(\n      `^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`,\n      'i'\n    );\n    const firebaseStorageIndices = { bucket: 1, path: 3 };\n\n    const cloudStorageHost =\n      host === DEFAULT_HOST\n        ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n        : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(\n      `^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`,\n      'i'\n    );\n    const cloudStorageIndices = { bucket: 1, path: 2 };\n\n    const groups = [\n      { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n      {\n        regex: firebaseStorageRegExp,\n        indices: firebaseStorageIndices,\n        postModify: httpModify\n      },\n      {\n        regex: cloudStorageRegExp,\n        indices: cloudStorageIndices,\n        postModify: httpModify\n      }\n    ];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Request } from './request';\n\n/**\n * A request whose promise always fails.\n */\nexport class FailRequest<T> implements Request<T> {\n  promise_: Promise<T>;\n\n  constructor(error: StorageError) {\n    this.promise_ = Promise.reject<T>(error);\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<T> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(_appDelete = false): void {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Provides a method for running a function with exponential\n * backoff.\n */\ntype id = (p1: boolean) => void;\n\nexport { id };\n\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nexport function start(\n  doRequest: (\n    onRequestComplete: (success: boolean) => void,\n    canceled: boolean\n  ) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  backoffCompleteCb: (...args: any[]) => unknown,\n  timeout: number\n): id {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId: any = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId: any = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n\n  function canceled(): boolean {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n\n  function triggerCallback(...args: any[]): void {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n\n  function callWithDelay(millis: number): void {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n\n  function clearGlobalTimeout(): void {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n\n  function responseHandler(success: boolean, ...args: any[]): void {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n\n  function stop(wasTimeout: boolean): void {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nexport function stop(id: id): void {\n  id(false);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { invalidArgument } from './error';\n\nexport function isJustDef<T>(p: T | null | undefined): p is T | null {\n  return p !== void 0;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isFunction(p: unknown): p is Function {\n  return typeof p === 'function';\n}\n\nexport function isNonArrayObject(p: unknown): boolean {\n  return typeof p === 'object' && !Array.isArray(p);\n}\n\nexport function isString(p: unknown): p is string {\n  return typeof p === 'string' || p instanceof String;\n}\n\nexport function isNativeBlob(p: unknown): p is Blob {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\n\nexport function isNativeBlobDefined(): boolean {\n  return typeof Blob !== 'undefined';\n}\n\nexport function validateNumber(\n  argument: string,\n  minValue: number,\n  maxValue: number,\n  value: number\n): void {\n  if (value < minValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${minValue} or greater.`\n    );\n  }\n  if (value > maxValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${maxValue} or less.`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functions to create and manipulate URLs for the server API.\n */\nimport { UrlParams } from './requestinfo';\n\nexport function makeUrl(\n  urlPart: string,\n  host: string,\n  protocol: string\n): string {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\n\nexport function makeQueryString(params: UrlParams): string {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Network headers */\nexport type Headers = Record<string, string>;\n\n/** Response type exposed by the networking APIs. */\nexport type ConnectionType =\n  | string\n  | ArrayBuffer\n  | Blob\n  | ReadableStream<Uint8Array>;\n\n/**\n * A lightweight wrapper around XMLHttpRequest with a\n * goog.net.XhrIo-like interface.\n *\n * You can create a new connection by invoking `newTextConnection()`,\n * `newBytesConnection()` or `newStreamConnection()`.\n */\nexport interface Connection<T extends ConnectionType> {\n  /**\n   * Sends a request to the provided URL.\n   *\n   * This method never rejects its promise. In case of encountering an error,\n   * it sets an error code internally which can be accessed by calling\n   * getErrorCode() by callers.\n   */\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string | null,\n    headers?: Headers\n  ): Promise<void>;\n\n  getErrorCode(): ErrorCode;\n\n  getStatus(): number;\n\n  getResponse(): T;\n\n  getErrorText(): string;\n\n  /**\n   * Abort the request.\n   */\n  abort(): void;\n\n  getResponseHeader(header: string): string | null;\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nexport enum ErrorCode {\n  NO_ERROR = 0,\n  NETWORK_ERROR = 1,\n  ABORT = 2\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nexport function isRetryStatusCode(\n  status: number,\n  additionalRetryCodes: number[]\n): boolean {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n    // Request Timeout: web server didn't receive full request in time.\n    408,\n    // Too Many Requests: you're getting rate-limited, basically.\n    429\n  ];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods used to actually send HTTP requests from\n * abstract representations.\n */\n\nimport { id as backoffId, start, stop } from './backoff';\nimport { appDeleted, canceled, retryLimitExceeded, unknown } from './error';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestHandler, RequestInfo } from './requestinfo';\nimport { isJustDef } from './type';\nimport { makeQueryString } from './url';\nimport { Connection, ErrorCode, Headers, ConnectionType } from './connection';\nimport { isRetryStatusCode } from './utils';\n\nexport interface Request<T> {\n  getPromise(): Promise<T>;\n\n  /**\n   * Cancels the request. IMPORTANT: the promise may still be resolved with an\n   * appropriate value (if the request is finished before you call this method,\n   * but the promise has not yet been resolved), so don't just assume it will be\n   * rejected if you call this function.\n   * @param appDelete - True if the cancelation came from the app being deleted.\n   */\n  cancel(appDelete?: boolean): void;\n}\n\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest<I extends ConnectionType, O> implements Request<O> {\n  private pendingConnection_: Connection<I> | null = null;\n  private backoffId_: backoffId | null = null;\n  private resolve_!: (value?: O | PromiseLike<O>) => void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private reject_!: (reason?: any) => void;\n  private canceled_: boolean = false;\n  private appDelete_: boolean = false;\n  private promise_: Promise<O>;\n\n  constructor(\n    private url_: string,\n    private method_: string,\n    private headers_: Headers,\n    private body_: string | Blob | Uint8Array | null,\n    private successCodes_: number[],\n    private additionalRetryCodes_: number[],\n    private callback_: RequestHandler<I, O>,\n    private errorCallback_: ErrorHandler | null,\n    private timeout_: number,\n    private progressCallback_: ((p1: number, p2: number) => void) | null,\n    private connectionFactory_: () => Connection<I>,\n    private retry = true\n  ) {\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve as (value?: O | PromiseLike<O>) => void;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n\n  /**\n   * Actually starts the retry loop.\n   */\n  private start_(): void {\n    const doTheRequest: (\n      backoffCallback: (success: boolean, ...p2: unknown[]) => void,\n      canceled: boolean\n    ) => void = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n\n      const progressListener: (\n        progressEvent: ProgressEvent\n      ) => void = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection\n        .send(this.url_, this.method_, this.body_, this.headers_)\n        .then(() => {\n          if (this.progressCallback_ !== null) {\n            connection.removeUploadProgressListener(progressListener);\n          }\n          this.pendingConnection_ = null;\n          const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n          const status = connection.getStatus();\n          if (\n            !hitServer ||\n            (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n              this.retry)\n          ) {\n            const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n            backoffCallback(\n              false,\n              new RequestEndStatus(false, null, wasCanceled)\n            );\n            return;\n          }\n          const successCode = this.successCodes_.indexOf(status) !== -1;\n          backoffCallback(true, new RequestEndStatus(successCode, connection));\n        });\n    };\n\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone: (\n      requestWentThrough: boolean,\n      status: RequestEndStatus<I>\n    ) => void = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection as Connection<I>;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<O> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(appDelete?: boolean): void {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nexport class RequestEndStatus<I extends ConnectionType> {\n  /**\n   * True if the request was canceled.\n   */\n  canceled: boolean;\n\n  constructor(\n    public wasSuccessCode: boolean,\n    public connection: Connection<I> | null,\n    canceled?: boolean\n  ) {\n    this.canceled = !!canceled;\n  }\n}\n\nexport function addAuthHeader_(\n  headers: Headers,\n  authToken: string | null\n): void {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\n\nexport function addVersionHeader_(\n  headers: Headers,\n  firebaseVersion?: string\n): void {\n  headers['X-Firebase-Storage-Version'] =\n    'webjs/' + (firebaseVersion ?? 'AppManager');\n}\n\nexport function addGmpidHeader_(headers: Headers, appId: string | null): void {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\n\nexport function addAppCheckHeader_(\n  headers: Headers,\n  appCheckToken: string | null\n): void {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\n\nexport function makeRequest<I extends ConnectionType, O>(\n  requestInfo: RequestInfo<I, O>,\n  appId: string | null,\n  authToken: string | null,\n  appCheckToken: string | null,\n  requestFactory: () => Connection<I>,\n  firebaseVersion?: string,\n  retry = true\n): Request<O> {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest<I, O>(\n    url,\n    requestInfo.method,\n    headers,\n    requestInfo.body,\n    requestInfo.successCodes,\n    requestInfo.additionalRetryCodes,\n    requestInfo.handler,\n    requestInfo.errorHandler,\n    requestInfo.timeout,\n    requestInfo.progressCallback,\n    requestFactory,\n    retry\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Some methods copied from goog.fs.\n * We don't include goog.fs because it pulls in a bunch of Deferred code that\n * bloats the size of the released binary.\n */\nimport { isNativeBlobDefined } from './type';\nimport { StorageErrorCode, StorageError } from './error';\n\nfunction getBlobBuilder(): typeof IBlobBuilder | undefined {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nexport function getBlob(...args: Array<string | Blob | ArrayBuffer>): Blob {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(\n        StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n        \"This browser doesn't seem to support creating Blobs\"\n      );\n    }\n  }\n}\n\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nexport function sliceBlob(blob: Blob, start: number, end: number): Blob | null {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { missingPolyFill } from '../../implementation/error';\n\n/** Converts a Base64 encoded string to a binary string. */\nexport function decodeBase64(encoded: string): string {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\nexport function decodeUint8Array(data: Uint8Array): string {\n  return new TextDecoder().decode(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { unknown, invalidFormat } from './error';\nimport { decodeBase64 } from '../platform/base64';\n\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport const StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n} as const;\n\nexport class StringData {\n  contentType: string | null;\n\n  constructor(public data: Uint8Array, contentType?: string | null) {\n    this.contentType = contentType || null;\n  }\n}\n\n/**\n * @internal\n */\nexport function dataFromString(\n  format: StringFormat,\n  stringData: string\n): StringData {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(\n        dataURLBytes_(stringData),\n        dataURLContentType_(stringData)\n      );\n    default:\n    // do nothing\n  }\n\n  // assert(false);\n  throw unknown();\n}\n\nexport function utf8Bytes_(value: string): Uint8Array {\n  const b: number[] = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | (c >> 6), 128 | (c & 63));\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid =\n            i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n            b.push(\n              240 | (c >> 18),\n              128 | ((c >> 12) & 63),\n              128 | ((c >> 6) & 63),\n              128 | (c & 63)\n            );\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\n\nexport function percentEncodedBytes_(value: string): Uint8Array {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\n\nexport function base64Bytes_(format: StringFormat, value: string): Uint8Array {\n  switch (format) {\n    case StringFormat.BASE64: {\n      const hasMinus = value.indexOf('-') !== -1;\n      const hasUnder = value.indexOf('_') !== -1;\n      if (hasMinus || hasUnder) {\n        const invalidChar = hasMinus ? '-' : '_';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" +\n            invalidChar +\n            \"' found: is it base64url encoded?\"\n        );\n      }\n      break;\n    }\n    case StringFormat.BASE64URL: {\n      const hasPlus = value.indexOf('+') !== -1;\n      const hasSlash = value.indexOf('/') !== -1;\n      if (hasPlus || hasSlash) {\n        const invalidChar = hasPlus ? '+' : '/';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\"\n        );\n      }\n      value = value.replace(/-/g, '+').replace(/_/g, '/');\n      break;\n    }\n    default:\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if ((e as Error).message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\n\nclass DataURLParts {\n  base64: boolean = false;\n  contentType: string | null = null;\n  rest: string;\n\n  constructor(dataURL: string) {\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(\n        StringFormat.DATA_URL,\n        \"Must be formatted 'data:[<mediatype>][;base64],<data>\"\n      );\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64\n        ? middle.substring(0, middle.length - ';base64'.length)\n        : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\n\nexport function dataURLBytes_(dataUrl: string): Uint8Array {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\n\nexport function dataURLContentType_(dataUrl: string): string | null {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\n\nfunction endsWith(s: string, end: string): boolean {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n\n  return s.substring(s.length - end.length) === end;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @file Provides a Blob-like wrapper for various binary types (including the\n * native Blob type). This makes it possible to upload types like ArrayBuffers,\n * making uploads possible in environments without the native Blob type.\n */\nimport { sliceBlob, getBlob } from './fs';\nimport { StringFormat, dataFromString } from './string';\nimport { isNativeBlob, isNativeBlobDefined, isString } from './type';\n\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nexport class FbsBlob {\n  private data_!: Blob | Uint8Array;\n  private size_: number;\n  private type_: string;\n\n  constructor(data: Blob | Uint8Array | ArrayBuffer, elideCopy?: boolean) {\n    let size: number = 0;\n    let blobType: string = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data as Blob;\n      size = (data as Blob).size;\n      blobType = (data as Blob).type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data as Uint8Array;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data as Uint8Array);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n\n  size(): number {\n    return this.size_;\n  }\n\n  type(): string {\n    return this.type_;\n  }\n\n  slice(startByte: number, endByte: number): FbsBlob | null {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_ as Blob;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(\n        (this.data_ as Uint8Array).buffer,\n        startByte,\n        endByte - startByte\n      );\n      return new FbsBlob(slice, true);\n    }\n  }\n\n  static getBlob(...args: Array<string | FbsBlob>): FbsBlob | null {\n    if (isNativeBlobDefined()) {\n      const blobby: Array<Blob | Uint8Array | string> = args.map(\n        (val: string | FbsBlob): Blob | Uint8Array | string => {\n          if (val instanceof FbsBlob) {\n            return val.data_;\n          } else {\n            return val;\n          }\n        }\n      );\n      return new FbsBlob(getBlob.apply(null, blobby));\n    } else {\n      const uint8Arrays: Uint8Array[] = args.map(\n        (val: string | FbsBlob): Uint8Array => {\n          if (isString(val)) {\n            return dataFromString(StringFormat.RAW, val as string).data;\n          } else {\n            // Blobs don't exist, so this has to be a Uint8Array.\n            return (val as FbsBlob).data_ as Uint8Array;\n          }\n        }\n      );\n      let finalLength = 0;\n      uint8Arrays.forEach((array: Uint8Array): void => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach((array: Uint8Array) => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n\n  uploadData(): Blob | Uint8Array {\n    return this.data_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isNonArrayObject } from './type';\n\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nexport function jsonObjectOrNull(\n  s: string\n): { [name: string]: unknown } | null {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n\n/**\n * @return Null if the path is already at the root.\n */\nexport function parent(path: string): string | null {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\n\nexport function child(path: string, childPath: string): string {\n  const canonicalChildPath = childPath\n    .split('/')\n    .filter(component => component.length > 0)\n    .join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nexport function lastComponent(path: string): string {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the metadata format\n */\nimport { Metadata } from '../metadata';\n\nimport { jsonObjectOrNull } from './json';\nimport { Location } from './location';\nimport { lastComponent } from './path';\nimport { isString } from './type';\nimport { makeUrl, makeQueryString } from './url';\nimport { Reference } from '../reference';\nimport { FirebaseStorageImpl } from '../service';\n\nexport function noXform_<T>(metadata: Metadata, value: T): T {\n  return value;\n}\n\nclass Mapping<T> {\n  local: string;\n  writable: boolean;\n  xform: (p1: Metadata, p2?: T) => T | undefined;\n\n  constructor(\n    public server: string,\n    local?: string | null,\n    writable?: boolean,\n    xform?: ((p1: Metadata, p2?: T) => T | undefined) | null\n  ) {\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\ntype Mappings = Array<Mapping<string> | Mapping<number>>;\n\nexport { Mappings };\n\nlet mappings_: Mappings | null = null;\n\nexport function xformPath(fullPath: string | undefined): string | undefined {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\n\nexport function getMappings(): Mappings {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings: Mappings = [];\n  mappings.push(new Mapping<string>('bucket'));\n  mappings.push(new Mapping<string>('generation'));\n  mappings.push(new Mapping<string>('metageneration'));\n  mappings.push(new Mapping<string>('name', 'fullPath', true));\n\n  function mappingsXformPath(\n    _metadata: Metadata,\n    fullPath: string | undefined\n  ): string | undefined {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping<string>('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(\n    _metadata: Metadata,\n    size?: number | string\n  ): number | undefined {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping<number>('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping<number>('timeCreated'));\n  mappings.push(new Mapping<string>('updated'));\n  mappings.push(new Mapping<string>('md5Hash', null, true));\n  mappings.push(new Mapping<string>('cacheControl', null, true));\n  mappings.push(new Mapping<string>('contentDisposition', null, true));\n  mappings.push(new Mapping<string>('contentEncoding', null, true));\n  mappings.push(new Mapping<string>('contentLanguage', null, true));\n  mappings.push(new Mapping<string>('contentType', null, true));\n  mappings.push(new Mapping<string>('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\n\nexport function addRef(metadata: Metadata, service: FirebaseStorageImpl): void {\n  function generateRef(): Reference {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\n\nexport function fromResource(\n  service: FirebaseStorageImpl,\n  resource: { [name: string]: unknown },\n  mappings: Mappings\n): Metadata {\n  const metadata: Metadata = {} as Metadata;\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = (mapping as Mapping<unknown>).xform(\n      metadata,\n      resource[mapping.server]\n    );\n  }\n  addRef(metadata, service);\n  return metadata;\n}\n\nexport function fromResourceString(\n  service: FirebaseStorageImpl,\n  resourceString: string,\n  mappings: Mappings\n): Metadata | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as Metadata;\n  return fromResource(service, resource, mappings);\n}\n\nexport function downloadUrlFromResourceString(\n  metadata: Metadata,\n  resourceString: string,\n  host: string,\n  protocol: string\n): string | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens: string = obj['downloadTokens'] as string;\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map((token: string): string => {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\n\nexport function toResourceString(\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): string {\n  const resource: {\n    [prop: string]: unknown;\n  } = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the listOptions and listResult format\n */\nimport { Location } from './location';\nimport { jsonObjectOrNull } from './json';\nimport { ListResult } from '../list';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Represents the simplified object metadata returned by List API.\n * Other fields are filtered because list in Firebase Rules does not grant\n * the permission to read the metadata.\n */\ninterface ListMetadataResponse {\n  name: string;\n  bucket: string;\n}\n\n/**\n * Represents the JSON response of List API.\n */\ninterface ListResultResponse {\n  prefixes: string[];\n  items: ListMetadataResponse[];\n  nextPageToken?: string;\n}\n\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\n\nfunction fromBackendResponse(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resource: ListResultResponse\n): ListResult {\n  const listResult: ListResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(\n        new Location(bucket, pathWithoutTrailingSlash)\n      );\n      listResult.prefixes.push(reference);\n    }\n  }\n\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(\n        new Location(bucket, item['name'])\n      );\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\n\nexport function fromResponseString(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resourceString: string\n): ListResult | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as unknown as ListResultResponse;\n  return fromBackendResponse(service, bucket, resource);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Headers, Connection, ConnectionType } from './connection';\n\n/**\n * Type for url params stored in RequestInfo.\n */\nexport interface UrlParams {\n  [name: string]: string | number;\n}\n\n/**\n * A function that converts a server response to the API type expected by the\n * SDK.\n *\n * @param I - the type of the backend's network response\n * @param O - the output response type used by the rest of the SDK.\n */\nexport type RequestHandler<I extends ConnectionType, O> = (\n  connection: Connection<I>,\n  response: I\n) => O;\n\n/** A function to handle an error. */\nexport type ErrorHandler = (\n  connection: Connection<ConnectionType>,\n  response: StorageError\n) => StorageError;\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nexport class RequestInfo<I extends ConnectionType, O> {\n  urlParams: UrlParams = {};\n  headers: Headers = {};\n  body: Blob | string | Uint8Array | null = null;\n  errorHandler: ErrorHandler | null = null;\n\n  /**\n   * Called with the current number of bytes uploaded and total size (-1 if not\n   * computable) of the request body (i.e. used to report upload progress).\n   */\n  progressCallback: ((p1: number, p2: number) => void) | null = null;\n  successCodes: number[] = [200];\n  additionalRetryCodes: number[] = [];\n\n  constructor(\n    public url: string,\n    public method: string,\n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    public handler: RequestHandler<I, O>,\n    public timeout: number\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods for interacting with the network.\n */\n\nimport { Metadata } from '../metadata';\nimport { ListResult } from '../list';\nimport { FbsBlob } from './blob';\nimport {\n  StorageError,\n  cannotSliceBlob,\n  unauthenticated,\n  quotaExceeded,\n  unauthorized,\n  objectNotFound,\n  serverFileWrongSize,\n  unknown,\n  unauthorizedApp\n} from './error';\nimport { Location } from './location';\nimport {\n  Mappings,\n  fromResourceString,\n  downloadUrlFromResourceString,\n  toResourceString\n} from './metadata';\nimport { fromResponseString } from './list';\nimport { RequestInfo, UrlParams } from './requestinfo';\nimport { isString } from './type';\nimport { makeUrl } from './url';\nimport { Connection, ConnectionType } from './connection';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nexport function handlerCheck(cndn: boolean): void {\n  if (!cndn) {\n    throw unknown();\n  }\n}\n\nexport function metadataHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => Metadata {\n  function handler(xhr: Connection<string>, text: string): Metadata {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata as Metadata;\n  }\n  return handler;\n}\n\nexport function listHandler(\n  service: FirebaseStorageImpl,\n  bucket: string\n): (p1: Connection<string>, p2: string) => ListResult {\n  function handler(xhr: Connection<string>, text: string): ListResult {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult as ListResult;\n  }\n  return handler;\n}\n\nexport function downloadUrlHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => string | null {\n  function handler(xhr: Connection<string>, text: string): string | null {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(\n      metadata as Metadata,\n      text,\n      service.host,\n      service._protocol\n    );\n  }\n  return handler;\n}\n\nexport function sharedErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr: StorageError;\n    if (xhr.getStatus() === 401) {\n      if (\n        // This exact message string is the only consistent part of the\n        // server's error response that identifies it as an App Check error.\n        xhr.getErrorText().includes('Firebase App Check token is invalid')\n      ) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function objectErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  const shared = sharedErrorHandler(location);\n\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function getMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function list(\n  service: FirebaseStorageImpl,\n  location: Location,\n  delimiter?: string,\n  pageToken?: string | null,\n  maxResults?: number | null\n): RequestInfo<string, ListResult> {\n  const urlParams: UrlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    listHandler(service, location.bucket),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\nexport function getBytes<I extends ConnectionType>(\n  service: FirebaseStorageImpl,\n  location: Location,\n  maxDownloadSizeBytes?: number\n): RequestInfo<I, I> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    (_: Connection<I>, data: I) => data,\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\n\nexport function getDownloadUrl(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, string | null> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    downloadUrlHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function updateMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function deleteObject(\n  service: FirebaseStorageImpl,\n  location: Location\n): RequestInfo<string, void> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n\n  function handler(_xhr: Connection<string>, _text: string): void {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function determineContentType_(\n  metadata: Metadata | null,\n  blob: FbsBlob | null\n): string {\n  return (\n    (metadata && metadata['contentType']) ||\n    (blob && blob.type()) ||\n    'application/octet-stream'\n  );\n}\n\nexport function metadataForUpload_(\n  location: Location,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): Metadata {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nexport function multipartUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, Metadata> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers: { [prop: string]: string } = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n\n  function genBoundary(): string {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart =\n    '--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n    metadataString +\n    '\\r\\n--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: ' +\n    metadata_['contentType'] +\n    '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams: UrlParams = { name: metadata_['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nexport class ResumableUploadStatus {\n  finalized: boolean;\n  metadata: Metadata | null;\n\n  constructor(\n    public current: number,\n    public total: number,\n    finalized?: boolean,\n    metadata?: Metadata | null\n  ) {\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\n\nexport function checkResumeHeader_(\n  xhr: Connection<string>,\n  allowed?: string[]\n): string {\n  let status: string | null = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status as string;\n}\n\nexport function createResumableUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, string> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams: UrlParams = { name: metadataForUpload['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType']!,\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n\n  function handler(xhr: Connection<string>): string {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url as string;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nexport function getResumableUploadStatus(\n  service: FirebaseStorageImpl,\n  location: Location,\n  url: string,\n  blob: FbsBlob\n): RequestInfo<string, ResumableUploadStatus> {\n  const headers = { 'X-Goog-Upload-Command': 'query' };\n\n  function handler(xhr: Connection<string>): ResumableUploadStatus {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString: string | null = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nexport const RESUMABLE_UPLOAD_CHUNK_SIZE: number = 256 * 1024;\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nexport function continueResumableUpload(\n  location: Location,\n  service: FirebaseStorageImpl,\n  url: string,\n  blob: FbsBlob,\n  chunkSize: number,\n  mappings: Mappings,\n  status?: ResumableUploadStatus | null,\n  progressCallback?: ((p1: number, p2: number) => void) | null\n): RequestInfo<string, ResumableUploadStatus> {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n\n  function handler(\n    xhr: Connection<string>,\n    text: string\n  ): ResumableUploadStatus {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(\n      newCurrent,\n      size,\n      uploadStatus === 'final',\n      metadata\n    );\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Enumerations used for upload tasks.\n */\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport type TaskEvent = string;\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport const TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n\n/**\n * Internal enum for task state.\n */\nexport const enum InternalTaskState {\n  RUNNING = 'running',\n  PAUSING = 'pausing',\n  PAUSED = 'paused',\n  SUCCESS = 'success',\n  CANCELING = 'canceling',\n  CANCELED = 'canceled',\n  ERROR = 'error'\n}\n\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport type TaskState = (typeof TaskState)[keyof typeof TaskState];\n\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport const TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n\n  /** The task failed with an error. */\n  ERROR: 'error'\n} as const;\n\nexport function taskStateFromInternalTaskState(\n  state: InternalTaskState\n): TaskState {\n  switch (state) {\n    case InternalTaskState.RUNNING:\n    case InternalTaskState.PAUSING:\n    case InternalTaskState.CANCELING:\n      return TaskState.RUNNING;\n    case InternalTaskState.PAUSED:\n      return TaskState.PAUSED;\n    case InternalTaskState.SUCCESS:\n      return TaskState.SUCCESS;\n    case InternalTaskState.CANCELED:\n      return TaskState.CANCELED;\n    case InternalTaskState.ERROR:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isFunction } from './type';\nimport { StorageError } from './error';\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `StorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * An observer identical to the `Observer` defined in packages/util except the\n * error passed into the ErrorFn is specifically a `StorageError`.\n */\nexport interface StorageObserver<T> {\n  /**\n   * Function that is called once for each value in the event stream.\n   */\n  next?: NextFn<T>;\n  /**\n   * A function that is called with a `StorageError`\n   * if the event stream ends due to an error.\n   */\n  error?: ErrorFn;\n  /**\n   * A function that is called if the event stream ends normally.\n   */\n  complete?: CompleteFn;\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\nexport class Observer<T> implements StorageObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n\n  constructor(\n    nextOrObserver?: NextFn<T> | StorageObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ) {\n    const asFunctions =\n      isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver as NextFn<T>;\n      this.error = error ?? undefined;\n      this.complete = complete ?? undefined;\n    } else {\n      const observer = nextOrObserver as {\n        next?: NextFn<T>;\n        error?: ErrorFn;\n        complete?: CompleteFn;\n      };\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(f: Function): Function {\n  return (...argsToForward: unknown[]) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Connection,\n  ConnectionType,\n  ErrorCode,\n  Headers\n} from '../../implementation/connection';\nimport { internalError } from '../../implementation/error';\n\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride: (() => Connection<string>) | null = null;\n\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nabstract class XhrConnection<T extends ConnectionType>\n  implements Connection<T>\n{\n  protected xhr_: XMLHttpRequest;\n  private errorCode_: ErrorCode;\n  private sendPromise_: Promise<void>;\n  protected sent_: boolean = false;\n\n  constructor() {\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n\n  abstract initXhr(): void;\n\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string,\n    headers?: Headers\n  ): Promise<void> {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n\n  getErrorCode(): ErrorCode {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n\n  getStatus(): number {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n\n  getResponse(): T {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n\n  getErrorText(): string {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n\n  /** Aborts the request. */\n  abort(): void {\n    this.xhr_.abort();\n  }\n\n  getResponseHeader(header: string): string | null {\n    return this.xhr_.getResponseHeader(header);\n  }\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\n\nexport class XhrTextConnection extends XhrConnection<string> {\n  initXhr(): void {\n    this.xhr_.responseType = 'text';\n  }\n}\n\nexport function newTextConnection(): Connection<string> {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\n\nexport class XhrBytesConnection extends XhrConnection<ArrayBuffer> {\n  private data_?: ArrayBuffer;\n\n  initXhr(): void {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\n\nexport function newBytesConnection(): Connection<ArrayBuffer> {\n  return new XhrBytesConnection();\n}\n\nexport class XhrBlobConnection extends XhrConnection<Blob> {\n  initXhr(): void {\n    this.xhr_.responseType = 'blob';\n  }\n}\n\nexport function newBlobConnection(): Connection<Blob> {\n  return new XhrBlobConnection();\n}\n\nexport function newStreamConnection(): Connection<ReadableStream> {\n  throw new Error('Streams are only supported on Node');\n}\n\nexport function injectTestConnection(\n  factory: (() => Connection<string>) | null\n): void {\n  textFactoryOverride = factory;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Defines types for interacting with blob transfer tasks.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport {\n  canceled,\n  StorageErrorCode,\n  StorageError,\n  retryLimitExceeded\n} from './implementation/error';\nimport {\n  InternalTaskState,\n  TaskEvent,\n  TaskState,\n  taskStateFromInternalTaskState\n} from './implementation/taskenums';\nimport { Metadata } from './metadata';\nimport {\n  Observer,\n  Subscribe,\n  Unsubscribe,\n  StorageObserver as StorageObserverInternal,\n  NextFn\n} from './implementation/observer';\nimport { Request } from './implementation/request';\nimport { UploadTaskSnapshot, StorageObserver } from './public-types';\nimport { async as fbsAsync } from './implementation/async';\nimport { Mappings, getMappings } from './implementation/metadata';\nimport {\n  createResumableUpload,\n  getResumableUploadStatus,\n  RESUMABLE_UPLOAD_CHUNK_SIZE,\n  ResumableUploadStatus,\n  continueResumableUpload,\n  getMetadata,\n  multipartUpload\n} from './implementation/requests';\nimport { Reference } from './reference';\nimport { newTextConnection } from './platform/connection';\nimport { isRetryStatusCode } from './implementation/utils';\nimport { CompleteFn } from '@firebase/util';\nimport { DEFAULT_MIN_SLEEP_TIME_MILLIS } from './implementation/constants';\n\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nexport class UploadTask {\n  private _ref: Reference;\n  /**\n   * The data to be uploaded.\n   */\n  _blob: FbsBlob;\n  /**\n   * Metadata related to the upload.\n   */\n  _metadata: Metadata | null;\n  private _mappings: Mappings;\n  /**\n   * Number of bytes transferred so far.\n   */\n  _transferred: number = 0;\n  private _needToFetchStatus: boolean = false;\n  private _needToFetchMetadata: boolean = false;\n  private _observers: Array<StorageObserverInternal<UploadTaskSnapshot>> = [];\n  private _resumable: boolean;\n  /**\n   * Upload state.\n   */\n  _state: InternalTaskState;\n  private _error?: StorageError = undefined;\n  private _uploadUrl?: string = undefined;\n  private _request?: Request<unknown> = undefined;\n  private _chunkMultiplier: number = 1;\n  private _errorHandler: (p1: StorageError) => void;\n  private _metadataErrorHandler: (p1: StorageError) => void;\n  private _resolve?: (p1: UploadTaskSnapshot) => void = undefined;\n  private _reject?: (p1: StorageError) => void = undefined;\n  private pendingTimeout?: ReturnType<typeof setTimeout>;\n  private _promise: Promise<UploadTaskSnapshot>;\n\n  private sleepTime: number;\n\n  private maxSleepTime: number;\n\n  isExponentialBackoffExpired(): boolean {\n    return this.sleepTime > this.maxSleepTime;\n  }\n\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref: Reference, blob: FbsBlob, metadata: Metadata | null = null) {\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = InternalTaskState.RUNNING;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(\n              this.sleepTime * 2,\n              DEFAULT_MIN_SLEEP_TIME_MILLIS\n            );\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n\n  private _makeProgressCallback(): (p1: number, p2: number) => void {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n\n  private _shouldDoResumable(blob: FbsBlob): boolean {\n    return blob.size() > 256 * 1024;\n  }\n\n  private _start(): void {\n    if (this._state !== InternalTaskState.RUNNING) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n\n  private _resolveToken(\n    callback: (authToken: string | null, appCheckToken: string | null) => void\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([\n      this._ref.storage._getAuthToken(),\n      this._ref.storage._getAppCheckToken()\n    ]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case InternalTaskState.RUNNING:\n          callback(authToken, appCheckToken);\n          break;\n        case InternalTaskState.CANCELING:\n          this._transition(InternalTaskState.CANCELED);\n          break;\n        case InternalTaskState.PAUSING:\n          this._transition(InternalTaskState.PAUSED);\n          break;\n        default:\n      }\n    });\n  }\n\n  // TODO(andysoto): assert false\n\n  private _createResumable(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const createRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = createRequest;\n      createRequest.getPromise().then((url: string) => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _fetchStatus(): void {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(\n        this._ref.storage,\n        this._ref._location,\n        url,\n        this._blob\n      );\n      const statusRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status as ResumableUploadStatus;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _continueUpload(): void {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(\n      this._transferred,\n      this._blob.size()\n    );\n\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(\n          this._ref._location,\n          this._ref.storage,\n          url,\n          this._blob,\n          chunkSize,\n          this._mappings,\n          status,\n          this._makeProgressCallback()\n        );\n      } catch (e) {\n        this._error = e as StorageError;\n        this._transition(InternalTaskState.ERROR);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken,\n        /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then((newStatus: ResumableUploadStatus) => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(InternalTaskState.SUCCESS);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n\n  private _increaseMultiplier(): void {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n\n  private _fetchMetadata(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings\n      );\n      const metadataRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._metadataErrorHandler);\n    });\n  }\n\n  private _oneShotUpload(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const multipartRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._errorHandler);\n    });\n  }\n\n  private _updateProgress(transferred: number): void {\n    const old = this._transferred;\n    this._transferred = transferred;\n\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n\n  private _transition(state: InternalTaskState): void {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case InternalTaskState.CANCELING:\n      case InternalTaskState.PAUSING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case InternalTaskState.RUNNING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === InternalTaskState.PAUSED;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case InternalTaskState.PAUSED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.CANCELED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.ERROR:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.SUCCESS:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      default: // Ignore\n    }\n  }\n\n  private completeTransitions_(): void {\n    switch (this._state) {\n      case InternalTaskState.PAUSING:\n        this._transition(InternalTaskState.PAUSED);\n        break;\n      case InternalTaskState.CANCELING:\n        this._transition(InternalTaskState.CANCELED);\n        break;\n      case InternalTaskState.RUNNING:\n        this._start();\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        break;\n    }\n  }\n\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot(): UploadTaskSnapshot {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata!,\n      task: this,\n      ref: this._ref\n    };\n  }\n\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | StorageObserver<UploadTaskSnapshot>\n      | null\n      | ((snapshot: UploadTaskSnapshot) => unknown),\n    error?: ((a: StorageError) => unknown) | null,\n    completed?: CompleteFn | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshot> {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(\n      (nextOrObserver as\n        | StorageObserverInternal<UploadTaskSnapshot>\n        | NextFn<UploadTaskSnapshot>) || undefined,\n      error || undefined,\n      completed || undefined\n    );\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then<U>(\n    onFulfilled?: ((value: UploadTaskSnapshot) => U | Promise<U>) | null,\n    onRejected?: ((error: StorageError) => U | Promise<U>) | null\n  ): Promise<U> {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then<U>(\n      onFulfilled as (value: UploadTaskSnapshot) => U | Promise<U>,\n      onRejected as ((error: unknown) => Promise<never>) | null\n    );\n  }\n\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch<T>(onRejected: (p1: StorageError) => T | Promise<T>): Promise<T> {\n    return this.then(null, onRejected);\n  }\n\n  /**\n   * Adds the given observer.\n   */\n  private _addObserver(observer: Observer<UploadTaskSnapshot>): void {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n\n  /**\n   * Removes the given observer.\n   */\n  private _removeObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n\n  private _notifyObservers(): void {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n\n  private _finishPromise(): void {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          fbsAsync(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject as (p1: StorageError) => void;\n          fbsAsync(toCall.bind(null, this._error as StorageError))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n\n  private _notifyObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          fbsAsync(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          fbsAsync(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n    }\n  }\n\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume(): boolean {\n    const valid =\n      this._state === InternalTaskState.PAUSED ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.RUNNING);\n    }\n    return valid;\n  }\n\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause(): boolean {\n    const valid = this._state === InternalTaskState.RUNNING;\n    if (valid) {\n      this._transition(InternalTaskState.PAUSING);\n    }\n    return valid;\n  }\n\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel(): boolean {\n    const valid =\n      this._state === InternalTaskState.RUNNING ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.CANCELING);\n    }\n    return valid;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines the Firebase StorageReference class.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport { Location } from './implementation/location';\nimport { getMappings } from './implementation/metadata';\nimport { child, lastComponent, parent } from './implementation/path';\nimport {\n  deleteObject as requestsDeleteObject,\n  getBytes,\n  getDownloadUrl as requestsGetDownloadUrl,\n  getMetadata as requestsGetMetadata,\n  list as requestsList,\n  multipartUpload,\n  updateMetadata as requestsUpdateMetadata\n} from './implementation/requests';\nimport { ListOptions, UploadResult } from './public-types';\nimport { dataFromString, StringFormat } from './implementation/string';\nimport { Metadata } from './metadata';\nimport { FirebaseStorageImpl } from './service';\nimport { ListResult } from './list';\nimport { UploadTask } from './task';\nimport { invalidRootOperation, noDownloadURL } from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport {\n  newBlobConnection,\n  newBytesConnection,\n  newStreamConnection,\n  newTextConnection\n} from './platform/connection';\nimport { RequestInfo } from './implementation/requestinfo';\n\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nexport class Reference {\n  _location: Location;\n\n  constructor(\n    private _service: FirebaseStorageImpl,\n    location: string | Location\n  ) {\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString(): string {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n\n  protected _newRef(\n    service: FirebaseStorageImpl,\n    location: Location\n  ): Reference {\n    return new Reference(service, location);\n  }\n\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root(): Reference {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket(): string {\n    return this._location.bucket;\n  }\n\n  /**\n   * The full path of this object.\n   */\n  get fullPath(): string {\n    return this._location.path;\n  }\n\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name(): string {\n    return lastComponent(this._location.path);\n  }\n\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage(): FirebaseStorageImpl {\n    return this._service;\n  }\n\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent(): Reference | null {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name: string): void {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nexport function getBytesInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBytesConnection)\n    .then(bytes =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (bytes as ArrayBuffer).slice(0, maxDownloadSizeBytes)\n        : (bytes as ArrayBuffer)\n    );\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nexport function getBlobInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBlobConnection)\n    .then(blob =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (blob as Blob).slice(0, maxDownloadSizeBytes)\n        : (blob as Blob)\n    );\n}\n\n/** Stream the bytes at the object's location. */\nexport function getStreamInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  ref._throwIfRoot('getStream');\n  const requestInfo: RequestInfo<ReadableStream, ReadableStream> = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n\n  // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n  const newMaxSizeTransform = (n: number): Transformer => {\n    let missingBytes = n;\n    return {\n      transform(chunk, controller: TransformStreamDefaultController) {\n        // GCS may not honor the Range header for small files\n        if (chunk.length < missingBytes) {\n          controller.enqueue(chunk);\n          missingBytes -= chunk.length;\n        } else {\n          controller.enqueue(chunk.slice(0, missingBytes));\n          controller.terminate();\n        }\n      }\n    };\n  };\n\n  const result =\n    maxDownloadSizeBytes !== undefined\n      ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n      : new TransformStream(); // The default transformer forwards all chunks to its readable side\n\n  ref.storage\n    .makeRequestWithTokens(requestInfo, newStreamConnection)\n    .then(readableStream => readableStream.pipeThrough(result))\n    .catch(err => result.writable.abort(err));\n\n  return result.readable;\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(\n    ref.storage,\n    ref._location,\n    getMappings(),\n    new FbsBlob(data, true),\n    metadata\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(finalMetadata => {\n      return {\n        metadata: finalMetadata,\n        ref\n      };\n    });\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): UploadTask {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: Reference,\n  value: string,\n  format: StringFormat = StringFormat.RAW,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = { ...metadata } as Metadata;\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType!;\n  }\n  return uploadBytes(ref, data.data, metadataClone);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: Reference): Promise<ListResult> {\n  const accumulator: ListResult = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(\n  ref: Reference,\n  accumulator: ListResult,\n  pageToken?: string\n): Promise<void> {\n  const opt: ListOptions = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: Reference,\n  options?: ListOptions | null\n): Promise<ListResult> {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber(\n        'options.maxResults',\n        /* minValue= */ 1,\n        /* maxValue= */ 1000,\n        options.maxResults\n      );\n    }\n  }\n  const op = options || {};\n  const requestInfo = requestsList(\n    ref.storage,\n    ref._location,\n    /*delimiter= */ '/',\n    op.pageToken,\n    op.maxResults\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nexport function getMetadata(ref: Reference): Promise<Metadata> {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = requestsGetMetadata(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nexport function updateMetadata(\n  ref: Reference,\n  metadata: Partial<Metadata>\n): Promise<Metadata> {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = requestsUpdateMetadata(\n    ref.storage,\n    ref._location,\n    metadata,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: Reference): Promise<string> {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = requestsGetDownloadUrl(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(url => {\n      if (url === null) {\n        throw noDownloadURL();\n      }\n      return url;\n    });\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: Reference): Promise<void> {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = requestsDeleteObject(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nexport function _getChild(ref: Reference, childPath: string): Reference {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Location } from './implementation/location';\nimport { FailRequest } from './implementation/failrequest';\nimport { Request, makeRequest } from './implementation/request';\nimport { RequestInfo } from './implementation/requestinfo';\nimport { Reference, _getChild } from './reference';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  _isFirebaseServerApp\n} from '@firebase/app';\nimport {\n  CONFIG_STORAGE_BUCKET_KEY,\n  DEFAULT_HOST,\n  DEFAULT_MAX_OPERATION_RETRY_TIME,\n  DEFAULT_MAX_UPLOAD_RETRY_TIME\n} from './implementation/constants';\nimport {\n  invalidArgument,\n  appDeleted,\n  noDefaultBucket\n} from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport { FirebaseStorage } from './public-types';\nimport { createMockUserToken, EmulatorMockTokenOptions } from '@firebase/util';\nimport { Connection, ConnectionType } from './implementation/connection';\n\nexport function isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service: FirebaseStorageImpl, url: string): Reference {\n  return new Reference(service, url);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(\n  ref: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket!);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\n\n/**\n * Returns a storage Reference for the given url.\n * @param storage - `Storage` instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorageImpl, url?: string): Reference;\n/**\n * Returns a storage Reference for the given path in the\n * default bucket.\n * @param storageOrRef - `Storage` service or storage `Reference`.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage\n * instance provided) or returns same reference (if Reference provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference;\nexport function ref(\n  serviceOrRef: FirebaseStorageImpl | Reference,\n  pathOrUrl?: string\n): Reference | null {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument(\n        'To use ref(service, url), the first argument must be a Storage instance.'\n      );\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\n\nfunction extractBucket(\n  host: string,\n  config?: FirebaseOptions\n): Location | null {\n  const bucketString = config?.[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\n\nexport function connectStorageEmulator(\n  storage: FirebaseStorageImpl,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  storage.host = `${host}:${port}`;\n  storage._protocol = 'http';\n  const { mockUserToken } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken =\n      typeof mockUserToken === 'string'\n        ? mockUserToken\n        : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nexport class FirebaseStorageImpl implements FirebaseStorage {\n  _bucket: Location | null = null;\n  /**\n   * This string can be in the formats:\n   * - host\n   * - host:port\n   */\n  private _host: string = DEFAULT_HOST;\n  _protocol: string = 'https';\n  protected readonly _appId: string | null = null;\n  private readonly _requests: Set<Request<unknown>>;\n  private _deleted: boolean = false;\n  private _maxOperationRetryTime: number;\n  private _maxUploadRetryTime: number;\n  _overrideAuthToken?: string;\n\n  constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    readonly app: FirebaseApp,\n    readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    /**\n     * @internal\n     */\n    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>,\n    /**\n     * @internal\n     */\n    readonly _url?: string,\n    readonly _firebaseVersion?: string\n  ) {\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host(): string {\n    return this._host;\n  }\n\n  set host(host: string) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime(): number {\n    return this._maxUploadRetryTime;\n  }\n\n  set maxUploadRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxUploadRetryTime = time;\n  }\n\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime(): number {\n    return this._maxOperationRetryTime;\n  }\n\n  set maxOperationRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxOperationRetryTime = time;\n  }\n\n  async _getAuthToken(): Promise<string | null> {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({ optional: true });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n\n  async _getAppCheckToken(): Promise<string | null> {\n    if (_isFirebaseServerApp(this.app) && this.app.settings.appCheckToken) {\n      return this.app.settings.appCheckToken;\n    }\n    const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete(): Promise<void> {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc: Location): Reference {\n    return new Reference(this, loc);\n  }\n\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>,\n    authToken: string | null,\n    appCheckToken: string | null,\n    retry = true\n  ): Request<O> {\n    if (!this._deleted) {\n      const request = makeRequest(\n        requestInfo,\n        this._appId,\n        authToken,\n        appCheckToken,\n        requestFactory,\n        this._firebaseVersion,\n        retry\n      );\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(\n        () => this._requests.delete(request),\n        () => this._requests.delete(request)\n      );\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n\n  async makeRequestWithTokens<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>\n  ): Promise<O> {\n    const [authToken, appCheckToken] = await Promise.all([\n      this._getAuthToken(),\n      this._getAppCheckToken()\n    ]);\n\n    return this._makeRequest(\n      requestInfo,\n      requestFactory,\n      authToken,\n      appCheckToken\n    ).getPromise();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Storage.\n */\nexport const STORAGE_TYPE = 'storage';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\n\nimport {\n  ref as refInternal,\n  FirebaseStorageImpl,\n  connectStorageEmulator as connectEmulatorInternal\n} from './service';\nimport { Provider } from '@firebase/component';\n\nimport {\n  StorageReference,\n  FirebaseStorage,\n  UploadResult,\n  ListOptions,\n  ListResult,\n  UploadTask,\n  SettableMetadata,\n  UploadMetadata,\n  FullMetadata\n} from './public-types';\nimport { Metadata as MetadataInternal } from './metadata';\nimport {\n  uploadBytes as uploadBytesInternal,\n  uploadBytesResumable as uploadBytesResumableInternal,\n  uploadString as uploadStringInternal,\n  getMetadata as getMetadataInternal,\n  updateMetadata as updateMetadataInternal,\n  list as listInternal,\n  listAll as listAllInternal,\n  getDownloadURL as getDownloadURLInternal,\n  deleteObject as deleteObjectInternal,\n  Reference,\n  _getChild as _getChildInternal,\n  getBytesInternal\n} from './reference';\nimport { STORAGE_TYPE } from './constants';\nimport {\n  EmulatorMockTokenOptions,\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\nimport { StringFormat } from './implementation/string';\n\nexport { EmulatorMockTokenOptions } from '@firebase/util';\n\nexport { StorageError, StorageErrorCode } from './implementation/error';\n\n/**\n * Public types.\n */\nexport * from './public-types';\n\nexport { Location as _Location } from './implementation/location';\nexport { UploadTask as _UploadTask } from './task';\nexport type { Reference as _Reference } from './reference';\nexport type { FirebaseStorageImpl as _FirebaseStorageImpl } from './service';\nexport { FbsBlob as _FbsBlob } from './implementation/blob';\nexport { dataFromString as _dataFromString } from './implementation/string';\nexport {\n  invalidRootOperation as _invalidRootOperation,\n  invalidArgument as _invalidArgument\n} from './implementation/error';\nexport {\n  TaskEvent as _TaskEvent,\n  TaskState as _TaskState\n} from './implementation/taskenums';\nexport { StringFormat };\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nexport function getBytes(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadBytesInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: StorageReference,\n  value: string,\n  format?: StringFormat,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadStringInternal(\n    ref as Reference,\n    value,\n    format,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): UploadTask {\n  ref = getModularInstance(ref);\n  return uploadBytesResumableInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  ) as UploadTask;\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nexport function getMetadata(ref: StorageReference): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return getMetadataInternal(ref as Reference) as Promise<FullMetadata>;\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nexport function updateMetadata(\n  ref: StorageReference,\n  metadata: SettableMetadata\n): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return updateMetadataInternal(\n    ref as Reference,\n    metadata as Partial<MetadataInternal>\n  ) as Promise<FullMetadata>;\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: StorageReference,\n  options?: ListOptions\n): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listInternal(ref as Reference, options);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: StorageReference): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listAllInternal(ref as Reference);\n}\n\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: StorageReference): Promise<string> {\n  ref = getModularInstance(ref);\n  return getDownloadURLInternal(ref as Reference);\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: StorageReference): Promise<void> {\n  ref = getModularInstance(ref);\n  return deleteObjectInternal(ref as Reference);\n}\n\n/**\n * Returns a {@link StorageReference} for the given url.\n * @param storage - {@link FirebaseStorage} instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorage, url?: string): StorageReference;\n/**\n * Returns a {@link StorageReference} for the given path in the\n * default bucket.\n * @param storageOrRef - {@link FirebaseStorage} or {@link StorageReference}.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if {@link FirebaseStorage}\n * instance provided) or returns same reference (if {@link StorageReference} provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorage | StorageReference,\n  path?: string\n): StorageReference;\nexport function ref(\n  serviceOrRef: FirebaseStorage | StorageReference,\n  pathOrUrl?: string\n): StorageReference | null {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return refInternal(\n    serviceOrRef as FirebaseStorageImpl | Reference,\n    pathOrUrl\n  );\n}\n\n/**\n * @internal\n */\nexport function _getChild(ref: StorageReference, childPath: string): Reference {\n  return _getChildInternal(ref as Reference, childPath);\n}\n\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nexport function getStorage(\n  app: FirebaseApp = getApp(),\n  bucketUrl?: string\n): FirebaseStorage {\n  app = getModularInstance(app);\n  const storageProvider: Provider<'storage'> = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nexport function connectStorageEmulator(\n  storage: FirebaseStorage,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  connectEmulatorInternal(storage as FirebaseStorageImpl, host, port, options);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { StorageReference } from './public-types';\nimport { Reference, getBlobInternal } from './reference';\nimport { getModularInstance } from '@firebase/util';\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\nexport function getBlob(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref = getModularInstance(ref);\n  return getBlobInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nexport function getStream(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  throw new Error('getStream() is only supported by NodeJS builds');\n}\n", "/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\n\nimport { FirebaseStorageImpl } from '../src/service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\nimport { FirebaseStorage } from './public-types';\nimport { STORAGE_TYPE } from './constants';\n\nexport * from './api';\nexport * from './api.browser';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): FirebaseStorage {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n\n  return new FirebaseStorageImpl(\n    app,\n    authProvider,\n    appCheckProvider,\n    url,\n    SDK_VERSION\n  );\n}\n\nfunction registerStorage(): void {\n  _registerComponent(\n    new Component(\n      STORAGE_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '__RUNTIME_ENV__');\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterStorage();\n"], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;AAeG;AACH;;AAEG;AAEH;;AAEG;AACI,MAAMA,YAAY,GAAG,gCAAgC;AAE5D;;AAEG;AACI,MAAMC,yBAAyB,GAAG,eAAe;AAExD;;;;AAIG;AACI,MAAMC,gCAAgC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;AAE7D;;;;AAIG;AACI,MAAMC,6BAA6B,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAE3D;;AAEG;AACI,MAAMC,6BAA6B,GAAG,IAAI;;AC/CjD;;;;;;;;;;;;;;;AAeG;AAMH;;;AAGG;AACG,MAAOC,YAAa,SAAQC,aAAa;EAO7C;;;;;AAKG;EACHC,YAAYC,IAAsB,EAAEC,OAAe,EAAqB;IAAA,IAAXC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAU,CAAC;IACtE,KAAK,CACHG,WAAW,CAACN,IAAI,CAAC,EACjB,qBAAqBC,OAAO,KAAKK,WAAW,CAACN,IAAI,CAAC,GAAG,CACtD;IAJ0D,IAAO,CAAAE,OAAA,GAAPA,OAAO;IAXpE;;AAEG;IACH,KAAAK,UAAU,GAAsC;MAAEC,cAAc,EAAE;IAAI,CAAE;IAatE,IAAI,CAACC,YAAY,GAAG,IAAI,CAACR,OAAO;;;IAGhCS,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEd,YAAY,CAACe,SAAS,CAAC;;EAGrD,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACX,OAAO;;EAGrB,IAAIW,MAAMA,CAACA,MAAc;IACvB,IAAI,CAACX,OAAO,GAAGW,MAAM;;EAGvB;;AAEG;EACHC,WAAWA,CAACd,IAAsB;IAChC,OAAOM,WAAW,CAACN,IAAI,CAAC,KAAK,IAAI,CAACA,IAAI;;EAGxC;;AAEG;EACH,IAAIQ,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACD,UAAU,CAACC,cAAc;;EAGvC,IAAIA,cAAcA,CAACA,cAA6B;IAC9C,IAAI,CAACD,UAAU,CAACC,cAAc,GAAGA,cAAc;IAC/C,IAAI,IAAI,CAACD,UAAU,CAACC,cAAc,EAAE;MAClC,IAAI,CAACP,OAAO,GAAG,GAAG,IAAI,CAACQ,YAAY,KAAK,IAAI,CAACF,UAAU,CAACC,cAAc,EAAE;KACzE,MAAM;MACL,IAAI,CAACP,OAAO,GAAG,IAAI,CAACQ,YAAY;;;AAGrC;AAID;;;AAGG;IACSM,gBAAA;AAAZ,WAAYA,gBAAgB;;EAE1BA,gBAAA,uBAAmB;EACnBA,gBAAA,yCAAqC;EACrCA,gBAAA,yCAAqC;EACrCA,gBAAA,2CAAuC;EACvCA,gBAAA,qCAAiC;EACjCA,gBAAA,uCAAmC;EACnCA,gBAAA,iCAA6B;EAC7BA,gBAAA,yCAAqC;EACrCA,gBAAA,iDAA6C;EAC7CA,gBAAA,yCAAqC;EACrCA,gBAAA,yBAAqB;;EAErBA,gBAAA,6CAAyC;EACzCA,gBAAA,+BAA2B;EAC3BA,gBAAA,qDAAiD;EACjDA,gBAAA,2CAAuC;EACvCA,gBAAA,2CAAuC;EACvCA,gBAAA,qDAAiD;EACjDA,gBAAA,uCAAmC;EACnCA,gBAAA,yCAAqC;EACrCA,gBAAA,qDAAiD;EACjDA,gBAAA,+BAA2B;EAC3BA,gBAAA,qDAAiD;EACjDA,gBAAA,qCAAiC;EACjCA,gBAAA,qCAAiC;EACjCA,gBAAA,uDAAmD;AACrD,CAAC,EA5BWA,gBAAgB,KAAhBA,gBAAgB,GA4B3B;AAEK,SAAUT,WAAWA,CAACN,IAAsB;EAChD,OAAO,UAAU,GAAGA,IAAI;AAC1B;SAEgBgB,OAAOA,CAAA;EACrB,MAAMf,OAAO,GACX,gEAAgE,GAChE,kBAAkB;EACpB,OAAO,IAAIJ,YAAY,CAACkB,gBAAgB,CAACE,OAAO,EAAEhB,OAAO,CAAC;AAC5D;AAEM,SAAUiB,cAAcA,CAACC,IAAY;EACzC,OAAO,IAAItB,YAAY,CACrBkB,gBAAgB,CAACK,gBAAgB,EACjC,UAAU,GAAGD,IAAI,GAAG,mBAAmB,CACxC;AACH;AAgBM,SAAUE,aAAaA,CAACC,MAAc;EAC1C,OAAO,IAAIzB,YAAY,CACrBkB,gBAAgB,CAACQ,cAAc,EAC/B,oBAAoB,GAClBD,MAAM,GACN,mCAAmC,GACnC,uCAAuC,CAC1C;AACH;SAEgBE,eAAeA,CAAA;EAC7B,MAAMvB,OAAO,GACX,gEAAgE,GAChE,+BAA+B;EACjC,OAAO,IAAIJ,YAAY,CAACkB,gBAAgB,CAACU,eAAe,EAAExB,OAAO,CAAC;AACpE;SAEgByB,eAAeA,CAAA;EAC7B,OAAO,IAAI7B,YAAY,CACrBkB,gBAAgB,CAACY,gBAAgB,EACjC,+EAA+E,CAChF;AACH;AAEM,SAAUC,YAAYA,CAACT,IAAY;EACvC,OAAO,IAAItB,YAAY,CACrBkB,gBAAgB,CAACc,YAAY,EAC7B,2CAA2C,GAAGV,IAAI,GAAG,IAAI,CAC1D;AACH;SAEgBW,kBAAkBA,CAAA;EAChC,OAAO,IAAIjC,YAAY,CACrBkB,gBAAgB,CAACgB,oBAAoB,EACrC,0DAA0D,CAC3D;AACH;SAmBgBC,QAAQA,CAAA;EACtB,OAAO,IAAInC,YAAY,CACrBkB,gBAAgB,CAACkB,QAAQ,EACzB,oCAAoC,CACrC;AACH;AASM,SAAUC,UAAUA,CAACC,GAAW;EACpC,OAAO,IAAItC,YAAY,CACrBkB,gBAAgB,CAACqB,WAAW,EAC5B,eAAe,GAAGD,GAAG,GAAG,IAAI,CAC7B;AACH;AAEM,SAAUE,oBAAoBA,CAACf,MAAc;EACjD,OAAO,IAAIzB,YAAY,CACrBkB,gBAAgB,CAACuB,sBAAsB,EACvC,0BAA0B,GAAGhB,MAAM,GAAG,IAAI,CAC3C;AACH;SAEgBiB,eAAeA,CAAA;EAC7B,OAAO,IAAI1C,YAAY,CACrBkB,gBAAgB,CAACyB,iBAAiB,EAClC,oBAAoB,GAClB,0BAA0B,GAC1B/C,yBAAyB,GACzB,uCAAuC,CAC1C;AACH;SAEgBgD,eAAeA,CAAA;EAC7B,OAAO,IAAI5C,YAAY,CACrBkB,gBAAgB,CAAC2B,iBAAiB,EAClC,wDAAwD,CACzD;AACH;SAEgBC,mBAAmBA,CAAA;EACjC,OAAO,IAAI9C,YAAY,CACrBkB,gBAAgB,CAAC6B,sBAAsB,EACvC,sEAAsE,CACvE;AACH;SAEgBC,aAAaA,CAAA;EAC3B,OAAO,IAAIhD,YAAY,CACrBkB,gBAAgB,CAAC+B,eAAe,EAChC,iDAAiD,CAClD;AACH;AAEM,SAAUC,eAAeA,CAACC,QAAgB;EAC9C,OAAO,IAAInD,YAAY,CACrBkB,gBAAgB,CAACkC,uBAAuB,EACxC,GAAGD,QAAQ,wJAAwJ,CACpK;AACH;AAEA;;AAEG;AACG,SAAUE,eAAeA,CAACjD,OAAe;EAC7C,OAAO,IAAIJ,YAAY,CAACkB,gBAAgB,CAACoC,gBAAgB,EAAElD,OAAO,CAAC;AACrE;SA+BgBmD,UAAUA,CAAA;EACxB,OAAO,IAAIvD,YAAY,CACrBkB,gBAAgB,CAACsC,WAAW,EAC5B,+BAA+B,CAChC;AACH;AAEA;;;;AAIG;AACG,SAAUC,oBAAoBA,CAACC,IAAY;EAC/C,OAAO,IAAI1D,YAAY,CACrBkB,gBAAgB,CAACyC,sBAAsB,EACvC,iBAAiB,GACfD,IAAI,GACJ,+DAA+D,GAC/D,oDAAoD,CACvD;AACH;AAEA;;;AAGG;AACa,SAAAE,aAAaA,CAACC,MAAc,EAAEzD,OAAe;EAC3D,OAAO,IAAIJ,YAAY,CACrBkB,gBAAgB,CAAC4C,cAAc,EAC/B,gCAAgC,GAAGD,MAAM,GAAG,KAAK,GAAGzD,OAAO,CAC5D;AACH;AASA;;AAEG;AACG,SAAU2D,aAAaA,CAAC3D,OAAe;EAC3C,MAAM,IAAIJ,YAAY,CACpBkB,gBAAgB,CAAC8C,cAAc,EAC/B,kBAAkB,GAAG5D,OAAO,CAC7B;AACH;;AClWA;;;;;;;;;;;;;;;AAeG;AAUH;;;;AAIG;MACU6D,QAAQ;EAGnB/D,WAA4BA,CAAAuB,MAAc,EAAEH,IAAY;IAA5B,IAAM,CAAAG,MAAA,GAANA,MAAM;IAChC,IAAI,CAACyC,KAAK,GAAG5C,IAAI;;EAGnB,IAAIA,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC4C,KAAK;;EAGnB,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAAC7C,IAAI,CAACf,MAAM,KAAK,CAAC;;EAG/B6D,aAAaA,CAAA;IACX,MAAMC,MAAM,GAAGC,kBAAkB;IACjC,OAAO,KAAK,GAAGD,MAAM,CAAC,IAAI,CAAC5C,MAAM,CAAC,GAAG,KAAK,GAAG4C,MAAM,CAAC,IAAI,CAAC/C,IAAI,CAAC;;EAGhEiD,mBAAmBA,CAAA;IACjB,MAAMF,MAAM,GAAGC,kBAAkB;IACjC,OAAO,KAAK,GAAGD,MAAM,CAAC,IAAI,CAAC5C,MAAM,CAAC,GAAG,IAAI;;EAG3C,OAAO+C,kBAAkBA,CAACC,YAAoB,EAAEC,IAAY;IAC1D,IAAIC,cAAc;IAClB,IAAI;MACFA,cAAc,GAAGV,QAAQ,CAACW,WAAW,CAACH,YAAY,EAAEC,IAAI,CAAC;KAC1D,CAAC,OAAOG,CAAC,EAAE;;;MAGV,OAAO,IAAIZ,QAAQ,CAACQ,YAAY,EAAE,EAAE,CAAC;;IAEvC,IAAIE,cAAc,CAACrD,IAAI,KAAK,EAAE,EAAE;MAC9B,OAAOqD,cAAc;KACtB,MAAM;MACL,MAAMnC,oBAAoB,CAACiC,YAAY,CAAC;;;EAI5C,OAAOG,WAAWA,CAACtC,GAAW,EAAEoC,IAAY;IAC1C,IAAII,QAAQ,GAAoB,IAAI;IACpC,MAAMC,YAAY,GAAG,qBAAqB;IAE1C,SAASC,QAAQA,CAACC,GAAa;MAC7B,IAAIA,GAAG,CAAC3D,IAAI,CAAC4D,MAAM,CAACD,GAAG,CAAC3D,IAAI,CAACf,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAChD0E,GAAG,CAACf,KAAK,GAAGe,GAAG,CAACf,KAAK,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;;IAGtC,MAAMC,MAAM,GAAG,WAAW;IAC1B,MAAMC,OAAO,GAAG,IAAIC,MAAM,CAAC,QAAQ,GAAGP,YAAY,GAAGK,MAAM,EAAE,GAAG,CAAC;IACjE,MAAMG,SAAS,GAAG;MAAE9D,MAAM,EAAE,CAAC;MAAEH,IAAI,EAAE;IAAC,CAAE;IAExC,SAASkE,UAAUA,CAACP,GAAa;MAC/BA,GAAG,CAACf,KAAK,GAAGuB,kBAAkB,CAACR,GAAG,CAAC3D,IAAI,CAAC;;IAE1C,MAAMoE,OAAO,GAAG,gBAAgB;IAChC,MAAMC,mBAAmB,GAAGjB,IAAI,CAACkB,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC;IACvD,MAAMC,mBAAmB,GAAG,iBAAiB;IAC7C,MAAMC,qBAAqB,GAAG,IAAIR,MAAM,CACtC,aAAaK,mBAAmB,IAAID,OAAO,MAAMX,YAAY,KAAKc,mBAAmB,EAAE,EACvF,GAAG,CACJ;IACD,MAAME,sBAAsB,GAAG;MAAEtE,MAAM,EAAE,CAAC;MAAEH,IAAI,EAAE;IAAC,CAAE;IAErD,MAAM0E,gBAAgB,GACpBtB,IAAI,KAAK/E,YAAY,GACjB,qDAAqD,GACrD+E,IAAI;IACV,MAAMuB,gBAAgB,GAAG,UAAU;IACnC,MAAMC,kBAAkB,GAAG,IAAIZ,MAAM,CACnC,aAAaU,gBAAgB,IAAIjB,YAAY,IAAIkB,gBAAgB,EAAE,EACnE,GAAG,CACJ;IACD,MAAME,mBAAmB,GAAG;MAAE1E,MAAM,EAAE,CAAC;MAAEH,IAAI,EAAE;IAAC,CAAE;IAElD,MAAM8E,MAAM,GAAG,CACb;MAAEC,KAAK,EAAEhB,OAAO;MAAEiB,OAAO,EAAEf,SAAS;MAAEgB,UAAU,EAAEvB;IAAQ,CAAE,EAC5D;MACEqB,KAAK,EAAEP,qBAAqB;MAC5BQ,OAAO,EAAEP,sBAAsB;MAC/BQ,UAAU,EAAEf;IACb,GACD;MACEa,KAAK,EAAEH,kBAAkB;MACzBI,OAAO,EAAEH,mBAAmB;MAC5BI,UAAU,EAAEf;IACb,EACF;IACD,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAAC7F,MAAM,EAAEiG,CAAC,EAAE,EAAE;MACtC,MAAMC,KAAK,GAAGL,MAAM,CAACI,CAAC,CAAC;MACvB,MAAME,QAAQ,GAAGD,KAAK,CAACJ,KAAK,CAACM,IAAI,CAACrE,GAAG,CAAC;MACtC,IAAIoE,QAAQ,EAAE;QACZ,MAAME,WAAW,GAAGF,QAAQ,CAACD,KAAK,CAACH,OAAO,CAAC7E,MAAM,CAAC;QAClD,IAAIoF,SAAS,GAAGH,QAAQ,CAACD,KAAK,CAACH,OAAO,CAAChF,IAAI,CAAC;QAC5C,IAAI,CAACuF,SAAS,EAAE;UACdA,SAAS,GAAG,EAAE;;QAEhB/B,QAAQ,GAAG,IAAIb,QAAQ,CAAC2C,WAAW,EAAEC,SAAS,CAAC;QAC/CJ,KAAK,CAACF,UAAU,CAACzB,QAAQ,CAAC;QAC1B;;;IAGJ,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpB,MAAMzC,UAAU,CAACC,GAAG,CAAC;;IAEvB,OAAOwC,QAAQ;;AAElB;;ACxHD;;AAEG;MACUgC,WAAW;EAGtB5G,YAAY6G,KAAmB;IAC7B,IAAI,CAACC,QAAQ,GAAGC,OAAO,CAACC,MAAM,CAAIH,KAAK,CAAC;;;EAI1CI,UAAUA,CAAA;IACR,OAAO,IAAI,CAACH,QAAQ;;;EAItBI,MAAMA,CAAA,EAAmB;IAAA,IAAlBC,UAAU,GAAA/G,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA;AAC1B;;ACpCD;;;;;;;;;;;;;;;AAeG;AAUH;;;;;;;;;;;AAWG;AACG,SAAUgH,KAAKA,CACnBC,SAGS;AACT;AACAC,iBAA8C,EAC9CC,OAAe;;;EAIf,IAAIC,WAAW,GAAG,CAAC;;;;EAInB,IAAIC,cAAc,GAAQ,IAAI;;EAE9B,IAAIC,eAAe,GAAQ,IAAI;EAC/B,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,WAAW,GAAG,CAAC;EAEnB,SAAS3F,QAAQA,CAAA;IACf,OAAO2F,WAAW,KAAK,CAAC;;EAE1B,IAAIC,iBAAiB,GAAG,KAAK;EAE7B,SAASC,eAAeA,CAAA,EAAe;IACrC,IAAI,CAACD,iBAAiB,EAAE;MACtBA,iBAAiB,GAAG,IAAI;MAAC,SAAAE,IAAA,GAAA3H,SAAA,CAAAC,MAAA,EAFD2H,IAAW,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAXF,IAAW,CAAAE,IAAA,IAAA9H,SAAA,CAAA8H,IAAA;MAAA;MAGnCZ,iBAAiB,CAACa,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;;;EAIvC,SAASI,aAAaA,CAACC,MAAc;IACnCZ,cAAc,GAAGa,UAAU,CAAC,MAAK;MAC/Bb,cAAc,GAAG,IAAI;MACrBJ,SAAS,CAACkB,eAAe,EAAEtG,QAAQ,EAAE,CAAC;KACvC,EAAEoG,MAAM,CAAC;;EAGZ,SAASG,kBAAkBA,CAAA;IACzB,IAAId,eAAe,EAAE;MACnBe,YAAY,CAACf,eAAe,CAAC;;;EAIjC,SAASa,eAAeA,CAACG,OAAgB,EAAgB;IACvD,IAAIb,iBAAiB,EAAE;MACrBW,kBAAkB,EAAE;MACpB;;IACD,SAAAG,KAAA,GAAAvI,SAAA,CAAAC,MAAA,EAJ2C2H,IAAW,OAAAC,KAAA,CAAAU,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAXZ,IAAW,CAAAY,KAAA,QAAAxI,SAAA,CAAAwI,KAAA;IAAA;IAKvD,IAAIF,OAAO,EAAE;MACXF,kBAAkB,EAAE;MACpBV,eAAe,CAACe,IAAI,CAAC,IAAI,EAAEH,OAAO,EAAE,GAAGV,IAAI,CAAC;MAC5C;;IAEF,MAAMc,QAAQ,GAAG7G,QAAQ,EAAE,IAAI0F,UAAU;IACzC,IAAImB,QAAQ,EAAE;MACZN,kBAAkB,EAAE;MACpBV,eAAe,CAACe,IAAI,CAAC,IAAI,EAAEH,OAAO,EAAE,GAAGV,IAAI,CAAC;MAC5C;;IAEF,IAAIR,WAAW,GAAG,EAAE,EAAE;;MAEpBA,WAAW,IAAI,CAAC;;IAElB,IAAIuB,UAAU;IACd,IAAInB,WAAW,KAAK,CAAC,EAAE;MACrBA,WAAW,GAAG,CAAC;MACfmB,UAAU,GAAG,CAAC;KACf,MAAM;MACLA,UAAU,GAAG,CAACvB,WAAW,GAAGwB,IAAI,CAACC,MAAM,EAAE,IAAI,IAAI;;IAEnDb,aAAa,CAACW,UAAU,CAAC;;EAE3B,IAAIG,OAAO,GAAG,KAAK;EAEnB,SAASC,IAAIA,CAACC,UAAmB;IAC/B,IAAIF,OAAO,EAAE;MACX;;IAEFA,OAAO,GAAG,IAAI;IACdV,kBAAkB,EAAE;IACpB,IAAIX,iBAAiB,EAAE;MACrB;;IAEF,IAAIJ,cAAc,KAAK,IAAI,EAAE;MAC3B,IAAI,CAAC2B,UAAU,EAAE;QACfxB,WAAW,GAAG,CAAC;;MAEjBa,YAAY,CAAChB,cAAc,CAAC;MAC5BW,aAAa,CAAC,CAAC,CAAC;KACjB,MAAM;MACL,IAAI,CAACgB,UAAU,EAAE;QACfxB,WAAW,GAAG,CAAC;;;;EAIrBQ,aAAa,CAAC,CAAC,CAAC;EAChBV,eAAe,GAAGY,UAAU,CAAC,MAAK;IAChCX,UAAU,GAAG,IAAI;IACjBwB,IAAI,CAAC,IAAI,CAAC;GACX,EAAE5B,OAAO,CAAC;EACX,OAAO4B,IAAI;AACb;AAEA;;;;;;AAMG;AACG,SAAUA,IAAIA,CAACE,EAAM;EACzBA,EAAE,CAAC,KAAK,CAAC;AACX;;ACxJA;;;;;;;;;;;;;;;AAeG;AAIG,SAAUC,SAASA,CAAIC,CAAuB;EAClD,OAAOA,CAAC,KAAK,KAAK,CAAC;AACrB;AAEA;AACM,SAAUC,UAAUA,CAACD,CAAU;EACnC,OAAO,OAAOA,CAAC,KAAK,UAAU;AAChC;AAEM,SAAUE,gBAAgBA,CAACF,CAAU;EACzC,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAI,CAACtB,KAAK,CAACyB,OAAO,CAACH,CAAC,CAAC;AACnD;AAEM,SAAUI,QAAQA,CAACJ,CAAU;EACjC,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,YAAYK,MAAM;AACrD;AAEM,SAAUC,YAAYA,CAACN,CAAU;EACrC,OAAOO,mBAAmB,EAAE,IAAIP,CAAC,YAAYQ,IAAI;AACnD;SAEgBD,mBAAmBA,CAAA;EACjC,OAAO,OAAOC,IAAI,KAAK,WAAW;AACpC;AAEM,SAAUC,cAAcA,CAC5BC,QAAgB,EAChBC,QAAgB,EAChBC,QAAgB,EAChBC,KAAa;EAEb,IAAIA,KAAK,GAAGF,QAAQ,EAAE;IACpB,MAAM/G,eAAe,CACnB,sBAAsB8G,QAAQ,eAAeC,QAAQ,cAAc,CACpE;;EAEH,IAAIE,KAAK,GAAGD,QAAQ,EAAE;IACpB,MAAMhH,eAAe,CACnB,sBAAsB8G,QAAQ,eAAeE,QAAQ,WAAW,CACjE;;AAEL;;AC5DA;;;;;;;;;;;;;;;AAeG;SAOaE,OAAOA,CACrBC,OAAe,EACf9F,IAAY,EACZ+F,QAAgB;EAEhB,IAAIC,MAAM,GAAGhG,IAAI;EACjB,IAAI+F,QAAQ,IAAI,IAAI,EAAE;IACpBC,MAAM,GAAG,WAAWhG,IAAI,EAAE;;EAE5B,OAAO,GAAG+F,QAAQ,MAAMC,MAAM,MAAMF,OAAO,EAAE;AAC/C;AAEM,SAAUG,eAAeA,CAACC,MAAiB;EAC/C,MAAMvG,MAAM,GAAGC,kBAAkB;EACjC,IAAIuG,SAAS,GAAG,GAAG;EACnB,KAAK,MAAMC,GAAG,IAAIF,MAAM,EAAE;IACxB,IAAIA,MAAM,CAACG,cAAc,CAACD,GAAG,CAAC,EAAE;MAC9B,MAAME,QAAQ,GAAG3G,MAAM,CAACyG,GAAG,CAAC,GAAG,GAAG,GAAGzG,MAAM,CAACuG,MAAM,CAACE,GAAG,CAAC,CAAC;MACxDD,SAAS,GAAGA,SAAS,GAAGG,QAAQ,GAAG,GAAG;;;;EAK1CH,SAAS,GAAGA,SAAS,CAAC1F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,OAAO0F,SAAS;AAClB;;ACqBA;;AAEG;AACH,IAAYI,SAIX;AAJD,WAAYA,SAAS;EACnBA,SAAA,CAAAA,SAAA,8BAAY;EACZA,SAAA,CAAAA,SAAA,wCAAiB;EACjBA,SAAA,CAAAA,SAAA,wBAAS;AACX,CAAC,EAJWA,SAAS,KAATA,SAAS,GAIpB;;AC3ED;;;;;;;;;;;;;;;AAeG;AAEH;;;;;AAKG;AACa,SAAAC,iBAAiBA,CAC/BlK,MAAc,EACdmK,oBAA8B;;;EAI9B,MAAMC,iBAAiB,GAAGpK,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;EACvD,MAAMqK,eAAe,GAAG;;EAEtB,GAAG;;EAEH,GAAG,CACJ;EACD,MAAMC,gBAAgB,GAAGD,eAAe,CAACE,OAAO,CAACvK,MAAM,CAAC,KAAK,CAAC,CAAC;EAC/D,MAAMwK,qBAAqB,GAAGL,oBAAoB,CAACI,OAAO,CAACvK,MAAM,CAAC,KAAK,CAAC,CAAC;EACzE,OAAOoK,iBAAiB,IAAIE,gBAAgB,IAAIE,qBAAqB;AACvE;;ACvCA;;;;;;;;;;;;;;;AAeG;AA4BH;;;;;;;AAOG;AACH,MAAMC,cAAc;EAUlBvL,WACUA,CAAAwL,IAAY,EACZC,OAAe,EACfC,QAAiB,EACjBC,KAAwC,EACxCC,aAAuB,EACvBC,qBAA+B,EAC/BC,SAA+B,EAC/BC,cAAmC,EACnCC,QAAgB,EAChBC,iBAA4D,EAC5DC,kBAAuC,EAC3B;IAAA,IAAZC,KAAA,GAAA/L,SAAA,CAAAC,MAAA,SAAAD,SAAA,SAAAE,SAAA,GAAAF,SAAA,OAAQ,IAAI;IAXZ,IAAI,CAAAoL,IAAA,GAAJA,IAAI;IACJ,IAAO,CAAAC,OAAA,GAAPA,OAAO;IACP,IAAQ,CAAAC,QAAA,GAARA,QAAQ;IACR,IAAK,CAAAC,KAAA,GAALA,KAAK;IACL,IAAa,CAAAC,aAAA,GAAbA,aAAa;IACb,IAAqB,CAAAC,qBAAA,GAArBA,qBAAqB;IACrB,IAAS,CAAAC,SAAA,GAATA,SAAS;IACT,IAAc,CAAAC,cAAA,GAAdA,cAAc;IACd,IAAQ,CAAAC,QAAA,GAARA,QAAQ;IACR,IAAiB,CAAAC,iBAAA,GAAjBA,iBAAiB;IACjB,IAAkB,CAAAC,kBAAA,GAAlBA,kBAAkB;IAClB,IAAK,CAAAC,KAAA,GAALA,KAAK;IArBP,IAAkB,CAAAC,kBAAA,GAAyB,IAAI;IAC/C,IAAU,CAAAC,UAAA,GAAqB,IAAI;IAInC,IAAS,CAAAC,SAAA,GAAY,KAAK;IAC1B,IAAU,CAAAC,UAAA,GAAY,KAAK;IAiBjC,IAAI,CAACzF,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAACyF,OAAO,EAAExF,MAAM,KAAI;MAC9C,IAAI,CAACyF,QAAQ,GAAGD,OAA+C;MAC/D,IAAI,CAACE,OAAO,GAAG1F,MAAM;MACrB,IAAI,CAAC2F,MAAM,EAAE;IACf,CAAC,CAAC;;EAGJ;;AAEG;EACKA,MAAMA,CAAA;IACZ,MAAMC,YAAY,GAGNA,CAACC,eAAe,EAAE5K,QAAQ,KAAI;MACxC,IAAIA,QAAQ,EAAE;QACZ4K,eAAe,CAAC,KAAK,EAAE,IAAIC,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/D;;MAEF,MAAMC,UAAU,GAAG,IAAI,CAACb,kBAAkB,EAAE;MAC5C,IAAI,CAACE,kBAAkB,GAAGW,UAAU;MAEpC,MAAMC,gBAAgB,GAEVC,aAAa,IAAG;QAC1B,MAAMC,MAAM,GAAGD,aAAa,CAACC,MAAM;QACnC,MAAMC,KAAK,GAAGF,aAAa,CAACG,gBAAgB,GAAGH,aAAa,CAACE,KAAK,GAAG,CAAC,CAAC;QACvE,IAAI,IAAI,CAAClB,iBAAiB,KAAK,IAAI,EAAE;UACnC,IAAI,CAACA,iBAAiB,CAACiB,MAAM,EAAEC,KAAK,CAAC;;MAEzC,CAAC;MACD,IAAI,IAAI,CAAClB,iBAAiB,KAAK,IAAI,EAAE;QACnCc,UAAU,CAACM,yBAAyB,CAACL,gBAAgB,CAAC;;;;MAKxDD,UAAU,CACPO,IAAI,CAAC,IAAI,CAAC9B,IAAI,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACE,KAAK,EAAE,IAAI,CAACD,QAAQ,CAAC,CACxD6B,IAAI,CAAC,MAAK;QACT,IAAI,IAAI,CAACtB,iBAAiB,KAAK,IAAI,EAAE;UACnCc,UAAU,CAACS,4BAA4B,CAACR,gBAAgB,CAAC;;QAE3D,IAAI,CAACZ,kBAAkB,GAAG,IAAI;QAC9B,MAAMqB,SAAS,GAAGV,UAAU,CAACW,YAAY,EAAE,KAAK3C,SAAS,CAAC4C,QAAQ;QAClE,MAAM7M,MAAM,GAAGiM,UAAU,CAACa,SAAS,EAAE;QACrC,IACE,CAACH,SAAS,IACTzC,iBAAiB,CAAClK,MAAM,EAAE,IAAI,CAAC+K,qBAAqB,CAAC,IACpD,IAAI,CAACM,KAAM,EACb;UACA,MAAM0B,WAAW,GAAGd,UAAU,CAACW,YAAY,EAAE,KAAK3C,SAAS,CAAC+C,KAAK;UACjEjB,eAAe,CACb,KAAK,EACL,IAAIC,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAEe,WAAW,CAAC,CAC/C;UACD;;QAEF,MAAME,WAAW,GAAG,IAAI,CAACnC,aAAa,CAACP,OAAO,CAACvK,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7D+L,eAAe,CAAC,IAAI,EAAE,IAAIC,gBAAgB,CAACiB,WAAW,EAAEhB,UAAU,CAAC,CAAC;MACtE,CAAC,CAAC;IACN,CAAC;IAED;;;AAGG;IACH,MAAMiB,WAAW,GAGLA,CAACC,kBAAkB,EAAEnN,MAAM,KAAI;MACzC,MAAM0L,OAAO,GAAG,IAAI,CAACC,QAAQ;MAC7B,MAAMzF,MAAM,GAAG,IAAI,CAAC0F,OAAO;MAC3B,MAAMK,UAAU,GAAGjM,MAAM,CAACiM,UAA2B;MACrD,IAAIjM,MAAM,CAACoN,cAAc,EAAE;QACzB,IAAI;UACF,MAAMC,MAAM,GAAG,IAAI,CAACrC,SAAS,CAACiB,UAAU,EAAEA,UAAU,CAACqB,WAAW,EAAE,CAAC;UACnE,IAAI9E,SAAS,CAAC6E,MAAM,CAAC,EAAE;YACrB3B,OAAO,CAAC2B,MAAM,CAAC;WAChB,MAAM;YACL3B,OAAO,EAAE;;SAEZ,CAAC,OAAO7H,CAAC,EAAE;UACVqC,MAAM,CAACrC,CAAC,CAAC;;OAEZ,MAAM;QACL,IAAIoI,UAAU,KAAK,IAAI,EAAE;UACvB,MAAMsB,GAAG,GAAGpN,OAAO,EAAE;UACrBoN,GAAG,CAAC5N,cAAc,GAAGsM,UAAU,CAACuB,YAAY,EAAE;UAC9C,IAAI,IAAI,CAACvC,cAAc,EAAE;YACvB/E,MAAM,CAAC,IAAI,CAAC+E,cAAc,CAACgB,UAAU,EAAEsB,GAAG,CAAC,CAAC;WAC7C,MAAM;YACLrH,MAAM,CAACqH,GAAG,CAAC;;SAEd,MAAM;UACL,IAAIvN,MAAM,CAACmB,QAAQ,EAAE;YACnB,MAAMoM,GAAG,GAAG,IAAI,CAAC9B,UAAU,GAAGlJ,UAAU,EAAE,GAAGpB,QAAQ,EAAE;YACvD+E,MAAM,CAACqH,GAAG,CAAC;WACZ,MAAM;YACL,MAAMA,GAAG,GAAGtM,kBAAkB,EAAE;YAChCiF,MAAM,CAACqH,GAAG,CAAC;;;;IAInB,CAAC;IACD,IAAI,IAAI,CAAC/B,SAAS,EAAE;MAClB0B,WAAW,CAAC,KAAK,EAAE,IAAIlB,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAC5D,MAAM;MACL,IAAI,CAACT,UAAU,GAAGjF,KAAK,CAACwF,YAAY,EAAEoB,WAAW,EAAE,IAAI,CAAChC,QAAQ,CAAC;;;;EAKrE/E,UAAUA,CAAA;IACR,OAAO,IAAI,CAACH,QAAQ;;;EAItBI,MAAMA,CAACqH,SAAmB;IACxB,IAAI,CAACjC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAGgC,SAAS,IAAI,KAAK;IACpC,IAAI,IAAI,CAAClC,UAAU,KAAK,IAAI,EAAE;MAC5BlD,IAAI,CAAC,IAAI,CAACkD,UAAU,CAAC;;IAEvB,IAAI,IAAI,CAACD,kBAAkB,KAAK,IAAI,EAAE;MACpC,IAAI,CAACA,kBAAkB,CAACoC,KAAK,EAAE;;;AAGpC;AAED;;;AAGG;MACU1B,gBAAgB;EAM3B9M,YACSkO,cAAuB,EACvBnB,UAAgC,EACvC9K,QAAkB;IAFX,IAAc,CAAAiM,cAAA,GAAdA,cAAc;IACd,IAAU,CAAAnB,UAAA,GAAVA,UAAU;IAGjB,IAAI,CAAC9K,QAAQ,GAAG,CAAC,CAACA,QAAQ;;AAE7B;AAEe,SAAAwM,cAAcA,CAC5BC,OAAgB,EAChBC,SAAwB;EAExB,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACtO,MAAM,GAAG,CAAC,EAAE;IAC9CqO,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,GAAGC,SAAS;;AAEtD;AAEgB,SAAAC,iBAAiBA,CAC/BF,OAAgB,EAChBG,eAAwB;EAExBH,OAAO,CAAC,4BAA4B,CAAC,GACnC,QAAQ,IAAIG,eAAe,KAAf,QAAAA,eAAe,cAAfA,eAAe,GAAI,YAAY,CAAC;AAChD;AAEgB,SAAAC,eAAeA,CAACJ,OAAgB,EAAEK,KAAoB;EACpE,IAAIA,KAAK,EAAE;IACTL,OAAO,CAAC,kBAAkB,CAAC,GAAGK,KAAK;;AAEvC;AAEgB,SAAAC,kBAAkBA,CAChCN,OAAgB,EAChBO,aAA4B;EAE5B,IAAIA,aAAa,KAAK,IAAI,EAAE;IAC1BP,OAAO,CAAC,qBAAqB,CAAC,GAAGO,aAAa;;AAElD;SAEgBC,WAAWA,CACzBC,WAA8B,EAC9BJ,KAAoB,EACpBJ,SAAwB,EACxBM,aAA4B,EAC5BG,cAAmC,EACnCP,eAAwB,EACZ;EAAA,IAAZ1C,KAAK,GAAA/L,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAEZ,MAAMuK,SAAS,GAAGF,eAAe,CAAC0E,WAAW,CAACE,SAAS,CAAC;EACxD,MAAMjN,GAAG,GAAG+M,WAAW,CAAC/M,GAAG,GAAGuI,SAAS;EACvC,MAAM+D,OAAO,GAAG/N,MAAM,CAAC2O,MAAM,CAAC,EAAE,EAAEH,WAAW,CAACT,OAAO,CAAC;EACtDI,eAAe,CAACJ,OAAO,EAAEK,KAAK,CAAC;EAC/BN,cAAc,CAACC,OAAO,EAAEC,SAAS,CAAC;EAClCC,iBAAiB,CAACF,OAAO,EAAEG,eAAe,CAAC;EAC3CG,kBAAkB,CAACN,OAAO,EAAEO,aAAa,CAAC;EAC1C,OAAO,IAAI1D,cAAc,CACvBnJ,GAAG,EACH+M,WAAW,CAACI,MAAM,EAClBb,OAAO,EACPS,WAAW,CAACK,IAAI,EAChBL,WAAW,CAACM,YAAY,EACxBN,WAAW,CAAClE,oBAAoB,EAChCkE,WAAW,CAACO,OAAO,EACnBP,WAAW,CAACQ,YAAY,EACxBR,WAAW,CAAC5H,OAAO,EACnB4H,WAAW,CAACS,gBAAgB,EAC5BR,cAAc,EACdjD,KAAK,CACN;AACH;;AC9RA;;;;;;;;;;;;;;;AAeG;AASH,SAAS0D,cAAcA,CAAA;EACrB,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAE;IACtC,OAAOA,WAAW;GACnB,MAAM,IAAI,OAAOC,iBAAiB,KAAK,WAAW,EAAE;IACnD,OAAOA,iBAAiB;GACzB,MAAM;IACL,OAAOzP,SAAS;;AAEpB;AAEA;;;;;AAKG;AACa,SAAA0P,SAAOC,CAAA,EAA4C;EACjE,MAAMH,WAAW,GAAGD,cAAc,EAAE;EAAC,SAAAK,KAAA,GAAA9P,SAAA,CAAAC,MAAA,EADZ2H,IAAwC,OAAAC,KAAA,CAAAiI,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAxCnI,IAAwC,CAAAmI,KAAA,IAAA/P,SAAA,CAAA+P,KAAA;EAAA;EAEjE,IAAIL,WAAW,KAAKxP,SAAS,EAAE;IAC7B,MAAM8P,EAAE,GAAG,IAAIN,WAAW,EAAE;IAC5B,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,IAAI,CAAC3H,MAAM,EAAEiG,CAAC,EAAE,EAAE;MACpC8J,EAAE,CAACC,MAAM,CAACrI,IAAI,CAAC1B,CAAC,CAAC,CAAC;;IAEpB,OAAO8J,EAAE,CAACH,OAAO,EAAE;GACpB,MAAM;IACL,IAAInG,mBAAmB,EAAE,EAAE;MACzB,OAAO,IAAIC,IAAI,CAAC/B,IAAI,CAAC;KACtB,MAAM;MACL,MAAM,IAAIlI,YAAY,CACpBkB,gBAAgB,CAACkC,uBAAuB,EACxC,qDAAqD,CACtD;;;AAGP;AAEA;;;;;;;;AAQG;SACaoN,SAASA,CAACC,IAAU,EAAEnJ,KAAa,EAAEoJ,GAAW;EAC9D,IAAID,IAAI,CAACE,WAAW,EAAE;IACpB,OAAOF,IAAI,CAACE,WAAW,CAACrJ,KAAK,EAAEoJ,GAAG,CAAC;GACpC,MAAM,IAAID,IAAI,CAACG,QAAQ,EAAE;IACxB,OAAOH,IAAI,CAACG,QAAQ,CAACtJ,KAAK,EAAEoJ,GAAG,CAAC;GACjC,MAAM,IAAID,IAAI,CAACtL,KAAK,EAAE;IACrB,OAAOsL,IAAI,CAACtL,KAAK,CAACmC,KAAK,EAAEoJ,GAAG,CAAC;;EAE/B,OAAO,IAAI;AACb;;AC9EA;;;;;;;;;;;;;;;AAeG;AAIH;AACM,SAAUG,YAAYA,CAACC,OAAe;EAC1C,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC/B,MAAM7N,eAAe,CAAC,SAAS,CAAC;;EAElC,OAAO6N,IAAI,CAACD,OAAO,CAAC;AACtB;;ACzBA;;;;;;;;;;;;;;;AAeG;AAUH;;;AAGG;AACU,MAAAE,YAAY,GAAG;EAC1B;;;;;;AAMG;EACHC,GAAG,EAAE,KAAK;EACV;;;;;AAKG;EACHC,MAAM,EAAE,QAAQ;EAChB;;;;;AAKG;EACHC,SAAS,EAAE,WAAW;EACtB;;;;;;;;AAQG;EACHC,QAAQ,EAAE;;MAGCC,UAAU;EAGrBnR,WAAmBA,CAAAoR,IAAgB,EAAEC,WAA2B;IAA7C,IAAI,CAAAD,IAAA,GAAJA,IAAI;IACrB,IAAI,CAACC,WAAW,GAAGA,WAAW,IAAI,IAAI;;AAEzC;AAED;;AAEG;AACa,SAAAC,cAAcA,CAC5B3N,MAAoB,EACpB4N,UAAkB;EAElB,QAAQ5N,MAAM;IACZ,KAAKmN,YAAY,CAACC,GAAG;MACnB,OAAO,IAAII,UAAU,CAACK,UAAU,CAACD,UAAU,CAAC,CAAC;IAC/C,KAAKT,YAAY,CAACE,MAAM;IACxB,KAAKF,YAAY,CAACG,SAAS;MACzB,OAAO,IAAIE,UAAU,CAACM,YAAY,CAAC9N,MAAM,EAAE4N,UAAU,CAAC,CAAC;IACzD,KAAKT,YAAY,CAACI,QAAQ;MACxB,OAAO,IAAIC,UAAU,CACnBO,aAAa,CAACH,UAAU,CAAC,EACzBI,mBAAmB,CAACJ,UAAU,CAAC,CAChC;;;;EAML,MAAMtQ,OAAO,EAAE;AACjB;AAEM,SAAUuQ,UAAUA,CAACpH,KAAa;EACtC,MAAMwH,CAAC,GAAa,EAAE;EACtB,KAAK,IAAItL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,KAAK,CAAC/J,MAAM,EAAEiG,CAAC,EAAE,EAAE;IACrC,IAAIuL,CAAC,GAAGzH,KAAK,CAAC0H,UAAU,CAACxL,CAAC,CAAC;IAC3B,IAAIuL,CAAC,IAAI,GAAG,EAAE;MACZD,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC;KACV,MAAM;MACL,IAAIA,CAAC,IAAI,IAAI,EAAE;QACbD,CAAC,CAACG,IAAI,CAAC,GAAG,GAAIF,CAAC,IAAI,CAAE,EAAE,GAAG,GAAIA,CAAC,GAAG,EAAG,CAAC;OACvC,MAAM;QACL,IAAI,CAACA,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;;UAEzB,MAAMG,KAAK,GACT1L,CAAC,GAAG8D,KAAK,CAAC/J,MAAM,GAAG,CAAC,IAAI,CAAC+J,KAAK,CAAC0H,UAAU,CAACxL,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK;UACrE,IAAI,CAAC0L,KAAK,EAAE;;YAEVJ,CAAC,CAACG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;WACtB,MAAM;YACL,MAAME,EAAE,GAAGJ,CAAC;YACZ,MAAMK,EAAE,GAAG9H,KAAK,CAAC0H,UAAU,CAAC,EAAExL,CAAC,CAAC;YAChCuL,CAAC,GAAG,KAAK,GAAI,CAACI,EAAE,GAAG,IAAI,KAAK,EAAG,GAAIC,EAAE,GAAG,IAAK;YAC7CN,CAAC,CAACG,IAAI,CACJ,GAAG,GAAIF,CAAC,IAAI,EAAG,EACf,GAAG,GAAKA,CAAC,IAAI,EAAE,GAAI,EAAG,EACtB,GAAG,GAAKA,CAAC,IAAI,CAAC,GAAI,EAAG,EACrB,GAAG,GAAIA,CAAC,GAAG,EAAG,CACf;;SAEJ,MAAM;UACL,IAAI,CAACA,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;;YAEzBD,CAAC,CAACG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;WACtB,MAAM;YACLH,CAAC,CAACG,IAAI,CAAC,GAAG,GAAIF,CAAC,IAAI,EAAG,EAAE,GAAG,GAAKA,CAAC,IAAI,CAAC,GAAI,EAAG,EAAE,GAAG,GAAIA,CAAC,GAAG,EAAG,CAAC;;;;;;EAMxE,OAAO,IAAIM,UAAU,CAACP,CAAC,CAAC;AAC1B;AAEM,SAAUQ,oBAAoBA,CAAChI,KAAa;EAChD,IAAIiI,OAAO;EACX,IAAI;IACFA,OAAO,GAAG9M,kBAAkB,CAAC6E,KAAK,CAAC;GACpC,CAAC,OAAOzF,CAAC,EAAE;IACV,MAAMjB,aAAa,CAACoN,YAAY,CAACI,QAAQ,EAAE,qBAAqB,CAAC;;EAEnE,OAAOM,UAAU,CAACa,OAAO,CAAC;AAC5B;AAEgB,SAAAZ,YAAYA,CAAC9N,MAAoB,EAAEyG,KAAa;EAC9D,QAAQzG,MAAM;IACZ,KAAKmN,YAAY,CAACE,MAAM;MAAE;QACxB,MAAMsB,QAAQ,GAAGlI,KAAK,CAACiB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1C,MAAMkH,QAAQ,GAAGnI,KAAK,CAACiB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAIiH,QAAQ,IAAIC,QAAQ,EAAE;UACxB,MAAMC,WAAW,GAAGF,QAAQ,GAAG,GAAG,GAAG,GAAG;UACxC,MAAM5O,aAAa,CACjBC,MAAM,EACN,qBAAqB,GACnB6O,WAAW,GACX,mCAAmC,CACtC;;QAEH;;IAEF,KAAK1B,YAAY,CAACG,SAAS;MAAE;QAC3B,MAAMwB,OAAO,GAAGrI,KAAK,CAACiB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzC,MAAMqH,QAAQ,GAAGtI,KAAK,CAACiB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAIoH,OAAO,IAAIC,QAAQ,EAAE;UACvB,MAAMF,WAAW,GAAGC,OAAO,GAAG,GAAG,GAAG,GAAG;UACvC,MAAM/O,aAAa,CACjBC,MAAM,EACN,qBAAqB,GAAG6O,WAAW,GAAG,gCAAgC,CACvE;;QAEHpI,KAAK,GAAGA,KAAK,CAAC1E,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;QACnD;;;;EAKJ,IAAIiN,KAAK;EACT,IAAI;IACFA,KAAK,GAAGhC,YAAY,CAACvG,KAAK,CAAC;GAC5B,CAAC,OAAOzF,CAAC,EAAE;IACV,IAAKA,CAAW,CAACzE,OAAO,CAAC0S,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7C,MAAMjO,CAAC;;IAET,MAAMjB,aAAa,CAACC,MAAM,EAAE,yBAAyB,CAAC;;EAExD,MAAMkP,KAAK,GAAG,IAAIV,UAAU,CAACQ,KAAK,CAACtS,MAAM,CAAC;EAC1C,KAAK,IAAIiG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqM,KAAK,CAACtS,MAAM,EAAEiG,CAAC,EAAE,EAAE;IACrCuM,KAAK,CAACvM,CAAC,CAAC,GAAGqM,KAAK,CAACb,UAAU,CAACxL,CAAC,CAAC;;EAEhC,OAAOuM,KAAK;AACd;AAEA,MAAMC,YAAY;EAKhB9S,YAAY+S,OAAe;IAJ3B,IAAM,CAAAC,MAAA,GAAY,KAAK;IACvB,IAAW,CAAA3B,WAAA,GAAkB,IAAI;IAI/B,MAAM4B,OAAO,GAAGF,OAAO,CAACG,KAAK,CAAC,iBAAiB,CAAC;IAChD,IAAID,OAAO,KAAK,IAAI,EAAE;MACpB,MAAMvP,aAAa,CACjBoN,YAAY,CAACI,QAAQ,EACrB,uDAAuD,CACxD;;IAEH,MAAMiC,MAAM,GAAGF,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IACjC,IAAIE,MAAM,IAAI,IAAI,EAAE;MAClB,IAAI,CAACH,MAAM,GAAGI,QAAQ,CAACD,MAAM,EAAE,SAAS,CAAC;MACzC,IAAI,CAAC9B,WAAW,GAAG,IAAI,CAAC2B,MAAM,GAC1BG,MAAM,CAACE,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC9S,MAAM,GAAG,SAAS,CAACA,MAAM,CAAC,GACrD8S,MAAM;;IAEZ,IAAI,CAACG,IAAI,GAAGP,OAAO,CAACM,SAAS,CAACN,OAAO,CAAC1H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;AAE1D;AAEK,SAAUqG,aAAaA,CAAC6B,OAAe;EAC3C,MAAMC,KAAK,GAAG,IAAIV,YAAY,CAACS,OAAO,CAAC;EACvC,IAAIC,KAAK,CAACR,MAAM,EAAE;IAChB,OAAOvB,YAAY,CAACX,YAAY,CAACE,MAAM,EAAEwC,KAAK,CAACF,IAAI,CAAC;GACrD,MAAM;IACL,OAAOlB,oBAAoB,CAACoB,KAAK,CAACF,IAAI,CAAC;;AAE3C;AAEM,SAAU3B,mBAAmBA,CAAC4B,OAAe;EACjD,MAAMC,KAAK,GAAG,IAAIV,YAAY,CAACS,OAAO,CAAC;EACvC,OAAOC,KAAK,CAACnC,WAAW;AAC1B;AAEA,SAAS+B,QAAQA,CAACK,CAAS,EAAEjD,GAAW;EACtC,MAAMkD,UAAU,GAAGD,CAAC,CAACpT,MAAM,IAAImQ,GAAG,CAACnQ,MAAM;EACzC,IAAI,CAACqT,UAAU,EAAE;IACf,OAAO,KAAK;;EAGd,OAAOD,CAAC,CAACJ,SAAS,CAACI,CAAC,CAACpT,MAAM,GAAGmQ,GAAG,CAACnQ,MAAM,CAAC,KAAKmQ,GAAG;AACnD;;ACnPA;;;;;;;;;;;;;;;AAeG;AAWH;;;;;;AAMG;MACUmD,OAAO;EAKlB3T,WAAYA,CAAAoR,IAAqC,EAAEwC,SAAmB;IACpE,IAAIC,IAAI,GAAW,CAAC;IACpB,IAAIC,QAAQ,GAAW,EAAE;IACzB,IAAIjK,YAAY,CAACuH,IAAI,CAAC,EAAE;MACtB,IAAI,CAAC2C,KAAK,GAAG3C,IAAY;MACzByC,IAAI,GAAIzC,IAAa,CAACyC,IAAI;MAC1BC,QAAQ,GAAI1C,IAAa,CAAC4C,IAAI;KAC/B,MAAM,IAAI5C,IAAI,YAAY6C,WAAW,EAAE;MACtC,IAAIL,SAAS,EAAE;QACb,IAAI,CAACG,KAAK,GAAG,IAAI5B,UAAU,CAACf,IAAI,CAAC;OAClC,MAAM;QACL,IAAI,CAAC2C,KAAK,GAAG,IAAI5B,UAAU,CAACf,IAAI,CAAC8C,UAAU,CAAC;QAC5C,IAAI,CAACH,KAAK,CAACI,GAAG,CAAC,IAAIhC,UAAU,CAACf,IAAI,CAAC,CAAC;;MAEtCyC,IAAI,GAAG,IAAI,CAACE,KAAK,CAAC1T,MAAM;KACzB,MAAM,IAAI+Q,IAAI,YAAYe,UAAU,EAAE;MACrC,IAAIyB,SAAS,EAAE;QACb,IAAI,CAACG,KAAK,GAAG3C,IAAkB;OAChC,MAAM;QACL,IAAI,CAAC2C,KAAK,GAAG,IAAI5B,UAAU,CAACf,IAAI,CAAC/Q,MAAM,CAAC;QACxC,IAAI,CAAC0T,KAAK,CAACI,GAAG,CAAC/C,IAAkB,CAAC;;MAEpCyC,IAAI,GAAGzC,IAAI,CAAC/Q,MAAM;;IAEpB,IAAI,CAAC+T,KAAK,GAAGP,IAAI;IACjB,IAAI,CAACQ,KAAK,GAAGP,QAAQ;;EAGvBD,IAAIA,CAAA;IACF,OAAO,IAAI,CAACO,KAAK;;EAGnBJ,IAAIA,CAAA;IACF,OAAO,IAAI,CAACK,KAAK;;EAGnBpP,KAAKA,CAACqP,SAAiB,EAAEC,OAAe;IACtC,IAAI1K,YAAY,CAAC,IAAI,CAACkK,KAAK,CAAC,EAAE;MAC5B,MAAMS,QAAQ,GAAG,IAAI,CAACT,KAAa;MACnC,MAAMU,MAAM,GAAGnE,SAAS,CAACkE,QAAQ,EAAEF,SAAS,EAAEC,OAAO,CAAC;MACtD,IAAIE,MAAM,KAAK,IAAI,EAAE;QACnB,OAAO,IAAI;;MAEb,OAAO,IAAId,OAAO,CAACc,MAAM,CAAC;KAC3B,MAAM;MACL,MAAMxP,KAAK,GAAG,IAAIkN,UAAU,CACzB,IAAI,CAAC4B,KAAoB,CAACW,MAAM,EACjCJ,SAAS,EACTC,OAAO,GAAGD,SAAS,CACpB;MACD,OAAO,IAAIX,OAAO,CAAC1O,KAAK,EAAE,IAAI,CAAC;;;EAInC,OAAOgL,OAAOA,CAAA,EAAiC;IAAA,SAAA0E,KAAA,GAAAvU,SAAA,CAAAC,MAAA,EAA7B2H,IAA6B,OAAAC,KAAA,CAAA0M,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAA7B5M,IAA6B,CAAA4M,KAAA,IAAAxU,SAAA,CAAAwU,KAAA;IAAA;IAC7C,IAAI9K,mBAAmB,EAAE,EAAE;MACzB,MAAM+K,MAAM,GAAsC7M,IAAI,CAAC8M,GAAG,CACvDC,GAAqB,IAAgC;QACpD,IAAIA,GAAG,YAAYpB,OAAO,EAAE;UAC1B,OAAOoB,GAAG,CAAChB,KAAK;SACjB,MAAM;UACL,OAAOgB,GAAG;;MAEd,CAAC,CACF;MACD,OAAO,IAAIpB,OAAO,CAAC3D,SAAO,CAAC7H,KAAK,CAAC,IAAI,EAAE0M,MAAM,CAAC,CAAC;KAChD,MAAM;MACL,MAAMG,WAAW,GAAiBhN,IAAI,CAAC8M,GAAG,CACvCC,GAAqB,IAAgB;QACpC,IAAIpL,QAAQ,CAACoL,GAAG,CAAC,EAAE;UACjB,OAAOzD,cAAc,CAACR,YAAY,CAACC,GAAG,EAAEgE,GAAa,CAAC,CAAC3D,IAAI;SAC5D,MAAM;;UAEL,OAAQ2D,GAAe,CAAChB,KAAmB;;MAE/C,CAAC,CACF;MACD,IAAIkB,WAAW,GAAG,CAAC;MACnBD,WAAW,CAACE,OAAO,CAAErC,KAAiB,IAAU;QAC9CoC,WAAW,IAAIpC,KAAK,CAACqB,UAAU;MACjC,CAAC,CAAC;MACF,MAAMiB,MAAM,GAAG,IAAIhD,UAAU,CAAC8C,WAAW,CAAC;MAC1C,IAAIG,KAAK,GAAG,CAAC;MACbJ,WAAW,CAACE,OAAO,CAAErC,KAAiB,IAAI;QACxC,KAAK,IAAIvM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuM,KAAK,CAACxS,MAAM,EAAEiG,CAAC,EAAE,EAAE;UACrC6O,MAAM,CAACC,KAAK,EAAE,CAAC,GAAGvC,KAAK,CAACvM,CAAC,CAAC;;MAE9B,CAAC,CAAC;MACF,OAAO,IAAIqN,OAAO,CAACwB,MAAM,EAAE,IAAI,CAAC;;;EAIpCE,UAAUA,CAAA;IACR,OAAO,IAAI,CAACtB,KAAK;;AAEpB;;ACrID;;;;;;;;;;;;;;;AAeG;AAGH;;;AAGG;AACG,SAAUuB,gBAAgBA,CAC9B7B,CAAS;EAET,IAAI8B,GAAG;EACP,IAAI;IACFA,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAChC,CAAC,CAAC;GACpB,CAAC,OAAO9O,CAAC,EAAE;IACV,OAAO,IAAI;;EAEb,IAAI8E,gBAAgB,CAAC8L,GAAG,CAAC,EAAE;IACzB,OAAOA,GAAG;GACX,MAAM;IACL,OAAO,IAAI;;AAEf;;ACpCA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AAEH;;AAEG;AACG,SAAUG,MAAMA,CAACtU,IAAY;EACjC,IAAIA,IAAI,CAACf,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO,IAAI;;EAEb,MAAM+U,KAAK,GAAGhU,IAAI,CAACuU,WAAW,CAAC,GAAG,CAAC;EACnC,IAAIP,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAO,EAAE;;EAEX,MAAMQ,OAAO,GAAGxU,IAAI,CAAC6D,KAAK,CAAC,CAAC,EAAEmQ,KAAK,CAAC;EACpC,OAAOQ,OAAO;AAChB;AAEgB,SAAAC,KAAKA,CAACzU,IAAY,EAAE0U,SAAiB;EACnD,MAAMC,kBAAkB,GAAGD,SAAS,CACjCE,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAACC,SAAS,IAAIA,SAAS,CAAC7V,MAAM,GAAG,CAAC,CAAC,CACzC8V,IAAI,CAAC,GAAG,CAAC;EACZ,IAAI/U,IAAI,CAACf,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO0V,kBAAkB;GAC1B,MAAM;IACL,OAAO3U,IAAI,GAAG,GAAG,GAAG2U,kBAAkB;;AAE1C;AAEA;;;;;AAKG;AACG,SAAUK,aAAaA,CAAChV,IAAY;EACxC,MAAMgU,KAAK,GAAGhU,IAAI,CAACuU,WAAW,CAAC,GAAG,EAAEvU,IAAI,CAACf,MAAM,GAAG,CAAC,CAAC;EACpD,IAAI+U,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAOhU,IAAI;GACZ,MAAM;IACL,OAAOA,IAAI,CAAC6D,KAAK,CAACmQ,KAAK,GAAG,CAAC,CAAC;;AAEhC;;AC7DA;;;;;;;;;;;;;;;AAeG;AAea,SAAAiB,QAAQA,CAAIC,QAAkB,EAAElM,KAAQ;EACtD,OAAOA,KAAK;AACd;AAEA,MAAMmM,OAAO;EAKXvW,YACSwW,MAAc,EACrBC,KAAqB,EACrBC,QAAkB,EAClBC,KAAwD;IAHjD,IAAM,CAAAH,MAAA,GAANA,MAAM;IAKb,IAAI,CAACC,KAAK,GAAGA,KAAK,IAAID,MAAM;IAC5B,IAAI,CAACE,QAAQ,GAAG,CAAC,CAACA,QAAQ;IAC1B,IAAI,CAACC,KAAK,GAAGA,KAAK,IAAIN,QAAQ;;AAEjC;AAKD,IAAIO,SAAS,GAAoB,IAAI;AAE/B,SAAUC,SAASA,CAACC,QAA4B;EACpD,IAAI,CAACnN,QAAQ,CAACmN,QAAQ,CAAC,IAAIA,QAAQ,CAACzW,MAAM,GAAG,CAAC,EAAE;IAC9C,OAAOyW,QAAQ;GAChB,MAAM;IACL,OAAOV,aAAa,CAACU,QAAQ,CAAC;;AAElC;SAEgBC,WAAWA,CAAA;EACzB,IAAIH,SAAS,EAAE;IACb,OAAOA,SAAS;;EAElB,MAAMI,QAAQ,GAAa,EAAE;EAC7BA,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,QAAQ,CAAC,CAAC;EAC5CS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,YAAY,CAAC,CAAC;EAChDS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,gBAAgB,CAAC,CAAC;EACpDS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;EAE5D,SAASU,iBAAiBA,CACxBC,SAAmB,EACnBJ,QAA4B;IAE5B,OAAOD,SAAS,CAACC,QAAQ,CAAC;;EAE5B,MAAMK,WAAW,GAAG,IAAIZ,OAAO,CAAS,MAAM,CAAC;EAC/CY,WAAW,CAACR,KAAK,GAAGM,iBAAiB;EACrCD,QAAQ,CAACjF,IAAI,CAACoF,WAAW,CAAC;EAE1B;;AAEG;EACH,SAASC,SAASA,CAChBF,SAAmB,EACnBrD,IAAsB;IAEtB,IAAIA,IAAI,KAAKvT,SAAS,EAAE;MACtB,OAAO+W,MAAM,CAACxD,IAAI,CAAC;KACpB,MAAM;MACL,OAAOA,IAAI;;;EAGf,MAAMyD,WAAW,GAAG,IAAIf,OAAO,CAAS,MAAM,CAAC;EAC/Ce,WAAW,CAACX,KAAK,GAAGS,SAAS;EAC7BJ,QAAQ,CAACjF,IAAI,CAACuF,WAAW,CAAC;EAC1BN,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,aAAa,CAAC,CAAC;EACjDS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,SAAS,CAAC,CAAC;EAC7CS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzDS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC9DS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,oBAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACpES,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACjES,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACjES,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC7DS,QAAQ,CAACjF,IAAI,CAAC,IAAIwE,OAAO,CAAS,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;EACtEK,SAAS,GAAGI,QAAQ;EACpB,OAAOJ,SAAS;AAClB;AAEgB,SAAAW,MAAMA,CAACjB,QAAkB,EAAEkB,OAA4B;EACrE,SAASC,WAAWA,CAAA;IAClB,MAAMlW,MAAM,GAAW+U,QAAQ,CAAC,QAAQ,CAAW;IACnD,MAAMlV,IAAI,GAAWkV,QAAQ,CAAC,UAAU,CAAW;IACnD,MAAMvR,GAAG,GAAG,IAAIhB,QAAQ,CAACxC,MAAM,EAAEH,IAAI,CAAC;IACtC,OAAOoW,OAAO,CAACE,qBAAqB,CAAC3S,GAAG,CAAC;;EAE3CpE,MAAM,CAACgX,cAAc,CAACrB,QAAQ,EAAE,KAAK,EAAE;IAAEsB,GAAG,EAAEH;EAAW,CAAE,CAAC;AAC9D;SAEgBI,YAAYA,CAC1BL,OAA4B,EAC5BM,QAAqC,EACrCd,QAAkB;EAElB,MAAMV,QAAQ,GAAa,EAAc;EACzCA,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM;EACzB,MAAMyB,GAAG,GAAGf,QAAQ,CAAC3W,MAAM;EAC3B,KAAK,IAAIiG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyR,GAAG,EAAEzR,CAAC,EAAE,EAAE;IAC5B,MAAM0R,OAAO,GAAGhB,QAAQ,CAAC1Q,CAAC,CAAC;IAC3BgQ,QAAQ,CAAC0B,OAAO,CAACvB,KAAK,CAAC,GAAIuB,OAA4B,CAACrB,KAAK,CAC3DL,QAAQ,EACRwB,QAAQ,CAACE,OAAO,CAACxB,MAAM,CAAC,CACzB;;EAEHe,MAAM,CAACjB,QAAQ,EAAEkB,OAAO,CAAC;EACzB,OAAOlB,QAAQ;AACjB;SAEgB2B,kBAAkBA,CAChCT,OAA4B,EAC5BU,cAAsB,EACtBlB,QAAkB;EAElB,MAAMzB,GAAG,GAAGD,gBAAgB,CAAC4C,cAAc,CAAC;EAC5C,IAAI3C,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,IAAI;;EAEb,MAAMuC,QAAQ,GAAGvC,GAAe;EAChC,OAAOsC,YAAY,CAACL,OAAO,EAAEM,QAAQ,EAAEd,QAAQ,CAAC;AAClD;AAEM,SAAUmB,6BAA6BA,CAC3C7B,QAAkB,EAClB4B,cAAsB,EACtB1T,IAAY,EACZ+F,QAAgB;EAEhB,MAAMgL,GAAG,GAAGD,gBAAgB,CAAC4C,cAAc,CAAC;EAC5C,IAAI3C,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,IAAI;;EAEb,IAAI,CAAC5L,QAAQ,CAAC4L,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE;;;IAGpC,OAAO,IAAI;;EAEb,MAAM6C,MAAM,GAAW7C,GAAG,CAAC,gBAAgB,CAAW;EACtD,IAAI6C,MAAM,CAAC/X,MAAM,KAAK,CAAC,EAAE;IACvB,OAAO,IAAI;;EAEb,MAAM8D,MAAM,GAAGC,kBAAkB;EACjC,MAAMiU,UAAU,GAAGD,MAAM,CAACpC,KAAK,CAAC,GAAG,CAAC;EACpC,MAAMsC,IAAI,GAAGD,UAAU,CAACvD,GAAG,CAAEyD,KAAa,IAAY;IACpD,MAAMhX,MAAM,GAAW+U,QAAQ,CAAC,QAAQ,CAAW;IACnD,MAAMlV,IAAI,GAAWkV,QAAQ,CAAC,UAAU,CAAW;IACnD,MAAMhM,OAAO,GAAG,KAAK,GAAGnG,MAAM,CAAC5C,MAAM,CAAC,GAAG,KAAK,GAAG4C,MAAM,CAAC/C,IAAI,CAAC;IAC7D,MAAMoX,IAAI,GAAGnO,OAAO,CAACC,OAAO,EAAE9F,IAAI,EAAE+F,QAAQ,CAAC;IAC7C,MAAMkO,WAAW,GAAGhO,eAAe,CAAC;MAClCiO,GAAG,EAAE,OAAO;MACZH;IACD,EAAC;IACF,OAAOC,IAAI,GAAGC,WAAW;EAC3B,CAAC,CAAC;EACF,OAAOH,IAAI,CAAC,CAAC,CAAC;AAChB;AAEgB,SAAAK,gBAAgBA,CAC9BrC,QAA2B,EAC3BU,QAAkB;EAElB,MAAMc,QAAQ,GAEV,EAAE;EACN,MAAMC,GAAG,GAAGf,QAAQ,CAAC3W,MAAM;EAC3B,KAAK,IAAIiG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyR,GAAG,EAAEzR,CAAC,EAAE,EAAE;IAC5B,MAAM0R,OAAO,GAAGhB,QAAQ,CAAC1Q,CAAC,CAAC;IAC3B,IAAI0R,OAAO,CAACtB,QAAQ,EAAE;MACpBoB,QAAQ,CAACE,OAAO,CAACxB,MAAM,CAAC,GAAGF,QAAQ,CAAC0B,OAAO,CAACvB,KAAK,CAAC;;;EAGtD,OAAOjB,IAAI,CAACoD,SAAS,CAACd,QAAQ,CAAC;AACjC;;AC7MA;;;;;;;;;;;;;;;AAeG;AA6BH,MAAMe,YAAY,GAAG,UAAU;AAC/B,MAAMC,SAAS,GAAG,OAAO;AAEzB,SAASC,mBAAmBA,CAC1BvB,OAA4B,EAC5BjW,MAAc,EACduW,QAA4B;EAE5B,MAAMkB,UAAU,GAAe;IAC7BC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,aAAa,EAAErB,QAAQ,CAAC,eAAe;GACxC;EACD,IAAIA,QAAQ,CAACe,YAAY,CAAC,EAAE;IAC1B,KAAK,MAAMzX,IAAI,IAAI0W,QAAQ,CAACe,YAAY,CAAC,EAAE;MACzC,MAAMO,wBAAwB,GAAGhY,IAAI,CAACsE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxD,MAAM2T,SAAS,GAAG7B,OAAO,CAACE,qBAAqB,CAC7C,IAAI3T,QAAQ,CAACxC,MAAM,EAAE6X,wBAAwB,CAAC,CAC/C;MACDJ,UAAU,CAACC,QAAQ,CAAClH,IAAI,CAACsH,SAAS,CAAC;;;EAIvC,IAAIvB,QAAQ,CAACgB,SAAS,CAAC,EAAE;IACvB,KAAK,MAAMQ,IAAI,IAAIxB,QAAQ,CAACgB,SAAS,CAAC,EAAE;MACtC,MAAMO,SAAS,GAAG7B,OAAO,CAACE,qBAAqB,CAC7C,IAAI3T,QAAQ,CAACxC,MAAM,EAAE+X,IAAI,CAAC,MAAM,CAAC,CAAC,CACnC;MACDN,UAAU,CAACE,KAAK,CAACnH,IAAI,CAACsH,SAAS,CAAC;;;EAGpC,OAAOL,UAAU;AACnB;SAEgBO,kBAAkBA,CAChC/B,OAA4B,EAC5BjW,MAAc,EACd2W,cAAsB;EAEtB,MAAM3C,GAAG,GAAGD,gBAAgB,CAAC4C,cAAc,CAAC;EAC5C,IAAI3C,GAAG,KAAK,IAAI,EAAE;IAChB,OAAO,IAAI;;EAEb,MAAMuC,QAAQ,GAAGvC,GAAoC;EACrD,OAAOwD,mBAAmB,CAACvB,OAAO,EAAEjW,MAAM,EAAEuW,QAAQ,CAAC;AACvD;;AC7CA;;;;;AAKG;MACU0B,WAAW;EActBxZ,WACSA,CAAAoC,GAAW,EACXmN,MAAc;EACrB;;;;;;AAMG;EACIG,OAA6B,EAC7BnI,OAAe;IAVf,IAAG,CAAAnF,GAAA,GAAHA,GAAG;IACH,IAAM,CAAAmN,MAAA,GAANA,MAAM;IAQN,IAAO,CAAAG,OAAA,GAAPA,OAAO;IACP,IAAO,CAAAnI,OAAA,GAAPA,OAAO;IAxBhB,IAAS,CAAA8H,SAAA,GAAc,EAAE;IACzB,IAAO,CAAAX,OAAA,GAAY,EAAE;IACrB,IAAI,CAAAc,IAAA,GAAsC,IAAI;IAC9C,IAAY,CAAAG,YAAA,GAAwB,IAAI;IAExC;;;AAGG;IACH,IAAgB,CAAAC,gBAAA,GAA8C,IAAI;IAClE,KAAAH,YAAY,GAAa,CAAC,GAAG,CAAC;IAC9B,IAAoB,CAAAxE,oBAAA,GAAa,EAAE;;AAepC;;AC7ED;;;;;;;;;;;;;;;AAeG;AAkCH;;AAEG;AACG,SAAUwO,YAAYA,CAACC,IAAa;EACxC,IAAI,CAACA,IAAI,EAAE;IACT,MAAMzY,OAAO,EAAE;;AAEnB;AAEgB,SAAA0Y,eAAeA,CAC7BnC,OAA4B,EAC5BR,QAAkB;EAElB,SAAStH,OAAOA,CAACkK,GAAuB,EAAEC,IAAY;IACpD,MAAMvD,QAAQ,GAAG2B,kBAAkB,CAACT,OAAO,EAAEqC,IAAI,EAAE7C,QAAQ,CAAC;IAC5DyC,YAAY,CAACnD,QAAQ,KAAK,IAAI,CAAC;IAC/B,OAAOA,QAAoB;;EAE7B,OAAO5G,OAAO;AAChB;AAEgB,SAAAoK,WAAWA,CACzBtC,OAA4B,EAC5BjW,MAAc;EAEd,SAASmO,OAAOA,CAACkK,GAAuB,EAAEC,IAAY;IACpD,MAAMb,UAAU,GAAGO,kBAAkB,CAAC/B,OAAO,EAAEjW,MAAM,EAAEsY,IAAI,CAAC;IAC5DJ,YAAY,CAACT,UAAU,KAAK,IAAI,CAAC;IACjC,OAAOA,UAAwB;;EAEjC,OAAOtJ,OAAO;AAChB;AAEgB,SAAAqK,kBAAkBA,CAChCvC,OAA4B,EAC5BR,QAAkB;EAElB,SAAStH,OAAOA,CAACkK,GAAuB,EAAEC,IAAY;IACpD,MAAMvD,QAAQ,GAAG2B,kBAAkB,CAACT,OAAO,EAAEqC,IAAI,EAAE7C,QAAQ,CAAC;IAC5DyC,YAAY,CAACnD,QAAQ,KAAK,IAAI,CAAC;IAC/B,OAAO6B,6BAA6B,CAClC7B,QAAoB,EACpBuD,IAAI,EACJrC,OAAO,CAAChT,IAAI,EACZgT,OAAO,CAACwC,SAAS,CAClB;;EAEH,OAAOtK,OAAO;AAChB;AAEM,SAAUuK,kBAAkBA,CAChCrV,QAAkB;EAElB,SAAS+K,YAAYA,CACnBiK,GAA+B,EAC/BvL,GAAiB;IAEjB,IAAI6L,MAAoB;IACxB,IAAIN,GAAG,CAAChM,SAAS,EAAE,KAAK,GAAG,EAAE;MAC3B;;;MAGEgM,GAAG,CAACtL,YAAY,EAAE,CAACsE,QAAQ,CAAC,qCAAqC,CAAC,EAClE;QACAsH,MAAM,GAAGvY,eAAe,EAAE;OAC3B,MAAM;QACLuY,MAAM,GAAGzY,eAAe,EAAE;;KAE7B,MAAM;MACL,IAAImY,GAAG,CAAChM,SAAS,EAAE,KAAK,GAAG,EAAE;QAC3BsM,MAAM,GAAG5Y,aAAa,CAACsD,QAAQ,CAACrD,MAAM,CAAC;OACxC,MAAM;QACL,IAAIqY,GAAG,CAAChM,SAAS,EAAE,KAAK,GAAG,EAAE;UAC3BsM,MAAM,GAAGrY,YAAY,CAAC+C,QAAQ,CAACxD,IAAI,CAAC;SACrC,MAAM;UACL8Y,MAAM,GAAG7L,GAAG;;;;IAIlB6L,MAAM,CAACpZ,MAAM,GAAG8Y,GAAG,CAAChM,SAAS,EAAE;IAC/BsM,MAAM,CAACzZ,cAAc,GAAG4N,GAAG,CAAC5N,cAAc;IAC1C,OAAOyZ,MAAM;;EAEf,OAAOvK,YAAY;AACrB;AAEM,SAAUwK,kBAAkBA,CAChCvV,QAAkB;EAElB,MAAMwV,MAAM,GAAGH,kBAAkB,CAACrV,QAAQ,CAAC;EAE3C,SAAS+K,YAAYA,CACnBiK,GAA+B,EAC/BvL,GAAiB;IAEjB,IAAI6L,MAAM,GAAGE,MAAM,CAACR,GAAG,EAAEvL,GAAG,CAAC;IAC7B,IAAIuL,GAAG,CAAChM,SAAS,EAAE,KAAK,GAAG,EAAE;MAC3BsM,MAAM,GAAG/Y,cAAc,CAACyD,QAAQ,CAACxD,IAAI,CAAC;;IAExC8Y,MAAM,CAACzZ,cAAc,GAAG4N,GAAG,CAAC5N,cAAc;IAC1C,OAAOyZ,MAAM;;EAEf,OAAOvK,YAAY;AACrB;SAEgB0K,aAAWC,CACzB9C,OAA4B,EAC5B5S,QAAkB,EAClBoS,QAAkB;EAElB,MAAM1M,OAAO,GAAG1F,QAAQ,CAACV,aAAa,EAAE;EACxC,MAAM9B,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,KAAK;EACpB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC+C,qBAAqB;EAC7C,MAAMpL,WAAW,GAAG,IAAIqK,WAAW,CACjCpX,GAAG,EACHmN,MAAM,EACNoK,eAAe,CAACnC,OAAO,EAAER,QAAQ,CAAC,EAClCzP,OAAO,CACR;EACD4H,WAAW,CAACQ,YAAY,GAAGwK,kBAAkB,CAACvV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEM,SAAUqL,MAAIC,CAClBjD,OAA4B,EAC5B5S,QAAkB,EAClB8V,SAAkB,EAClBC,SAAyB,EACzBC,UAA0B;EAE1B,MAAMvL,SAAS,GAAc,EAAE;EAC/B,IAAIzK,QAAQ,CAACX,MAAM,EAAE;IACnBoL,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE;GACzB,MAAM;IACLA,SAAS,CAAC,QAAQ,CAAC,GAAGzK,QAAQ,CAACxD,IAAI,GAAG,GAAG;;EAE3C,IAAIsZ,SAAS,IAAIA,SAAS,CAACra,MAAM,GAAG,CAAC,EAAE;IACrCgP,SAAS,CAAC,WAAW,CAAC,GAAGqL,SAAS;;EAEpC,IAAIC,SAAS,EAAE;IACbtL,SAAS,CAAC,WAAW,CAAC,GAAGsL,SAAS;;EAEpC,IAAIC,UAAU,EAAE;IACdvL,SAAS,CAAC,YAAY,CAAC,GAAGuL,UAAU;;EAEtC,MAAMtQ,OAAO,GAAG1F,QAAQ,CAACP,mBAAmB,EAAE;EAC9C,MAAMjC,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,KAAK;EACpB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC+C,qBAAqB;EAC7C,MAAMpL,WAAW,GAAG,IAAIqK,WAAW,CACjCpX,GAAG,EACHmN,MAAM,EACNuK,WAAW,CAACtC,OAAO,EAAE5S,QAAQ,CAACrD,MAAM,CAAC,EACrCgG,OAAO,CACR;EACD4H,WAAW,CAACE,SAAS,GAAGA,SAAS;EACjCF,WAAW,CAACQ,YAAY,GAAGsK,kBAAkB,CAACrV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;SAEgB0L,UAAQC,CACtBtD,OAA4B,EAC5B5S,QAAkB,EAClBmW,oBAA6B;EAE7B,MAAMzQ,OAAO,GAAG1F,QAAQ,CAACV,aAAa,EAAE;EACxC,MAAM9B,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC,GAAG,YAAY;EAC5E,MAAMzK,MAAM,GAAG,KAAK;EACpB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC+C,qBAAqB;EAC7C,MAAMpL,WAAW,GAAG,IAAIqK,WAAW,CACjCpX,GAAG,EACHmN,MAAM,EACN,CAACyL,CAAgB,EAAE5J,IAAO,KAAKA,IAAI,EACnC7J,OAAO,CACR;EACD4H,WAAW,CAACQ,YAAY,GAAGwK,kBAAkB,CAACvV,QAAQ,CAAC;EACvD,IAAImW,oBAAoB,KAAKza,SAAS,EAAE;IACtC6O,WAAW,CAACT,OAAO,CAAC,OAAO,CAAC,GAAG,WAAWqM,oBAAoB,EAAE;IAChE5L,WAAW,CAACM,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,uBAAuB;;EAEtE,OAAON,WAAW;AACpB;SAEgB8L,cAAcA,CAC5BzD,OAA4B,EAC5B5S,QAAkB,EAClBoS,QAAkB;EAElB,MAAM1M,OAAO,GAAG1F,QAAQ,CAACV,aAAa,EAAE;EACxC,MAAM9B,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,KAAK;EACpB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC+C,qBAAqB;EAC7C,MAAMpL,WAAW,GAAG,IAAIqK,WAAW,CACjCpX,GAAG,EACHmN,MAAM,EACNwK,kBAAkB,CAACvC,OAAO,EAAER,QAAQ,CAAC,EACrCzP,OAAO,CACR;EACD4H,WAAW,CAACQ,YAAY,GAAGwK,kBAAkB,CAACvV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEM,SAAU+L,gBAAcC,CAC5B3D,OAA4B,EAC5B5S,QAAkB,EAClB0R,QAA2B,EAC3BU,QAAkB;EAElB,MAAM1M,OAAO,GAAG1F,QAAQ,CAACV,aAAa,EAAE;EACxC,MAAM9B,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,OAAO;EACtB,MAAMC,IAAI,GAAGmJ,gBAAgB,CAACrC,QAAQ,EAAEU,QAAQ,CAAC;EACjD,MAAMtI,OAAO,GAAG;IAAE,cAAc,EAAE;EAAiC,CAAE;EACrE,MAAMnH,OAAO,GAAGiQ,OAAO,CAAC+C,qBAAqB;EAC7C,MAAMpL,WAAW,GAAG,IAAIqK,WAAW,CACjCpX,GAAG,EACHmN,MAAM,EACNoK,eAAe,CAACnC,OAAO,EAAER,QAAQ,CAAC,EAClCzP,OAAO,CACR;EACD4H,WAAW,CAACT,OAAO,GAAGA,OAAO;EAC7BS,WAAW,CAACK,IAAI,GAAGA,IAAI;EACvBL,WAAW,CAACQ,YAAY,GAAGwK,kBAAkB,CAACvV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEgB,SAAAiM,cAAYC,CAC1B7D,OAA4B,EAC5B5S,QAAkB;EAElB,MAAM0F,OAAO,GAAG1F,QAAQ,CAACV,aAAa,EAAE;EACxC,MAAM9B,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,QAAQ;EACvB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC+C,qBAAqB;EAE7C,SAAS7K,OAAOA,CAAC4L,IAAwB,EAAEC,KAAa;EACxD,MAAMpM,WAAW,GAAG,IAAIqK,WAAW,CAACpX,GAAG,EAAEmN,MAAM,EAAEG,OAAO,EAAEnI,OAAO,CAAC;EAClE4H,WAAW,CAACM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EACrCN,WAAW,CAACQ,YAAY,GAAGwK,kBAAkB,CAACvV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEgB,SAAAqM,qBAAqBA,CACnClF,QAAyB,EACzB/F,IAAoB;EAEpB,OACG+F,QAAQ,IAAIA,QAAQ,CAAC,aAAa,CAAC,IACnC/F,IAAI,IAAIA,IAAI,CAACyD,IAAI,EAAG,IACrB,0BAA0B;AAE9B;SAEgByH,kBAAkBA,CAChC7W,QAAkB,EAClB2L,IAAa,EACb+F,QAA0B;EAE1B,MAAMoF,aAAa,GAAG/a,MAAM,CAAC2O,MAAM,CAAC,EAAE,EAAEgH,QAAQ,CAAC;EACjDoF,aAAa,CAAC,UAAU,CAAC,GAAG9W,QAAQ,CAACxD,IAAI;EACzCsa,aAAa,CAAC,MAAM,CAAC,GAAGnL,IAAI,CAACsD,IAAI,EAAE;EACnC,IAAI,CAAC6H,aAAa,CAAC,aAAa,CAAC,EAAE;IACjCA,aAAa,CAAC,aAAa,CAAC,GAAGF,qBAAqB,CAAC,IAAI,EAAEjL,IAAI,CAAC;;EAElE,OAAOmL,aAAa;AACtB;AAEA;;AAEG;AACG,SAAUC,eAAeA,CAC7BnE,OAA4B,EAC5B5S,QAAkB,EAClBoS,QAAkB,EAClBzG,IAAa,EACb+F,QAA0B;EAE1B,MAAMhM,OAAO,GAAG1F,QAAQ,CAACP,mBAAmB,EAAE;EAC9C,MAAMqK,OAAO,GAA+B;IAC1C,wBAAwB,EAAE;GAC3B;EAED,SAASkN,WAAWA,CAAA;IAClB,IAAIC,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIvV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BuV,GAAG,GAAGA,GAAG,GAAG7S,IAAI,CAACC,MAAM,EAAE,CAAC6S,QAAQ,EAAE,CAAC7W,KAAK,CAAC,CAAC,CAAC;;IAE/C,OAAO4W,GAAG;;EAEZ,MAAME,QAAQ,GAAGH,WAAW,EAAE;EAC9BlN,OAAO,CAAC,cAAc,CAAC,GAAG,8BAA8B,GAAGqN,QAAQ;EACnE,MAAMC,SAAS,GAAGP,kBAAkB,CAAC7W,QAAQ,EAAE2L,IAAI,EAAE+F,QAAQ,CAAC;EAC9D,MAAM2F,cAAc,GAAGtD,gBAAgB,CAACqD,SAAS,EAAEhF,QAAQ,CAAC;EAC5D,MAAMkF,WAAW,GACf,IAAI,GACJH,QAAQ,GACR,MAAM,GACN,uDAAuD,GACvDE,cAAc,GACd,QAAQ,GACRF,QAAQ,GACR,MAAM,GACN,gBAAgB,GAChBC,SAAS,CAAC,aAAa,CAAC,GACxB,UAAU;EACZ,MAAMG,YAAY,GAAG,QAAQ,GAAGJ,QAAQ,GAAG,IAAI;EAC/C,MAAMvM,IAAI,GAAGmE,OAAO,CAAC1D,OAAO,CAACiM,WAAW,EAAE3L,IAAI,EAAE4L,YAAY,CAAC;EAC7D,IAAI3M,IAAI,KAAK,IAAI,EAAE;IACjB,MAAM9M,eAAe,EAAE;;EAEzB,MAAM2M,SAAS,GAAc;IAAE7L,IAAI,EAAEwY,SAAS,CAAC,UAAU;EAAE,CAAE;EAC7D,MAAM5Z,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,MAAM;EACrB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC4E,kBAAkB;EAC1C,MAAMjN,WAAW,GAAG,IAAIqK,WAAW,CACjCpX,GAAG,EACHmN,MAAM,EACNoK,eAAe,CAACnC,OAAO,EAAER,QAAQ,CAAC,EAClCzP,OAAO,CACR;EACD4H,WAAW,CAACE,SAAS,GAAGA,SAAS;EACjCF,WAAW,CAACT,OAAO,GAAGA,OAAO;EAC7BS,WAAW,CAACK,IAAI,GAAGA,IAAI,CAAC6F,UAAU,EAAE;EACpClG,WAAW,CAACQ,YAAY,GAAGsK,kBAAkB,CAACrV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEA;;;;;;AAMG;MACUkN,qBAAqB;EAIhCrc,YACSsc,OAAe,EACfnP,KAAa,EACpBoP,SAAmB,EACnBjG,QAA0B;IAHnB,IAAO,CAAAgG,OAAA,GAAPA,OAAO;IACP,IAAK,CAAAnP,KAAA,GAALA,KAAK;IAIZ,IAAI,CAACoP,SAAS,GAAG,CAAC,CAACA,SAAS;IAC5B,IAAI,CAACjG,QAAQ,GAAGA,QAAQ,IAAI,IAAI;;AAEnC;AAEe,SAAAkG,kBAAkBA,CAChC5C,GAAuB,EACvB6C,OAAkB;EAElB,IAAI3b,MAAM,GAAkB,IAAI;EAChC,IAAI;IACFA,MAAM,GAAG8Y,GAAG,CAAC8C,iBAAiB,CAAC,sBAAsB,CAAC;GACvD,CAAC,OAAO/X,CAAC,EAAE;IACV8U,YAAY,CAAC,KAAK,CAAC;;EAErB,MAAMkD,aAAa,GAAGF,OAAO,IAAI,CAAC,QAAQ,CAAC;EAC3ChD,YAAY,CAAC,CAAC,CAAC3Y,MAAM,IAAI6b,aAAa,CAACtR,OAAO,CAACvK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9D,OAAOA,MAAgB;AACzB;AAEM,SAAU8b,qBAAqBA,CACnCpF,OAA4B,EAC5B5S,QAAkB,EAClBoS,QAAkB,EAClBzG,IAAa,EACb+F,QAA0B;EAE1B,MAAMhM,OAAO,GAAG1F,QAAQ,CAACP,mBAAmB,EAAE;EAC9C,MAAMwY,iBAAiB,GAAGpB,kBAAkB,CAAC7W,QAAQ,EAAE2L,IAAI,EAAE+F,QAAQ,CAAC;EACtE,MAAMjH,SAAS,GAAc;IAAE7L,IAAI,EAAEqZ,iBAAiB,CAAC,UAAU;EAAE,CAAE;EACrE,MAAMza,GAAG,GAAGiI,OAAO,CAACC,OAAO,EAAEkN,OAAO,CAAChT,IAAI,EAAEgT,OAAO,CAACwC,SAAS,CAAC;EAC7D,MAAMzK,MAAM,GAAG,MAAM;EACrB,MAAMb,OAAO,GAAG;IACd,wBAAwB,EAAE,WAAW;IACrC,uBAAuB,EAAE,OAAO;IAChC,qCAAqC,EAAE,GAAG6B,IAAI,CAACsD,IAAI,EAAE,EAAE;IACvD,mCAAmC,EAAEgJ,iBAAiB,CAAC,aAAa,CAAE;IACtE,cAAc,EAAE;GACjB;EACD,MAAMrN,IAAI,GAAGmJ,gBAAgB,CAACkE,iBAAiB,EAAE7F,QAAQ,CAAC;EAC1D,MAAMzP,OAAO,GAAGiQ,OAAO,CAAC4E,kBAAkB;EAE1C,SAAS1M,OAAOA,CAACkK,GAAuB;IACtC4C,kBAAkB,CAAC5C,GAAG,CAAC;IACvB,IAAIxX,GAAG;IACP,IAAI;MACFA,GAAG,GAAGwX,GAAG,CAAC8C,iBAAiB,CAAC,mBAAmB,CAAC;KACjD,CAAC,OAAO/X,CAAC,EAAE;MACV8U,YAAY,CAAC,KAAK,CAAC;;IAErBA,YAAY,CAAC9P,QAAQ,CAACvH,GAAG,CAAC,CAAC;IAC3B,OAAOA,GAAa;;EAEtB,MAAM+M,WAAW,GAAG,IAAIqK,WAAW,CAACpX,GAAG,EAAEmN,MAAM,EAAEG,OAAO,EAAEnI,OAAO,CAAC;EAClE4H,WAAW,CAACE,SAAS,GAAGA,SAAS;EACjCF,WAAW,CAACT,OAAO,GAAGA,OAAO;EAC7BS,WAAW,CAACK,IAAI,GAAGA,IAAI;EACvBL,WAAW,CAACQ,YAAY,GAAGsK,kBAAkB,CAACrV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEA;;AAEG;AACG,SAAU2N,wBAAwBA,CACtCtF,OAA4B,EAC5B5S,QAAkB,EAClBxC,GAAW,EACXmO,IAAa;EAEb,MAAM7B,OAAO,GAAG;IAAE,uBAAuB,EAAE;EAAO,CAAE;EAEpD,SAASgB,OAAOA,CAACkK,GAAuB;IACtC,MAAM9Y,MAAM,GAAG0b,kBAAkB,CAAC5C,GAAG,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3D,IAAImD,UAAU,GAAkB,IAAI;IACpC,IAAI;MACFA,UAAU,GAAGnD,GAAG,CAAC8C,iBAAiB,CAAC,6BAA6B,CAAC;KAClE,CAAC,OAAO/X,CAAC,EAAE;MACV8U,YAAY,CAAC,KAAK,CAAC;;IAGrB,IAAI,CAACsD,UAAU,EAAE;;MAEftD,YAAY,CAAC,KAAK,CAAC;;IAGrB,MAAM5F,IAAI,GAAGwD,MAAM,CAAC0F,UAAU,CAAC;IAC/BtD,YAAY,CAAC,CAACuD,KAAK,CAACnJ,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAIwI,qBAAqB,CAACxI,IAAI,EAAEtD,IAAI,CAACsD,IAAI,EAAE,EAAE/S,MAAM,KAAK,OAAO,CAAC;;EAEzE,MAAMyO,MAAM,GAAG,MAAM;EACrB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC4E,kBAAkB;EAC1C,MAAMjN,WAAW,GAAG,IAAIqK,WAAW,CAACpX,GAAG,EAAEmN,MAAM,EAAEG,OAAO,EAAEnI,OAAO,CAAC;EAClE4H,WAAW,CAACT,OAAO,GAAGA,OAAO;EAC7BS,WAAW,CAACQ,YAAY,GAAGsK,kBAAkB,CAACrV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;AAEA;;;AAGG;AACI,MAAM8N,2BAA2B,GAAW,GAAG,GAAG,IAAI;AAE7D;;;;;;;;AAQG;SACaC,uBAAuBA,CACrCtY,QAAkB,EAClB4S,OAA4B,EAC5BpV,GAAW,EACXmO,IAAa,EACb4M,SAAiB,EACjBnG,QAAkB,EAClBlW,MAAqC,EACrC8O,gBAA4D;;;EAI5D,MAAMzP,OAAO,GAAG,IAAIkc,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,IAAIvb,MAAM,EAAE;IACVX,OAAO,CAACmc,OAAO,GAAGxb,MAAM,CAACwb,OAAO;IAChCnc,OAAO,CAACgN,KAAK,GAAGrM,MAAM,CAACqM,KAAK;GAC7B,MAAM;IACLhN,OAAO,CAACmc,OAAO,GAAG,CAAC;IACnBnc,OAAO,CAACgN,KAAK,GAAGoD,IAAI,CAACsD,IAAI,EAAE;;EAE7B,IAAItD,IAAI,CAACsD,IAAI,EAAE,KAAK1T,OAAO,CAACgN,KAAK,EAAE;IACjC,MAAMvK,mBAAmB,EAAE;;EAE7B,MAAMwa,SAAS,GAAGjd,OAAO,CAACgN,KAAK,GAAGhN,OAAO,CAACmc,OAAO;EACjD,IAAIe,aAAa,GAAGD,SAAS;EAC7B,IAAID,SAAS,GAAG,CAAC,EAAE;IACjBE,aAAa,GAAGrU,IAAI,CAACsU,GAAG,CAACD,aAAa,EAAEF,SAAS,CAAC;;EAEpD,MAAM7I,SAAS,GAAGnU,OAAO,CAACmc,OAAO;EACjC,MAAM/H,OAAO,GAAGD,SAAS,GAAG+I,aAAa;EACzC,IAAIE,aAAa,GAAG,EAAE;EACtB,IAAIF,aAAa,KAAK,CAAC,EAAE;IACvBE,aAAa,GAAG,UAAU;GAC3B,MAAM,IAAIH,SAAS,KAAKC,aAAa,EAAE;IACtCE,aAAa,GAAG,kBAAkB;GACnC,MAAM;IACLA,aAAa,GAAG,QAAQ;;EAE1B,MAAM7O,OAAO,GAAG;IACd,uBAAuB,EAAE6O,aAAa;IACtC,sBAAsB,EAAE,GAAGpd,OAAO,CAACmc,OAAO;GAC3C;EACD,MAAM9M,IAAI,GAAGe,IAAI,CAACtL,KAAK,CAACqP,SAAS,EAAEC,OAAO,CAAC;EAC3C,IAAI/E,IAAI,KAAK,IAAI,EAAE;IACjB,MAAM9M,eAAe,EAAE;;EAGzB,SAASgN,OAAOA,CACdkK,GAAuB,EACvBC,IAAY;;;;;IAMZ,MAAM2D,YAAY,GAAGhB,kBAAkB,CAAC5C,GAAG,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACjE,MAAM6D,UAAU,GAAGtd,OAAO,CAACmc,OAAO,GAAGe,aAAa;IAClD,MAAMxJ,IAAI,GAAGtD,IAAI,CAACsD,IAAI,EAAE;IACxB,IAAIyC,QAAQ;IACZ,IAAIkH,YAAY,KAAK,OAAO,EAAE;MAC5BlH,QAAQ,GAAGqD,eAAe,CAACnC,OAAO,EAAER,QAAQ,CAAC,CAAC4C,GAAG,EAAEC,IAAI,CAAC;KACzD,MAAM;MACLvD,QAAQ,GAAG,IAAI;;IAEjB,OAAO,IAAI+F,qBAAqB,CAC9BoB,UAAU,EACV5J,IAAI,EACJ2J,YAAY,KAAK,OAAO,EACxBlH,QAAQ,CACT;;EAEH,MAAM/G,MAAM,GAAG,MAAM;EACrB,MAAMhI,OAAO,GAAGiQ,OAAO,CAAC4E,kBAAkB;EAC1C,MAAMjN,WAAW,GAAG,IAAIqK,WAAW,CAACpX,GAAG,EAAEmN,MAAM,EAAEG,OAAO,EAAEnI,OAAO,CAAC;EAClE4H,WAAW,CAACT,OAAO,GAAGA,OAAO;EAC7BS,WAAW,CAACK,IAAI,GAAGA,IAAI,CAAC6F,UAAU,EAAE;EACpClG,WAAW,CAACS,gBAAgB,GAAGA,gBAAgB,IAAI,IAAI;EACvDT,WAAW,CAACQ,YAAY,GAAGsK,kBAAkB,CAACrV,QAAQ,CAAC;EACvD,OAAOuK,WAAW;AACpB;;AC1kBA;;;;;;;;;;;;;;;AAeG;AAYH;;;AAGG;AACU,MAAAuO,SAAS,GAAG;EACvB;;;;;;;;;;;AAWG;EACHC,aAAa,EAAE;;AAsBjB;AACA;;;AAGG;AACU,MAAAC,SAAS,GAAG;;EAEvBC,OAAO,EAAE,SAAS;;EAGlBC,MAAM,EAAE,QAAQ;;EAGhBC,OAAO,EAAE,SAAS;;EAGlB7b,QAAQ,EAAE,UAAU;;EAGpB8b,KAAK,EAAE;;AAGH,SAAUC,8BAA8BA,CAC5CC,KAAwB;EAExB,QAAQA,KAAK;IACX,KAA+B;IAC/B,KAA+B;IAC/B;MACE,OAAON,SAAS,CAACC,OAAO;IAC1B;MACE,OAAOD,SAAS,CAACE,MAAM;IACzB;MACE,OAAOF,SAAS,CAACG,OAAO;IAC1B;MACE,OAAOH,SAAS,CAAC1b,QAAQ;IAC3B;MACE,OAAO0b,SAAS,CAACI,KAAK;IACxB;;MAEE,OAAOJ,SAAS,CAACI,KAAK;;AAE5B;;AC5GA;;;;;;;;;;;;;;;AAeG;MAsDUG,QAAQ;EAKnBne,YACEoe,cAA+C,EAC/CvX,KAAe,EACfwX,QAAqB;IAErB,MAAMC,WAAW,GACf9U,UAAU,CAAC4U,cAAc,CAAC,IAAIvX,KAAK,IAAI,IAAI,IAAIwX,QAAQ,IAAI,IAAI;IACjE,IAAIC,WAAW,EAAE;MACf,IAAI,CAACC,IAAI,GAAGH,cAA2B;MACvC,IAAI,CAACvX,KAAK,GAAGA,KAAK,aAALA,KAAK,KAAL,SAAAA,KAAK,GAAIvG,SAAS;MAC/B,IAAI,CAAC+d,QAAQ,GAAGA,QAAQ,aAARA,QAAQ,KAAR,SAAAA,QAAQ,GAAI/d,SAAS;KACtC,MAAM;MACL,MAAMke,QAAQ,GAAGJ,cAIhB;MACD,IAAI,CAACG,IAAI,GAAGC,QAAQ,CAACD,IAAI;MACzB,IAAI,CAAC1X,KAAK,GAAG2X,QAAQ,CAAC3X,KAAK;MAC3B,IAAI,CAACwX,QAAQ,GAAGG,QAAQ,CAACH,QAAQ;;;AAGtC;;AChGD;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;AACH;AACM,SAAUI,KAAKA,CAACC,CAAW;EAC/B,OAAO,YAAgC;IAAA,SAAAC,KAAA,GAAAve,SAAA,CAAAC,MAAA,EAA5Bue,aAAwB,OAAA3W,KAAA,CAAA0W,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAAxBD,aAAwB,CAAAC,KAAA,IAAAze,SAAA,CAAAye,KAAA;IAAA;;IAEjC9X,OAAO,CAACyF,OAAO,EAAE,CAACe,IAAI,CAAC,MAAMmR,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC;EACnD,CAAC;AACH;;AC5BA;;;;;;;;;;;;;;;AAeG;AAUH;AACA,IAAIE,mBAAmB,GAAsC,IAAI;AAEjE;;;AAGG;AACH,MAAeC,aAAa;EAQ1B/e,YAAA;IAFU,IAAK,CAAAgf,KAAA,GAAY,KAAK;IAG9B,IAAI,CAACC,IAAI,GAAG,IAAIC,cAAc,EAAE;IAChC,IAAI,CAACC,OAAO,EAAE;IACd,IAAI,CAACC,UAAU,GAAGrU,SAAS,CAAC4C,QAAQ;IACpC,IAAI,CAAC0R,YAAY,GAAG,IAAItY,OAAO,CAACyF,OAAO,IAAG;MACxC,IAAI,CAACyS,IAAI,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACvC,IAAI,CAACF,UAAU,GAAGrU,SAAS,CAAC+C,KAAK;QACjCtB,OAAO,EAAE;MACX,CAAC,CAAC;MACF,IAAI,CAACyS,IAAI,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACvC,IAAI,CAACF,UAAU,GAAGrU,SAAS,CAACwU,aAAa;QACzC/S,OAAO,EAAE;MACX,CAAC,CAAC;MACF,IAAI,CAACyS,IAAI,CAACK,gBAAgB,CAAC,MAAM,EAAE,MAAK;QACtC9S,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC;;EAKJc,IAAIA,CACFlL,GAAW,EACXmN,MAAc,EACdC,IAAsC,EACtCd,OAAiB;IAEjB,IAAI,IAAI,CAACsQ,KAAK,EAAE;MACd,MAAMnb,aAAa,CAAC,+BAA+B,CAAC;;IAEtD,IAAI,CAACmb,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,IAAI,CAACO,IAAI,CAACjQ,MAAM,EAAEnN,GAAG,EAAE,IAAI,CAAC;IACjC,IAAIsM,OAAO,KAAKpO,SAAS,EAAE;MACzB,KAAK,MAAMsK,GAAG,IAAI8D,OAAO,EAAE;QACzB,IAAIA,OAAO,CAAC7D,cAAc,CAACD,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACqU,IAAI,CAACQ,gBAAgB,CAAC7U,GAAG,EAAE8D,OAAO,CAAC9D,GAAG,CAAC,CAACkR,QAAQ,EAAE,CAAC;;;;IAI9D,IAAItM,IAAI,KAAKlP,SAAS,EAAE;MACtB,IAAI,CAAC2e,IAAI,CAAC3R,IAAI,CAACkC,IAAI,CAAC;KACrB,MAAM;MACL,IAAI,CAACyP,IAAI,CAAC3R,IAAI,EAAE;;IAElB,OAAO,IAAI,CAAC+R,YAAY;;EAG1B3R,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACsR,KAAK,EAAE;MACf,MAAMnb,aAAa,CAAC,uCAAuC,CAAC;;IAE9D,OAAO,IAAI,CAACub,UAAU;;EAGxBxR,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACoR,KAAK,EAAE;MACf,MAAMnb,aAAa,CAAC,oCAAoC,CAAC;;IAE3D,IAAI;MACF,OAAO,IAAI,CAACob,IAAI,CAACne,MAAM;KACxB,CAAC,OAAO6D,CAAC,EAAE;MACV,OAAO,CAAC,CAAC;;;EAIbyJ,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC4Q,KAAK,EAAE;MACf,MAAMnb,aAAa,CAAC,sCAAsC,CAAC;;IAE7D,OAAO,IAAI,CAACob,IAAI,CAACS,QAAQ;;EAG3BpR,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC0Q,KAAK,EAAE;MACf,MAAMnb,aAAa,CAAC,uCAAuC,CAAC;;IAE9D,OAAO,IAAI,CAACob,IAAI,CAACU,UAAU;;;EAI7BnR,KAAKA,CAAA;IACH,IAAI,CAACyQ,IAAI,CAACzQ,KAAK,EAAE;;EAGnBkO,iBAAiBA,CAACkD,MAAc;IAC9B,OAAO,IAAI,CAACX,IAAI,CAACvC,iBAAiB,CAACkD,MAAM,CAAC;;EAG5CvS,yBAAyBA,CAACwS,QAAqC;IAC7D,IAAI,IAAI,CAACZ,IAAI,CAACa,MAAM,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACb,IAAI,CAACa,MAAM,CAACR,gBAAgB,CAAC,UAAU,EAAEO,QAAQ,CAAC;;;EAI3DrS,4BAA4BA,CAACqS,QAAqC;IAChE,IAAI,IAAI,CAACZ,IAAI,CAACa,MAAM,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACb,IAAI,CAACa,MAAM,CAACC,mBAAmB,CAAC,UAAU,EAAEF,QAAQ,CAAC;;;AAG/D;AAEK,MAAOG,iBAAkB,SAAQjB,aAAqB;EAC1DI,OAAOA,CAAA;IACL,IAAI,CAACF,IAAI,CAACgB,YAAY,GAAG,MAAM;;AAElC;SAEeC,iBAAiBA,CAAA;EAC/B,OAAOpB,mBAAmB,GAAGA,mBAAmB,EAAE,GAAG,IAAIkB,iBAAiB,EAAE;AAC9E;AAEM,MAAOG,kBAAmB,SAAQpB,aAA0B;EAGhEI,OAAOA,CAAA;IACL,IAAI,CAACF,IAAI,CAACgB,YAAY,GAAG,aAAa;;AAEzC;SAEeG,kBAAkBA,CAAA;EAChC,OAAO,IAAID,kBAAkB,EAAE;AACjC;AAEM,MAAOE,iBAAkB,SAAQtB,aAAmB;EACxDI,OAAOA,CAAA;IACL,IAAI,CAACF,IAAI,CAACgB,YAAY,GAAG,MAAM;;AAElC;SAEeK,iBAAiBA,CAAA;EAC/B,OAAO,IAAID,iBAAiB,EAAE;AAChC;;AC3KA;;;;;;;;;;;;;;;AAeG;AA6CH;;;;AAIG;MACUE,UAAU;EAsCrBC,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;;EAG3C;;;;AAIG;EACH1gB,YAAY2gB,GAAc,EAAEpQ,IAAa,EAAkC;IAAA,IAAhC+F,QAAA,GAAAlW,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA4B,IAAI;IApC3E;;AAEG;IACH,IAAY,CAAAwgB,YAAA,GAAW,CAAC;IAChB,IAAkB,CAAAC,kBAAA,GAAY,KAAK;IACnC,IAAoB,CAAAC,oBAAA,GAAY,KAAK;IACrC,IAAU,CAAAC,UAAA,GAAuD,EAAE;IAMnE,IAAM,CAAAC,MAAA,GAAkB1gB,SAAS;IACjC,IAAU,CAAA2gB,UAAA,GAAY3gB,SAAS;IAC/B,IAAQ,CAAA4gB,QAAA,GAAsB5gB,SAAS;IACvC,IAAgB,CAAA6gB,gBAAA,GAAW,CAAC;IAG5B,IAAQ,CAAAC,QAAA,GAAsC9gB,SAAS;IACvD,IAAO,CAAA+gB,OAAA,GAAgC/gB,SAAS;IAkBtD,IAAI,CAACghB,IAAI,GAAGX,GAAG;IACf,IAAI,CAACY,KAAK,GAAGhR,IAAI;IACjB,IAAI,CAAC2G,SAAS,GAAGZ,QAAQ;IACzB,IAAI,CAACkL,SAAS,GAAGzK,WAAW,EAAE;IAC9B,IAAI,CAAC0K,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACH,KAAK,CAAC;IACrD,IAAI,CAACI,MAAM;IACX,IAAI,CAACC,aAAa,GAAG/a,KAAK,IAAG;MAC3B,IAAI,CAACqa,QAAQ,GAAG5gB,SAAS;MACzB,IAAI,CAAC6gB,gBAAgB,GAAG,CAAC;MACzB,IAAIta,KAAK,CAAC9F,WAAW,CAACC,gBAAgB,CAACkB,QAAQ,CAAC,EAAE;QAChD,IAAI,CAAC2e,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACgB,oBAAoB,EAAE;OAC5B,MAAM;QACL,MAAMC,cAAc,GAAG,IAAI,CAACtB,2BAA2B,EAAE;QACzD,IAAIxV,iBAAiB,CAACnE,KAAK,CAAC/F,MAAM,EAAE,EAAE,CAAC,EAAE;UACvC,IAAIghB,cAAc,EAAE;YAClBjb,KAAK,GAAG9E,kBAAkB,EAAE;WAC7B,MAAM;YACL,IAAI,CAAC0e,SAAS,GAAGzX,IAAI,CAAC+Y,GAAG,CACvB,IAAI,CAACtB,SAAS,GAAG,CAAC,EAClB5gB,6BAA6B,CAC9B;YACD,IAAI,CAACghB,kBAAkB,GAAG,IAAI;YAC9B,IAAI,CAACgB,oBAAoB,EAAE;YAC3B;;;QAGJ,IAAI,CAACb,MAAM,GAAGna,KAAK;QACnB,IAAI,CAACmb,WAAW,uCAAyB;;IAE7C,CAAC;IACD,IAAI,CAACC,qBAAqB,GAAGpb,KAAK,IAAG;MACnC,IAAI,CAACqa,QAAQ,GAAG5gB,SAAS;MACzB,IAAIuG,KAAK,CAAC9F,WAAW,CAACC,gBAAgB,CAACkB,QAAQ,CAAC,EAAE;QAChD,IAAI,CAAC2f,oBAAoB,EAAE;OAC5B,MAAM;QACL,IAAI,CAACb,MAAM,GAAGna,KAAK;QACnB,IAAI,CAACmb,WAAW,uCAAyB;;IAE7C,CAAC;IACD,IAAI,CAACvB,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACY,IAAI,CAACY,OAAO,CAAC9F,kBAAkB;IACxD,IAAI,CAAC+F,QAAQ,GAAG,IAAIpb,OAAO,CAAC,CAACyF,OAAO,EAAExF,MAAM,KAAI;MAC9C,IAAI,CAACoa,QAAQ,GAAG5U,OAAO;MACvB,IAAI,CAAC6U,OAAO,GAAGra,MAAM;MACrB,IAAI,CAACob,MAAM,EAAE;IACf,CAAC,CAAC;;;IAIF,IAAI,CAACD,QAAQ,CAAC5U,IAAI,CAAC,IAAI,EAAE,MAAK,EAAG,CAAC;;EAG5B8U,qBAAqBA,CAAA;IAC3B,MAAMC,UAAU,GAAG,IAAI,CAAC1B,YAAY;IACpC,OAAO1T,MAAM,IAAI,IAAI,CAACqV,eAAe,CAACD,UAAU,GAAGpV,MAAM,CAAC;;EAGpDwU,kBAAkBA,CAACnR,IAAa;IACtC,OAAOA,IAAI,CAACsD,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI;;EAGzBuO,MAAMA,CAAA;IACZ,IAAI,IAAI,CAACT,MAAM,gDAAgC;;MAE7C;;IAEF,IAAI,IAAI,CAACT,QAAQ,KAAK5gB,SAAS,EAAE;MAC/B;;IAEF,IAAI,IAAI,CAACmhB,UAAU,EAAE;MACnB,IAAI,IAAI,CAACR,UAAU,KAAK3gB,SAAS,EAAE;QACjC,IAAI,CAACkiB,gBAAgB,EAAE;OACxB,MAAM;QACL,IAAI,IAAI,CAAC3B,kBAAkB,EAAE;UAC3B,IAAI,CAAC4B,YAAY,EAAE;SACpB,MAAM;UACL,IAAI,IAAI,CAAC3B,oBAAoB,EAAE;;YAE7B,IAAI,CAAC4B,cAAc,EAAE;WACtB,MAAM;YACL,IAAI,CAACC,cAAc,GAAGra,UAAU,CAAC,MAAK;cACpC,IAAI,CAACqa,cAAc,GAAGriB,SAAS;cAC/B,IAAI,CAACsiB,eAAe,EAAE;YACxB,CAAC,EAAE,IAAI,CAACnC,SAAS,CAAC;;;;KAIzB,MAAM;MACL,IAAI,CAACoC,cAAc,EAAE;;;EAIjBC,aAAaA,CACnBC,QAA0E;;IAG1Ehc,OAAO,CAACic,GAAG,CAAC,CACV,IAAI,CAAC1B,IAAI,CAACY,OAAO,CAACe,aAAa,EAAE,EACjC,IAAI,CAAC3B,IAAI,CAACY,OAAO,CAACgB,iBAAiB,EAAE,CACtC,CAAC,CAAC3V,IAAI,CAAC+T,IAAA,IAA+B;MAAA,IAA9B,CAAC3S,SAAS,EAAEM,aAAa,CAAC,GAAAqS,IAAA;MACjC,QAAQ,IAAI,CAACK,MAAM;QACjB;UACEoB,QAAQ,CAACpU,SAAS,EAAEM,aAAa,CAAC;UAClC;QACF;UACE,IAAI,CAAC+S,WAAW,6CAA4B;UAC5C;QACF;UACE,IAAI,CAACA,WAAW,yCAA0B;UAC1C;;IAGN,CAAC,CAAC;;;EAKIQ,gBAAgBA,CAAA;IACtB,IAAI,CAACM,aAAa,CAAC,CAACnU,SAAS,EAAEM,aAAa,KAAI;MAC9C,MAAME,WAAW,GAAGyN,qBAAqB,CACvC,IAAI,CAAC0E,IAAI,CAACY,OAAO,EACjB,IAAI,CAACZ,IAAI,CAAC6B,SAAS,EACnB,IAAI,CAAC3B,SAAS,EACd,IAAI,CAACD,KAAK,EACV,IAAI,CAACrK,SAAS,CACf;MACD,MAAMkM,aAAa,GAAG,IAAI,CAAC9B,IAAI,CAACY,OAAO,CAACmB,YAAY,CAClDlU,WAAW,EACX+Q,iBAAiB,EACjBvR,SAAS,EACTM,aAAa,CACd;MACD,IAAI,CAACiS,QAAQ,GAAGkC,aAAa;MAC7BA,aAAa,CAACnc,UAAU,EAAE,CAACsG,IAAI,CAAEnL,GAAW,IAAI;QAC9C,IAAI,CAAC8e,QAAQ,GAAG5gB,SAAS;QACzB,IAAI,CAAC2gB,UAAU,GAAG7e,GAAG;QACrB,IAAI,CAACye,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACgB,oBAAoB,EAAE;MAC7B,CAAC,EAAE,IAAI,CAACD,aAAa,CAAC;IACxB,CAAC,CAAC;;EAGIa,YAAYA,CAAA;;IAElB,MAAMrgB,GAAG,GAAG,IAAI,CAAC6e,UAAoB;IACrC,IAAI,CAAC6B,aAAa,CAAC,CAACnU,SAAS,EAAEM,aAAa,KAAI;MAC9C,MAAME,WAAW,GAAG2N,wBAAwB,CAC1C,IAAI,CAACwE,IAAI,CAACY,OAAO,EACjB,IAAI,CAACZ,IAAI,CAAC6B,SAAS,EACnB/gB,GAAG,EACH,IAAI,CAACmf,KAAK,CACX;MACD,MAAM+B,aAAa,GAAG,IAAI,CAAChC,IAAI,CAACY,OAAO,CAACmB,YAAY,CAClDlU,WAAW,EACX+Q,iBAAiB,EACjBvR,SAAS,EACTM,aAAa,CACd;MACD,IAAI,CAACiS,QAAQ,GAAGoC,aAAa;MAC7BA,aAAa,CAACrc,UAAU,EAAE,CAACsG,IAAI,CAACzM,MAAM,IAAG;QACvCA,MAAM,GAAGA,MAA+B;QACxC,IAAI,CAACogB,QAAQ,GAAG5gB,SAAS;QACzB,IAAI,CAACiiB,eAAe,CAACzhB,MAAM,CAACwb,OAAO,CAAC;QACpC,IAAI,CAACuE,kBAAkB,GAAG,KAAK;QAC/B,IAAI/f,MAAM,CAACyb,SAAS,EAAE;UACpB,IAAI,CAACuE,oBAAoB,GAAG,IAAI;;QAElC,IAAI,CAACe,oBAAoB,EAAE;MAC7B,CAAC,EAAE,IAAI,CAACD,aAAa,CAAC;IACxB,CAAC,CAAC;;EAGIgB,eAAeA,CAAA;IACrB,MAAMzF,SAAS,GAAGF,2BAA2B,GAAG,IAAI,CAACkE,gBAAgB;IACrE,MAAMrgB,MAAM,GAAG,IAAIub,qBAAqB,CACtC,IAAI,CAACuE,YAAY,EACjB,IAAI,CAACW,KAAK,CAAC1N,IAAI,EAAE,CAClB;;IAGD,MAAMzR,GAAG,GAAG,IAAI,CAAC6e,UAAoB;IACrC,IAAI,CAAC6B,aAAa,CAAC,CAACnU,SAAS,EAAEM,aAAa,KAAI;MAC9C,IAAIE,WAAW;MACf,IAAI;QACFA,WAAW,GAAG+N,uBAAuB,CACnC,IAAI,CAACoE,IAAI,CAAC6B,SAAS,EACnB,IAAI,CAAC7B,IAAI,CAACY,OAAO,EACjB9f,GAAG,EACH,IAAI,CAACmf,KAAK,EACVpE,SAAS,EACT,IAAI,CAACqE,SAAS,EACd1gB,MAAM,EACN,IAAI,CAACuhB,qBAAqB,EAAE,CAC7B;OACF,CAAC,OAAO1d,CAAC,EAAE;QACV,IAAI,CAACqc,MAAM,GAAGrc,CAAiB;QAC/B,IAAI,CAACqd,WAAW,uCAAyB;QACzC;;MAEF,MAAMuB,aAAa,GAAG,IAAI,CAACjC,IAAI,CAACY,OAAO,CAACmB,YAAY,CAClDlU,WAAW,EACX+Q,iBAAiB,EACjBvR,SAAS,EACTM,aAAa,E,UACF,KAAK;OACjB;MACD,IAAI,CAACiS,QAAQ,GAAGqC,aAAa;MAC7BA,aAAa,CAACtc,UAAU,EAAE,CAACsG,IAAI,CAAEiW,SAAgC,IAAI;QACnE,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACvC,QAAQ,GAAG5gB,SAAS;QACzB,IAAI,CAACiiB,eAAe,CAACiB,SAAS,CAAClH,OAAO,CAAC;QACvC,IAAIkH,SAAS,CAACjH,SAAS,EAAE;UACvB,IAAI,CAACrF,SAAS,GAAGsM,SAAS,CAAClN,QAAQ;UACnC,IAAI,CAAC0L,WAAW,2CAA2B;SAC5C,MAAM;UACL,IAAI,CAACH,oBAAoB,EAAE;;MAE/B,CAAC,EAAE,IAAI,CAACD,aAAa,CAAC;IACxB,CAAC,CAAC;;EAGI6B,mBAAmBA,CAAA;IACzB,MAAMC,WAAW,GAAGzG,2BAA2B,GAAG,IAAI,CAACkE,gBAAgB;;IAGvE,IAAIuC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;MACtC,IAAI,CAACvC,gBAAgB,IAAI,CAAC;;;EAItBuB,cAAcA,CAAA;IACpB,IAAI,CAACI,aAAa,CAAC,CAACnU,SAAS,EAAEM,aAAa,KAAI;MAC9C,MAAME,WAAW,GAAGkL,aAAW,CAC7B,IAAI,CAACiH,IAAI,CAACY,OAAO,EACjB,IAAI,CAACZ,IAAI,CAAC6B,SAAS,EACnB,IAAI,CAAC3B,SAAS,CACf;MACD,MAAMmC,eAAe,GAAG,IAAI,CAACrC,IAAI,CAACY,OAAO,CAACmB,YAAY,CACpDlU,WAAW,EACX+Q,iBAAiB,EACjBvR,SAAS,EACTM,aAAa,CACd;MACD,IAAI,CAACiS,QAAQ,GAAGyC,eAAe;MAC/BA,eAAe,CAAC1c,UAAU,EAAE,CAACsG,IAAI,CAAC+I,QAAQ,IAAG;QAC3C,IAAI,CAAC4K,QAAQ,GAAG5gB,SAAS;QACzB,IAAI,CAAC4W,SAAS,GAAGZ,QAAQ;QACzB,IAAI,CAAC0L,WAAW,2CAA2B;MAC7C,CAAC,EAAE,IAAI,CAACC,qBAAqB,CAAC;IAChC,CAAC,CAAC;;EAGIY,cAAcA,CAAA;IACpB,IAAI,CAACC,aAAa,CAAC,CAACnU,SAAS,EAAEM,aAAa,KAAI;MAC9C,MAAME,WAAW,GAAGwM,eAAe,CACjC,IAAI,CAAC2F,IAAI,CAACY,OAAO,EACjB,IAAI,CAACZ,IAAI,CAAC6B,SAAS,EACnB,IAAI,CAAC3B,SAAS,EACd,IAAI,CAACD,KAAK,EACV,IAAI,CAACrK,SAAS,CACf;MACD,MAAM0M,gBAAgB,GAAG,IAAI,CAACtC,IAAI,CAACY,OAAO,CAACmB,YAAY,CACrDlU,WAAW,EACX+Q,iBAAiB,EACjBvR,SAAS,EACTM,aAAa,CACd;MACD,IAAI,CAACiS,QAAQ,GAAG0C,gBAAgB;MAChCA,gBAAgB,CAAC3c,UAAU,EAAE,CAACsG,IAAI,CAAC+I,QAAQ,IAAG;QAC5C,IAAI,CAAC4K,QAAQ,GAAG5gB,SAAS;QACzB,IAAI,CAAC4W,SAAS,GAAGZ,QAAQ;QACzB,IAAI,CAACiM,eAAe,CAAC,IAAI,CAAChB,KAAK,CAAC1N,IAAI,EAAE,CAAC;QACvC,IAAI,CAACmO,WAAW,2CAA2B;MAC7C,CAAC,EAAE,IAAI,CAACJ,aAAa,CAAC;IACxB,CAAC,CAAC;;EAGIW,eAAeA,CAACsB,WAAmB;IACzC,MAAMC,GAAG,GAAG,IAAI,CAAClD,YAAY;IAC7B,IAAI,CAACA,YAAY,GAAGiD,WAAW;;;;IAK/B,IAAI,IAAI,CAACjD,YAAY,KAAKkD,GAAG,EAAE;MAC7B,IAAI,CAACC,gBAAgB,EAAE;;;EAInB/B,WAAWA,CAAC9D,KAAwB;IAC1C,IAAI,IAAI,CAACyD,MAAM,KAAKzD,KAAK,EAAE;MACzB;;IAEF,QAAQA,KAAK;MACX,KAAiC;MACjC;;;;QAIE,IAAI,CAACyD,MAAM,GAAGzD,KAAK;QACnB,IAAI,IAAI,CAACgD,QAAQ,KAAK5gB,SAAS,EAAE;UAC/B,IAAI,CAAC4gB,QAAQ,CAACha,MAAM,EAAE;SACvB,MAAM,IAAI,IAAI,CAACyb,cAAc,EAAE;UAC9Bla,YAAY,CAAC,IAAI,CAACka,cAAc,CAAC;UACjC,IAAI,CAACA,cAAc,GAAGriB,SAAS;UAC/B,IAAI,CAACuhB,oBAAoB,EAAE;;QAE7B;MACF;;;;QAIE,MAAMmC,SAAS,GAAG,IAAI,CAACrC,MAAM;QAC7B,IAAI,CAACA,MAAM,GAAGzD,KAAK;QACnB,IAAI8F,SAAS,EAAE;UACb,IAAI,CAACD,gBAAgB,EAAE;UACvB,IAAI,CAAC3B,MAAM,EAAE;;QAEf;MACF;;;QAGE,IAAI,CAACT,MAAM,GAAGzD,KAAK;QACnB,IAAI,CAAC6F,gBAAgB,EAAE;QACvB;MACF;;;;QAIE,IAAI,CAAC/C,MAAM,GAAG/e,QAAQ,EAAE;QACxB,IAAI,CAAC0f,MAAM,GAAGzD,KAAK;QACnB,IAAI,CAAC6F,gBAAgB,EAAE;QACvB;MACF;;;;;QAKE,IAAI,CAACpC,MAAM,GAAGzD,KAAK;QACnB,IAAI,CAAC6F,gBAAgB,EAAE;QACvB;MACF;;;;;QAKE,IAAI,CAACpC,MAAM,GAAGzD,KAAK;QACnB,IAAI,CAAC6F,gBAAgB,EAAE;QACvB;;;EAKElC,oBAAoBA,CAAA;IAC1B,QAAQ,IAAI,CAACF,MAAM;MACjB;QACE,IAAI,CAACK,WAAW,yCAA0B;QAC1C;MACF;QACE,IAAI,CAACA,WAAW,6CAA4B;QAC5C;MACF;QACE,IAAI,CAACI,MAAM,EAAE;QACb;;;EAON;;AAEG;EACH,IAAI6B,QAAQA,CAAA;IACV,MAAMC,aAAa,GAAGjG,8BAA8B,CAAC,IAAI,CAAC0D,MAAM,CAAC;IACjE,OAAO;MACLwC,gBAAgB,EAAE,IAAI,CAACvD,YAAY;MACnCwD,UAAU,EAAE,IAAI,CAAC7C,KAAK,CAAC1N,IAAI,EAAE;MAC7BqK,KAAK,EAAEgG,aAAa;MACpB5N,QAAQ,EAAE,IAAI,CAACY,SAAU;MACzBmN,IAAI,EAAE,IAAI;MACV1D,GAAG,EAAE,IAAI,CAACW;KACX;;EAGH;;;;;;;;;;;;;;;;AAgBG;EACHgD,EAAEA,CACAtQ,IAAe,EACfoK,cAG+C,EAC/CvX,KAA6C,EAC7C0d,SAA6B;;IAG7B,MAAM/F,QAAQ,GAAG,IAAIL,QAAQ,CAC1BC,cAE8B,IAAI9d,SAAS,EAC5CuG,KAAK,IAAIvG,SAAS,EAClBikB,SAAS,IAAIjkB,SAAS,CACvB;IACD,IAAI,CAACkkB,YAAY,CAAChG,QAAQ,CAAC;IAC3B,OAAO,MAAK;MACV,IAAI,CAACiG,eAAe,CAACjG,QAAQ,CAAC;IAChC,CAAC;;EAGH;;;;;AAKG;EACHjR,IAAIA,CACFmX,WAAoE,EACpEC,UAA6D;;;IAI7D,OAAO,IAAI,CAACxC,QAAQ,CAAC5U,IAAI,CACvBmX,WAA4D,EAC5DC,UAAyD,CAC1D;;EAGH;;AAEG;EACHC,KAAKA,CAAID,UAAgD;IACvD,OAAO,IAAI,CAACpX,IAAI,CAAC,IAAI,EAAEoX,UAAU,CAAC;;EAGpC;;AAEG;EACKH,YAAYA,CAAChG,QAAsC;IACzD,IAAI,CAACuC,UAAU,CAAChP,IAAI,CAACyM,QAAQ,CAAC;IAC9B,IAAI,CAACqG,eAAe,CAACrG,QAAQ,CAAC;;EAGhC;;AAEG;EACKiG,eAAeA,CAACjG,QAAsC;IAC5D,MAAMlY,CAAC,GAAG,IAAI,CAACya,UAAU,CAAC1V,OAAO,CAACmT,QAAQ,CAAC;IAC3C,IAAIlY,CAAC,KAAK,CAAC,CAAC,EAAE;MACZ,IAAI,CAACya,UAAU,CAAC+D,MAAM,CAACxe,CAAC,EAAE,CAAC,CAAC;;;EAIxByd,gBAAgBA,CAAA;IACtB,IAAI,CAACgB,cAAc,EAAE;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACjE,UAAU,CAAC9b,KAAK,EAAE;IACzC+f,SAAS,CAAC9P,OAAO,CAACsJ,QAAQ,IAAG;MAC3B,IAAI,CAACqG,eAAe,CAACrG,QAAQ,CAAC;IAChC,CAAC,CAAC;;EAGIuG,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC3D,QAAQ,KAAK9gB,SAAS,EAAE;MAC/B,IAAI2kB,SAAS,GAAG,IAAI;MACpB,QAAQhH,8BAA8B,CAAC,IAAI,CAAC0D,MAAM,CAAC;QACjD,KAAK/D,SAAS,CAACG,OAAO;UACpBU,KAAQ,CAAC,IAAI,CAAC2C,QAAQ,CAAC8D,IAAI,CAAC,IAAI,EAAE,IAAI,CAACjB,QAAQ,CAAC,CAAC,EAAE;UACnD;QACF,KAAKrG,SAAS,CAAC1b,QAAQ;QACvB,KAAK0b,SAAS,CAACI,KAAK;UAClB,MAAMmH,MAAM,GAAG,IAAI,CAAC9D,OAAqC;UACzD5C,KAAQ,CAAC0G,MAAM,CAACD,IAAI,CAAC,IAAI,EAAE,IAAI,CAAClE,MAAsB,CAAC,CAAC,EAAE;UAC1D;QACF;UACEiE,SAAS,GAAG,KAAK;UACjB;;MAEJ,IAAIA,SAAS,EAAE;QACb,IAAI,CAAC7D,QAAQ,GAAG9gB,SAAS;QACzB,IAAI,CAAC+gB,OAAO,GAAG/gB,SAAS;;;;EAKtBukB,eAAeA,CAACrG,QAAsC;IAC5D,MAAM0F,aAAa,GAAGjG,8BAA8B,CAAC,IAAI,CAAC0D,MAAM,CAAC;IACjE,QAAQuC,aAAa;MACnB,KAAKtG,SAAS,CAACC,OAAO;MACtB,KAAKD,SAAS,CAACE,MAAM;QACnB,IAAIU,QAAQ,CAACD,IAAI,EAAE;UACjBE,KAAQ,CAACD,QAAQ,CAACD,IAAI,CAAC2G,IAAI,CAAC1G,QAAQ,EAAE,IAAI,CAACyF,QAAQ,CAAC,CAAC,EAAE;;QAEzD;MACF,KAAKrG,SAAS,CAACG,OAAO;QACpB,IAAIS,QAAQ,CAACH,QAAQ,EAAE;UACrBI,KAAQ,CAACD,QAAQ,CAACH,QAAQ,CAAC6G,IAAI,CAAC1G,QAAQ,CAAC,CAAC,EAAE;;QAE9C;MACF,KAAKZ,SAAS,CAAC1b,QAAQ;MACvB,KAAK0b,SAAS,CAACI,KAAK;QAClB,IAAIQ,QAAQ,CAAC3X,KAAK,EAAE;UAClB4X,KAAQ,CACND,QAAQ,CAAC3X,KAAK,CAACqe,IAAI,CAAC1G,QAAQ,EAAE,IAAI,CAACwC,MAAsB,CAAC,CAC3D,EAAE;;QAEL;MACF;;QAEE,IAAIxC,QAAQ,CAAC3X,KAAK,EAAE;UAClB4X,KAAQ,CACND,QAAQ,CAAC3X,KAAK,CAACqe,IAAI,CAAC1G,QAAQ,EAAE,IAAI,CAACwC,MAAsB,CAAC,CAC3D,EAAE;;;;EAKX;;;AAGG;EACHoE,MAAMA,CAAA;IACJ,MAAMpT,KAAK,GACT,IAAI,CAAC2P,MAAM,KAA6B,2CACxC,IAAI,CAACA,MAAM;IACb,IAAI3P,KAAK,EAAE;MACT,IAAI,CAACgQ,WAAW,2CAA2B;;IAE7C,OAAOhQ,KAAK;;EAGd;;;AAGG;EACHqT,KAAKA,CAAA;IACH,MAAMrT,KAAK,GAAG,IAAI,CAAC2P,MAAM;IACzB,IAAI3P,KAAK,EAAE;MACT,IAAI,CAACgQ,WAAW,2CAA2B;;IAE7C,OAAOhQ,KAAK;;EAGd;;;;AAIG;EACH9K,MAAMA,CAAA;IACJ,MAAM8K,KAAK,GACT,IAAI,CAAC2P,MAAM,KAA8B,6CACzC,IAAI,CAACA,MAAM;IACb,IAAI3P,KAAK,EAAE;MACT,IAAI,CAACgQ,WAAW,+CAA6B;;IAE/C,OAAOhQ,KAAK;;AAEf;;AC7qBD;;;;;;;;;;;;;;;AAeG;AAmCH;;;;;;;;;;;AAWG;MACUsT,SAAS;EAGpBtlB,WACUA,CAAAulB,QAA6B,EACrC3gB,QAA2B;IADnB,IAAQ,CAAA2gB,QAAA,GAARA,QAAQ;IAGhB,IAAI3gB,QAAQ,YAAYb,QAAQ,EAAE;MAChC,IAAI,CAACof,SAAS,GAAGve,QAAQ;KAC1B,MAAM;MACL,IAAI,CAACue,SAAS,GAAGpf,QAAQ,CAACW,WAAW,CAACE,QAAQ,EAAE2gB,QAAQ,CAAC/gB,IAAI,CAAC;;;EAIlE;;;;AAIG;EACHsX,QAAQA,CAAA;IACN,OAAO,OAAO,GAAG,IAAI,CAACqH,SAAS,CAAC5hB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC4hB,SAAS,CAAC/hB,IAAI;;EAG1DokB,OAAOA,CACfhO,OAA4B,EAC5B5S,QAAkB;IAElB,OAAO,IAAI0gB,SAAS,CAAC9N,OAAO,EAAE5S,QAAQ,CAAC;;EAGzC;;AAEG;EACH,IAAI6gB,IAAIA,CAAA;IACN,MAAM7gB,QAAQ,GAAG,IAAIb,QAAQ,CAAC,IAAI,CAACof,SAAS,CAAC5hB,MAAM,EAAE,EAAE,CAAC;IACxD,OAAO,IAAI,CAACikB,OAAO,CAAC,IAAI,CAACD,QAAQ,EAAE3gB,QAAQ,CAAC;;EAG9C;;AAEG;EACH,IAAIrD,MAAMA,CAAA;IACR,OAAO,IAAI,CAAC4hB,SAAS,CAAC5hB,MAAM;;EAG9B;;AAEG;EACH,IAAIuV,QAAQA,CAAA;IACV,OAAO,IAAI,CAACqM,SAAS,CAAC/hB,IAAI;;EAG5B;;;AAGG;EACH,IAAIoC,IAAIA,CAAA;IACN,OAAO4S,aAAa,CAAC,IAAI,CAAC+M,SAAS,CAAC/hB,IAAI,CAAC;;EAG3C;;AAEG;EACH,IAAI8gB,OAAOA,CAAA;IACT,OAAO,IAAI,CAACqD,QAAQ;;EAGtB;;;AAGG;EACH,IAAI7P,MAAMA,CAAA;IACR,MAAME,OAAO,GAAGF,MAAM,CAAC,IAAI,CAACyN,SAAS,CAAC/hB,IAAI,CAAC;IAC3C,IAAIwU,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;;IAEb,MAAMhR,QAAQ,GAAG,IAAIb,QAAQ,CAAC,IAAI,CAACof,SAAS,CAAC5hB,MAAM,EAAEqU,OAAO,CAAC;IAC7D,OAAO,IAAI0P,SAAS,CAAC,IAAI,CAACC,QAAQ,EAAE3gB,QAAQ,CAAC;;EAG/C;;AAEG;EACH8gB,YAAYA,CAACliB,IAAY;IACvB,IAAI,IAAI,CAAC2f,SAAS,CAAC/hB,IAAI,KAAK,EAAE,EAAE;MAC9B,MAAMmC,oBAAoB,CAACC,IAAI,CAAC;;;AAGrC;AAED;;;AAGG;AACa,SAAAmiB,gBAAgBA,CAC9BhF,GAAc,EACd5F,oBAA6B;EAE7B4F,GAAG,CAAC+E,YAAY,CAAC,UAAU,CAAC;EAC5B,MAAMvW,WAAW,GAAG0L,UAAQ,CAC1B8F,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,EACbpI,oBAAoB,CACrB;EACD,OAAO4F,GAAG,CAACuB,OAAO,CACf0D,qBAAqB,CAACzW,WAAW,EAAEiR,kBAAkB,CAAC,CACtD7S,IAAI,CAACoF,KAAK,IACToI,oBAAoB,KAAKza,SAAS;EAChC;EACGqS,KAAqB,CAAC1N,KAAK,CAAC,CAAC,EAAE8V,oBAAoB,CAAC,GACpDpI,KAAqB,CAC3B;AACL;AAEA;;;AAGG;AACa,SAAAkT,eAAeA,CAC7BlF,GAAc,EACd5F,oBAA6B;EAE7B4F,GAAG,CAAC+E,YAAY,CAAC,SAAS,CAAC;EAC3B,MAAMvW,WAAW,GAAG0L,UAAQ,CAC1B8F,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,EACbpI,oBAAoB,CACrB;EACD,OAAO4F,GAAG,CAACuB,OAAO,CACf0D,qBAAqB,CAACzW,WAAW,EAAEmR,iBAAiB,CAAC,CACrD/S,IAAI,CAACgD,IAAI,IACRwK,oBAAoB,KAAKza,SAAS;EAChC;EACGiQ,IAAa,CAACtL,KAAK,CAAC,CAAC,EAAE8V,oBAAoB,CAAC,GAC5CxK,IAAa,CACnB;AACL;AA4CA;;;;;;;;AAQG;SACauV,aAAWC,CACzBpF,GAAc,EACdvP,IAAqC,EACrCkF,QAAmB;EAEnBqK,GAAG,CAAC+E,YAAY,CAAC,aAAa,CAAC;EAC/B,MAAMvW,WAAW,GAAGwM,eAAe,CACjCgF,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,EACbpM,WAAW,EAAE,EACb,IAAIpD,OAAO,CAACvC,IAAI,EAAE,IAAI,CAAC,EACvBkF,QAAQ,CACT;EACD,OAAOqK,GAAG,CAACuB,OAAO,CACf0D,qBAAqB,CAACzW,WAAW,EAAE+Q,iBAAiB,CAAC,CACrD3S,IAAI,CAACyY,aAAa,IAAG;IACpB,OAAO;MACL1P,QAAQ,EAAE0P,aAAa;MACvBrF;KACD;EACH,CAAC,CAAC;AACN;AAEA;;;;;;;;AAQG;SACasF,sBAAoBC,CAClCvF,GAAc,EACdvP,IAAqC,EACrCkF,QAAmB;EAEnBqK,GAAG,CAAC+E,YAAY,CAAC,sBAAsB,CAAC;EACxC,OAAO,IAAInF,UAAU,CAACI,GAAG,EAAE,IAAIhN,OAAO,CAACvC,IAAI,CAAC,EAAEkF,QAAQ,CAAC;AACzD;AAEA;;;;;;;;;AASG;AACa,SAAA6P,cAAYC,CAC1BzF,GAAc,EACdvW,KAAa,EAEM;EAAA,IADnBzG,MAAA,GAAAvD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB0Q,YAAY,CAACC,GAAG;EAAA,IACvCuF,QAAmB,GAAAlW,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAEnBqgB,GAAG,CAAC+E,YAAY,CAAC,cAAc,CAAC;EAChC,MAAMtU,IAAI,GAAGE,cAAc,CAAC3N,MAAM,EAAEyG,KAAK,CAAC;EAC1C,MAAMsR,aAAa,GAAG/a,MAAK,CAAA2O,MAAA,KAAAgH,QAAQ,CAAc;EACjD,IAAIoF,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,IAAItK,IAAI,CAACC,WAAW,IAAI,IAAI,EAAE;IACpEqK,aAAa,CAAC,aAAa,CAAC,GAAGtK,IAAI,CAACC,WAAY;;EAElD,OAAOyU,aAAW,CAACnF,GAAG,EAAEvP,IAAI,CAACA,IAAI,EAAEsK,aAAa,CAAC;AACnD;AAEA;;;;;;;;;;;;;;;;;;AAkBG;AACG,SAAU2K,SAAOC,CAAC3F,GAAc;EACpC,MAAM4F,WAAW,GAAe;IAC9BtN,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;GACR;EACD,OAAOsN,aAAa,CAAC7F,GAAG,EAAE4F,WAAW,CAAC,CAAChZ,IAAI,CAAC,MAAMgZ,WAAW,CAAC;AAChE;AAEA;;;;;AAKG;AACH,eAAeC,aAAaA,CAC1B7F,GAAc,EACd4F,WAAuB,EACvB5L,SAAkB;EAElB,MAAM8L,GAAG,GAAgB;;IAEvB9L;GACD;EACD,MAAM+L,QAAQ,GAAG,MAAMC,MAAI,CAAChG,GAAG,EAAE8F,GAAG,CAAC;EACrCF,WAAW,CAACtN,QAAQ,CAAClH,IAAI,CAAC,GAAG2U,QAAQ,CAACzN,QAAQ,CAAC;EAC/CsN,WAAW,CAACrN,KAAK,CAACnH,IAAI,CAAC,GAAG2U,QAAQ,CAACxN,KAAK,CAAC;EACzC,IAAIwN,QAAQ,CAACvN,aAAa,IAAI,IAAI,EAAE;IAClC,MAAMqN,aAAa,CAAC7F,GAAG,EAAE4F,WAAW,EAAEG,QAAQ,CAACvN,aAAa,CAAC;;AAEjE;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAAwN,MAAIlM,CAClBkG,GAAc,EACdiG,OAA4B;EAE5B,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,IAAI,OAAOA,OAAO,CAAChM,UAAU,KAAK,QAAQ,EAAE;MAC1C5Q,cAAc,CACZ,oBAAoB,EACpB,eAAgB,CAAC,EACjB,eAAgB,IAAI,EACpB4c,OAAO,CAAChM,UAAU,CACnB;;;EAGL,MAAMiM,EAAE,GAAGD,OAAO,IAAI,EAAE;EACxB,MAAMzX,WAAW,GAAGqL,MAAY,CAC9BmG,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,E,eACG,GAAG,EACnB0D,EAAE,CAAClM,SAAS,EACZkM,EAAE,CAACjM,UAAU,CACd;EACD,OAAO+F,GAAG,CAACuB,OAAO,CAAC0D,qBAAqB,CAACzW,WAAW,EAAE+Q,iBAAiB,CAAC;AAC1E;AAEA;;;;;;AAMG;AACG,SAAU4G,aAAWxM,CAACqG,GAAc;EACxCA,GAAG,CAAC+E,YAAY,CAAC,aAAa,CAAC;EAC/B,MAAMvW,WAAW,GAAGkL,aAAmB,CACrCsG,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,EACbpM,WAAW,EAAE,CACd;EACD,OAAO4J,GAAG,CAACuB,OAAO,CAAC0D,qBAAqB,CAACzW,WAAW,EAAE+Q,iBAAiB,CAAC;AAC1E;AAEA;;;;;;;;;;AAUG;AACa,SAAA6G,gBAAc5L,CAC5BwF,GAAc,EACdrK,QAA2B;EAE3BqK,GAAG,CAAC+E,YAAY,CAAC,gBAAgB,CAAC;EAClC,MAAMvW,WAAW,GAAG+L,gBAAsB,CACxCyF,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,EACb7M,QAAQ,EACRS,WAAW,EAAE,CACd;EACD,OAAO4J,GAAG,CAACuB,OAAO,CAAC0D,qBAAqB,CAACzW,WAAW,EAAE+Q,iBAAiB,CAAC;AAC1E;AAEA;;;;;AAKG;AACG,SAAU8G,gBAAcC,CAACtG,GAAc;EAC3CA,GAAG,CAAC+E,YAAY,CAAC,gBAAgB,CAAC;EAClC,MAAMvW,WAAW,GAAG8L,cAAsB,CACxC0F,GAAG,CAACuB,OAAO,EACXvB,GAAG,CAACwC,SAAS,EACbpM,WAAW,EAAE,CACd;EACD,OAAO4J,GAAG,CAACuB,OAAO,CACf0D,qBAAqB,CAACzW,WAAW,EAAE+Q,iBAAiB,CAAC,CACrD3S,IAAI,CAACnL,GAAG,IAAG;IACV,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB,MAAMU,aAAa,EAAE;;IAEvB,OAAOV,GAAG;EACZ,CAAC,CAAC;AACN;AAEA;;;;;AAKG;AACG,SAAU8kB,cAAY7L,CAACsF,GAAc;EACzCA,GAAG,CAAC+E,YAAY,CAAC,cAAc,CAAC;EAChC,MAAMvW,WAAW,GAAGiM,cAAoB,CAACuF,GAAG,CAACuB,OAAO,EAAEvB,GAAG,CAACwC,SAAS,CAAC;EACpE,OAAOxC,GAAG,CAACuB,OAAO,CAAC0D,qBAAqB,CAACzW,WAAW,EAAE+Q,iBAAiB,CAAC;AAC1E;AAEA;;;;;;;;;AASG;AACa,SAAAiH,WAASC,CAACzG,GAAc,EAAE7K,SAAiB;EACzD,MAAMF,OAAO,GAAGC,KAAK,CAAC8K,GAAG,CAACwC,SAAS,CAAC/hB,IAAI,EAAE0U,SAAS,CAAC;EACpD,MAAMlR,QAAQ,GAAG,IAAIb,QAAQ,CAAC4c,GAAG,CAACwC,SAAS,CAAC5hB,MAAM,EAAEqU,OAAO,CAAC;EAC5D,OAAO,IAAI0P,SAAS,CAAC3E,GAAG,CAACuB,OAAO,EAAEtd,QAAQ,CAAC;AAC7C;;ACzfA;;;;;;;;;;;;;;;AAeG;AAgCG,SAAUyiB,KAAKA,CAACjmB,IAAa;EACjC,OAAO,iBAAiB,CAACkmB,IAAI,CAAClmB,IAAc,CAAC;AAC/C;AAEA;;AAEG;AACH,SAASmmB,UAAUA,CAAC/P,OAA4B,EAAEpV,GAAW;EAC3D,OAAO,IAAIkjB,SAAS,CAAC9N,OAAO,EAAEpV,GAAG,CAAC;AACpC;AAEA;;;AAGG;AACH,SAASolB,WAAWA,CAClB7G,GAAoC,EACpCvf,IAAa;EAEb,IAAIuf,GAAG,YAAY8G,mBAAmB,EAAE;IACtC,MAAMjQ,OAAO,GAAGmJ,GAAG;IACnB,IAAInJ,OAAO,CAACkQ,OAAO,IAAI,IAAI,EAAE;MAC3B,MAAMllB,eAAe,EAAE;;IAEzB,MAAM6W,SAAS,GAAG,IAAIiM,SAAS,CAAC9N,OAAO,EAAEA,OAAO,CAACkQ,OAAQ,CAAC;IAC1D,IAAItmB,IAAI,IAAI,IAAI,EAAE;MAChB,OAAOomB,WAAW,CAACnO,SAAS,EAAEjY,IAAI,CAAC;KACpC,MAAM;MACL,OAAOiY,SAAS;;GAEnB,MAAM;;IAEL,IAAIjY,IAAI,KAAKd,SAAS,EAAE;MACtB,OAAO6mB,WAAS,CAACxG,GAAG,EAAEvf,IAAI,CAAC;KAC5B,MAAM;MACL,OAAOuf,GAAG;;;AAGhB;AAqBgB,SAAAgH,KAAGhH,CACjBiH,YAA6C,EAC7CC,SAAkB;EAElB,IAAIA,SAAS,IAAIR,KAAK,CAACQ,SAAS,CAAC,EAAE;IACjC,IAAID,YAAY,YAAYH,mBAAmB,EAAE;MAC/C,OAAOF,UAAU,CAACK,YAAY,EAAEC,SAAS,CAAC;KAC3C,MAAM;MACL,MAAM1kB,eAAe,CACnB,0EAA0E,CAC3E;;GAEJ,MAAM;IACL,OAAOqkB,WAAW,CAACI,YAAY,EAAEC,SAAS,CAAC;;AAE/C;AAEA,SAASC,aAAaA,CACpBtjB,IAAY,EACZujB,MAAwB;EAExB,MAAMxjB,YAAY,GAAGwjB,MAAM,KAAN,QAAAA,MAAM,uBAANA,MAAM,CAAGroB,yBAAyB,CAAC;EACxD,IAAI6E,YAAY,IAAI,IAAI,EAAE;IACxB,OAAO,IAAI;;EAEb,OAAOR,QAAQ,CAACO,kBAAkB,CAACC,YAAY,EAAEC,IAAI,CAAC;AACxD;AAEM,SAAUwjB,wBAAsBC,CACpC/F,OAA4B,EAC5B1d,IAAY,EACZ0jB,IAAY,EAGN;EAAA,IAFNtB,OAAA,GAAAxmB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAEI,EAAE;EAEN8hB,OAAO,CAAC1d,IAAI,GAAG,GAAGA,IAAI,IAAI0jB,IAAI,EAAE;EAChChG,OAAO,CAAClI,SAAS,GAAG,MAAM;EAC1B,MAAM;IAAEmO;EAAa,CAAE,GAAGvB,OAAO;EACjC,IAAIuB,aAAa,EAAE;IACjBjG,OAAO,CAACkG,kBAAkB,GACxB,OAAOD,aAAa,KAAK,QAAQ,GAC7BA,aAAa,GACbE,mBAAmB,CAACF,aAAa,EAAEjG,OAAO,CAACoG,GAAG,CAAC1B,OAAO,CAAC2B,SAAS,CAAC;;AAE3E;AAEA;;;;;AAKG;MACUd,mBAAmB;EAgB9BznB;EACE;;AAEG;EACMsoB,GAAgB,EAChBE,aAAiD;EAC1D;;AAEG;EACMC,iBAA0D;EACnE;;AAEG;EACMC,IAAa,EACbC,gBAAyB;IAVzB,IAAG,CAAAL,GAAA,GAAHA,GAAG;IACH,IAAa,CAAAE,aAAA,GAAbA,aAAa;IAIb,IAAiB,CAAAC,iBAAA,GAAjBA,iBAAiB;IAIjB,IAAI,CAAAC,IAAA,GAAJA,IAAI;IACJ,IAAgB,CAAAC,gBAAA,GAAhBA,gBAAgB;IA7B3B,IAAO,CAAAjB,OAAA,GAAoB,IAAI;IAC/B;;;;AAIG;IACK,IAAK,CAAAkB,KAAA,GAAWnpB,YAAY;IACpC,IAAS,CAAAua,SAAA,GAAW,OAAO;IACR,IAAM,CAAA6O,MAAA,GAAkB,IAAI;IAEvC,IAAQ,CAAAC,QAAA,GAAY,KAAK;IAqB/B,IAAI,CAACC,sBAAsB,GAAGppB,gCAAgC;IAC9D,IAAI,CAACqpB,mBAAmB,GAAGppB,6BAA6B;IACxD,IAAI,CAACqpB,SAAS,GAAG,IAAIC,GAAG,EAAE;IAC1B,IAAIR,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI,CAAChB,OAAO,GAAG3jB,QAAQ,CAACO,kBAAkB,CAACokB,IAAI,EAAE,IAAI,CAACE,KAAK,CAAC;KAC7D,MAAM;MACL,IAAI,CAAClB,OAAO,GAAGI,aAAa,CAAC,IAAI,CAACc,KAAK,EAAE,IAAI,CAACN,GAAG,CAAC1B,OAAO,CAAC;;;EAI9D;;;AAGG;EACH,IAAIpiB,IAAIA,CAAA;IACN,OAAO,IAAI,CAACokB,KAAK;;EAGnB,IAAIpkB,IAAIA,CAACA,IAAY;IACnB,IAAI,CAACokB,KAAK,GAAGpkB,IAAI;IACjB,IAAI,IAAI,CAACkkB,IAAI,IAAI,IAAI,EAAE;MACrB,IAAI,CAAChB,OAAO,GAAG3jB,QAAQ,CAACO,kBAAkB,CAAC,IAAI,CAACokB,IAAI,EAAElkB,IAAI,CAAC;KAC5D,MAAM;MACL,IAAI,CAACkjB,OAAO,GAAGI,aAAa,CAACtjB,IAAI,EAAE,IAAI,CAAC8jB,GAAG,CAAC1B,OAAO,CAAC;;;EAIxD;;AAEG;EACH,IAAIxK,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAAC4M,mBAAmB;;EAGjC,IAAI5M,kBAAkBA,CAAC+M,IAAY;IACjCnf,cAAc,CACZ,MAAM,EACN,cAAe,CAAC,EAChB,eAAgBqN,MAAM,CAAC+R,iBAAiB,EACxCD,IAAI,CACL;IACD,IAAI,CAACH,mBAAmB,GAAGG,IAAI;;EAGjC;;;AAGG;EACH,IAAI5O,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACwO,sBAAsB;;EAGpC,IAAIxO,qBAAqBA,CAAC4O,IAAY;IACpCnf,cAAc,CACZ,MAAM,EACN,cAAe,CAAC,EAChB,eAAgBqN,MAAM,CAAC+R,iBAAiB,EACxCD,IAAI,CACL;IACD,IAAI,CAACJ,sBAAsB,GAAGI,IAAI;;EAGpC,MAAMlG,aAAaA,CAAA;IACjB,IAAI,IAAI,CAACmF,kBAAkB,EAAE;MAC3B,OAAO,IAAI,CAACA,kBAAkB;;IAEhC,MAAMiB,IAAI,GAAG,IAAI,CAACb,aAAa,CAACc,YAAY,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAChE,IAAIF,IAAI,EAAE;MACR,MAAMG,SAAS,GAAG,MAAMH,IAAI,CAACI,QAAQ,EAAE;MACvC,IAAID,SAAS,KAAK,IAAI,EAAE;QACtB,OAAOA,SAAS,CAACE,WAAW;;;IAGhC,OAAO,IAAI;;EAGb,MAAMxG,iBAAiBA,CAAA;IACrB,IAAIyG,oBAAoB,CAAC,IAAI,CAACrB,GAAG,CAAC,IAAI,IAAI,CAACA,GAAG,CAACsB,QAAQ,CAAC3a,aAAa,EAAE;MACrE,OAAO,IAAI,CAACqZ,GAAG,CAACsB,QAAQ,CAAC3a,aAAa;;IAExC,MAAM4a,QAAQ,GAAG,IAAI,CAACpB,iBAAiB,CAACa,YAAY,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IACxE,IAAIM,QAAQ,EAAE;MACZ,MAAM1b,MAAM,GAAG,MAAM0b,QAAQ,CAACJ,QAAQ,EAAE;;;;;MAKxC,OAAOtb,MAAM,CAACoK,KAAK;;IAErB,OAAO,IAAI;;EAGb;;AAEG;EACHuR,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAAChB,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACG,SAAS,CAAC/T,OAAO,CAAC6U,OAAO,IAAIA,OAAO,CAAC7iB,MAAM,EAAE,CAAC;MACnD,IAAI,CAAC+hB,SAAS,CAACe,KAAK,EAAE;;IAExB,OAAOjjB,OAAO,CAACyF,OAAO,EAAE;;EAG1B;;;AAGG;EACHkL,qBAAqBA,CAAC3S,GAAa;IACjC,OAAO,IAAIugB,SAAS,CAAC,IAAI,EAAEvgB,GAAG,CAAC;;EAGjC;;;AAGG;EACHse,YAAYA,CACVlU,WAA8B,EAC9BC,cAAmC,EACnCT,SAAwB,EACxBM,aAA4B,EAChB;IAAA,IAAZ9C,KAAK,GAAA/L,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAEZ,IAAI,CAAC,IAAI,CAAC0oB,QAAQ,EAAE;MAClB,MAAMiB,OAAO,GAAG7a,WAAW,CACzBC,WAAW,EACX,IAAI,CAAC0Z,MAAM,EACXla,SAAS,EACTM,aAAa,EACbG,cAAc,EACd,IAAI,CAACuZ,gBAAgB,EACrBxc,KAAK,CACN;MACD,IAAI,CAAC8c,SAAS,CAACgB,GAAG,CAACF,OAAO,CAAC;;MAE3BA,OAAO,CAAC9iB,UAAU,EAAE,CAACsG,IAAI,CACvB,MAAM,IAAI,CAAC0b,SAAS,CAACiB,MAAM,CAACH,OAAO,CAAC,EACpC,MAAM,IAAI,CAACd,SAAS,CAACiB,MAAM,CAACH,OAAO,CAAC,CACrC;MACD,OAAOA,OAAO;KACf,MAAM;MACL,OAAO,IAAInjB,WAAW,CAACvD,UAAU,EAAE,CAAC;;;EAIxC,MAAMuiB,qBAAqBA,CACzBzW,WAA8B,EAC9BC,cAAmC;IAEnC,MAAM,CAACT,SAAS,EAAEM,aAAa,CAAC,GAAG,MAAMlI,OAAO,CAACic,GAAG,CAAC,CACnD,IAAI,CAACC,aAAa,EAAE,EACpB,IAAI,CAACC,iBAAiB,EAAE,CACzB,CAAC;IAEF,OAAO,IAAI,CAACG,YAAY,CACtBlU,WAAW,EACXC,cAAc,EACdT,SAAS,EACTM,aAAa,CACd,CAAChI,UAAU,EAAE;;AAEjB;;;;AChWD;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAMkjB,YAAY,GAAG,SAAS;;ACpBrC;;;;;;;;;;;;;;;AAeG;AAqEH;;;;;;;;;;;;;AAaG;AACa,SAAArP,QAAQA,CACtB6F,GAAqB,EACrB5F,oBAA6B;EAE7B4F,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOgF,gBAAgB,CAAChF,GAAgB,EAAE5F,oBAAoB,CAAC;AACjE;AAEA;;;;;;;;AAQG;SACagL,WAAWA,CACzBpF,GAAqB,EACrBvP,IAAqC,EACrCkF,QAAyB;EAEzBqK,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOmF,aAAmB,CACxBnF,GAAgB,EAChBvP,IAAI,EACJkF,QAA4B,CAC7B;AACH;AAEA;;;;;;;;;AASG;AACG,SAAU8P,YAAYA,CAC1BzF,GAAqB,EACrBvW,KAAa,EACbzG,MAAqB,EACrB2S,QAAyB;EAEzBqK,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOwF,cAAoB,CACzBxF,GAAgB,EAChBvW,KAAK,EACLzG,MAAM,EACN2S,QAA4B,CAC7B;AACH;AAEA;;;;;;;;AAQG;SACa4P,oBAAoBA,CAClCvF,GAAqB,EACrBvP,IAAqC,EACrCkF,QAAyB;EAEzBqK,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOsF,sBAA4B,CACjCtF,GAAgB,EAChBvP,IAAI,EACJkF,QAA4B,CACf;AACjB;AAEA;;;;;;AAMG;AACG,SAAUgE,WAAWA,CAACqG,GAAqB;EAC/CA,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOmG,aAAmB,CAACnG,GAAgB,CAA0B;AACvE;AAEA;;;;;;;;AAQG;AACa,SAAAxF,cAAcA,CAC5BwF,GAAqB,EACrBrK,QAA0B;EAE1BqK,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOoG,gBAAsB,CAC3BpG,GAAgB,EAChBrK,QAAqC,CACb;AAC5B;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAAmE,IAAIA,CAClBkG,GAAqB,EACrBiG,OAAqB;EAErBjG,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOgG,MAAY,CAAChG,GAAgB,EAAEiG,OAAO,CAAC;AAChD;AAEA;;;;;;;;;;;;;;;;;;AAkBG;AACG,SAAUN,OAAOA,CAAC3F,GAAqB;EAC3CA,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAO0F,SAAe,CAAC1F,GAAgB,CAAC;AAC1C;AAEA;;;;;;AAMG;AACG,SAAUsG,cAAcA,CAACtG,GAAqB;EAClDA,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOqG,gBAAsB,CAACrG,GAAgB,CAAC;AACjD;AAEA;;;;;AAKG;AACG,SAAUtF,YAAYA,CAACsF,GAAqB;EAChDA,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOuG,cAAoB,CAACvG,GAAgB,CAAC;AAC/C;AAqBgB,SAAAA,GAAGA,CACjBiH,YAAgD,EAChDC,SAAkB;EAElBD,YAAY,GAAGwC,kBAAkB,CAACxC,YAAY,CAAC;EAC/C,OAAOD,KAAW,CAChBC,YAA+C,EAC/CC,SAAS,CACV;AACH;AAEA;;AAEG;AACa,SAAAT,SAASA,CAACzG,GAAqB,EAAE7K,SAAiB;EAChE,OAAOqR,WAAiB,CAACxG,GAAgB,EAAE7K,SAAS,CAAC;AACvD;AAEA;;;;;;;AAOG;SACauU,UAAUA,CAAA,EAEN;EAAA,IADlB/B,GAAA,GAAAloB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAmBkqB,MAAM,EAAE;EAAA,IAC3BC,SAAkB,GAAAnqB,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAElBgoB,GAAG,GAAG8B,kBAAkB,CAAC9B,GAAG,CAAC;EAC7B,MAAMkC,eAAe,GAAwBC,YAAY,CAACnC,GAAG,EAAE6B,YAAY,CAAC;EAC5E,MAAMO,eAAe,GAAGF,eAAe,CAAClB,YAAY,CAAC;IACnDqB,UAAU,EAAEJ;EACb,EAAC;EACF,MAAMK,QAAQ,GAAGC,iCAAiC,CAAC,SAAS,CAAC;EAC7D,IAAID,QAAQ,EAAE;IACZ3C,sBAAsB,CAACyC,eAAe,EAAE,GAAGE,QAAQ,CAAC;;EAEtD,OAAOF,eAAe;AACxB;AAEA;;;;;;;;;AASG;AACG,SAAUzC,sBAAsBA,CACpC/F,OAAwB,EACxB1d,IAAY,EACZ0jB,IAAY,EAGN;EAAA,IAFNtB,OAAA,GAAAxmB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAEI,EAAE;EAEN4nB,wBAAuB,CAAC9F,OAA8B,EAAE1d,IAAI,EAAE0jB,IAAI,EAAEtB,OAAO,CAAC;AAC9E;;AC5WA;;;;;;;;;;;;;;;AAeG;AAMH;;;;;;;;;;;;;;;AAeG;AACa,SAAA3W,OAAOA,CACrB0Q,GAAqB,EACrB5F,oBAA6B;EAE7B4F,GAAG,GAAGyJ,kBAAkB,CAACzJ,GAAG,CAAC;EAC7B,OAAOkF,eAAe,CAAClF,GAAgB,EAAE5F,oBAAoB,CAAC;AAChE;AAEA;;;;;;;;;;;AAWG;AACa,SAAA+P,SAASA,CACvBnK,GAAqB,EACrB5F,oBAA6B;EAE7B,MAAM,IAAIgQ,KAAK,CAAC,gDAAgD,CAAC;AACnE;;AC9DA;;;;AAIG;AAyCH,SAASC,OAAOA,CACdC,SAA6B,EAAAC,KAAA,EACsB;EAAA,IAAnD;IAAEC,kBAAkB,EAAE/oB;EAAG,CAA0B,GAAA8oB,KAAA;EAEnD,MAAM5C,GAAG,GAAG2C,SAAS,CAACG,WAAW,CAAC,KAAK,CAAC,CAAC9B,YAAY,EAAE;EACvD,MAAM+B,YAAY,GAAGJ,SAAS,CAACG,WAAW,CAAC,eAAe,CAAC;EAC3D,MAAME,gBAAgB,GAAGL,SAAS,CAACG,WAAW,CAAC,oBAAoB,CAAC;EAEpE,OAAO,IAAI3D,mBAAmB,CAC5Ba,GAAG,EACH+C,YAAY,EACZC,gBAAgB,EAChBlpB,GAAG,EACHmpB,WAAW,CACZ;AACH;AAEA,SAASC,eAAeA,CAAA;EACtBC,kBAAkB,CAChB,IAAIC,SAAS,CACXvB,YAAY,EACZa,OAAO,EAER,qCAACW,oBAAoB,CAAC,IAAI,CAAC,CAC7B;;EAEDC,eAAe,CAACpoB,IAAI,EAAEgC,OAAO,EAAE,EAAiB,CAAC;;EAEjDomB,eAAe,CAACpoB,IAAI,EAAEgC,OAAO,EAAE,SAAkB,CAAC;AACpD;AAEAgmB,eAAe,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}