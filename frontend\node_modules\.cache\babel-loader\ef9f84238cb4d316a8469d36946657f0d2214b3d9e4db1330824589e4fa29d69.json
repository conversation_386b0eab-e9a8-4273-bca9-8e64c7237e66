{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\components\\\\ColorVariantSelector.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dropdown } from 'react-bootstrap';\nimport { BsChevronDown } from 'react-icons/bs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ColorVariantSelector = ({\n  variants,\n  selectedValue,\n  onSelect,\n  placeholder = \"Select Color Variant\",\n  isDuplicateFunction,\n  groupIndex,\n  variantIndex,\n  disabled = false,\n  showFabricName = false // For EditCutting where we need to show fabric name too\n}) => {\n  _s();\n  var _selectedVariant$fabr;\n  const [isOpen, setIsOpen] = useState(false);\n  const selectedVariant = variants.find(v => v.id === selectedValue);\n  return /*#__PURE__*/_jsxDEV(Dropdown, {\n    show: isOpen && !disabled,\n    onToggle: setIsOpen,\n    children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n      variant: \"outline-secondary\",\n      className: \"w-100 d-flex justify-content-between align-items-center\",\n      style: {\n        textAlign: 'left'\n      },\n      disabled: disabled,\n      children: [selectedVariant ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '20px',\n            height: '20px',\n            backgroundColor: selectedVariant.color,\n            border: '1px solid #ccc',\n            borderRadius: '4px',\n            marginRight: '8px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [showFabricName && ((_selectedVariant$fabr = selectedVariant.fabric_definition_data) === null || _selectedVariant$fabr === void 0 ? void 0 : _selectedVariant$fabr.fabric_name) && `${selectedVariant.fabric_definition_data.fabric_name} - `, selectedVariant.color_name || selectedVariant.color, \" - \", selectedVariant.available_yard, \" yards available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-muted\",\n        children: placeholder\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(BsChevronDown, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n      className: \"w-100\",\n      style: {\n        maxHeight: '300px',\n        overflowY: 'auto'\n      },\n      children: variants.length === 0 ? /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n        disabled: true,\n        children: \"No variants available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this) : variants.map(variant => {\n        var _variant$fabric_defin;\n        const isAlreadySelected = isDuplicateFunction ? isDuplicateFunction(groupIndex, variant.id, variantIndex) : false;\n        return /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n          onClick: () => {\n            if (!isAlreadySelected) {\n              onSelect(variant.id);\n              setIsOpen(false);\n            }\n          },\n          disabled: isAlreadySelected,\n          className: isAlreadySelected ? 'text-muted' : '',\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '20px',\n                height: '20px',\n                backgroundColor: variant.color,\n                border: '1px solid #ccc',\n                borderRadius: '4px',\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fw-bold\",\n                children: [showFabricName && ((_variant$fabric_defin = variant.fabric_definition_data) === null || _variant$fabric_defin === void 0 ? void 0 : _variant$fabric_defin.fabric_name) && `${variant.fabric_definition_data.fabric_name} - `, variant.color_name || variant.color]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: [variant.available_yard, \" yards available\", isAlreadySelected ? ' (Already Selected)' : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 17\n          }, this)\n        }, variant.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(ColorVariantSelector, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = ColorVariantSelector;\nexport default ColorVariantSelector;\nvar _c;\n$RefreshReg$(_c, \"ColorVariantSelector\");", "map": {"version": 3, "names": ["React", "useState", "Dropdown", "BsChevronDown", "jsxDEV", "_jsxDEV", "ColorVariantSelector", "variants", "selected<PERSON><PERSON><PERSON>", "onSelect", "placeholder", "isDuplicateFunction", "groupIndex", "variantIndex", "disabled", "showFabricName", "_s", "_selectedVariant$fabr", "isOpen", "setIsOpen", "<PERSON><PERSON><PERSON><PERSON>", "find", "v", "id", "show", "onToggle", "children", "Toggle", "variant", "className", "style", "textAlign", "width", "height", "backgroundColor", "color", "border", "borderRadius", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fabric_definition_data", "fabric_name", "color_name", "available_yard", "<PERSON><PERSON>", "maxHeight", "overflowY", "length", "<PERSON><PERSON>", "map", "_variant$fabric_defin", "isAlreadySelected", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/components/ColorVariantSelector.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Dropdown } from 'react-bootstrap';\nimport { BsChevronDown } from 'react-icons/bs';\n\nconst ColorVariantSelector = ({ \n  variants, \n  selectedValue, \n  onSelect, \n  placeholder = \"Select Color Variant\",\n  isDuplicateFunction,\n  groupIndex,\n  variantIndex,\n  disabled = false,\n  showFabricName = false // For EditCutting where we need to show fabric name too\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const selectedVariant = variants.find(v => v.id === selectedValue);\n\n  return (\n    <Dropdown show={isOpen && !disabled} onToggle={setIsOpen}>\n      <Dropdown.Toggle \n        variant=\"outline-secondary\" \n        className=\"w-100 d-flex justify-content-between align-items-center\"\n        style={{ textAlign: 'left' }}\n        disabled={disabled}\n      >\n        {selectedVariant ? (\n          <div className=\"d-flex align-items-center\">\n            <div\n              style={{\n                width: '20px',\n                height: '20px',\n                backgroundColor: selectedVariant.color,\n                border: '1px solid #ccc',\n                borderRadius: '4px',\n                marginRight: '8px'\n              }}\n            />\n            <span>\n              {showFabricName && selectedVariant.fabric_definition_data?.fabric_name && \n                `${selectedVariant.fabric_definition_data.fabric_name} - `\n              }\n              {selectedVariant.color_name || selectedVariant.color} - {selectedVariant.available_yard} yards available\n            </span>\n          </div>\n        ) : (\n          <span className=\"text-muted\">{placeholder}</span>\n        )}\n        <BsChevronDown />\n      </Dropdown.Toggle>\n\n      <Dropdown.Menu className=\"w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n        {variants.length === 0 ? (\n          <Dropdown.Item disabled>No variants available</Dropdown.Item>\n        ) : (\n          variants.map((variant) => {\n            const isAlreadySelected = isDuplicateFunction ? isDuplicateFunction(groupIndex, variant.id, variantIndex) : false;\n            return (\n              <Dropdown.Item\n                key={variant.id}\n                onClick={() => {\n                  if (!isAlreadySelected) {\n                    onSelect(variant.id);\n                    setIsOpen(false);\n                  }\n                }}\n                disabled={isAlreadySelected}\n                className={isAlreadySelected ? 'text-muted' : ''}\n              >\n                <div className=\"d-flex align-items-center\">\n                  <div\n                    style={{\n                      width: '20px',\n                      height: '20px',\n                      backgroundColor: variant.color,\n                      border: '1px solid #ccc',\n                      borderRadius: '4px',\n                      marginRight: '8px'\n                    }}\n                  />\n                  <div>\n                    <div className=\"fw-bold\">\n                      {showFabricName && variant.fabric_definition_data?.fabric_name && \n                        `${variant.fabric_definition_data.fabric_name} - `\n                      }\n                      {variant.color_name || variant.color}\n                    </div>\n                    <small className=\"text-muted\">\n                      {variant.available_yard} yards available\n                      {isAlreadySelected ? ' (Already Selected)' : ''}\n                    </small>\n                  </div>\n                </div>\n              </Dropdown.Item>\n            );\n          })\n        )}\n      </Dropdown.Menu>\n    </Dropdown>\n  );\n};\n\nexport default ColorVariantSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,oBAAoB,GAAGA,CAAC;EAC5BC,QAAQ;EACRC,aAAa;EACbC,QAAQ;EACRC,WAAW,GAAG,sBAAsB;EACpCC,mBAAmB;EACnBC,UAAU;EACVC,YAAY;EACZC,QAAQ,GAAG,KAAK;EAChBC,cAAc,GAAG,KAAK,CAAC;AACzB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMmB,eAAe,GAAGb,QAAQ,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKf,aAAa,CAAC;EAElE,oBACEH,OAAA,CAACH,QAAQ;IAACsB,IAAI,EAAEN,MAAM,IAAI,CAACJ,QAAS;IAACW,QAAQ,EAAEN,SAAU;IAAAO,QAAA,gBACvDrB,OAAA,CAACH,QAAQ,CAACyB,MAAM;MACdC,OAAO,EAAC,mBAAmB;MAC3BC,SAAS,EAAC,yDAAyD;MACnEC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAC7BjB,QAAQ,EAAEA,QAAS;MAAAY,QAAA,GAElBN,eAAe,gBACdf,OAAA;QAAKwB,SAAS,EAAC,2BAA2B;QAAAH,QAAA,gBACxCrB,OAAA;UACEyB,KAAK,EAAE;YACLE,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,eAAe,EAAEd,eAAe,CAACe,KAAK;YACtCC,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBC,WAAW,EAAE;UACf;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFrC,OAAA;UAAAqB,QAAA,GACGX,cAAc,MAAAE,qBAAA,GAAIG,eAAe,CAACuB,sBAAsB,cAAA1B,qBAAA,uBAAtCA,qBAAA,CAAwC2B,WAAW,KACpE,GAAGxB,eAAe,CAACuB,sBAAsB,CAACC,WAAW,KAAK,EAE3DxB,eAAe,CAACyB,UAAU,IAAIzB,eAAe,CAACe,KAAK,EAAC,KAAG,EAACf,eAAe,CAAC0B,cAAc,EAAC,kBAC1F;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAENrC,OAAA;QAAMwB,SAAS,EAAC,YAAY;QAAAH,QAAA,EAAEhB;MAAW;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACjD,eACDrC,OAAA,CAACF,aAAa;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAElBrC,OAAA,CAACH,QAAQ,CAAC6C,IAAI;MAAClB,SAAS,EAAC,OAAO;MAACC,KAAK,EAAE;QAAEkB,SAAS,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAvB,QAAA,EAC/EnB,QAAQ,CAAC2C,MAAM,KAAK,CAAC,gBACpB7C,OAAA,CAACH,QAAQ,CAACiD,IAAI;QAACrC,QAAQ;QAAAY,QAAA,EAAC;MAAqB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,GAE7DnC,QAAQ,CAAC6C,GAAG,CAAExB,OAAO,IAAK;QAAA,IAAAyB,qBAAA;QACxB,MAAMC,iBAAiB,GAAG3C,mBAAmB,GAAGA,mBAAmB,CAACC,UAAU,EAAEgB,OAAO,CAACL,EAAE,EAAEV,YAAY,CAAC,GAAG,KAAK;QACjH,oBACER,OAAA,CAACH,QAAQ,CAACiD,IAAI;UAEZI,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI,CAACD,iBAAiB,EAAE;cACtB7C,QAAQ,CAACmB,OAAO,CAACL,EAAE,CAAC;cACpBJ,SAAS,CAAC,KAAK,CAAC;YAClB;UACF,CAAE;UACFL,QAAQ,EAAEwC,iBAAkB;UAC5BzB,SAAS,EAAEyB,iBAAiB,GAAG,YAAY,GAAG,EAAG;UAAA5B,QAAA,eAEjDrB,OAAA;YAAKwB,SAAS,EAAC,2BAA2B;YAAAH,QAAA,gBACxCrB,OAAA;cACEyB,KAAK,EAAE;gBACLE,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdC,eAAe,EAAEN,OAAO,CAACO,KAAK;gBAC9BC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBC,WAAW,EAAE;cACf;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrC,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAKwB,SAAS,EAAC,SAAS;gBAAAH,QAAA,GACrBX,cAAc,MAAAsC,qBAAA,GAAIzB,OAAO,CAACe,sBAAsB,cAAAU,qBAAA,uBAA9BA,qBAAA,CAAgCT,WAAW,KAC5D,GAAGhB,OAAO,CAACe,sBAAsB,CAACC,WAAW,KAAK,EAEnDhB,OAAO,CAACiB,UAAU,IAAIjB,OAAO,CAACO,KAAK;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNrC,OAAA;gBAAOwB,SAAS,EAAC,YAAY;gBAAAH,QAAA,GAC1BE,OAAO,CAACkB,cAAc,EAAC,kBACxB,EAACQ,iBAAiB,GAAG,qBAAqB,GAAG,EAAE;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjCDd,OAAO,CAACL,EAAE;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCF,CAAC;MAEpB,CAAC;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEf,CAAC;AAAC1B,EAAA,CAhGIV,oBAAoB;AAAAkD,EAAA,GAApBlD,oBAAoB;AAkG1B,eAAeA,oBAAoB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}