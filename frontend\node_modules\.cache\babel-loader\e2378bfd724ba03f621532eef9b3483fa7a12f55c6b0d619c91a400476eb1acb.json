{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A container for all of the Logger instances\n */\nconst instances = [];\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n  LogLevel[LogLevel[\"VERBOSE\"] = 1] = \"VERBOSE\";\n  LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n  LogLevel[LogLevel[\"WARN\"] = 3] = \"WARN\";\n  LogLevel[LogLevel[\"ERROR\"] = 4] = \"ERROR\";\n  LogLevel[LogLevel[\"SILENT\"] = 5] = \"SILENT\";\n})(LogLevel || (LogLevel = {}));\nconst levelStringToEnum = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n/**\n * The default log level\n */\nconst defaultLogLevel = LogLevel.INFO;\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler = function (instance, logType) {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType];\n  if (method) {\n    for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      args[_key - 2] = arguments[_key];\n    }\n    console[method](`[${now}]  ${instance.name}:`, ...args);\n  } else {\n    throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);\n  }\n};\nclass Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(name) {\n    this.name = name;\n    /**\n     * The log level of the given Logger instance.\n     */\n    this._logLevel = defaultLogLevel;\n    /**\n     * The main (internal) log handler for the Logger instance.\n     * Can be set to a new function in internal package code but not by user.\n     */\n    this._logHandler = defaultLogHandler;\n    /**\n     * The optional, additional, user-defined log handler for the Logger instance.\n     */\n    this._userLogHandler = null;\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n  get logLevel() {\n    return this._logLevel;\n  }\n  set logLevel(val) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val) {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n  get logHandler() {\n    return this._logHandler;\n  }\n  set logHandler(val) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n  get userLogHandler() {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val) {\n    this._userLogHandler = val;\n  }\n  /**\n   * The functions below are all based on the `console` interface\n   */\n  debug() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    this._userLogHandler && this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn() {\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error() {\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\nfunction setLogLevel(level) {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\nfunction setUserLogHandler(logCallback, options) {\n  for (const instance of instances) {\n    let customLogLevel = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = function (instance, level) {\n        for (var _len7 = arguments.length, args = new Array(_len7 > 2 ? _len7 - 2 : 0), _key7 = 2; _key7 < _len7; _key7++) {\n          args[_key7 - 2] = arguments[_key7];\n        }\n        const message = args.map(arg => {\n          if (arg == null) {\n            return null;\n          } else if (typeof arg === 'string') {\n            return arg;\n          } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n            return arg.toString();\n          } else if (arg instanceof Error) {\n            return arg.message;\n          } else {\n            try {\n              return JSON.stringify(arg);\n            } catch (ignored) {\n              return null;\n            }\n          }\n        }).filter(arg => arg).join(' ');\n        if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase(),\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\nexport { LogLevel, Logger, setLogLevel, setUserLogHandler };", "map": {"version": 3, "names": ["instances", "LogLevel", "levelStringToEnum", "DEBUG", "VERBOSE", "INFO", "WARN", "ERROR", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "logLevel", "now", "Date", "toISOString", "method", "_len", "arguments", "length", "args", "Array", "_key", "console", "name", "Error", "<PERSON><PERSON>", "constructor", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "push", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "debug", "_len2", "_key2", "log", "_len3", "_key3", "info", "_len4", "_key4", "warn", "_len5", "_key5", "error", "_len6", "_key6", "level", "for<PERSON>ach", "inst", "setUserLogHandler", "logCallback", "options", "customLogLevel", "_len7", "_key7", "message", "map", "arg", "toString", "JSON", "stringify", "ignored", "filter", "join", "toLowerCase", "type"], "sources": ["D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\node_modules\\@firebase\\logger\\src\\logger.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;AAeG;AAuBH;;AAEG;AACI,MAAMA,SAAS,GAAa,EAAE;AAErC;;;;;;;;;;AAUG;IACSC,QAAA;AAAZ,WAAYA,QAAQ;EAClBA,QAAA,CAAAA,QAAA,wBAAK;EACLA,QAAA,CAAAA,QAAA,4BAAO;EACPA,QAAA,CAAAA,QAAA,sBAAI;EACJA,QAAA,CAAAA,QAAA,sBAAI;EACJA,QAAA,CAAAA,QAAA,wBAAK;EACLA,QAAA,CAAAA,QAAA,0BAAM;AACR,CAAC,EAPWA,QAAQ,KAARA,QAAQ,GAOnB;AAED,MAAMC,iBAAiB,GAA0C;EAC/D,OAAO,EAAED,QAAQ,CAACE,KAAK;EACvB,SAAS,EAAEF,QAAQ,CAACG,OAAO;EAC3B,MAAM,EAAEH,QAAQ,CAACI,IAAI;EACrB,MAAM,EAAEJ,QAAQ,CAACK,IAAI;EACrB,OAAO,EAAEL,QAAQ,CAACM,KAAK;EACvB,QAAQ,EAAEN,QAAQ,CAACO;CACpB;AAED;;AAEG;AACH,MAAMC,eAAe,GAAaR,QAAQ,CAACI,IAAI;AAa/C;;;;;AAKG;AACH,MAAMK,aAAa,GAAG;EACpB,CAACT,QAAQ,CAACE,KAAK,GAAG,KAAK;EACvB,CAACF,QAAQ,CAACG,OAAO,GAAG,KAAK;EACzB,CAACH,QAAQ,CAACI,IAAI,GAAG,MAAM;EACvB,CAACJ,QAAQ,CAACK,IAAI,GAAG,MAAM;EACvB,CAACL,QAAQ,CAACM,KAAK,GAAG;CACnB;AAED;;;;AAIG;AACH,MAAMI,iBAAiB,GAAe,SAAAA,CAACC,QAAQ,EAAEC,OAAO,EAAmB;EACzE,IAAIA,OAAO,GAAGD,QAAQ,CAACE,QAAQ,EAAE;IAC/B;;EAEF,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACpC,MAAMC,MAAM,GAAGR,aAAa,CAACG,OAAqC,CAAC;EACnE,IAAIK,MAAM,EAAE;IAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAN+CC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAO7DC,OAAO,CAACP,MAA2C,CAAC,CAClD,IAAIH,GAAG,MAAMH,QAAQ,CAACc,IAAI,GAAG,EAC7B,GAAGJ,IAAI,CACR;GACF,MAAM;IACL,MAAM,IAAIK,KAAK,CACb,8DAA8Dd,OAAO,GAAG,CACzE;;AAEL,CAAC;MAEYe,MAAM;EACjB;;;;;AAKG;EACHC,YAAmBH,IAAY;IAAZ,IAAI,CAAAA,IAAA,GAAJA,IAAI;IAOvB;;AAEG;IACK,IAAS,CAAAI,SAAA,GAAGrB,eAAe;IAkBnC;;;AAGG;IACK,IAAW,CAAAsB,WAAA,GAAepB,iBAAiB;IAWnD;;AAEG;IACK,IAAe,CAAAqB,eAAA,GAAsB,IAAI;IA7C/C;;AAEG;IACHhC,SAAS,CAACiC,IAAI,CAAC,IAAI,CAAC;;EAQtB,IAAInB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACgB,SAAS;;EAGvB,IAAIhB,QAAQA,CAACoB,GAAa;IACxB,IAAI,EAAEA,GAAG,IAAIjC,QAAQ,CAAC,EAAE;MACtB,MAAM,IAAIkC,SAAS,CAAC,kBAAkBD,GAAG,4BAA4B,CAAC;;IAExE,IAAI,CAACJ,SAAS,GAAGI,GAAG;;;EAItBE,WAAWA,CAACF,GAA8B;IACxC,IAAI,CAACJ,SAAS,GAAG,OAAOI,GAAG,KAAK,QAAQ,GAAGhC,iBAAiB,CAACgC,GAAG,CAAC,GAAGA,GAAG;;EAQzE,IAAIG,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACN,WAAW;;EAEzB,IAAIM,UAAUA,CAACH,GAAe;IAC5B,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;MAC7B,MAAM,IAAIC,SAAS,CAAC,mDAAmD,CAAC;;IAE1E,IAAI,CAACJ,WAAW,GAAGG,GAAG;;EAOxB,IAAII,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACN,eAAe;;EAE7B,IAAIM,cAAcA,CAACJ,GAAsB;IACvC,IAAI,CAACF,eAAe,GAAGE,GAAG;;EAG5B;;AAEG;EAEHK,KAAKA,CAAA,EAAmB;IAAA,SAAAC,KAAA,GAAApB,SAAA,CAAAC,MAAA,EAAfC,IAAe,OAAAC,KAAA,CAAAiB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAfnB,IAAe,CAAAmB,KAAA,IAAArB,SAAA,CAAAqB,KAAA;IAAA;IACtB,IAAI,CAACT,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE/B,QAAQ,CAACE,KAAK,EAAE,GAAGmB,IAAI,CAAC;IAC3E,IAAI,CAACS,WAAW,CAAC,IAAI,EAAE9B,QAAQ,CAACE,KAAK,EAAE,GAAGmB,IAAI,CAAC;;EAEjDoB,GAAGA,CAAA,EAAmB;IAAA,SAAAC,KAAA,GAAAvB,SAAA,CAAAC,MAAA,EAAfC,IAAe,OAAAC,KAAA,CAAAoB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAftB,IAAe,CAAAsB,KAAA,IAAAxB,SAAA,CAAAwB,KAAA;IAAA;IACpB,IAAI,CAACZ,eAAe,IAClB,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE/B,QAAQ,CAACG,OAAO,EAAE,GAAGkB,IAAI,CAAC;IACvD,IAAI,CAACS,WAAW,CAAC,IAAI,EAAE9B,QAAQ,CAACG,OAAO,EAAE,GAAGkB,IAAI,CAAC;;EAEnDuB,IAAIA,CAAA,EAAmB;IAAA,SAAAC,KAAA,GAAA1B,SAAA,CAAAC,MAAA,EAAfC,IAAe,OAAAC,KAAA,CAAAuB,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAfzB,IAAe,CAAAyB,KAAA,IAAA3B,SAAA,CAAA2B,KAAA;IAAA;IACrB,IAAI,CAACf,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE/B,QAAQ,CAACI,IAAI,EAAE,GAAGiB,IAAI,CAAC;IAC1E,IAAI,CAACS,WAAW,CAAC,IAAI,EAAE9B,QAAQ,CAACI,IAAI,EAAE,GAAGiB,IAAI,CAAC;;EAEhD0B,IAAIA,CAAA,EAAmB;IAAA,SAAAC,KAAA,GAAA7B,SAAA,CAAAC,MAAA,EAAfC,IAAe,OAAAC,KAAA,CAAA0B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAf5B,IAAe,CAAA4B,KAAA,IAAA9B,SAAA,CAAA8B,KAAA;IAAA;IACrB,IAAI,CAAClB,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE/B,QAAQ,CAACK,IAAI,EAAE,GAAGgB,IAAI,CAAC;IAC1E,IAAI,CAACS,WAAW,CAAC,IAAI,EAAE9B,QAAQ,CAACK,IAAI,EAAE,GAAGgB,IAAI,CAAC;;EAEhD6B,KAAKA,CAAA,EAAmB;IAAA,SAAAC,KAAA,GAAAhC,SAAA,CAAAC,MAAA,EAAfC,IAAe,OAAAC,KAAA,CAAA6B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAf/B,IAAe,CAAA+B,KAAA,IAAAjC,SAAA,CAAAiC,KAAA;IAAA;IACtB,IAAI,CAACrB,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE/B,QAAQ,CAACM,KAAK,EAAE,GAAGe,IAAI,CAAC;IAC3E,IAAI,CAACS,WAAW,CAAC,IAAI,EAAE9B,QAAQ,CAACM,KAAK,EAAE,GAAGe,IAAI,CAAC;;AAElD;AAEK,SAAUc,WAAWA,CAACkB,KAAgC;EAC1DtD,SAAS,CAACuD,OAAO,CAACC,IAAI,IAAG;IACvBA,IAAI,CAACpB,WAAW,CAACkB,KAAK,CAAC;EACzB,CAAC,CAAC;AACJ;AAEgB,SAAAG,iBAAiBA,CAC/BC,WAA+B,EAC/BC,OAAoB;EAEpB,KAAK,MAAM/C,QAAQ,IAAIZ,SAAS,EAAE;IAChC,IAAI4D,cAAc,GAAoB,IAAI;IAC1C,IAAID,OAAO,IAAIA,OAAO,CAACL,KAAK,EAAE;MAC5BM,cAAc,GAAG1D,iBAAiB,CAACyD,OAAO,CAACL,KAAK,CAAC;;IAEnD,IAAII,WAAW,KAAK,IAAI,EAAE;MACxB9C,QAAQ,CAAC0B,cAAc,GAAG,IAAI;KAC/B,MAAM;MACL1B,QAAQ,CAAC0B,cAAc,GAAG,UACxB1B,QAAgB,EAChB0C,KAAe,EAEb;QAAA,SAAAO,KAAA,GAAAzC,SAAA,CAAAC,MAAA,EADCC,IAAe,OAAAC,KAAA,CAAAsC,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAfxC,IAAe,CAAAwC,KAAA,QAAA1C,SAAA,CAAA0C,KAAA;QAAA;QAElB,MAAMC,OAAO,GAAGzC,IAAI,CACjB0C,GAAG,CAACC,GAAG,IAAG;UACT,IAAIA,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,IAAI;WACZ,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YAClC,OAAOA,GAAG;WACX,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;YAC9D,OAAOA,GAAG,CAACC,QAAQ,EAAE;WACtB,MAAM,IAAID,GAAG,YAAYtC,KAAK,EAAE;YAC/B,OAAOsC,GAAG,CAACF,OAAO;WACnB,MAAM;YACL,IAAI;cACF,OAAOI,IAAI,CAACC,SAAS,CAACH,GAAG,CAAC;aAC3B,CAAC,OAAOI,OAAO,EAAE;cAChB,OAAO,IAAI;;;QAGjB,CAAC,CAAC,CACDC,MAAM,CAACL,GAAG,IAAIA,GAAG,CAAC,CAClBM,IAAI,CAAC,GAAG,CAAC;QACZ,IAAIjB,KAAK,KAAKM,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAIhD,QAAQ,CAACE,QAAQ,CAAC,EAAE;UAClD4C,WAAW,CAAC;YACVJ,KAAK,EAAErD,QAAQ,CAACqD,KAAK,CAAC,CAACkB,WAAW,EAAoB;YACtDT,OAAO;YACPzC,IAAI;YACJmD,IAAI,EAAE7D,QAAQ,CAACc;UAChB,EAAC;;MAEN,CAAC;;;AAGP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}