{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import axios from\"axios\";import Select from\"react-select\";import{FaPlus,FaTrash,FaSave}from\"react-icons/fa\";import{<PERSON>,Col,<PERSON>,<PERSON><PERSON>,<PERSON>,Alert,Spinner}from'react-bootstrap';import'bootstrap/dist/css/bootstrap.min.css';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import{useParams,useNavigate}from\"react-router-dom\";import{getUserRole,hasRole}from'../utils/auth';// Color presets for fabric variants\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const COLOR_PRESETS=[{color:\"#000000\",name:\"Black\"},{color:\"#FFFFFF\",name:\"White\"},{color:\"#FF0000\",name:\"Red\"},{color:\"#0000FF\",name:\"<PERSON>\"},{color:\"#FFFF00\",name:\"Yellow\"},{color:\"#00FF00\",name:\"Green\"},{color:\"#FFA500\",name:\"Orange\"},{color:\"#800080\",name:\"Purple\"},{color:\"#FFC0CB\",name:\"Pink\"},{color:\"#A52A2A\",name:\"Brown\"},{color:\"#808080\",name:\"Gray\"},{color:\"#C0C0C0\",name:\"Silver\"}];const EditFabric=()=>{const{id}=useParams();const navigate=useNavigate();// State variables\nconst[fabricName,setFabricName]=useState(\"\");const[selectedSupplier,setSelectedSupplier]=useState(null);const[dateAdded,setDateAdded]=useState(\"\");const[variants,setVariants]=useState([]);const[suppliers,setSuppliers]=useState([]);const[message,setMessage]=useState(\"\");const[error,setError]=useState(\"\");const[isSubmitting,setIsSubmitting]=useState(false);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[loading,setLoading]=useState(true);const[isInventoryManager,setIsInventoryManager]=useState(hasRole('Inventory Manager'));// Check if user is authorized to access this page\nuseEffect(()=>{if(!isInventoryManager){setError(\"Only Inventory Managers can edit fabrics.\");// Redirect to fabric list after a short delay\nsetTimeout(()=>{navigate('/viewfabric');},2000);}},[isInventoryManager,navigate]);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Fetch suppliers for dropdown\nuseEffect(()=>{const fetchSuppliers=async()=>{try{const response=await axios.get(\"http://localhost:8000/api/suppliers/\");const supplierOptions=response.data.map(supplier=>({value:supplier.supplier_id,label:supplier.name}));setSuppliers(supplierOptions);}catch(error){console.error(\"Error fetching suppliers:\",error);setError(\"Error loading suppliers. Please try again.\");}};fetchSuppliers();},[]);// Fetch fabric data\nuseEffect(()=>{const fetchFabricData=async()=>{setLoading(true);try{// Fetch fabric definition\nconst fabricResponse=await axios.get(`http://localhost:8000/api/fabric-definitions/${id}/`);const fabricData=fabricResponse.data;setFabricName(fabricData.fabric_name);setDateAdded(fabricData.date_added);// Set selected supplier\nconst supplierOption=suppliers.find(s=>s.value===fabricData.supplier);if(supplierOption){setSelectedSupplier(supplierOption);}// Fetch fabric variants\nconst variantsResponse=await axios.get(`http://localhost:8000/api/fabric-definitions/${id}/variants/`);const variantsData=variantsResponse.data;// Format variants for state\nconst formattedVariants=variantsData.map(variant=>({id:variant.id,color:variant.color,colorName:variant.color_name,totalYard:variant.total_yard.toString(),originalTotalYard:variant.total_yard,availableYard:variant.available_yard!==null?variant.available_yard:variant.total_yard,originalAvailableYard:variant.available_yard!==null?variant.available_yard:variant.total_yard,pricePerYard:variant.price_per_yard.toString()}));setVariants(formattedVariants);setLoading(false);}catch(error){console.error(\"Error fetching fabric data:\",error);setError(\"Error loading fabric data. Please try again.\");setLoading(false);}};if(suppliers.length>0){fetchFabricData();}},[id,suppliers]);// Handle adding a variant\nconst handleAddVariant=()=>{setVariants([...variants,{color:\"#000000\",colorName:\"Black\",totalYard:\"\",originalTotalYard:0,availableYard:0,originalAvailableYard:0,pricePerYard:\"\"}]);};// Handle removing a variant\nconst handleRemoveVariant=index=>{const updated=variants.filter((_,i)=>i!==index);setVariants(updated);};// Handle variant input changes\nconst handleVariantChange=(index,field,value)=>{const updated=[...variants];const variant=updated[index];// Store the previous value before updating\nconst previousValue=variant[field];// Update the field with the new value\nvariant[field]=value;// If color is changed, update the color name\nif(field===\"color\"){const colorPreset=COLOR_PRESETS.find(preset=>preset.color===value);variant.colorName=colorPreset?colorPreset.name:\"\";}// If total yard is changed, update available yard proportionally\nif(field===\"totalYard\"&&value!==\"\"){const newTotalYard=parseFloat(value);const originalTotalYard=variant.originalTotalYard;const originalAvailableYard=variant.originalAvailableYard;// Calculate how much of the fabric has been used\nconst usedYard=originalTotalYard-originalAvailableYard;// Validate that new total yard is not less than what's already been used\nif(newTotalYard<usedYard){setError(`Cannot reduce total yard below ${usedYard} yards as that amount has already been used in cutting records.`);// Revert to previous value\nvariant.totalYard=previousValue;return;}else{// Clear error if it was previously set\nsetError(\"\");}// Update available yard based on the change in total yard\nconst newAvailableYard=newTotalYard-usedYard;variant.availableYard=newAvailableYard;}setVariants(updated);};// Select a preset color\nconst selectPresetColor=(index,preset)=>{handleVariantChange(index,\"color\",preset.color);handleVariantChange(index,\"colorName\",preset.name);};// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();if(!fabricName||!selectedSupplier||!dateAdded){setError(\"Please fill in all required fields.\");return;}if(variants.length===0){setError(\"Please add at least one fabric variant.\");return;}for(const variant of variants){if(!variant.color||!variant.colorName||!variant.totalYard||!variant.pricePerYard){setError(\"Please fill in all variant details.\");return;}// For existing variants, check if total yard is not reduced below what's been used\nif(variant.id){const newTotalYard=parseFloat(variant.totalYard);const usedYard=variant.originalTotalYard-variant.originalAvailableYard;if(newTotalYard<usedYard){setError(`Cannot reduce total yard below ${usedYard.toFixed(2)} yards for ${variant.colorName} as that amount has already been used in cutting records.`);return;}}}setIsSubmitting(true);setError(\"\");setMessage(\"\");try{// Update fabric definition\nawait axios.put(`http://localhost:8000/api/fabric-definitions/${id}/`,{fabric_name:fabricName,supplier:selectedSupplier.value,date_added:dateAdded});// Update existing variants and add new ones\nfor(let variant of variants){if(variant.id){// Update existing variant\nawait axios.put(`http://localhost:8000/api/fabric-variants/${variant.id}/`,{fabric_definition:id,color:variant.color,color_name:variant.colorName,total_yard:parseFloat(variant.totalYard)||0,available_yard:parseFloat(variant.availableYard)||0,price_per_yard:parseFloat(variant.pricePerYard)||0});}else{// Add new variant\nawait axios.post(\"http://localhost:8000/api/fabric-variants/\",{fabric_definition:id,color:variant.color,color_name:variant.colorName,total_yard:parseFloat(variant.totalYard)||0,available_yard:parseFloat(variant.totalYard)||0,// For new variants, available_yard equals total_yard\nprice_per_yard:parseFloat(variant.pricePerYard)||0});}}setMessage(\"✅ Fabric updated successfully!\");// Navigate back to fabric list after a short delay\nsetTimeout(()=>{navigate('/viewfabric');},2000);}catch(error){console.error(\"Error updating fabric:\",error);setError(\"Error updating fabric. Please try again.\");}finally{setIsSubmitting(false);}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:\"Edit Fabric\"}),message&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",children:message}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center my-5\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",variant:\"primary\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:\"Loading fabric data...\"})]}):/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[!isInventoryManager&&/*#__PURE__*/_jsxs(Alert,{variant:\"warning\",className:\"mb-3\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Access Denied:\"}),\" Only Inventory Managers can edit fabrics.\"]}),/*#__PURE__*/_jsxs(\"fieldset\",{disabled:!isInventoryManager,children:[/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric Name\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:fabricName,onChange:e=>setFabricName(e.target.value),required:true,placeholder:\"Enter fabric name\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Supplier\"})}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading suppliers...\"})]}):/*#__PURE__*/_jsx(Select,{options:suppliers,value:selectedSupplier,onChange:setSelectedSupplier,placeholder:\"Select a supplier...\",isSearchable:true})]})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Date Added\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:dateAdded,onChange:e=>setDateAdded(e.target.value),required:true})]})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"mt-4 mb-3 border-bottom pb-2\",children:\"Fabric Variants\"}),variants.map((variant,index)=>/*#__PURE__*/_jsxs(Card,{className:\"mb-3 border\",style:{borderLeft:`5px solid ${variant.color}`,borderRadius:\"8px\"},children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"d-flex justify-content-between align-items-center bg-light\",children:[/*#__PURE__*/_jsxs(\"h5\",{className:\"mb-0\",children:[\"Variant #\",index+1]}),variants.length>1&&/*#__PURE__*/_jsxs(Button,{variant:\"outline-danger\",size:\"sm\",onClick:()=>handleRemoveVariant(index),children:[/*#__PURE__*/_jsx(FaTrash,{className:\"me-1\"}),\" Remove\"]})]}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Color\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mb-2\",children:[/*#__PURE__*/_jsx(Form.Control,{type:\"color\",value:variant.color,onChange:e=>handleVariantChange(index,\"color\",e.target.value),className:\"me-2\",style:{width:'38px',height:'38px'}}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:\"e.g., Black Line, Black Circle, Navy Stripe\",value:variant.colorName,onChange:e=>handleVariantChange(index,\"colorName\",e.target.value)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"color-presets d-flex flex-wrap gap-1 mt-1\",children:COLOR_PRESETS.map((preset,presetIndex)=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>selectPresetColor(index,preset),style:{width:'20px',height:'20px',backgroundColor:preset.color,cursor:'pointer',border:'1px solid #ccc',borderRadius:'3px'},title:preset.name},presetIndex))})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Total Yard\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:variant.totalYard,onChange:e=>handleVariantChange(index,\"totalYard\",e.target.value),placeholder:\"Enter total yard\"}),variant.id&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2 small\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-muted\",children:\"Available: \"}),/*#__PURE__*/_jsxs(\"span\",{className:parseFloat(variant.availableYard)<10?'text-danger fw-bold':'text-success',children:[parseFloat(variant.availableYard).toFixed(2),\" yards\"]}),variant.originalTotalYard!==parseFloat(variant.totalYard)&&/*#__PURE__*/_jsx(\"div\",{className:\"text-info mt-1\",children:/*#__PURE__*/_jsx(\"small\",{children:parseFloat(variant.totalYard)>variant.originalTotalYard?`Increasing total yard will add ${(parseFloat(variant.totalYard)-variant.originalTotalYard).toFixed(2)} yards to available stock.`:`Decreasing total yard will reduce available stock by ${(variant.originalTotalYard-parseFloat(variant.totalYard)).toFixed(2)} yards.`})})]})]})}),/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Price per Yard (Rs.)\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0\",value:variant.pricePerYard,onChange:e=>handleVariantChange(index,\"pricePerYard\",e.target.value),placeholder:\"Enter price per yard\"})]})})]})})]},index)),/*#__PURE__*/_jsx(\"div\",{className:\"d-flex justify-content-center mb-4\",children:/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",onClick:handleAddVariant,className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(FaPlus,{className:\"me-2\"}),\" Add Another Variant\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-center mt-4\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",className:\"me-2 px-4\",onClick:()=>navigate('/viewfabric'),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"primary\",disabled:isSubmitting||!isInventoryManager,className:\"px-4\",children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Updating...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FaSave,{className:\"me-2\"}),\" Save Changes\"]})})]})]})]})})})]})]});};export default EditFabric;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Select", "FaPlus", "FaTrash", "FaSave", "Row", "Col", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "Spinner", "RoleBasedNavBar", "useParams", "useNavigate", "getUserRole", "hasRole", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "COLOR_PRESETS", "color", "name", "EditFabric", "id", "navigate", "fabricName", "setFabricName", "selectedSupplier", "setSelectedSupplier", "dateAdded", "setDateAdded", "variants", "setVariants", "suppliers", "setSuppliers", "message", "setMessage", "error", "setError", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "loading", "setLoading", "isInventoryManager", "setIsInventoryManager", "setTimeout", "handleResize", "addEventListener", "removeEventListener", "fetchSuppliers", "response", "get", "supplierOptions", "data", "map", "supplier", "value", "supplier_id", "label", "console", "fetchFabricData", "fabricResponse", "fabricData", "fabric_name", "date_added", "supplierOption", "find", "s", "variantsResponse", "variantsData", "formattedVariants", "variant", "colorName", "color_name", "totalYard", "total_yard", "toString", "originalTotalYard", "availableYard", "available_yard", "originalAvailableYard", "pricePerYard", "price_per_yard", "length", "handleAddVariant", "handleRemoveVariant", "index", "updated", "filter", "_", "i", "handleVariantChange", "field", "previousValue", "colorPreset", "preset", "newTotalYard", "parseFloat", "usedYard", "newAvailableYard", "selectPresetColor", "handleSubmit", "e", "preventDefault", "toFixed", "put", "fabric_definition", "post", "children", "style", "marginLeft", "width", "transition", "padding", "className", "animation", "role", "backgroundColor", "borderRadius", "Body", "onSubmit", "disabled", "md", "Group", "Label", "Control", "type", "onChange", "target", "required", "placeholder", "size", "options", "isSearchable", "borderLeft", "Header", "onClick", "height", "presetIndex", "cursor", "border", "title", "step", "min", "as"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/EditFabric.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport Select from \"react-select\";\r\nimport { FaPlus, FaTrash, FaSave } from \"react-icons/fa\";\r\nimport { <PERSON>, Col, <PERSON>, <PERSON><PERSON>, <PERSON>, Alert, Spinner } from 'react-bootstrap';\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport { getUserRole, hasRole } from '../utils/auth';\r\n\r\n// Color presets for fabric variants\r\nconst COLOR_PRESETS = [\r\n  { color: \"#000000\", name: \"Black\" },\r\n  { color: \"#FFFFFF\", name: \"White\" },\r\n  { color: \"#FF0000\", name: \"Red\" },\r\n  { color: \"#0000FF\", name: \"Blue\" },\r\n  { color: \"#FFFF00\", name: \"Yellow\" },\r\n  { color: \"#00FF00\", name: \"<PERSON>\" },\r\n  { color: \"#FFA500\", name: \"<PERSON>\" },\r\n  { color: \"#800080\", name: \"<PERSON>\" },\r\n  { color: \"#FFC0CB\", name: \"<PERSON>\" },\r\n  { color: \"#A52A2A\", name: \"<PERSON>\" },\r\n  { color: \"#808080\", name: \"Gray\" },\r\n  { color: \"#C0C0C0\", name: \"Silver\" },\r\n];\r\n\r\nconst EditFabric = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  // State variables\r\n  const [fabricName, setFabricName] = useState(\"\");\r\n  const [selectedSupplier, setSelectedSupplier] = useState(null);\r\n  const [dateAdded, setDateAdded] = useState(\"\");\r\n  const [variants, setVariants] = useState([]);\r\n  const [suppliers, setSuppliers] = useState([]);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isInventoryManager, setIsInventoryManager] = useState(hasRole('Inventory Manager'));\r\n\r\n  // Check if user is authorized to access this page\r\n  useEffect(() => {\r\n    if (!isInventoryManager) {\r\n      setError(\"Only Inventory Managers can edit fabrics.\");\r\n      // Redirect to fabric list after a short delay\r\n      setTimeout(() => {\r\n        navigate('/viewfabric');\r\n      }, 2000);\r\n    }\r\n  }, [isInventoryManager, navigate]);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch suppliers for dropdown\r\n  useEffect(() => {\r\n    const fetchSuppliers = async () => {\r\n      try {\r\n        const response = await axios.get(\"http://localhost:8000/api/suppliers/\");\r\n        const supplierOptions = response.data.map((supplier) => ({\r\n          value: supplier.supplier_id,\r\n          label: supplier.name,\r\n        }));\r\n        setSuppliers(supplierOptions);\r\n      } catch (error) {\r\n        console.error(\"Error fetching suppliers:\", error);\r\n        setError(\"Error loading suppliers. Please try again.\");\r\n      }\r\n    };\r\n\r\n    fetchSuppliers();\r\n  }, []);\r\n\r\n  // Fetch fabric data\r\n  useEffect(() => {\r\n    const fetchFabricData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        // Fetch fabric definition\r\n        const fabricResponse = await axios.get(`http://localhost:8000/api/fabric-definitions/${id}/`);\r\n        const fabricData = fabricResponse.data;\r\n\r\n        setFabricName(fabricData.fabric_name);\r\n        setDateAdded(fabricData.date_added);\r\n\r\n        // Set selected supplier\r\n        const supplierOption = suppliers.find(s => s.value === fabricData.supplier);\r\n        if (supplierOption) {\r\n          setSelectedSupplier(supplierOption);\r\n        }\r\n\r\n        // Fetch fabric variants\r\n        const variantsResponse = await axios.get(`http://localhost:8000/api/fabric-definitions/${id}/variants/`);\r\n        const variantsData = variantsResponse.data;\r\n\r\n        // Format variants for state\r\n        const formattedVariants = variantsData.map(variant => ({\r\n          id: variant.id,\r\n          color: variant.color,\r\n          colorName: variant.color_name,\r\n          totalYard: variant.total_yard.toString(),\r\n          originalTotalYard: variant.total_yard,\r\n          availableYard: variant.available_yard !== null ? variant.available_yard : variant.total_yard,\r\n          originalAvailableYard: variant.available_yard !== null ? variant.available_yard : variant.total_yard,\r\n          pricePerYard: variant.price_per_yard.toString()\r\n        }));\r\n\r\n        setVariants(formattedVariants);\r\n        setLoading(false);\r\n      } catch (error) {\r\n        console.error(\"Error fetching fabric data:\", error);\r\n        setError(\"Error loading fabric data. Please try again.\");\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (suppliers.length > 0) {\r\n      fetchFabricData();\r\n    }\r\n  }, [id, suppliers]);\r\n\r\n  // Handle adding a variant\r\n  const handleAddVariant = () => {\r\n    setVariants([...variants, {\r\n      color: \"#000000\",\r\n      colorName: \"Black\",\r\n      totalYard: \"\",\r\n      originalTotalYard: 0,\r\n      availableYard: 0,\r\n      originalAvailableYard: 0,\r\n      pricePerYard: \"\"\r\n    }]);\r\n  };\r\n\r\n  // Handle removing a variant\r\n  const handleRemoveVariant = (index) => {\r\n    const updated = variants.filter((_, i) => i !== index);\r\n    setVariants(updated);\r\n  };\r\n\r\n  // Handle variant input changes\r\n  const handleVariantChange = (index, field, value) => {\r\n    const updated = [...variants];\r\n    const variant = updated[index];\r\n\r\n    // Store the previous value before updating\r\n    const previousValue = variant[field];\r\n\r\n    // Update the field with the new value\r\n    variant[field] = value;\r\n\r\n    // If color is changed, update the color name\r\n    if (field === \"color\") {\r\n      const colorPreset = COLOR_PRESETS.find(preset => preset.color === value);\r\n      variant.colorName = colorPreset ? colorPreset.name : \"\";\r\n    }\r\n\r\n    // If total yard is changed, update available yard proportionally\r\n    if (field === \"totalYard\" && value !== \"\") {\r\n      const newTotalYard = parseFloat(value);\r\n      const originalTotalYard = variant.originalTotalYard;\r\n      const originalAvailableYard = variant.originalAvailableYard;\r\n\r\n      // Calculate how much of the fabric has been used\r\n      const usedYard = originalTotalYard - originalAvailableYard;\r\n\r\n      // Validate that new total yard is not less than what's already been used\r\n      if (newTotalYard < usedYard) {\r\n        setError(`Cannot reduce total yard below ${usedYard} yards as that amount has already been used in cutting records.`);\r\n        // Revert to previous value\r\n        variant.totalYard = previousValue;\r\n        return;\r\n      } else {\r\n        // Clear error if it was previously set\r\n        setError(\"\");\r\n      }\r\n\r\n      // Update available yard based on the change in total yard\r\n      const newAvailableYard = newTotalYard - usedYard;\r\n      variant.availableYard = newAvailableYard;\r\n    }\r\n\r\n    setVariants(updated);\r\n  };\r\n\r\n  // Select a preset color\r\n  const selectPresetColor = (index, preset) => {\r\n    handleVariantChange(index, \"color\", preset.color);\r\n    handleVariantChange(index, \"colorName\", preset.name);\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!fabricName || !selectedSupplier || !dateAdded) {\r\n      setError(\"Please fill in all required fields.\");\r\n      return;\r\n    }\r\n\r\n    if (variants.length === 0) {\r\n      setError(\"Please add at least one fabric variant.\");\r\n      return;\r\n    }\r\n\r\n    for (const variant of variants) {\r\n      if (!variant.color || !variant.colorName || !variant.totalYard || !variant.pricePerYard) {\r\n        setError(\"Please fill in all variant details.\");\r\n        return;\r\n      }\r\n\r\n      // For existing variants, check if total yard is not reduced below what's been used\r\n      if (variant.id) {\r\n        const newTotalYard = parseFloat(variant.totalYard);\r\n        const usedYard = variant.originalTotalYard - variant.originalAvailableYard;\r\n\r\n        if (newTotalYard < usedYard) {\r\n          setError(`Cannot reduce total yard below ${usedYard.toFixed(2)} yards for ${variant.colorName} as that amount has already been used in cutting records.`);\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    setError(\"\");\r\n    setMessage(\"\");\r\n\r\n    try {\r\n      // Update fabric definition\r\n      await axios.put(`http://localhost:8000/api/fabric-definitions/${id}/`, {\r\n        fabric_name: fabricName,\r\n        supplier: selectedSupplier.value,\r\n        date_added: dateAdded,\r\n      });\r\n\r\n      // Update existing variants and add new ones\r\n      for (let variant of variants) {\r\n        if (variant.id) {\r\n          // Update existing variant\r\n          await axios.put(`http://localhost:8000/api/fabric-variants/${variant.id}/`, {\r\n            fabric_definition: id,\r\n            color: variant.color,\r\n            color_name: variant.colorName,\r\n            total_yard: parseFloat(variant.totalYard) || 0,\r\n            available_yard: parseFloat(variant.availableYard) || 0,\r\n            price_per_yard: parseFloat(variant.pricePerYard) || 0,\r\n          });\r\n        } else {\r\n          // Add new variant\r\n          await axios.post(\"http://localhost:8000/api/fabric-variants/\", {\r\n            fabric_definition: id,\r\n            color: variant.color,\r\n            color_name: variant.colorName,\r\n            total_yard: parseFloat(variant.totalYard) || 0,\r\n            available_yard: parseFloat(variant.totalYard) || 0, // For new variants, available_yard equals total_yard\r\n            price_per_yard: parseFloat(variant.pricePerYard) || 0,\r\n          });\r\n        }\r\n      }\r\n\r\n      setMessage(\"✅ Fabric updated successfully!\");\r\n\r\n      // Navigate back to fabric list after a short delay\r\n      setTimeout(() => {\r\n        navigate('/viewfabric');\r\n      }, 2000);\r\n    } catch (error) {\r\n      console.error(\"Error updating fabric:\", error);\r\n      setError(\"Error updating fabric. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\",\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">Edit Fabric</h2>\r\n\r\n        {message && <Alert variant=\"success\">{message}</Alert>}\r\n        {error && <Alert variant=\"danger\">{error}</Alert>}\r\n\r\n        {loading ? (\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading fabric data...</p>\r\n          </div>\r\n        ) : (\r\n          <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n            <Card.Body>\r\n              <Form onSubmit={handleSubmit}>\r\n                {!isInventoryManager && (\r\n                  <Alert variant=\"warning\" className=\"mb-3\">\r\n                    <strong>Access Denied:</strong> Only Inventory Managers can edit fabrics.\r\n                  </Alert>\r\n                )}\r\n                <fieldset disabled={!isInventoryManager}>\r\n                <Row>\r\n                  <Col md={6}>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label><strong>Fabric Name</strong></Form.Label>\r\n                      <Form.Control\r\n                        type=\"text\"\r\n                        value={fabricName}\r\n                        onChange={(e) => setFabricName(e.target.value)}\r\n                        required\r\n                        placeholder=\"Enter fabric name\"\r\n                      />\r\n                    </Form.Group>\r\n                  </Col>\r\n\r\n                  <Col md={6}>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label><strong>Supplier</strong></Form.Label>\r\n                      {loading ? (\r\n                        <div className=\"d-flex align-items-center\">\r\n                          <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                          <span>Loading suppliers...</span>\r\n                        </div>\r\n                      ) : (\r\n                        <Select\r\n                          options={suppliers}\r\n                          value={selectedSupplier}\r\n                          onChange={setSelectedSupplier}\r\n                          placeholder=\"Select a supplier...\"\r\n                          isSearchable\r\n                        />\r\n                      )}\r\n                    </Form.Group>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <Row>\r\n                  <Col md={6}>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label><strong>Date Added</strong></Form.Label>\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        value={dateAdded}\r\n                        onChange={(e) => setDateAdded(e.target.value)}\r\n                        required\r\n                      />\r\n                    </Form.Group>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <h4 className=\"mt-4 mb-3 border-bottom pb-2\">Fabric Variants</h4>\r\n\r\n                {variants.map((variant, index) => (\r\n                  <Card\r\n                    key={index}\r\n                    className=\"mb-3 border\"\r\n                    style={{\r\n                      borderLeft: `5px solid ${variant.color}`,\r\n                      borderRadius: \"8px\"\r\n                    }}\r\n                  >\r\n                    <Card.Header className=\"d-flex justify-content-between align-items-center bg-light\">\r\n                      <h5 className=\"mb-0\">Variant #{index + 1}</h5>\r\n                      {variants.length > 1 && (\r\n                        <Button\r\n                          variant=\"outline-danger\"\r\n                          size=\"sm\"\r\n                          onClick={() => handleRemoveVariant(index)}\r\n                        >\r\n                          <FaTrash className=\"me-1\" /> Remove\r\n                        </Button>\r\n                      )}\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <Row>\r\n                        <Col md={4}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Color</strong></Form.Label>\r\n                            <div className=\"d-flex align-items-center mb-2\">\r\n                              <Form.Control\r\n                                type=\"color\"\r\n                                value={variant.color}\r\n                                onChange={(e) => handleVariantChange(index, \"color\", e.target.value)}\r\n                                className=\"me-2\"\r\n                                style={{ width: '38px', height: '38px' }}\r\n                              />\r\n                              <Form.Control\r\n                                type=\"text\"\r\n                                placeholder=\"e.g., Black Line, Black Circle, Navy Stripe\"\r\n                                value={variant.colorName}\r\n                                onChange={(e) => handleVariantChange(index, \"colorName\", e.target.value)}\r\n                              />\r\n                            </div>\r\n                            <div className=\"color-presets d-flex flex-wrap gap-1 mt-1\">\r\n                              {COLOR_PRESETS.map((preset, presetIndex) => (\r\n                                <div\r\n                                  key={presetIndex}\r\n                                  onClick={() => selectPresetColor(index, preset)}\r\n                                  style={{\r\n                                    width: '20px',\r\n                                    height: '20px',\r\n                                    backgroundColor: preset.color,\r\n                                    cursor: 'pointer',\r\n                                    border: '1px solid #ccc',\r\n                                    borderRadius: '3px'\r\n                                  }}\r\n                                  title={preset.name}\r\n                                />\r\n                              ))}\r\n                            </div>\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={4}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Total Yard</strong></Form.Label>\r\n                            <Form.Control\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0\"\r\n                              value={variant.totalYard}\r\n                              onChange={(e) => handleVariantChange(index, \"totalYard\", e.target.value)}\r\n                              placeholder=\"Enter total yard\"\r\n                            />\r\n                            {variant.id && (\r\n                              <div className=\"mt-2 small\">\r\n                                <span className=\"text-muted\">Available: </span>\r\n                                <span className={parseFloat(variant.availableYard) < 10 ? 'text-danger fw-bold' : 'text-success'}>\r\n                                  {parseFloat(variant.availableYard).toFixed(2)} yards\r\n                                </span>\r\n                                {variant.originalTotalYard !== parseFloat(variant.totalYard) && (\r\n                                  <div className=\"text-info mt-1\">\r\n                                    <small>\r\n                                      {parseFloat(variant.totalYard) > variant.originalTotalYard\r\n                                        ? `Increasing total yard will add ${(parseFloat(variant.totalYard) - variant.originalTotalYard).toFixed(2)} yards to available stock.`\r\n                                        : `Decreasing total yard will reduce available stock by ${(variant.originalTotalYard - parseFloat(variant.totalYard)).toFixed(2)} yards.`\r\n                                      }\r\n                                    </small>\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={4}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Price per Yard (Rs.)</strong></Form.Label>\r\n                            <Form.Control\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0\"\r\n                              value={variant.pricePerYard}\r\n                              onChange={(e) => handleVariantChange(index, \"pricePerYard\", e.target.value)}\r\n                              placeholder=\"Enter price per yard\"\r\n                            />\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n                    </Card.Body>\r\n                  </Card>\r\n                ))}\r\n\r\n                <div className=\"d-flex justify-content-center mb-4\">\r\n                  <Button\r\n                    variant=\"outline-primary\"\r\n                    onClick={handleAddVariant}\r\n                    className=\"d-flex align-items-center\"\r\n                  >\r\n                    <FaPlus className=\"me-2\" /> Add Another Variant\r\n                  </Button>\r\n                </div>\r\n\r\n                <div className=\"d-flex justify-content-center mt-4\">\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    className=\"me-2 px-4\"\r\n                    onClick={() => navigate('/viewfabric')}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                  <Button\r\n                    type=\"submit\"\r\n                    variant=\"primary\"\r\n                    disabled={isSubmitting || !isInventoryManager}\r\n                    className=\"px-4\"\r\n                  >\r\n                    {isSubmitting ? (\r\n                      <>\r\n                        <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                        Updating...\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <FaSave className=\"me-2\" /> Save Changes\r\n                      </>\r\n                    )}\r\n                  </Button>\r\n                </div>\r\n                </fieldset>\r\n              </Form>\r\n            </Card.Body>\r\n          </Card>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default EditFabric;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OAASC,MAAM,CAAEC,OAAO,CAAEC,MAAM,KAAQ,gBAAgB,CACxD,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,KAAQ,iBAAiB,CAC9E,MAAO,sCAAsC,CAC7C,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,WAAW,CAAEC,OAAO,KAAQ,eAAe,CAEpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,aAAa,CAAG,CACpB,CAAEC,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,KAAM,CAAC,CACjC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,MAAO,CAAC,CAClC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,MAAO,CAAC,CAClC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACnC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,MAAO,CAAC,CAClC,CAAED,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE,QAAS,CAAC,CACrC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAEC,EAAG,CAAC,CAAGd,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAAe,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAACmC,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqC,QAAQ,CAAEC,WAAW,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuC,SAAS,CAAEC,YAAY,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC2C,KAAK,CAAEC,QAAQ,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC6C,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC+C,aAAa,CAAEC,gBAAgB,CAAC,CAAGhD,QAAQ,CAACiD,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGtD,QAAQ,CAACkB,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAE1F;AACAjB,SAAS,CAAC,IAAM,CACd,GAAI,CAACoD,kBAAkB,CAAE,CACvBT,QAAQ,CAAC,2CAA2C,CAAC,CACrD;AACAW,UAAU,CAAC,IAAM,CACfzB,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAC,CAAE,CAACuB,kBAAkB,CAAEvB,QAAQ,CAAC,CAAC,CAElC;AACA7B,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuD,YAAY,CAAGA,CAAA,GAAM,CACzBR,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMP,MAAM,CAACS,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACAvD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA0D,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA1D,KAAK,CAAC2D,GAAG,CAAC,sCAAsC,CAAC,CACxE,KAAM,CAAAC,eAAe,CAAGF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAAEC,QAAQ,GAAM,CACvDC,KAAK,CAAED,QAAQ,CAACE,WAAW,CAC3BC,KAAK,CAAEH,QAAQ,CAACtC,IAClB,CAAC,CAAC,CAAC,CACHa,YAAY,CAACsB,eAAe,CAAC,CAC/B,CAAE,MAAOnB,KAAK,CAAE,CACd0B,OAAO,CAAC1B,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDC,QAAQ,CAAC,4CAA4C,CAAC,CACxD,CACF,CAAC,CAEDe,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA1D,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqE,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClClB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAmB,cAAc,CAAG,KAAM,CAAArE,KAAK,CAAC2D,GAAG,CAAC,gDAAgDhC,EAAE,GAAG,CAAC,CAC7F,KAAM,CAAA2C,UAAU,CAAGD,cAAc,CAACR,IAAI,CAEtC/B,aAAa,CAACwC,UAAU,CAACC,WAAW,CAAC,CACrCrC,YAAY,CAACoC,UAAU,CAACE,UAAU,CAAC,CAEnC;AACA,KAAM,CAAAC,cAAc,CAAGpC,SAAS,CAACqC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACX,KAAK,GAAKM,UAAU,CAACP,QAAQ,CAAC,CAC3E,GAAIU,cAAc,CAAE,CAClBzC,mBAAmB,CAACyC,cAAc,CAAC,CACrC,CAEA;AACA,KAAM,CAAAG,gBAAgB,CAAG,KAAM,CAAA5E,KAAK,CAAC2D,GAAG,CAAC,gDAAgDhC,EAAE,YAAY,CAAC,CACxG,KAAM,CAAAkD,YAAY,CAAGD,gBAAgB,CAACf,IAAI,CAE1C;AACA,KAAM,CAAAiB,iBAAiB,CAAGD,YAAY,CAACf,GAAG,CAACiB,OAAO,GAAK,CACrDpD,EAAE,CAAEoD,OAAO,CAACpD,EAAE,CACdH,KAAK,CAAEuD,OAAO,CAACvD,KAAK,CACpBwD,SAAS,CAAED,OAAO,CAACE,UAAU,CAC7BC,SAAS,CAAEH,OAAO,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CACxCC,iBAAiB,CAAEN,OAAO,CAACI,UAAU,CACrCG,aAAa,CAAEP,OAAO,CAACQ,cAAc,GAAK,IAAI,CAAGR,OAAO,CAACQ,cAAc,CAAGR,OAAO,CAACI,UAAU,CAC5FK,qBAAqB,CAAET,OAAO,CAACQ,cAAc,GAAK,IAAI,CAAGR,OAAO,CAACQ,cAAc,CAAGR,OAAO,CAACI,UAAU,CACpGM,YAAY,CAAEV,OAAO,CAACW,cAAc,CAACN,QAAQ,CAAC,CAChD,CAAC,CAAC,CAAC,CAEHhD,WAAW,CAAC0C,iBAAiB,CAAC,CAC9B5B,UAAU,CAAC,KAAK,CAAC,CACnB,CAAE,MAAOT,KAAK,CAAE,CACd0B,OAAO,CAAC1B,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDC,QAAQ,CAAC,8CAA8C,CAAC,CACxDQ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIb,SAAS,CAACsD,MAAM,CAAG,CAAC,CAAE,CACxBvB,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACzC,EAAE,CAAEU,SAAS,CAAC,CAAC,CAEnB;AACA,KAAM,CAAAuD,gBAAgB,CAAGA,CAAA,GAAM,CAC7BxD,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAE,CACxBX,KAAK,CAAE,SAAS,CAChBwD,SAAS,CAAE,OAAO,CAClBE,SAAS,CAAE,EAAE,CACbG,iBAAiB,CAAE,CAAC,CACpBC,aAAa,CAAE,CAAC,CAChBE,qBAAqB,CAAE,CAAC,CACxBC,YAAY,CAAE,EAChB,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAI,mBAAmB,CAAIC,KAAK,EAAK,CACrC,KAAM,CAAAC,OAAO,CAAG5D,QAAQ,CAAC6D,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKJ,KAAK,CAAC,CACtD1D,WAAW,CAAC2D,OAAO,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAI,mBAAmB,CAAGA,CAACL,KAAK,CAAEM,KAAK,CAAEpC,KAAK,GAAK,CACnD,KAAM,CAAA+B,OAAO,CAAG,CAAC,GAAG5D,QAAQ,CAAC,CAC7B,KAAM,CAAA4C,OAAO,CAAGgB,OAAO,CAACD,KAAK,CAAC,CAE9B;AACA,KAAM,CAAAO,aAAa,CAAGtB,OAAO,CAACqB,KAAK,CAAC,CAEpC;AACArB,OAAO,CAACqB,KAAK,CAAC,CAAGpC,KAAK,CAEtB;AACA,GAAIoC,KAAK,GAAK,OAAO,CAAE,CACrB,KAAM,CAAAE,WAAW,CAAG/E,aAAa,CAACmD,IAAI,CAAC6B,MAAM,EAAIA,MAAM,CAAC/E,KAAK,GAAKwC,KAAK,CAAC,CACxEe,OAAO,CAACC,SAAS,CAAGsB,WAAW,CAAGA,WAAW,CAAC7E,IAAI,CAAG,EAAE,CACzD,CAEA;AACA,GAAI2E,KAAK,GAAK,WAAW,EAAIpC,KAAK,GAAK,EAAE,CAAE,CACzC,KAAM,CAAAwC,YAAY,CAAGC,UAAU,CAACzC,KAAK,CAAC,CACtC,KAAM,CAAAqB,iBAAiB,CAAGN,OAAO,CAACM,iBAAiB,CACnD,KAAM,CAAAG,qBAAqB,CAAGT,OAAO,CAACS,qBAAqB,CAE3D;AACA,KAAM,CAAAkB,QAAQ,CAAGrB,iBAAiB,CAAGG,qBAAqB,CAE1D;AACA,GAAIgB,YAAY,CAAGE,QAAQ,CAAE,CAC3BhE,QAAQ,CAAC,kCAAkCgE,QAAQ,iEAAiE,CAAC,CACrH;AACA3B,OAAO,CAACG,SAAS,CAAGmB,aAAa,CACjC,OACF,CAAC,IAAM,CACL;AACA3D,QAAQ,CAAC,EAAE,CAAC,CACd,CAEA;AACA,KAAM,CAAAiE,gBAAgB,CAAGH,YAAY,CAAGE,QAAQ,CAChD3B,OAAO,CAACO,aAAa,CAAGqB,gBAAgB,CAC1C,CAEAvE,WAAW,CAAC2D,OAAO,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAa,iBAAiB,CAAGA,CAACd,KAAK,CAAES,MAAM,GAAK,CAC3CJ,mBAAmB,CAACL,KAAK,CAAE,OAAO,CAAES,MAAM,CAAC/E,KAAK,CAAC,CACjD2E,mBAAmB,CAACL,KAAK,CAAE,WAAW,CAAES,MAAM,CAAC9E,IAAI,CAAC,CACtD,CAAC,CAED;AACA,KAAM,CAAAoF,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAAClF,UAAU,EAAI,CAACE,gBAAgB,EAAI,CAACE,SAAS,CAAE,CAClDS,QAAQ,CAAC,qCAAqC,CAAC,CAC/C,OACF,CAEA,GAAIP,QAAQ,CAACwD,MAAM,GAAK,CAAC,CAAE,CACzBjD,QAAQ,CAAC,yCAAyC,CAAC,CACnD,OACF,CAEA,IAAK,KAAM,CAAAqC,OAAO,GAAI,CAAA5C,QAAQ,CAAE,CAC9B,GAAI,CAAC4C,OAAO,CAACvD,KAAK,EAAI,CAACuD,OAAO,CAACC,SAAS,EAAI,CAACD,OAAO,CAACG,SAAS,EAAI,CAACH,OAAO,CAACU,YAAY,CAAE,CACvF/C,QAAQ,CAAC,qCAAqC,CAAC,CAC/C,OACF,CAEA;AACA,GAAIqC,OAAO,CAACpD,EAAE,CAAE,CACd,KAAM,CAAA6E,YAAY,CAAGC,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,CAClD,KAAM,CAAAwB,QAAQ,CAAG3B,OAAO,CAACM,iBAAiB,CAAGN,OAAO,CAACS,qBAAqB,CAE1E,GAAIgB,YAAY,CAAGE,QAAQ,CAAE,CAC3BhE,QAAQ,CAAC,kCAAkCgE,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,cAAcjC,OAAO,CAACC,SAAS,2DAA2D,CAAC,CACzJ,OACF,CACF,CACF,CAEApC,eAAe,CAAC,IAAI,CAAC,CACrBF,QAAQ,CAAC,EAAE,CAAC,CACZF,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF;AACA,KAAM,CAAAxC,KAAK,CAACiH,GAAG,CAAC,gDAAgDtF,EAAE,GAAG,CAAE,CACrE4C,WAAW,CAAE1C,UAAU,CACvBkC,QAAQ,CAAEhC,gBAAgB,CAACiC,KAAK,CAChCQ,UAAU,CAAEvC,SACd,CAAC,CAAC,CAEF;AACA,IAAK,GAAI,CAAA8C,OAAO,GAAI,CAAA5C,QAAQ,CAAE,CAC5B,GAAI4C,OAAO,CAACpD,EAAE,CAAE,CACd;AACA,KAAM,CAAA3B,KAAK,CAACiH,GAAG,CAAC,6CAA6ClC,OAAO,CAACpD,EAAE,GAAG,CAAE,CAC1EuF,iBAAiB,CAAEvF,EAAE,CACrBH,KAAK,CAAEuD,OAAO,CAACvD,KAAK,CACpByD,UAAU,CAAEF,OAAO,CAACC,SAAS,CAC7BG,UAAU,CAAEsB,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,EAAI,CAAC,CAC9CK,cAAc,CAAEkB,UAAU,CAAC1B,OAAO,CAACO,aAAa,CAAC,EAAI,CAAC,CACtDI,cAAc,CAAEe,UAAU,CAAC1B,OAAO,CAACU,YAAY,CAAC,EAAI,CACtD,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,KAAM,CAAAzF,KAAK,CAACmH,IAAI,CAAC,4CAA4C,CAAE,CAC7DD,iBAAiB,CAAEvF,EAAE,CACrBH,KAAK,CAAEuD,OAAO,CAACvD,KAAK,CACpByD,UAAU,CAAEF,OAAO,CAACC,SAAS,CAC7BG,UAAU,CAAEsB,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,EAAI,CAAC,CAC9CK,cAAc,CAAEkB,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,EAAI,CAAC,CAAE;AACpDQ,cAAc,CAAEe,UAAU,CAAC1B,OAAO,CAACU,YAAY,CAAC,EAAI,CACtD,CAAC,CAAC,CACJ,CACF,CAEAjD,UAAU,CAAC,gCAAgC,CAAC,CAE5C;AACAa,UAAU,CAAC,IAAM,CACfzB,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOa,KAAK,CAAE,CACd0B,OAAO,CAAC1B,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CC,QAAQ,CAAC,0CAA0C,CAAC,CACtD,CAAC,OAAS,CACRE,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,mBACExB,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACElG,IAAA,CAACN,eAAe,GAAE,CAAC,cACnBQ,KAAA,QACEiG,KAAK,CAAE,CACLC,UAAU,CAAEzE,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5C0E,KAAK,CAAE,eAAe1E,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzD2E,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAL,QAAA,eAEFlG,IAAA,OAAIwG,SAAS,CAAC,MAAM,CAAAN,QAAA,CAAC,aAAW,CAAI,CAAC,CAEpC7E,OAAO,eAAIrB,IAAA,CAACR,KAAK,EAACqE,OAAO,CAAC,SAAS,CAAAqC,QAAA,CAAE7E,OAAO,CAAQ,CAAC,CACrDE,KAAK,eAAIvB,IAAA,CAACR,KAAK,EAACqE,OAAO,CAAC,QAAQ,CAAAqC,QAAA,CAAE3E,KAAK,CAAQ,CAAC,CAEhDQ,OAAO,cACN7B,KAAA,QAAKsG,SAAS,CAAC,kBAAkB,CAAAN,QAAA,eAC/BlG,IAAA,CAACP,OAAO,EAACgH,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAC7C,OAAO,CAAC,SAAS,CAAAqC,QAAA,cACzDlG,IAAA,SAAMwG,SAAS,CAAC,iBAAiB,CAAAN,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,cACVlG,IAAA,MAAGwG,SAAS,CAAC,MAAM,CAAAN,QAAA,CAAC,wBAAsB,CAAG,CAAC,EAC3C,CAAC,cAENlG,IAAA,CAACT,IAAI,EAACiH,SAAS,CAAC,gBAAgB,CAACL,KAAK,CAAE,CAAEQ,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAV,QAAA,cAC3FlG,IAAA,CAACT,IAAI,CAACsH,IAAI,EAAAX,QAAA,cACRhG,KAAA,CAACb,IAAI,EAACyH,QAAQ,CAAEnB,YAAa,CAAAO,QAAA,EAC1B,CAACjE,kBAAkB,eAClB/B,KAAA,CAACV,KAAK,EAACqE,OAAO,CAAC,SAAS,CAAC2C,SAAS,CAAC,MAAM,CAAAN,QAAA,eACvClG,IAAA,WAAAkG,QAAA,CAAQ,gBAAc,CAAQ,CAAC,6CACjC,EAAO,CACR,cACDhG,KAAA,aAAU6G,QAAQ,CAAE,CAAC9E,kBAAmB,CAAAiE,QAAA,eACxChG,KAAA,CAACf,GAAG,EAAA+G,QAAA,eACFlG,IAAA,CAACZ,GAAG,EAAC4H,EAAE,CAAE,CAAE,CAAAd,QAAA,cACThG,KAAA,CAACb,IAAI,CAAC4H,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1BlG,IAAA,CAACX,IAAI,CAAC6H,KAAK,EAAAhB,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAY,CAAC,cACrDlG,IAAA,CAACX,IAAI,CAAC8H,OAAO,EACXC,IAAI,CAAC,MAAM,CACXtE,KAAK,CAAEnC,UAAW,CAClB0G,QAAQ,CAAGzB,CAAC,EAAKhF,aAAa,CAACgF,CAAC,CAAC0B,MAAM,CAACxE,KAAK,CAAE,CAC/CyE,QAAQ,MACRC,WAAW,CAAC,mBAAmB,CAChC,CAAC,EACQ,CAAC,CACV,CAAC,cAENxH,IAAA,CAACZ,GAAG,EAAC4H,EAAE,CAAE,CAAE,CAAAd,QAAA,cACThG,KAAA,CAACb,IAAI,CAAC4H,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1BlG,IAAA,CAACX,IAAI,CAAC6H,KAAK,EAAAhB,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAAQ,UAAQ,CAAQ,CAAC,CAAY,CAAC,CACjDnE,OAAO,cACN7B,KAAA,QAAKsG,SAAS,CAAC,2BAA2B,CAAAN,QAAA,eACxClG,IAAA,CAACP,OAAO,EAACgH,SAAS,CAAC,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAACjB,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDxG,IAAA,SAAAkG,QAAA,CAAM,sBAAoB,CAAM,CAAC,EAC9B,CAAC,cAENlG,IAAA,CAACjB,MAAM,EACL2I,OAAO,CAAEvG,SAAU,CACnB2B,KAAK,CAAEjC,gBAAiB,CACxBwG,QAAQ,CAAEvG,mBAAoB,CAC9B0G,WAAW,CAAC,sBAAsB,CAClCG,YAAY,MACb,CACF,EACS,CAAC,CACV,CAAC,EACH,CAAC,cAEN3H,IAAA,CAACb,GAAG,EAAA+G,QAAA,cACFlG,IAAA,CAACZ,GAAG,EAAC4H,EAAE,CAAE,CAAE,CAAAd,QAAA,cACThG,KAAA,CAACb,IAAI,CAAC4H,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1BlG,IAAA,CAACX,IAAI,CAAC6H,KAAK,EAAAhB,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,cACpDlG,IAAA,CAACX,IAAI,CAAC8H,OAAO,EACXC,IAAI,CAAC,MAAM,CACXtE,KAAK,CAAE/B,SAAU,CACjBsG,QAAQ,CAAGzB,CAAC,EAAK5E,YAAY,CAAC4E,CAAC,CAAC0B,MAAM,CAACxE,KAAK,CAAE,CAC9CyE,QAAQ,MACT,CAAC,EACQ,CAAC,CACV,CAAC,CACH,CAAC,cAENvH,IAAA,OAAIwG,SAAS,CAAC,8BAA8B,CAAAN,QAAA,CAAC,iBAAe,CAAI,CAAC,CAEhEjF,QAAQ,CAAC2B,GAAG,CAAC,CAACiB,OAAO,CAAEe,KAAK,gBAC3B1E,KAAA,CAACX,IAAI,EAEHiH,SAAS,CAAC,aAAa,CACvBL,KAAK,CAAE,CACLyB,UAAU,CAAE,aAAa/D,OAAO,CAACvD,KAAK,EAAE,CACxCsG,YAAY,CAAE,KAChB,CAAE,CAAAV,QAAA,eAEFhG,KAAA,CAACX,IAAI,CAACsI,MAAM,EAACrB,SAAS,CAAC,4DAA4D,CAAAN,QAAA,eACjFhG,KAAA,OAAIsG,SAAS,CAAC,MAAM,CAAAN,QAAA,EAAC,WAAS,CAACtB,KAAK,CAAG,CAAC,EAAK,CAAC,CAC7C3D,QAAQ,CAACwD,MAAM,CAAG,CAAC,eAClBvE,KAAA,CAACZ,MAAM,EACLuE,OAAO,CAAC,gBAAgB,CACxB4D,IAAI,CAAC,IAAI,CACTK,OAAO,CAAEA,CAAA,GAAMnD,mBAAmB,CAACC,KAAK,CAAE,CAAAsB,QAAA,eAE1ClG,IAAA,CAACf,OAAO,EAACuH,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CACT,EACU,CAAC,cACdxG,IAAA,CAACT,IAAI,CAACsH,IAAI,EAAAX,QAAA,cACRhG,KAAA,CAACf,GAAG,EAAA+G,QAAA,eACFlG,IAAA,CAACZ,GAAG,EAAC4H,EAAE,CAAE,CAAE,CAAAd,QAAA,cACThG,KAAA,CAACb,IAAI,CAAC4H,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1BlG,IAAA,CAACX,IAAI,CAAC6H,KAAK,EAAAhB,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAAQ,OAAK,CAAQ,CAAC,CAAY,CAAC,cAC/ChG,KAAA,QAAKsG,SAAS,CAAC,gCAAgC,CAAAN,QAAA,eAC7ClG,IAAA,CAACX,IAAI,CAAC8H,OAAO,EACXC,IAAI,CAAC,OAAO,CACZtE,KAAK,CAAEe,OAAO,CAACvD,KAAM,CACrB+G,QAAQ,CAAGzB,CAAC,EAAKX,mBAAmB,CAACL,KAAK,CAAE,OAAO,CAAEgB,CAAC,CAAC0B,MAAM,CAACxE,KAAK,CAAE,CACrE0D,SAAS,CAAC,MAAM,CAChBL,KAAK,CAAE,CAAEE,KAAK,CAAE,MAAM,CAAE0B,MAAM,CAAE,MAAO,CAAE,CAC1C,CAAC,cACF/H,IAAA,CAACX,IAAI,CAAC8H,OAAO,EACXC,IAAI,CAAC,MAAM,CACXI,WAAW,CAAC,6CAA6C,CACzD1E,KAAK,CAAEe,OAAO,CAACC,SAAU,CACzBuD,QAAQ,CAAGzB,CAAC,EAAKX,mBAAmB,CAACL,KAAK,CAAE,WAAW,CAAEgB,CAAC,CAAC0B,MAAM,CAACxE,KAAK,CAAE,CAC1E,CAAC,EACC,CAAC,cACN9C,IAAA,QAAKwG,SAAS,CAAC,2CAA2C,CAAAN,QAAA,CACvD7F,aAAa,CAACuC,GAAG,CAAC,CAACyC,MAAM,CAAE2C,WAAW,gBACrChI,IAAA,QAEE8H,OAAO,CAAEA,CAAA,GAAMpC,iBAAiB,CAACd,KAAK,CAAES,MAAM,CAAE,CAChDc,KAAK,CAAE,CACLE,KAAK,CAAE,MAAM,CACb0B,MAAM,CAAE,MAAM,CACdpB,eAAe,CAAEtB,MAAM,CAAC/E,KAAK,CAC7B2H,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,gBAAgB,CACxBtB,YAAY,CAAE,KAChB,CAAE,CACFuB,KAAK,CAAE9C,MAAM,CAAC9E,IAAK,EAVdyH,WAWN,CACF,CAAC,CACC,CAAC,EACI,CAAC,CACV,CAAC,cACNhI,IAAA,CAACZ,GAAG,EAAC4H,EAAE,CAAE,CAAE,CAAAd,QAAA,cACThG,KAAA,CAACb,IAAI,CAAC4H,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1BlG,IAAA,CAACX,IAAI,CAAC6H,KAAK,EAAAhB,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,cACpDlG,IAAA,CAACX,IAAI,CAAC8H,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbgB,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPvF,KAAK,CAAEe,OAAO,CAACG,SAAU,CACzBqD,QAAQ,CAAGzB,CAAC,EAAKX,mBAAmB,CAACL,KAAK,CAAE,WAAW,CAAEgB,CAAC,CAAC0B,MAAM,CAACxE,KAAK,CAAE,CACzE0E,WAAW,CAAC,kBAAkB,CAC/B,CAAC,CACD3D,OAAO,CAACpD,EAAE,eACTP,KAAA,QAAKsG,SAAS,CAAC,YAAY,CAAAN,QAAA,eACzBlG,IAAA,SAAMwG,SAAS,CAAC,YAAY,CAAAN,QAAA,CAAC,aAAW,CAAM,CAAC,cAC/ChG,KAAA,SAAMsG,SAAS,CAAEjB,UAAU,CAAC1B,OAAO,CAACO,aAAa,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAAG,cAAe,CAAA8B,QAAA,EAC9FX,UAAU,CAAC1B,OAAO,CAACO,aAAa,CAAC,CAAC0B,OAAO,CAAC,CAAC,CAAC,CAAC,QAChD,EAAM,CAAC,CACNjC,OAAO,CAACM,iBAAiB,GAAKoB,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,eAC1DhE,IAAA,QAAKwG,SAAS,CAAC,gBAAgB,CAAAN,QAAA,cAC7BlG,IAAA,UAAAkG,QAAA,CACGX,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,CAAGH,OAAO,CAACM,iBAAiB,CACtD,kCAAkC,CAACoB,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,CAAGH,OAAO,CAACM,iBAAiB,EAAE2B,OAAO,CAAC,CAAC,CAAC,4BAA4B,CACpI,wDAAwD,CAACjC,OAAO,CAACM,iBAAiB,CAAGoB,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,EAAE8B,OAAO,CAAC,CAAC,CAAC,SAAS,CAEtI,CAAC,CACL,CACN,EACE,CACN,EACS,CAAC,CACV,CAAC,cACN9F,IAAA,CAACZ,GAAG,EAAC4H,EAAE,CAAE,CAAE,CAAAd,QAAA,cACThG,KAAA,CAACb,IAAI,CAAC4H,KAAK,EAACT,SAAS,CAAC,MAAM,CAAAN,QAAA,eAC1BlG,IAAA,CAACX,IAAI,CAAC6H,KAAK,EAAAhB,QAAA,cAAClG,IAAA,WAAAkG,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,CAAY,CAAC,cAC9DlG,IAAA,CAACX,IAAI,CAAC8H,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbgB,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,GAAG,CACPvF,KAAK,CAAEe,OAAO,CAACU,YAAa,CAC5B8C,QAAQ,CAAGzB,CAAC,EAAKX,mBAAmB,CAACL,KAAK,CAAE,cAAc,CAAEgB,CAAC,CAAC0B,MAAM,CAACxE,KAAK,CAAE,CAC5E0E,WAAW,CAAC,sBAAsB,CACnC,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,CACG,CAAC,GAvGP5C,KAwGD,CACP,CAAC,cAEF5E,IAAA,QAAKwG,SAAS,CAAC,oCAAoC,CAAAN,QAAA,cACjDhG,KAAA,CAACZ,MAAM,EACLuE,OAAO,CAAC,iBAAiB,CACzBiE,OAAO,CAAEpD,gBAAiB,CAC1B8B,SAAS,CAAC,2BAA2B,CAAAN,QAAA,eAErClG,IAAA,CAAChB,MAAM,EAACwH,SAAS,CAAC,MAAM,CAAE,CAAC,uBAC7B,EAAQ,CAAC,CACN,CAAC,cAENtG,KAAA,QAAKsG,SAAS,CAAC,oCAAoC,CAAAN,QAAA,eACjDlG,IAAA,CAACV,MAAM,EACLuE,OAAO,CAAC,WAAW,CACnB2C,SAAS,CAAC,WAAW,CACrBsB,OAAO,CAAEA,CAAA,GAAMpH,QAAQ,CAAC,aAAa,CAAE,CAAAwF,QAAA,CACxC,QAED,CAAQ,CAAC,cACTlG,IAAA,CAACV,MAAM,EACL8H,IAAI,CAAC,QAAQ,CACbvD,OAAO,CAAC,SAAS,CACjBkD,QAAQ,CAAEtF,YAAY,EAAI,CAACQ,kBAAmB,CAC9CuE,SAAS,CAAC,MAAM,CAAAN,QAAA,CAEfzE,YAAY,cACXvB,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACElG,IAAA,CAACP,OAAO,EAAC6I,EAAE,CAAC,MAAM,CAAC7B,SAAS,CAAC,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAACf,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,cAEtG,EAAE,CAAC,cAEHtG,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eACElG,IAAA,CAACd,MAAM,EAACsH,SAAS,CAAC,MAAM,CAAE,CAAC,gBAC7B,EAAE,CACH,CACK,CAAC,EACN,CAAC,EACI,CAAC,EACP,CAAC,CACE,CAAC,CACR,CACP,EACE,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAAhG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}