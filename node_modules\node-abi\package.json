{"name": "node-abi", "version": "4.12.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "type": "module", "exports": "./index.js", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "files": ["abi_registry.json", "index.js", "getNextTarget.js"], "repository": {"type": "git", "url": "git+https://github.com/electron/node-abi.git"}, "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "homepage": "https://github.com/electron/node-abi#readme", "devDependencies": {}, "dependencies": {"semver": "^7.6.3"}, "engines": {"node": ">=22.12.0"}, "publishConfig": {"provenance": true}}