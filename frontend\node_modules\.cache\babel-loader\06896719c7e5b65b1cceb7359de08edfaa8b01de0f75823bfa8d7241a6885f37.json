{"ast": null, "code": "import{BrowserRouter as Router,Route,Routes,Navigate}from\"react-router-dom\";import Signup from\"./pages/Signup\";import Login from\"./pages/Login\";import OwnerDashboard from\"./pages/OwnerDashboard\";import InventoryDashboard from\"./pages/InventoryDashboard\";import OrdersDashboard from\"./pages/OrdersDashboard\";import SalesDashboard from\"./pages/SalesDashboard\";import SalesProductView from\"./pages/SalesProductView\";import SalesProductImageViewer from\"./pages/SalesProductImageViewer\";import AddSupplier from\"./pages/AddSupplier\";import ViewSuppliers from\"./pages/ViewSuppliers\";import AddFabric from\"./pages/AddFabric\";import EditFabric from\"./pages/EditFabric\";import ViewFabrics from\"./pages/ViewFabrics\";import ViewCutting from\"./pages/ViewCutting.js\";import ViewFabricVariants from\"./pages/ViewFabricVariants\";import FabricInventoryDetail from\"./pages/FabricInventoryDetail\";import CuttingRecordDetail from\"./pages/CuttingRecordDetail\";import AddCutting from\"./pages/AddCutting.js\";import EditCutting from\"./pages/EditCutting.js\";import AddDailySewingRecord from\"./pages/AddDailySewingRecord\";import ViewDailySewingHistory from'./pages/ViewDailySewingHistory';import ViewProductList from'./pages/ViewProductList.js';import ApproveFinishedProduct from\"./pages/ApproveFinishedProduct\";import ViewApproveProduct from\"./pages/ViewApproveProduct.js\";import AddPackingSession from\"./pages/AddPackingSession.js\";import ViewPackingSessions from\"./pages/ViewPackingSessions.js\";import ViewPackingInventory from\"./pages/ViewPackingInventory.js\";import ViewPackingInventorySales from\"./pages/ViewPackingInventorySales.js\";import SellProductPage from\"./pages/SellProductPage.js\";import PackingReportChart from\"./pages/PackingReportChart.js\";import AddShop from\"./pages/AddShop.js\";import CreateOrder from\"./pages/CreateOrder.js\";import OrderListPage from\"./pages/OrderListPage.js.js\";import OwnerOrdersPage from\"./pages/OwnerOrdersPage.js\";import SalesTeamOrdersPage from\"./pages/SalesTeamOrdersPage.js\";import ShopAnalysisDashboard from\"./pages/ShopAnalysisDashboard.js\";import OrderAnalysisPage from\"./pages/OrderAnalysisPage.js\";import ViewShops from\"./pages/ViewShops.js\";import SalesReport from\"./pages/SalesReport.js\";// Import the ProtectedRoute component\nimport ProtectedRoute from\"./components/ProtectedRoute\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/signup\",element:/*#__PURE__*/_jsx(Signup,{})}),/*#__PURE__*/_jsx(Route,{path:\"\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/owner-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner'],children:/*#__PURE__*/_jsx(OwnerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/owner-orders\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner'],children:/*#__PURE__*/_jsx(OwnerOrdersPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/inventory-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(InventoryDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/addsupplier\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(AddSupplier,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/viewsuppliers\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewSuppliers,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/addfabric\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(AddFabric,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/edit-fabric/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(EditFabric,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/viewfabric\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewFabrics,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/fabric-definitions/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewFabricVariants,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/fabric-inventory/:variantId\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(FabricInventoryDetail,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/viewcutting\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewCutting,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/cutting-record/:recordId\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(CuttingRecordDetail,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/addcutting\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(AddCutting,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/edit-cutting/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(EditCutting,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/adddailysewing\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(AddDailySewingRecord,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/daily-sewing-history\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewDailySewingHistory,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/viewproductlist\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewProductList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/approve-finished-product/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner'],children:/*#__PURE__*/_jsx(ApproveFinishedProduct,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/approveproduct-list\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner'],children:/*#__PURE__*/_jsx(ViewApproveProduct,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/add-packing-session\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager'],children:/*#__PURE__*/_jsx(AddPackingSession,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/view-packing-sessions\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(ViewPackingSessions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/view-packing-inventory\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Inventory Manager','Owner','Sales Team','Order Coordinator'],children:/*#__PURE__*/_jsx(ViewPackingInventory,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/packing-report\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner','Inventory Manager'],children:/*#__PURE__*/_jsx(PackingReportChart,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/orders-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Order Coordinator'],children:/*#__PURE__*/_jsx(OrdersDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sales-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(SalesDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sales-products\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(SalesProductView,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sales-product-gallery\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(SalesProductImageViewer,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sales-packing-inventory\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(ViewPackingInventorySales,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sell-product\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(SellProductPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sales-team-orders\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(SalesTeamOrdersPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/viewshops\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team'],children:/*#__PURE__*/_jsx(ViewShops,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/sales-report\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Sales Team','Inventory Manager','Owner'],children:/*#__PURE__*/_jsx(SalesReport,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/addshop\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner','Sales Team'],children:/*#__PURE__*/_jsx(AddShop,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/shop-analysis\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner','Sales Team'],children:/*#__PURE__*/_jsx(ShopAnalysisDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/addorder\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Order Coordinator','Sales Team'],children:/*#__PURE__*/_jsx(CreateOrder,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/order-list\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Order Coordinator','Sales Team','Owner'],children:/*#__PURE__*/_jsx(OrderListPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/order-analysis\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:['Owner','Sales Team'],children:/*#__PURE__*/_jsx(OrderAnalysisPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})})]})});}export default App;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Navigate", "Signup", "<PERSON><PERSON>", "OwnerDashboard", "InventoryDashboard", "OrdersDashboard", "SalesDashboard", "SalesProductView", "SalesProductImageViewer", "AddSupplier", "ViewSuppliers", "AddFabric", "EditFabric", "ViewFabrics", "ViewCutting", "ViewFabricVariants", "FabricInventoryDetail", "CuttingRecordDetail", "AddCutting", "EditCutting", "AddDailySewingRecord", "ViewDailySewingHistory", "ViewProductList", "ApproveFinishedProduct", "ViewApproveProduct", "AddPackingSession", "ViewPackingSessions", "ViewPackingInventory", "ViewPackingInventorySales", "SellProductPage", "PackingReportChart", "AddShop", "CreateOrder", "OrderListPage", "OwnerOrdersPage", "SalesTeamOrdersPage", "ShopAnalysisDashboard", "OrderAnalysisPage", "ViewShops", "SalesReport", "ProtectedRoute", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element", "allowedRoles", "to", "replace"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Route, Routes, Navigate } from \"react-router-dom\";\r\nimport Signup from \"./pages/Signup\";\r\nimport Login from \"./pages/Login\";\r\nimport OwnerDashboard from \"./pages/OwnerDashboard\";\r\nimport InventoryDashboard from \"./pages/InventoryDashboard\";\r\nimport OrdersDashboard from \"./pages/OrdersDashboard\";\r\nimport SalesDashboard from \"./pages/SalesDashboard\";\r\nimport SalesProductView from \"./pages/SalesProductView\";\r\nimport SalesProductImageViewer from \"./pages/SalesProductImageViewer\";\r\nimport AddSupplier from \"./pages/AddSupplier\";\r\nimport ViewSuppliers from \"./pages/ViewSuppliers\";\r\nimport AddFabric from \"./pages/AddFabric\";\r\n\r\nimport EditFabric from \"./pages/EditFabric\";\r\nimport ViewFabrics from \"./pages/ViewFabrics\";\r\nimport ViewCutting from \"./pages/ViewCutting.js\";\r\nimport ViewFabricVariants from \"./pages/ViewFabricVariants\";\r\nimport FabricInventoryDetail from \"./pages/FabricInventoryDetail\";\r\nimport CuttingRecordDetail from \"./pages/CuttingRecordDetail\";\r\nimport AddCutting from \"./pages/AddCutting.js\"\r\nimport EditCutting from \"./pages/EditCutting.js\"\r\nimport AddDailySewingRecord from \"./pages/AddDailySewingRecord\";\r\nimport ViewDailySewingHistory from './pages/ViewDailySewingHistory';\r\nimport ViewProductList from './pages/ViewProductList.js';\r\nimport ApproveFinishedProduct from \"./pages/ApproveFinishedProduct\";\r\nimport ViewApproveProduct from \"./pages/ViewApproveProduct.js\";\r\nimport AddPackingSession from \"./pages/AddPackingSession.js\";\r\nimport ViewPackingSessions from \"./pages/ViewPackingSessions.js\";\r\nimport ViewPackingInventory from \"./pages/ViewPackingInventory.js\";\r\nimport ViewPackingInventorySales from \"./pages/ViewPackingInventorySales.js\";\r\nimport SellProductPage from \"./pages/SellProductPage.js\";\r\nimport PackingReportChart from \"./pages/PackingReportChart.js\";\r\nimport AddShop from \"./pages/AddShop.js\";\r\nimport CreateOrder from \"./pages/CreateOrder.js\";\r\nimport OrderListPage from \"./pages/OrderListPage.js.js\";\r\nimport OwnerOrdersPage from \"./pages/OwnerOrdersPage.js\";\r\nimport SalesTeamOrdersPage from \"./pages/SalesTeamOrdersPage.js\";\r\nimport ShopAnalysisDashboard from \"./pages/ShopAnalysisDashboard.js\";\r\nimport OrderAnalysisPage from \"./pages/OrderAnalysisPage.js\";\r\nimport ViewShops from \"./pages/ViewShops.js\";\r\nimport SalesReport from \"./pages/SalesReport.js\";\r\n\r\n// Import the ProtectedRoute component\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\n\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Routes>\r\n        {/* Public routes - accessible without login */}\r\n        <Route path=\"/signup\" element={<Signup />} />\r\n        <Route path=\"\" element={<Login />} />\r\n\r\n        {/* Owner routes */}\r\n        <Route path=\"/owner-dashboard\" element={\r\n          <ProtectedRoute allowedRoles={['Owner']}>\r\n            <OwnerDashboard />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/owner-orders\" element={\r\n          <ProtectedRoute allowedRoles={['Owner']}>\r\n            <OwnerOrdersPage />\r\n          </ProtectedRoute>\r\n        } />\r\n\r\n        {/* Inventory Manager routes */}\r\n        <Route path=\"/inventory-dashboard\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <InventoryDashboard />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/addsupplier\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <AddSupplier />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/viewsuppliers\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewSuppliers />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/addfabric\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <AddFabric />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/edit-fabric/:id\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <EditFabric />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/viewfabric\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewFabrics />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/fabric-definitions/:id\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewFabricVariants />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/fabric-inventory/:variantId\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <FabricInventoryDetail />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/viewcutting\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewCutting />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/cutting-record/:recordId\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <CuttingRecordDetail />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/addcutting\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <AddCutting />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/edit-cutting/:id\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <EditCutting />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/adddailysewing\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <AddDailySewingRecord />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/daily-sewing-history\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewDailySewingHistory />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/viewproductlist\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewProductList />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/approve-finished-product/:id\" element={\r\n          <ProtectedRoute allowedRoles={['Owner']}>\r\n            <ApproveFinishedProduct />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/approveproduct-list\" element={\r\n          <ProtectedRoute allowedRoles={['Owner']}>\r\n            <ViewApproveProduct />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/add-packing-session\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager']}>\r\n            <AddPackingSession />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/view-packing-sessions\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner']}>\r\n            <ViewPackingSessions />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/view-packing-inventory\" element={\r\n          <ProtectedRoute allowedRoles={['Inventory Manager', 'Owner', 'Sales Team', 'Order Coordinator']}>\r\n            <ViewPackingInventory />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/packing-report\" element={\r\n          <ProtectedRoute allowedRoles={['Owner', 'Inventory Manager']}>\r\n            <PackingReportChart />\r\n          </ProtectedRoute>\r\n        } />\r\n\r\n        {/* Order Coordinator routes */}\r\n        <Route path=\"/orders-dashboard\" element={\r\n          <ProtectedRoute allowedRoles={['Order Coordinator']}>\r\n            <OrdersDashboard />\r\n          </ProtectedRoute>\r\n        } />\r\n\r\n        {/* Sales Team routes */}\r\n        <Route path=\"/sales-dashboard\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <SalesDashboard />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/sales-products\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <SalesProductView />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/sales-product-gallery\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <SalesProductImageViewer />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/sales-packing-inventory\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <ViewPackingInventorySales />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/sell-product\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <SellProductPage />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/sales-team-orders\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <SalesTeamOrdersPage />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/viewshops\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team']}>\r\n            <ViewShops />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/sales-report\" element={\r\n          <ProtectedRoute allowedRoles={['Sales Team', 'Inventory Manager', 'Owner']}>\r\n            <SalesReport />\r\n          </ProtectedRoute>\r\n        } />\r\n\r\n        {/* Shared routes */}\r\n        <Route path=\"/addshop\" element={\r\n          <ProtectedRoute allowedRoles={['Owner', 'Sales Team']}>\r\n            <AddShop />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/shop-analysis\" element={\r\n          <ProtectedRoute allowedRoles={['Owner', 'Sales Team']}>\r\n            <ShopAnalysisDashboard />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/addorder\" element={\r\n          <ProtectedRoute allowedRoles={['Order Coordinator', 'Sales Team']}>\r\n            <CreateOrder />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/order-list\" element={\r\n          <ProtectedRoute allowedRoles={['Order Coordinator', 'Sales Team', 'Owner']}>\r\n            <OrderListPage />\r\n          </ProtectedRoute>\r\n        } />\r\n        <Route path=\"/order-analysis\" element={\r\n          <ProtectedRoute allowedRoles={['Owner', 'Sales Team']}>\r\n            <OrderAnalysisPage />\r\n          </ProtectedRoute>\r\n        } />\r\n\r\n        {/* Catch-all route for invalid URLs - redirect to login or dashboard based on auth status */}\r\n        <Route path=\"*\" element={\r\n          <ProtectedRoute>\r\n            {/* This will redirect to the appropriate dashboard based on user role */}\r\n            {/* If not logged in, the ProtectedRoute will redirect to login */}\r\n            <Navigate to=\"/\" replace />\r\n          </ProtectedRoute>\r\n        } />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": "AAAA,OAASA,aAAa,GAAI,CAAAC,MAAM,CAAEC,KAAK,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,MAAO,CAAAC,MAAM,KAAM,gBAAgB,CACnC,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,kBAAkB,KAAM,4BAA4B,CAC3D,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,uBAAuB,KAAM,iCAAiC,CACrE,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CAEzC,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,WAAW,KAAM,wBAAwB,CAChD,MAAO,CAAAC,kBAAkB,KAAM,4BAA4B,CAC3D,MAAO,CAAAC,qBAAqB,KAAM,+BAA+B,CACjE,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,UAAU,KAAM,uBAAuB,CAC9C,MAAO,CAAAC,WAAW,KAAM,wBAAwB,CAChD,MAAO,CAAAC,oBAAoB,KAAM,8BAA8B,CAC/D,MAAO,CAAAC,sBAAsB,KAAM,gCAAgC,CACnE,MAAO,CAAAC,eAAe,KAAM,4BAA4B,CACxD,MAAO,CAAAC,sBAAsB,KAAM,gCAAgC,CACnE,MAAO,CAAAC,kBAAkB,KAAM,+BAA+B,CAC9D,MAAO,CAAAC,iBAAiB,KAAM,8BAA8B,CAC5D,MAAO,CAAAC,mBAAmB,KAAM,gCAAgC,CAChE,MAAO,CAAAC,oBAAoB,KAAM,iCAAiC,CAClE,MAAO,CAAAC,yBAAyB,KAAM,sCAAsC,CAC5E,MAAO,CAAAC,eAAe,KAAM,4BAA4B,CACxD,MAAO,CAAAC,kBAAkB,KAAM,+BAA+B,CAC9D,MAAO,CAAAC,OAAO,KAAM,oBAAoB,CACxC,MAAO,CAAAC,WAAW,KAAM,wBAAwB,CAChD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,4BAA4B,CACxD,MAAO,CAAAC,mBAAmB,KAAM,gCAAgC,CAChE,MAAO,CAAAC,qBAAqB,KAAM,kCAAkC,CACpE,MAAO,CAAAC,iBAAiB,KAAM,8BAA8B,CAC5D,MAAO,CAAAC,SAAS,KAAM,sBAAsB,CAC5C,MAAO,CAAAC,WAAW,KAAM,wBAAwB,CAEhD;AACA,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGzD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAAC7C,MAAM,EAAAiD,QAAA,cACLF,KAAA,CAAC7C,MAAM,EAAA+C,QAAA,eAELJ,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEN,IAAA,CAACzC,MAAM,GAAE,CAAE,CAAE,CAAC,cAC7CyC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,EAAE,CAACC,OAAO,cAAEN,IAAA,CAACxC,KAAK,GAAE,CAAE,CAAE,CAAC,cAGrCwC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAH,QAAA,cACtCJ,IAAA,CAACvC,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJuC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,eAAe,CAACC,OAAO,cACjCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAH,QAAA,cACtCJ,IAAA,CAACR,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cAGJQ,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAACtC,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJsC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,cAAc,CAACC,OAAO,cAChCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAACjC,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJiC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAClCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAAChC,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJgC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,YAAY,CAACC,OAAO,cAC9BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAAC/B,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cACJ+B,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAAC9B,UAAU,GAAE,CAAC,CACA,CACjB,CAAE,CAAC,cACJ8B,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,aAAa,CAACC,OAAO,cAC/BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAAC7B,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJ6B,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAC3CN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAAC3B,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJ2B,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,8BAA8B,CAACC,OAAO,cAChDN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAAC1B,qBAAqB,GAAE,CAAC,CACX,CACjB,CAAE,CAAC,cACJ0B,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,cAAc,CAACC,OAAO,cAChCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAAC5B,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJ4B,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,2BAA2B,CAACC,OAAO,cAC7CN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAACzB,mBAAmB,GAAE,CAAC,CACT,CACjB,CAAE,CAAC,cACJyB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,aAAa,CAACC,OAAO,cAC/BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAACxB,UAAU,GAAE,CAAC,CACA,CACjB,CAAE,CAAC,cACJwB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAACvB,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJuB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAACtB,oBAAoB,GAAE,CAAC,CACV,CACjB,CAAE,CAAC,cACJsB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAACrB,sBAAsB,GAAE,CAAC,CACZ,CACjB,CAAE,CAAC,cACJqB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAACpB,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJoB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,+BAA+B,CAACC,OAAO,cACjDN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAH,QAAA,cACtCJ,IAAA,CAACnB,sBAAsB,GAAE,CAAC,CACZ,CACjB,CAAE,CAAC,cACJmB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,CAAAH,QAAA,cACtCJ,IAAA,CAAClB,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJkB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAACjB,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJiB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAC1CN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAAChB,mBAAmB,GAAE,CAAC,CACT,CACjB,CAAE,CAAC,cACJgB,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAC3CN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,OAAO,CAAE,YAAY,CAAE,mBAAmB,CAAE,CAAAH,QAAA,cAC9FJ,IAAA,CAACf,oBAAoB,GAAE,CAAC,CACV,CACjB,CAAE,CAAC,cACJe,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,mBAAmB,CAAE,CAAAH,QAAA,cAC3DJ,IAAA,CAACZ,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cAGJY,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,CAAAH,QAAA,cAClDJ,IAAA,CAACrC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cAGJqC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAACpC,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJoC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAACnC,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJmC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAC1CN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAAClC,uBAAuB,GAAE,CAAC,CACb,CACjB,CAAE,CAAC,cACJkC,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,0BAA0B,CAACC,OAAO,cAC5CN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAACd,yBAAyB,GAAE,CAAC,CACf,CACjB,CAAE,CAAC,cACJc,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,eAAe,CAACC,OAAO,cACjCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAACb,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJa,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,oBAAoB,CAACC,OAAO,cACtCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAACP,mBAAmB,GAAE,CAAC,CACT,CACjB,CAAE,CAAC,cACJO,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,YAAY,CAACC,OAAO,cAC9BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,CAAAH,QAAA,cAC3CJ,IAAA,CAACJ,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cACJI,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,eAAe,CAACC,OAAO,cACjCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,YAAY,CAAE,mBAAmB,CAAE,OAAO,CAAE,CAAAH,QAAA,cACzEJ,IAAA,CAACH,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cAGJG,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,UAAU,CAACC,OAAO,cAC5BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,YAAY,CAAE,CAAAH,QAAA,cACpDJ,IAAA,CAACX,OAAO,GAAE,CAAC,CACG,CACjB,CAAE,CAAC,cACJW,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAClCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,YAAY,CAAE,CAAAH,QAAA,cACpDJ,IAAA,CAACN,qBAAqB,GAAE,CAAC,CACX,CACjB,CAAE,CAAC,cACJM,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,WAAW,CAACC,OAAO,cAC7BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,YAAY,CAAE,CAAAH,QAAA,cAChEJ,IAAA,CAACV,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJU,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,aAAa,CAACC,OAAO,cAC/BN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,mBAAmB,CAAE,YAAY,CAAE,OAAO,CAAE,CAAAH,QAAA,cACzEJ,IAAA,CAACT,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJS,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnCN,IAAA,CAACF,cAAc,EAACS,YAAY,CAAE,CAAC,OAAO,CAAE,YAAY,CAAE,CAAAH,QAAA,cACpDJ,IAAA,CAACL,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cAGJK,IAAA,CAAC5C,KAAK,EAACiD,IAAI,CAAC,GAAG,CAACC,OAAO,cACrBN,IAAA,CAACF,cAAc,EAAAM,QAAA,cAGbJ,IAAA,CAAC1C,QAAQ,EAACkD,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACb,CACjB,CAAE,CAAC,EACE,CAAC,CACH,CAAC,CAEb,CAEA,cAAe,CAAAN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}