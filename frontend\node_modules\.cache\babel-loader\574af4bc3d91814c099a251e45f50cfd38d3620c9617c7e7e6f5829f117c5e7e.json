{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{useParams,useNavigate}from'react-router-dom';import Select from'react-select';import RoleBasedNavBar from\"../components/RoleBasedNavBar\";import ColorVariantSelector from\"../components/ColorVariantSelector\";import{Card,Form,Button,Row,Col,Spinner,Alert,Container,Badge,Modal}from'react-bootstrap';import{BsScissors,BsPlus,BsTrash,BsCheck2Circle,BsExclamationTriangle,BsArrowLeft}from'react-icons/bs';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const EditCutting=()=>{const{id}=useParams();const navigate=useNavigate();// Overall cutting record fields\nconst[allFabricVariants,setAllFabricVariants]=useState([]);const[cuttingDate,setCuttingDate]=useState('');const[description,setDescription]=useState('');const[productName,setProductName]=useState('');// Cutting detail rows\nconst[details,setDetails]=useState([{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]);// Original yard usage for each variant (to calculate available yards)\nconst[originalYardUsage,setOriginalYardUsage]=useState({});// Validation errors for each detail row\nconst[detailErrors,setDetailErrors]=useState([]);// Loading, error, success states\nconst[loadingVariants,setLoadingVariants]=useState(true);const[loadingRecord,setLoadingRecord]=useState(true);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[isSubmitting,setIsSubmitting]=useState(false);const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[validated,setValidated]=useState(false);const[originalRecord,setOriginalRecord]=useState(null);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// 1. Fetch the cutting record to edit\nuseEffect(()=>{setLoadingRecord(true);console.log(`Fetching cutting record with ID: ${id}`);axios.get(`http://localhost:8000/api/cutting/cutting-records/${id}/`).then(res=>{const record=res.data;console.log('Cutting record fetched successfully:',record);setOriginalRecord(record);// Set form fields with record data\nsetCuttingDate(record.cutting_date);setDescription(record.description||'');setProductName(record.product_name||'');// Set details and store original yard usage\nif(record.details&&record.details.length>0){console.log('Setting details from record:',record.details);// Create a map of original yard usage by variant ID\nconst yardUsageMap={};record.details.forEach(detail=>{yardUsageMap[detail.fabric_variant]=parseFloat(detail.yard_usage);});console.log('Original yard usage map:',yardUsageMap);setOriginalYardUsage(yardUsageMap);// Initialize detail errors array with empty strings\nsetDetailErrors(Array(record.details.length).fill(''));// Set details\nsetDetails(record.details.map(detail=>({id:detail.id,// Keep the original ID for updating\nfabric_variant:detail.fabric_variant,yard_usage:detail.yard_usage,xs:detail.xs||0,s:detail.s||0,m:detail.m||0,l:detail.l||0,xl:detail.xl||0})));}setLoadingRecord(false);}).catch(err=>{console.error('Error fetching cutting record:',err);setError('Failed to load cutting record. Please try again.');setLoadingRecord(false);});},[id]);// 2. Fetch all fabric variants on mount\nuseEffect(()=>{setLoadingVariants(true);axios.get(\"http://localhost:8000/api/fabric-variants/\").then(res=>{console.log('Fabric variants fetched successfully:',res.data);setAllFabricVariants(res.data);setLoadingVariants(false);}).catch(err=>{console.error('Error fetching fabric variants:',err);setError('Failed to load fabric variants. Please try again.');setLoadingVariants(false);});},[]);// 3. Validate yard usage when variants or details change\nuseEffect(()=>{if(allFabricVariants.length>0&&details.length>0){// Create a new array for detail errors\nconst newDetailErrors=[...detailErrors];// Validate each detail\ndetails.forEach((detail,index)=>{if(detail.fabric_variant&&detail.yard_usage){const variantId=detail.fabric_variant;// Find the variant in the allFabricVariants array\nconst variant=allFabricVariants.find(v=>v.id===parseInt(variantId));if(!variant)return;// Get the original yard usage for this variant (or 0 if it's a new detail)\nconst original=originalYardUsage[variantId]||0;// Calculate the maximum allowed yard usage\nconst maxAllowed=parseFloat(variant.available_yard)+original;// Check if the yard usage exceeds the maximum allowed\nif(parseFloat(detail.yard_usage)>maxAllowed){newDetailErrors[index]=`Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;}else if(parseFloat(detail.yard_usage)<=0){newDetailErrors[index]='Yard usage must be greater than 0';}else{newDetailErrors[index]='';}}});// Update detail errors state\nsetDetailErrors(newDetailErrors);}},[allFabricVariants,details,originalYardUsage]);// Add a new empty detail row\nconst addDetailRow=()=>{setDetails([...details,{fabric_variant:'',yard_usage:'',xs:0,s:0,m:0,l:0,xl:0}]);setDetailErrors([...detailErrors,'']);// Add an empty error for the new row\n};// Delete a detail row\nconst removeDetailRow=index=>{const newDetails=details.filter((_,i)=>i!==index);setDetails(newDetails);// Also remove the corresponding error\nconst newDetailErrors=detailErrors.filter((_,i)=>i!==index);setDetailErrors(newDetailErrors);};// Handle change for each detail row field\nconst handleDetailChange=(index,field,value)=>{const newDetails=[...details];newDetails[index][field]=value;setDetails(newDetails);// Validate yard usage if that's the field being changed\nif(field==='yard_usage'){validateYardUsage(index,value);}};// Validate yard usage against available yards\nconst validateYardUsage=(index,newYardUsage)=>{const newDetailErrors=[...detailErrors];const detail=details[index];const variantId=detail.fabric_variant;// Skip validation if no variant is selected\nif(!variantId){newDetailErrors[index]='';setDetailErrors(newDetailErrors);return;}// Find the variant in the allFabricVariants array\nconst variant=allFabricVariants.find(v=>v.id===parseInt(variantId));if(!variant){newDetailErrors[index]='';setDetailErrors(newDetailErrors);return;}// Get the original yard usage for this variant (or 0 if it's a new detail)\nconst original=originalYardUsage[variantId]||0;// Calculate the maximum allowed yard usage\n// This is the current available yards plus the original yard usage\nconst maxAllowed=parseFloat(variant.available_yard)+original;// Check if the new yard usage exceeds the maximum allowed\nif(parseFloat(newYardUsage)>maxAllowed){newDetailErrors[index]=`Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;}else if(parseFloat(newYardUsage)<=0){newDetailErrors[index]='Yard usage must be greater than 0';}else{newDetailErrors[index]='';}setDetailErrors(newDetailErrors);};// Handle form submission\nconst handleSubmit=async e=>{e.preventDefault();// Form validation\nconst form=e.currentTarget;if(form.checkValidity()===false){e.stopPropagation();setValidated(true);return;}// Check if any detail has a fabric variant selected\nconst hasValidDetails=details.some(detail=>detail.fabric_variant);if(!hasValidDetails){setError('Please select at least one fabric variant for your cutting details.');return;}// Validate all yard usage values\nlet hasYardUsageErrors=false;details.forEach((detail,index)=>{validateYardUsage(index,detail.yard_usage);if(detailErrors[index]){hasYardUsageErrors=true;}});// Check if there are any yard usage validation errors\nif(hasYardUsageErrors){setError('Please fix the yard usage errors before submitting.');return;}setValidated(true);setIsSubmitting(true);setError('');setSuccess('');try{const payload={cutting_date:cuttingDate,description:description,product_name:productName,details:details};const response=await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`,payload);setSuccess('Cutting record updated successfully!');// Redirect back to the cutting records list after a short delay\nsetTimeout(()=>{navigate('/viewcutting');},2000);}catch(err){console.error('Error updating cutting record:',err);if(err.response&&err.response.data){// Display more specific error message if available\nconst errorMessage=typeof err.response.data==='string'?err.response.data:'Failed to update cutting record. Please check your inputs.';setError(errorMessage);}else{setError('Failed to update cutting record. Please try again.');}}finally{setIsSubmitting(false);}};// Custom option component that shows a color swatch + label\nconst ColourOption=_ref=>{let{data,innerRef,innerProps}=_ref;return/*#__PURE__*/_jsxs(\"div\",{ref:innerRef,...innerProps,style:{display:'flex',alignItems:'center',padding:'4px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:16,height:16,backgroundColor:data.color,marginRight:8,border:'1px solid #ccc'}}),/*#__PURE__*/_jsx(\"span\",{children:data.label})]});};// Calculate total quantities for all details\nconst totalQuantities=details.reduce((acc,detail)=>{acc.xs+=parseInt(detail.xs)||0;acc.s+=parseInt(detail.s)||0;acc.m+=parseInt(detail.m)||0;acc.l+=parseInt(detail.l)||0;acc.xl+=parseInt(detail.xl)||0;acc.total+=(parseInt(detail.xs)||0)+(parseInt(detail.s)||0)+(parseInt(detail.m)||0)+(parseInt(detail.l)||0)+(parseInt(detail.xl)||0);acc.yard_usage+=parseFloat(detail.yard_usage)||0;return acc;},{xs:0,s:0,m:0,l:0,xl:0,total:0,yard_usage:0});if(loadingRecord){return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsx(Container,{fluid:true,style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center my-5\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",variant:\"primary\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:\"Loading cutting record...\"})]})})]});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(\"div\",{style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center mb-4\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",className:\"me-3\",onClick:()=>navigate('/viewcutting'),children:[/*#__PURE__*/_jsx(BsArrowLeft,{className:\"me-1\"}),\" Back\"]}),/*#__PURE__*/_jsxs(\"h2\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(BsScissors,{className:\"me-2\"}),\"Edit Cutting Record\"]})]}),success&&/*#__PURE__*/_jsxs(Alert,{variant:\"success\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsCheck2Circle,{className:\"me-2\",size:20}),success]}),error&&/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(BsExclamationTriangle,{className:\"me-2\",size:20}),error]}),/*#__PURE__*/_jsx(Card,{className:\"mb-4 shadow-sm\",style:{backgroundColor:\"#D9EDFB\",borderRadius:\"10px\"},children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{noValidate:true,validated:validated,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Product Name\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",value:productName,onChange:e=>setProductName(e.target.value),placeholder:\"Enter product name\",required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please provide a product name.\"})]})})}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"date\",value:cuttingDate,onChange:e=>setCuttingDate(e.target.value),required:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please select a cutting date.\"})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Description\"})}),/*#__PURE__*/_jsx(Form.Control,{as:\"textarea\",rows:3,value:description,onChange:e=>setDescription(e.target.value),placeholder:\"Enter details about this cutting record...\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:\"Fabric Details\"}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",size:\"sm\",onClick:addDetailRow,disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Fabric Variant\"]})]}),details.map((detail,index)=>{// Find the selected variant object to set the value in React-Select\nconst currentVariant=allFabricVariants.find(v=>v.id===detail.fabric_variant);return/*#__PURE__*/_jsx(Card,{className:\"mb-3 border-light\",children:/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{className:\"align-items-end\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric Variant (Color)\"})}),loadingVariants?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading variants...\"})]}):allFabricVariants.length===0?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Form.Select,{disabled:true,children:/*#__PURE__*/_jsx(\"option\",{children:\"No variants available\"})}),/*#__PURE__*/_jsx(\"small\",{className:\"text-danger\",children:\"No fabric variants found.\"})]}):/*#__PURE__*/_jsx(ColorVariantSelector,{variants:allFabricVariants,selectedValue:detail.fabric_variant,onSelect:value=>handleDetailChange(index,'fabric_variant',value),placeholder:\"Select Fabric Variant\",disabled:isSubmitting,showFabricName:true}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:\"Please select a fabric variant.\"})]})}),/*#__PURE__*/_jsx(Col,{md:2,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Yard Usage\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",step:\"0.01\",min:\"0.01\",value:detail.yard_usage,onChange:e=>handleDetailChange(index,'yard_usage',e.target.value),required:true,disabled:isSubmitting,isInvalid:!!detailErrors[index],className:detailErrors[index]?'border-danger':''}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:detailErrors[index]||'Please enter valid yard usage.'}),currentVariant&&/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[\"Available: \",parseFloat(currentVariant.available_yard)+(originalYardUsage[detail.fabric_variant]||0),\" yards (Original: \",originalYardUsage[detail.fabric_variant]||0,\" yards + Current: \",currentVariant.available_yard,\" yards)\"]})]})}),/*#__PURE__*/_jsx(Col,{md:5,children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"XS\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.xs,onChange:e=>handleDetailChange(index,'xs',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"S\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.s,onChange:e=>handleDetailChange(index,'s',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"M\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.m,onChange:e=>handleDetailChange(index,'m',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"L\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.l,onChange:e=>handleDetailChange(index,'l',e.target.value),disabled:isSubmitting})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"XL\"})}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"0\",value:detail.xl,onChange:e=>handleDetailChange(index,'xl',e.target.value),disabled:isSubmitting})]})})]})}),/*#__PURE__*/_jsx(Col,{md:1,className:\"d-flex align-items-center justify-content-end\",children:/*#__PURE__*/_jsx(Button,{variant:\"outline-danger\",onClick:()=>removeDetailRow(index),disabled:details.length===1||isSubmitting,className:\"mt-2\",children:/*#__PURE__*/_jsx(BsTrash,{})})})]})})},index);}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between mb-4\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-primary\",onClick:addDetailRow,disabled:isSubmitting,children:[/*#__PURE__*/_jsx(BsPlus,{className:\"me-1\"}),\" Add Another Variant\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Badge,{bg:\"info\",className:\"me-2 p-2\",children:[\"Total Pieces: \",totalQuantities.total]}),/*#__PURE__*/_jsxs(Badge,{bg:\"warning\",text:\"dark\",className:\"p-2\",children:[\"Total Yard Usage: \",totalQuantities.yard_usage.toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-end\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",className:\"me-2\",onClick:()=>navigate('/viewcutting'),disabled:isSubmitting,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",type:\"submit\",disabled:isSubmitting,children:isSubmitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Updating...\"]}):'Update Cutting Record'})]})]})})})]})]});};export default EditCutting;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "Select", "RoleBasedNavBar", "ColorVariantSelector", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Container", "Badge", "Modal", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsArrowLeft", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "EditCutting", "id", "navigate", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "details", "setDetails", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "originalYardUsage", "setOriginalYardUsage", "detailErrors", "setDetailErrors", "loadingVariants", "setLoadingVariants", "loadingRecord", "setLoadingRecord", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "originalRecord", "setOriginalRecord", "handleResize", "addEventListener", "removeEventListener", "console", "log", "get", "then", "res", "record", "data", "cutting_date", "product_name", "length", "yardUsageMap", "for<PERSON>ach", "detail", "parseFloat", "Array", "fill", "map", "catch", "err", "newDetailErrors", "index", "variantId", "variant", "find", "v", "parseInt", "original", "maxAllowed", "available_yard", "toFixed", "addDetailRow", "removeDetailRow", "newDetails", "filter", "_", "i", "handleDetailChange", "field", "value", "validateYardUsage", "newYardUsage", "handleSubmit", "e", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidDetails", "some", "hasYardUsageErrors", "payload", "response", "put", "setTimeout", "errorMessage", "ColourOption", "_ref", "innerRef", "innerProps", "ref", "style", "display", "alignItems", "padding", "children", "width", "height", "backgroundColor", "color", "marginRight", "border", "label", "totalQuantities", "reduce", "acc", "total", "fluid", "marginLeft", "transition", "className", "animation", "role", "onClick", "size", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "disabled", "currentV<PERSON>t", "variants", "selected<PERSON><PERSON><PERSON>", "onSelect", "showFabricName", "step", "min", "isInvalid", "bg", "text"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/EditCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport Select from 'react-select';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport ColorVariantSelector from \"../components/ColorVariantSelector\";\r\nimport { Card, Form, Button, Row, Col, Spinner, Alert, Container, Badge, Modal } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsArrowLeft } from 'react-icons/bs';\r\n\r\nconst EditCutting = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  // Overall cutting record fields\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n\r\n  // Cutting detail rows\r\n  const [details, setDetails] = useState([\r\n    { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }\r\n  ]);\r\n\r\n  // Original yard usage for each variant (to calculate available yards)\r\n  const [originalYardUsage, setOriginalYardUsage] = useState({});\r\n\r\n  // Validation errors for each detail row\r\n  const [detailErrors, setDetailErrors] = useState([]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [loadingRecord, setLoadingRecord] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [originalRecord, setOriginalRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // 1. Fetch the cutting record to edit\r\n  useEffect(() => {\r\n    setLoadingRecord(true);\r\n    console.log(`Fetching cutting record with ID: ${id}`);\r\n\r\n    axios.get(`http://localhost:8000/api/cutting/cutting-records/${id}/`)\r\n      .then((res) => {\r\n        const record = res.data;\r\n        console.log('Cutting record fetched successfully:', record);\r\n        setOriginalRecord(record);\r\n\r\n        // Set form fields with record data\r\n        setCuttingDate(record.cutting_date);\r\n        setDescription(record.description || '');\r\n        setProductName(record.product_name || '');\r\n\r\n        // Set details and store original yard usage\r\n        if (record.details && record.details.length > 0) {\r\n          console.log('Setting details from record:', record.details);\r\n\r\n          // Create a map of original yard usage by variant ID\r\n          const yardUsageMap = {};\r\n          record.details.forEach(detail => {\r\n            yardUsageMap[detail.fabric_variant] = parseFloat(detail.yard_usage);\r\n          });\r\n          console.log('Original yard usage map:', yardUsageMap);\r\n          setOriginalYardUsage(yardUsageMap);\r\n\r\n          // Initialize detail errors array with empty strings\r\n          setDetailErrors(Array(record.details.length).fill(''));\r\n\r\n          // Set details\r\n          setDetails(record.details.map(detail => ({\r\n            id: detail.id, // Keep the original ID for updating\r\n            fabric_variant: detail.fabric_variant,\r\n            yard_usage: detail.yard_usage,\r\n            xs: detail.xs || 0,\r\n            s: detail.s || 0,\r\n            m: detail.m || 0,\r\n            l: detail.l || 0,\r\n            xl: detail.xl || 0\r\n          })));\r\n        }\r\n\r\n        setLoadingRecord(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching cutting record:', err);\r\n        setError('Failed to load cutting record. Please try again.');\r\n        setLoadingRecord(false);\r\n      });\r\n  }, [id]);\r\n\r\n  // 2. Fetch all fabric variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n    axios.get(\"http://localhost:8000/api/fabric-variants/\")\r\n      .then((res) => {\r\n        console.log('Fabric variants fetched successfully:', res.data);\r\n        setAllFabricVariants(res.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric variants:', err);\r\n        setError('Failed to load fabric variants. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n\r\n\r\n  // 3. Validate yard usage when variants or details change\r\n  useEffect(() => {\r\n    if (allFabricVariants.length > 0 && details.length > 0) {\r\n      // Create a new array for detail errors\r\n      const newDetailErrors = [...detailErrors];\r\n\r\n      // Validate each detail\r\n      details.forEach((detail, index) => {\r\n        if (detail.fabric_variant && detail.yard_usage) {\r\n          const variantId = detail.fabric_variant;\r\n\r\n          // Find the variant in the allFabricVariants array\r\n          const variant = allFabricVariants.find(v => v.id === parseInt(variantId));\r\n          if (!variant) return;\r\n\r\n          // Get the original yard usage for this variant (or 0 if it's a new detail)\r\n          const original = originalYardUsage[variantId] || 0;\r\n\r\n          // Calculate the maximum allowed yard usage\r\n          const maxAllowed = parseFloat(variant.available_yard) + original;\r\n\r\n          // Check if the yard usage exceeds the maximum allowed\r\n          if (parseFloat(detail.yard_usage) > maxAllowed) {\r\n            newDetailErrors[index] = `Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;\r\n          } else if (parseFloat(detail.yard_usage) <= 0) {\r\n            newDetailErrors[index] = 'Yard usage must be greater than 0';\r\n          } else {\r\n            newDetailErrors[index] = '';\r\n          }\r\n        }\r\n      });\r\n\r\n      // Update detail errors state\r\n      setDetailErrors(newDetailErrors);\r\n    }\r\n  }, [allFabricVariants, details, originalYardUsage]);\r\n\r\n\r\n\r\n  // Add a new empty detail row\r\n  const addDetailRow = () => {\r\n    setDetails([...details, { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n    setDetailErrors([...detailErrors, '']); // Add an empty error for the new row\r\n  };\r\n\r\n  // Delete a detail row\r\n  const removeDetailRow = (index) => {\r\n    const newDetails = details.filter((_, i) => i !== index);\r\n    setDetails(newDetails);\r\n\r\n    // Also remove the corresponding error\r\n    const newDetailErrors = detailErrors.filter((_, i) => i !== index);\r\n    setDetailErrors(newDetailErrors);\r\n  };\r\n\r\n  // Handle change for each detail row field\r\n  const handleDetailChange = (index, field, value) => {\r\n    const newDetails = [...details];\r\n    newDetails[index][field] = value;\r\n    setDetails(newDetails);\r\n\r\n    // Validate yard usage if that's the field being changed\r\n    if (field === 'yard_usage') {\r\n      validateYardUsage(index, value);\r\n    }\r\n  };\r\n\r\n  // Validate yard usage against available yards\r\n  const validateYardUsage = (index, newYardUsage) => {\r\n    const newDetailErrors = [...detailErrors];\r\n    const detail = details[index];\r\n    const variantId = detail.fabric_variant;\r\n\r\n    // Skip validation if no variant is selected\r\n    if (!variantId) {\r\n      newDetailErrors[index] = '';\r\n      setDetailErrors(newDetailErrors);\r\n      return;\r\n    }\r\n\r\n    // Find the variant in the allFabricVariants array\r\n    const variant = allFabricVariants.find(v => v.id === parseInt(variantId));\r\n    if (!variant) {\r\n      newDetailErrors[index] = '';\r\n      setDetailErrors(newDetailErrors);\r\n      return;\r\n    }\r\n\r\n    // Get the original yard usage for this variant (or 0 if it's a new detail)\r\n    const original = originalYardUsage[variantId] || 0;\r\n\r\n    // Calculate the maximum allowed yard usage\r\n    // This is the current available yards plus the original yard usage\r\n    const maxAllowed = parseFloat(variant.available_yard) + original;\r\n\r\n    // Check if the new yard usage exceeds the maximum allowed\r\n    if (parseFloat(newYardUsage) > maxAllowed) {\r\n      newDetailErrors[index] = `Exceeds available yards. Maximum allowed: ${maxAllowed.toFixed(2)} yards`;\r\n    } else if (parseFloat(newYardUsage) <= 0) {\r\n      newDetailErrors[index] = 'Yard usage must be greater than 0';\r\n    } else {\r\n      newDetailErrors[index] = '';\r\n    }\r\n\r\n    setDetailErrors(newDetailErrors);\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any detail has a fabric variant selected\r\n    const hasValidDetails = details.some(detail => detail.fabric_variant);\r\n    if (!hasValidDetails) {\r\n      setError('Please select at least one fabric variant for your cutting details.');\r\n      return;\r\n    }\r\n\r\n    // Validate all yard usage values\r\n    let hasYardUsageErrors = false;\r\n    details.forEach((detail, index) => {\r\n      validateYardUsage(index, detail.yard_usage);\r\n      if (detailErrors[index]) {\r\n        hasYardUsageErrors = true;\r\n      }\r\n    });\r\n\r\n    // Check if there are any yard usage validation errors\r\n    if (hasYardUsageErrors) {\r\n      setError('Please fix the yard usage errors before submitting.');\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      const payload = {\r\n        cutting_date: cuttingDate,\r\n        description: description,\r\n        product_name: productName,\r\n        details: details\r\n      };\r\n\r\n      const response = await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\r\n      setSuccess('Cutting record updated successfully!');\r\n\r\n      // Redirect back to the cutting records list after a short delay\r\n      setTimeout(() => {\r\n        navigate('/viewcutting');\r\n      }, 2000);\r\n    } catch (err) {\r\n      console.error('Error updating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to update cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to update cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Custom option component that shows a color swatch + label\r\n  const ColourOption = ({ data, innerRef, innerProps }) => (\r\n    <div\r\n      ref={innerRef}\r\n      {...innerProps}\r\n      style={{ display: 'flex', alignItems: 'center', padding: '4px' }}\r\n    >\r\n      <div\r\n        style={{\r\n          width: 16,\r\n          height: 16,\r\n          backgroundColor: data.color,\r\n          marginRight: 8,\r\n          border: '1px solid #ccc'\r\n        }}\r\n      />\r\n      <span>{data.label}</span>\r\n    </div>\r\n  );\r\n\r\n  // Calculate total quantities for all details\r\n  const totalQuantities = details.reduce(\r\n    (acc, detail) => {\r\n      acc.xs += parseInt(detail.xs) || 0;\r\n      acc.s += parseInt(detail.s) || 0;\r\n      acc.m += parseInt(detail.m) || 0;\r\n      acc.l += parseInt(detail.l) || 0;\r\n      acc.xl += parseInt(detail.xl) || 0;\r\n      acc.total += (parseInt(detail.xs) || 0) +\r\n                  (parseInt(detail.s) || 0) +\r\n                  (parseInt(detail.m) || 0) +\r\n                  (parseInt(detail.l) || 0) +\r\n                  (parseInt(detail.xl) || 0);\r\n      acc.yard_usage += parseFloat(detail.yard_usage) || 0;\r\n      return acc;\r\n    },\r\n    { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 }\r\n  );\r\n\r\n  if (loadingRecord) {\r\n    return (\r\n      <>\r\n        <RoleBasedNavBar />\r\n        <Container fluid\r\n          style={{\r\n            marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n            width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n            transition: \"all 0.3s ease\",\r\n            padding: \"20px\"\r\n          }}\r\n        >\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading cutting record...</p>\r\n          </div>\r\n        </Container>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <div className=\"d-flex align-items-center mb-4\">\r\n          <Button\r\n            variant=\"outline-secondary\"\r\n            className=\"me-3\"\r\n            onClick={() => navigate('/viewcutting')}\r\n          >\r\n            <BsArrowLeft className=\"me-1\" /> Back\r\n          </Button>\r\n          <h2 className=\"mb-0\">\r\n            <BsScissors className=\"me-2\" />\r\n            Edit Cutting Record\r\n          </h2>\r\n        </div>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addDetailRow}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Variant\r\n                </Button>\r\n              </div>\r\n\r\n              {details.map((detail, index) => {\r\n                // Find the selected variant object to set the value in React-Select\r\n                const currentVariant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n\r\n                return (\r\n                  <Card key={index} className=\"mb-3 border-light\">\r\n                    <Card.Body>\r\n                      <Row className=\"align-items-end\">\r\n                        <Col md={4}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading variants...</span>\r\n                              </div>\r\n                            ) : allFabricVariants.length === 0 ? (\r\n                              <div>\r\n                                <Form.Select disabled>\r\n                                  <option>No variants available</option>\r\n                                </Form.Select>\r\n                                <small className=\"text-danger\">\r\n                                  No fabric variants found.\r\n                                </small>\r\n                              </div>\r\n                            ) : (\r\n                              <ColorVariantSelector\r\n                                variants={allFabricVariants}\r\n                                selectedValue={detail.fabric_variant}\r\n                                onSelect={(value) => handleDetailChange(index, 'fabric_variant', value)}\r\n                                placeholder=\"Select Fabric Variant\"\r\n                                disabled={isSubmitting}\r\n                                showFabricName={true}\r\n                              />\r\n                            )}\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              Please select a fabric variant.\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={2}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Yard Usage</strong></Form.Label>\r\n                            <Form.Control\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0.01\"\r\n                              value={detail.yard_usage}\r\n                              onChange={(e) => handleDetailChange(index, 'yard_usage', e.target.value)}\r\n                              required\r\n                              disabled={isSubmitting}\r\n                              isInvalid={!!detailErrors[index]}\r\n                              className={detailErrors[index] ? 'border-danger' : ''}\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {detailErrors[index] || 'Please enter valid yard usage.'}\r\n                            </Form.Control.Feedback>\r\n                            {currentVariant && (\r\n                              <small className=\"text-muted\">\r\n                                Available: {parseFloat(currentVariant.available_yard) + (originalYardUsage[detail.fabric_variant] || 0)} yards\r\n                                (Original: {originalYardUsage[detail.fabric_variant] || 0} yards + Current: {currentVariant.available_yard} yards)\r\n                              </small>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={5}>\r\n                          <Row>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>XS</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.xs}\r\n                                  onChange={(e) => handleDetailChange(index, 'xs', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>S</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.s}\r\n                                  onChange={(e) => handleDetailChange(index, 's', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>M</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.m}\r\n                                  onChange={(e) => handleDetailChange(index, 'm', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>L</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.l}\r\n                                  onChange={(e) => handleDetailChange(index, 'l', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                            <Col>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label><strong>XL</strong></Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail.xl}\r\n                                  onChange={(e) => handleDetailChange(index, 'xl', e.target.value)}\r\n                                  disabled={isSubmitting}\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                          </Row>\r\n                        </Col>\r\n                        <Col md={1} className=\"d-flex align-items-center justify-content-end\">\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            onClick={() => removeDetailRow(index)}\r\n                            disabled={details.length === 1 || isSubmitting}\r\n                            className=\"mt-2\"\r\n                          >\r\n                            <BsTrash />\r\n                          </Button>\r\n                        </Col>\r\n                      </Row>\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-between mb-4\">\r\n                <Button\r\n                  variant=\"outline-primary\"\r\n                  onClick={addDetailRow}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Another Variant\r\n                </Button>\r\n                <div>\r\n                  <Badge bg=\"info\" className=\"me-2 p-2\">\r\n                    Total Pieces: {totalQuantities.total}\r\n                  </Badge>\r\n                  <Badge bg=\"warning\" text=\"dark\" className=\"p-2\">\r\n                    Total Yard Usage: {totalQuantities.yard_usage.toFixed(2)}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-end\">\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  className=\"me-2\"\r\n                  onClick={() => navigate('/viewcutting')}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  variant=\"primary\"\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Updating...\r\n                    </>\r\n                  ) : (\r\n                    'Update Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default EditCutting;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,CAAAC,oBAAoB,KAAM,oCAAoC,CACrE,OAASC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,GAAG,CAAEC,GAAG,CAAEC,OAAO,CAAEC,KAAK,CAAEC,SAAS,CAAEC,KAAK,CAAEC,KAAK,KAAQ,iBAAiB,CACvG,OAASC,UAAU,CAAEC,MAAM,CAAEC,OAAO,CAAEC,cAAc,CAAEC,qBAAqB,CAAEC,WAAW,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjH,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,EAAG,CAAC,CAAG5B,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAA6B,QAAQ,CAAG5B,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAC6B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACmC,WAAW,CAAEC,cAAc,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACuC,WAAW,CAAEC,cAAc,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAElD;AACA,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,CACrC,CAAE2C,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACvE,CAAC,CAEF;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAE9D;AACA,KAAM,CAACoD,YAAY,CAAEC,eAAe,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAACsD,eAAe,CAAEC,kBAAkB,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACwD,aAAa,CAAEC,gBAAgB,CAAC,CAAGzD,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC0D,KAAK,CAAEC,QAAQ,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4D,OAAO,CAAEC,UAAU,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC8D,YAAY,CAAEC,eAAe,CAAC,CAAG/D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgE,aAAa,CAAEC,gBAAgB,CAAC,CAAGjE,QAAQ,CAACkE,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGrE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACsE,cAAc,CAAEC,iBAAiB,CAAC,CAAGvE,QAAQ,CAAC,IAAI,CAAC,CAE1D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuE,YAAY,CAAGA,CAAA,GAAM,CACzBP,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACO,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMN,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACAvE,SAAS,CAAC,IAAM,CACdwD,gBAAgB,CAAC,IAAI,CAAC,CACtBkB,OAAO,CAACC,GAAG,CAAC,oCAAoC7C,EAAE,EAAE,CAAC,CAErD7B,KAAK,CAAC2E,GAAG,CAAC,qDAAqD9C,EAAE,GAAG,CAAC,CAClE+C,IAAI,CAAEC,GAAG,EAAK,CACb,KAAM,CAAAC,MAAM,CAAGD,GAAG,CAACE,IAAI,CACvBN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEI,MAAM,CAAC,CAC3DT,iBAAiB,CAACS,MAAM,CAAC,CAEzB;AACA5C,cAAc,CAAC4C,MAAM,CAACE,YAAY,CAAC,CACnC5C,cAAc,CAAC0C,MAAM,CAAC3C,WAAW,EAAI,EAAE,CAAC,CACxCG,cAAc,CAACwC,MAAM,CAACG,YAAY,EAAI,EAAE,CAAC,CAEzC;AACA,GAAIH,MAAM,CAACvC,OAAO,EAAIuC,MAAM,CAACvC,OAAO,CAAC2C,MAAM,CAAG,CAAC,CAAE,CAC/CT,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEI,MAAM,CAACvC,OAAO,CAAC,CAE3D;AACA,KAAM,CAAA4C,YAAY,CAAG,CAAC,CAAC,CACvBL,MAAM,CAACvC,OAAO,CAAC6C,OAAO,CAACC,MAAM,EAAI,CAC/BF,YAAY,CAACE,MAAM,CAAC5C,cAAc,CAAC,CAAG6C,UAAU,CAACD,MAAM,CAAC3C,UAAU,CAAC,CACrE,CAAC,CAAC,CACF+B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAES,YAAY,CAAC,CACrDlC,oBAAoB,CAACkC,YAAY,CAAC,CAElC;AACAhC,eAAe,CAACoC,KAAK,CAACT,MAAM,CAACvC,OAAO,CAAC2C,MAAM,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,CAAC,CAEtD;AACAhD,UAAU,CAACsC,MAAM,CAACvC,OAAO,CAACkD,GAAG,CAACJ,MAAM,GAAK,CACvCxD,EAAE,CAAEwD,MAAM,CAACxD,EAAE,CAAE;AACfY,cAAc,CAAE4C,MAAM,CAAC5C,cAAc,CACrCC,UAAU,CAAE2C,MAAM,CAAC3C,UAAU,CAC7BC,EAAE,CAAE0C,MAAM,CAAC1C,EAAE,EAAI,CAAC,CAClBC,CAAC,CAAEyC,MAAM,CAACzC,CAAC,EAAI,CAAC,CAChBC,CAAC,CAAEwC,MAAM,CAACxC,CAAC,EAAI,CAAC,CAChBC,CAAC,CAAEuC,MAAM,CAACvC,CAAC,EAAI,CAAC,CAChBC,EAAE,CAAEsC,MAAM,CAACtC,EAAE,EAAI,CACnB,CAAC,CAAC,CAAC,CAAC,CACN,CAEAQ,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAC,CACDmC,KAAK,CAAEC,GAAG,EAAK,CACdlB,OAAO,CAACjB,KAAK,CAAC,gCAAgC,CAAEmC,GAAG,CAAC,CACpDlC,QAAQ,CAAC,kDAAkD,CAAC,CAC5DF,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAC,CACN,CAAC,CAAE,CAAC1B,EAAE,CAAC,CAAC,CAER;AACA9B,SAAS,CAAC,IAAM,CACdsD,kBAAkB,CAAC,IAAI,CAAC,CACxBrD,KAAK,CAAC2E,GAAG,CAAC,4CAA4C,CAAC,CACpDC,IAAI,CAAEC,GAAG,EAAK,CACbJ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEG,GAAG,CAACE,IAAI,CAAC,CAC9D/C,oBAAoB,CAAC6C,GAAG,CAACE,IAAI,CAAC,CAC9B1B,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACDqC,KAAK,CAAEC,GAAG,EAAK,CACdlB,OAAO,CAACjB,KAAK,CAAC,iCAAiC,CAAEmC,GAAG,CAAC,CACrDlC,QAAQ,CAAC,mDAAmD,CAAC,CAC7DJ,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAIN;AACAtD,SAAS,CAAC,IAAM,CACd,GAAIgC,iBAAiB,CAACmD,MAAM,CAAG,CAAC,EAAI3C,OAAO,CAAC2C,MAAM,CAAG,CAAC,CAAE,CACtD;AACA,KAAM,CAAAU,eAAe,CAAG,CAAC,GAAG1C,YAAY,CAAC,CAEzC;AACAX,OAAO,CAAC6C,OAAO,CAAC,CAACC,MAAM,CAAEQ,KAAK,GAAK,CACjC,GAAIR,MAAM,CAAC5C,cAAc,EAAI4C,MAAM,CAAC3C,UAAU,CAAE,CAC9C,KAAM,CAAAoD,SAAS,CAAGT,MAAM,CAAC5C,cAAc,CAEvC;AACA,KAAM,CAAAsD,OAAO,CAAGhE,iBAAiB,CAACiE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpE,EAAE,GAAKqE,QAAQ,CAACJ,SAAS,CAAC,CAAC,CACzE,GAAI,CAACC,OAAO,CAAE,OAEd;AACA,KAAM,CAAAI,QAAQ,CAAGnD,iBAAiB,CAAC8C,SAAS,CAAC,EAAI,CAAC,CAElD;AACA,KAAM,CAAAM,UAAU,CAAGd,UAAU,CAACS,OAAO,CAACM,cAAc,CAAC,CAAGF,QAAQ,CAEhE;AACA,GAAIb,UAAU,CAACD,MAAM,CAAC3C,UAAU,CAAC,CAAG0D,UAAU,CAAE,CAC9CR,eAAe,CAACC,KAAK,CAAC,CAAG,6CAA6CO,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CACrG,CAAC,IAAM,IAAIhB,UAAU,CAACD,MAAM,CAAC3C,UAAU,CAAC,EAAI,CAAC,CAAE,CAC7CkD,eAAe,CAACC,KAAK,CAAC,CAAG,mCAAmC,CAC9D,CAAC,IAAM,CACLD,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC7B,CACF,CACF,CAAC,CAAC,CAEF;AACA1C,eAAe,CAACyC,eAAe,CAAC,CAClC,CACF,CAAC,CAAE,CAAC7D,iBAAiB,CAAEQ,OAAO,CAAES,iBAAiB,CAAC,CAAC,CAInD;AACA,KAAM,CAAAuD,YAAY,CAAGA,CAAA,GAAM,CACzB/D,UAAU,CAAC,CAAC,GAAGD,OAAO,CAAE,CAAEE,cAAc,CAAE,EAAE,CAAEC,UAAU,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAC,CAAC,CAChGI,eAAe,CAAC,CAAC,GAAGD,YAAY,CAAE,EAAE,CAAC,CAAC,CAAE;AAC1C,CAAC,CAED;AACA,KAAM,CAAAsD,eAAe,CAAIX,KAAK,EAAK,CACjC,KAAM,CAAAY,UAAU,CAAGlE,OAAO,CAACmE,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKf,KAAK,CAAC,CACxDrD,UAAU,CAACiE,UAAU,CAAC,CAEtB;AACA,KAAM,CAAAb,eAAe,CAAG1C,YAAY,CAACwD,MAAM,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,GAAKf,KAAK,CAAC,CAClE1C,eAAe,CAACyC,eAAe,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAiB,kBAAkB,CAAGA,CAAChB,KAAK,CAAEiB,KAAK,CAAEC,KAAK,GAAK,CAClD,KAAM,CAAAN,UAAU,CAAG,CAAC,GAAGlE,OAAO,CAAC,CAC/BkE,UAAU,CAACZ,KAAK,CAAC,CAACiB,KAAK,CAAC,CAAGC,KAAK,CAChCvE,UAAU,CAACiE,UAAU,CAAC,CAEtB;AACA,GAAIK,KAAK,GAAK,YAAY,CAAE,CAC1BE,iBAAiB,CAACnB,KAAK,CAAEkB,KAAK,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAACnB,KAAK,CAAEoB,YAAY,GAAK,CACjD,KAAM,CAAArB,eAAe,CAAG,CAAC,GAAG1C,YAAY,CAAC,CACzC,KAAM,CAAAmC,MAAM,CAAG9C,OAAO,CAACsD,KAAK,CAAC,CAC7B,KAAM,CAAAC,SAAS,CAAGT,MAAM,CAAC5C,cAAc,CAEvC;AACA,GAAI,CAACqD,SAAS,CAAE,CACdF,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC3B1C,eAAe,CAACyC,eAAe,CAAC,CAChC,OACF,CAEA;AACA,KAAM,CAAAG,OAAO,CAAGhE,iBAAiB,CAACiE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpE,EAAE,GAAKqE,QAAQ,CAACJ,SAAS,CAAC,CAAC,CACzE,GAAI,CAACC,OAAO,CAAE,CACZH,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC3B1C,eAAe,CAACyC,eAAe,CAAC,CAChC,OACF,CAEA;AACA,KAAM,CAAAO,QAAQ,CAAGnD,iBAAiB,CAAC8C,SAAS,CAAC,EAAI,CAAC,CAElD;AACA;AACA,KAAM,CAAAM,UAAU,CAAGd,UAAU,CAACS,OAAO,CAACM,cAAc,CAAC,CAAGF,QAAQ,CAEhE;AACA,GAAIb,UAAU,CAAC2B,YAAY,CAAC,CAAGb,UAAU,CAAE,CACzCR,eAAe,CAACC,KAAK,CAAC,CAAG,6CAA6CO,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC,QAAQ,CACrG,CAAC,IAAM,IAAIhB,UAAU,CAAC2B,YAAY,CAAC,EAAI,CAAC,CAAE,CACxCrB,eAAe,CAACC,KAAK,CAAC,CAAG,mCAAmC,CAC9D,CAAC,IAAM,CACLD,eAAe,CAACC,KAAK,CAAC,CAAG,EAAE,CAC7B,CAEA1C,eAAe,CAACyC,eAAe,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAsB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB;AACA,KAAM,CAAAC,IAAI,CAAGF,CAAC,CAACG,aAAa,CAC5B,GAAID,IAAI,CAACE,aAAa,CAAC,CAAC,GAAK,KAAK,CAAE,CAClCJ,CAAC,CAACK,eAAe,CAAC,CAAC,CACnBrD,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEA;AACA,KAAM,CAAAsD,eAAe,CAAGlF,OAAO,CAACmF,IAAI,CAACrC,MAAM,EAAIA,MAAM,CAAC5C,cAAc,CAAC,CACrE,GAAI,CAACgF,eAAe,CAAE,CACpBhE,QAAQ,CAAC,qEAAqE,CAAC,CAC/E,OACF,CAEA;AACA,GAAI,CAAAkE,kBAAkB,CAAG,KAAK,CAC9BpF,OAAO,CAAC6C,OAAO,CAAC,CAACC,MAAM,CAAEQ,KAAK,GAAK,CACjCmB,iBAAiB,CAACnB,KAAK,CAAER,MAAM,CAAC3C,UAAU,CAAC,CAC3C,GAAIQ,YAAY,CAAC2C,KAAK,CAAC,CAAE,CACvB8B,kBAAkB,CAAG,IAAI,CAC3B,CACF,CAAC,CAAC,CAEF;AACA,GAAIA,kBAAkB,CAAE,CACtBlE,QAAQ,CAAC,qDAAqD,CAAC,CAC/D,OACF,CAEAU,YAAY,CAAC,IAAI,CAAC,CAClBN,eAAe,CAAC,IAAI,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAiE,OAAO,CAAG,CACd5C,YAAY,CAAE/C,WAAW,CACzBE,WAAW,CAAEA,WAAW,CACxB8C,YAAY,CAAE5C,WAAW,CACzBE,OAAO,CAAEA,OACX,CAAC,CAED,KAAM,CAAAsF,QAAQ,CAAG,KAAM,CAAA7H,KAAK,CAAC8H,GAAG,CAAC,qDAAqDjG,EAAE,GAAG,CAAE+F,OAAO,CAAC,CACrGjE,UAAU,CAAC,sCAAsC,CAAC,CAElD;AACAoE,UAAU,CAAC,IAAM,CACfjG,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAO6D,GAAG,CAAE,CACZlB,OAAO,CAACjB,KAAK,CAAC,gCAAgC,CAAEmC,GAAG,CAAC,CACpD,GAAIA,GAAG,CAACkC,QAAQ,EAAIlC,GAAG,CAACkC,QAAQ,CAAC9C,IAAI,CAAE,CACrC;AACA,KAAM,CAAAiD,YAAY,CAAG,MAAO,CAAArC,GAAG,CAACkC,QAAQ,CAAC9C,IAAI,GAAK,QAAQ,CACtDY,GAAG,CAACkC,QAAQ,CAAC9C,IAAI,CACjB,4DAA4D,CAChEtB,QAAQ,CAACuE,YAAY,CAAC,CACxB,CAAC,IAAM,CACLvE,QAAQ,CAAC,oDAAoD,CAAC,CAChE,CACF,CAAC,OAAS,CACRI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAAoE,YAAY,CAAGC,IAAA,MAAC,CAAEnD,IAAI,CAAEoD,QAAQ,CAAEC,UAAW,CAAC,CAAAF,IAAA,oBAClDzG,KAAA,QACE4G,GAAG,CAAEF,QAAS,IACVC,UAAU,CACdE,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,OAAO,CAAE,KAAM,CAAE,CAAAC,QAAA,eAEjEnH,IAAA,QACE+G,KAAK,CAAE,CACLK,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,eAAe,CAAE9D,IAAI,CAAC+D,KAAK,CAC3BC,WAAW,CAAE,CAAC,CACdC,MAAM,CAAE,gBACV,CAAE,CACH,CAAC,cACFzH,IAAA,SAAAmH,QAAA,CAAO3D,IAAI,CAACkE,KAAK,CAAO,CAAC,EACtB,CAAC,EACP,CAED;AACA,KAAM,CAAAC,eAAe,CAAG3G,OAAO,CAAC4G,MAAM,CACpC,CAACC,GAAG,CAAE/D,MAAM,GAAK,CACf+D,GAAG,CAACzG,EAAE,EAAIuD,QAAQ,CAACb,MAAM,CAAC1C,EAAE,CAAC,EAAI,CAAC,CAClCyG,GAAG,CAACxG,CAAC,EAAIsD,QAAQ,CAACb,MAAM,CAACzC,CAAC,CAAC,EAAI,CAAC,CAChCwG,GAAG,CAACvG,CAAC,EAAIqD,QAAQ,CAACb,MAAM,CAACxC,CAAC,CAAC,EAAI,CAAC,CAChCuG,GAAG,CAACtG,CAAC,EAAIoD,QAAQ,CAACb,MAAM,CAACvC,CAAC,CAAC,EAAI,CAAC,CAChCsG,GAAG,CAACrG,EAAE,EAAImD,QAAQ,CAACb,MAAM,CAACtC,EAAE,CAAC,EAAI,CAAC,CAClCqG,GAAG,CAACC,KAAK,EAAI,CAACnD,QAAQ,CAACb,MAAM,CAAC1C,EAAE,CAAC,EAAI,CAAC,GACzBuD,QAAQ,CAACb,MAAM,CAACzC,CAAC,CAAC,EAAI,CAAC,CAAC,EACxBsD,QAAQ,CAACb,MAAM,CAACxC,CAAC,CAAC,EAAI,CAAC,CAAC,EACxBqD,QAAQ,CAACb,MAAM,CAACvC,CAAC,CAAC,EAAI,CAAC,CAAC,EACxBoD,QAAQ,CAACb,MAAM,CAACtC,EAAE,CAAC,EAAI,CAAC,CAAC,CACtCqG,GAAG,CAAC1G,UAAU,EAAI4C,UAAU,CAACD,MAAM,CAAC3C,UAAU,CAAC,EAAI,CAAC,CACpD,MAAO,CAAA0G,GAAG,CACZ,CAAC,CACD,CAAEzG,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEsG,KAAK,CAAE,CAAC,CAAE3G,UAAU,CAAE,CAAE,CAC5D,CAAC,CAED,GAAIY,aAAa,CAAE,CACjB,mBACE7B,KAAA,CAAAE,SAAA,EAAA+G,QAAA,eACEnH,IAAA,CAACnB,eAAe,GAAE,CAAC,cACnBmB,IAAA,CAACV,SAAS,EAACyI,KAAK,MACdhB,KAAK,CAAE,CACLiB,UAAU,CAAEzF,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5C6E,KAAK,CAAE,eAAe7E,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzD0F,UAAU,CAAE,eAAe,CAC3Bf,OAAO,CAAE,MACX,CAAE,CAAAC,QAAA,cAEFjH,KAAA,QAAKgI,SAAS,CAAC,kBAAkB,CAAAf,QAAA,eAC/BnH,IAAA,CAACZ,OAAO,EAAC+I,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAC5D,OAAO,CAAC,SAAS,CAAA2C,QAAA,cACzDnH,IAAA,SAAMkI,SAAS,CAAC,iBAAiB,CAAAf,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,cACVnH,IAAA,MAAGkI,SAAS,CAAC,MAAM,CAAAf,QAAA,CAAC,2BAAyB,CAAG,CAAC,EAC9C,CAAC,CACG,CAAC,EACZ,CAAC,CAEP,CAEA,mBACEjH,KAAA,CAAAE,SAAA,EAAA+G,QAAA,eACEnH,IAAA,CAACnB,eAAe,GAAE,CAAC,cACnBqB,KAAA,QACE6G,KAAK,CAAE,CACLiB,UAAU,CAAEzF,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5C6E,KAAK,CAAE,eAAe7E,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzD0F,UAAU,CAAE,eAAe,CAC3Bf,OAAO,CAAE,MACX,CAAE,CAAAC,QAAA,eAEFjH,KAAA,QAAKgI,SAAS,CAAC,gCAAgC,CAAAf,QAAA,eAC7CjH,KAAA,CAACjB,MAAM,EACLuF,OAAO,CAAC,mBAAmB,CAC3B0D,SAAS,CAAC,MAAM,CAChBG,OAAO,CAAEA,CAAA,GAAM9H,QAAQ,CAAC,cAAc,CAAE,CAAA4G,QAAA,eAExCnH,IAAA,CAACF,WAAW,EAACoI,SAAS,CAAC,MAAM,CAAE,CAAC,QAClC,EAAQ,CAAC,cACThI,KAAA,OAAIgI,SAAS,CAAC,MAAM,CAAAf,QAAA,eAClBnH,IAAA,CAACP,UAAU,EAACyI,SAAS,CAAC,MAAM,CAAE,CAAC,sBAEjC,EAAI,CAAC,EACF,CAAC,CAEL/F,OAAO,eACNjC,KAAA,CAACb,KAAK,EAACmF,OAAO,CAAC,SAAS,CAAC0D,SAAS,CAAC,2BAA2B,CAAAf,QAAA,eAC5DnH,IAAA,CAACJ,cAAc,EAACsI,SAAS,CAAC,MAAM,CAACI,IAAI,CAAE,EAAG,CAAE,CAAC,CAC5CnG,OAAO,EACH,CACR,CAEAF,KAAK,eACJ/B,KAAA,CAACb,KAAK,EAACmF,OAAO,CAAC,QAAQ,CAAC0D,SAAS,CAAC,2BAA2B,CAAAf,QAAA,eAC3DnH,IAAA,CAACH,qBAAqB,EAACqI,SAAS,CAAC,MAAM,CAACI,IAAI,CAAE,EAAG,CAAE,CAAC,CACnDrG,KAAK,EACD,CACR,cAEDjC,IAAA,CAACjB,IAAI,EAACmJ,SAAS,CAAC,gBAAgB,CAACnB,KAAK,CAAE,CAAEO,eAAe,CAAE,SAAS,CAAEiB,YAAY,CAAE,MAAO,CAAE,CAAApB,QAAA,cAC3FnH,IAAA,CAACjB,IAAI,CAACyJ,IAAI,EAAArB,QAAA,cACRjH,KAAA,CAAClB,IAAI,EAACyJ,UAAU,MAAC9F,SAAS,CAAEA,SAAU,CAAC+F,QAAQ,CAAE/C,YAAa,CAAAwB,QAAA,eAC5DnH,IAAA,CAACd,GAAG,EAAAiI,QAAA,cACFnH,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtDnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAE1E,WAAY,CACnBkI,QAAQ,CAAGpD,CAAC,EAAK7E,cAAc,CAAC6E,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CAChD0D,WAAW,CAAC,oBAAoB,CAChCC,QAAQ,MACT,CAAC,cACFnJ,IAAA,CAAChB,IAAI,CAAC8J,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAAC,gCAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,CACH,CAAC,cAENjH,KAAA,CAAChB,GAAG,EAAAiI,QAAA,eACFnH,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAY,CAAC,cACtDnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,MAAM,CACXvD,KAAK,CAAE9E,WAAY,CACnBsI,QAAQ,CAAGpD,CAAC,EAAKjF,cAAc,CAACiF,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CAChD2D,QAAQ,MACT,CAAC,cACFnJ,IAAA,CAAChB,IAAI,CAAC8J,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAAC,+BAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,cACNnH,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAY,CAAC,cACrDnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXO,EAAE,CAAC,UAAU,CACbC,IAAI,CAAE,CAAE,CACR9D,KAAK,CAAE5E,WAAY,CACnBoI,QAAQ,CAAGpD,CAAC,EAAK/E,cAAc,CAAC+E,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CAChD0D,WAAW,CAAC,4CAA4C,CACzD,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,cAINhJ,KAAA,QAAKgI,SAAS,CAAC,gFAAgF,CAAAf,QAAA,eAC7FnH,IAAA,OAAIkI,SAAS,CAAC,MAAM,CAAAf,QAAA,CAAC,gBAAc,CAAI,CAAC,cACxCjH,KAAA,CAACjB,MAAM,EACLuF,OAAO,CAAC,SAAS,CACjB8D,IAAI,CAAC,IAAI,CACTD,OAAO,CAAErD,YAAa,CACtBuE,QAAQ,CAAElH,YAAa,CAAA8E,QAAA,eAEvBnH,IAAA,CAACN,MAAM,EAACwI,SAAS,CAAC,MAAM,CAAE,CAAC,sBAC7B,EAAQ,CAAC,EACN,CAAC,CAELlH,OAAO,CAACkD,GAAG,CAAC,CAACJ,MAAM,CAAEQ,KAAK,GAAK,CAC9B;AACA,KAAM,CAAAkF,cAAc,CAAGhJ,iBAAiB,CAACiE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpE,EAAE,GAAKwD,MAAM,CAAC5C,cAAc,CAAC,CAElF,mBACElB,IAAA,CAACjB,IAAI,EAAamJ,SAAS,CAAC,mBAAmB,CAAAf,QAAA,cAC7CnH,IAAA,CAACjB,IAAI,CAACyJ,IAAI,EAAArB,QAAA,cACRjH,KAAA,CAAChB,GAAG,EAACgJ,SAAS,CAAC,iBAAiB,CAAAf,QAAA,eAC9BnH,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAY,CAAC,CAC/DtF,eAAe,cACd3B,KAAA,QAAKgI,SAAS,CAAC,2BAA2B,CAAAf,QAAA,eACxCnH,IAAA,CAACZ,OAAO,EAAC+I,SAAS,CAAC,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACJ,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDlI,IAAA,SAAAmH,QAAA,CAAM,qBAAmB,CAAM,CAAC,EAC7B,CAAC,CACJ3G,iBAAiB,CAACmD,MAAM,GAAK,CAAC,cAChCzD,KAAA,QAAAiH,QAAA,eACEnH,IAAA,CAAChB,IAAI,CAACJ,MAAM,EAAC2K,QAAQ,MAAApC,QAAA,cACnBnH,IAAA,WAAAmH,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,CAC3B,CAAC,cACdnH,IAAA,UAAOkI,SAAS,CAAC,aAAa,CAAAf,QAAA,CAAC,2BAE/B,CAAO,CAAC,EACL,CAAC,cAENnH,IAAA,CAAClB,oBAAoB,EACnB2K,QAAQ,CAAEjJ,iBAAkB,CAC5BkJ,aAAa,CAAE5F,MAAM,CAAC5C,cAAe,CACrCyI,QAAQ,CAAGnE,KAAK,EAAKF,kBAAkB,CAAChB,KAAK,CAAE,gBAAgB,CAAEkB,KAAK,CAAE,CACxE0D,WAAW,CAAC,uBAAuB,CACnCK,QAAQ,CAAElH,YAAa,CACvBuH,cAAc,CAAE,IAAK,CACtB,CACF,cACD5J,IAAA,CAAChB,IAAI,CAAC8J,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAAC,iCAEtC,CAAuB,CAAC,EACd,CAAC,CACV,CAAC,cACNnH,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAY,CAAC,cACpDnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbc,IAAI,CAAC,MAAM,CACXC,GAAG,CAAC,MAAM,CACVtE,KAAK,CAAE1B,MAAM,CAAC3C,UAAW,CACzB6H,QAAQ,CAAGpD,CAAC,EAAKN,kBAAkB,CAAChB,KAAK,CAAE,YAAY,CAAEsB,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CACzE2D,QAAQ,MACRI,QAAQ,CAAElH,YAAa,CACvB0H,SAAS,CAAE,CAAC,CAACpI,YAAY,CAAC2C,KAAK,CAAE,CACjC4D,SAAS,CAAEvG,YAAY,CAAC2C,KAAK,CAAC,CAAG,eAAe,CAAG,EAAG,CACvD,CAAC,cACFtE,IAAA,CAAChB,IAAI,CAAC8J,OAAO,CAACM,QAAQ,EAACL,IAAI,CAAC,SAAS,CAAA5B,QAAA,CAClCxF,YAAY,CAAC2C,KAAK,CAAC,EAAI,gCAAgC,CACnC,CAAC,CACvBkF,cAAc,eACbtJ,KAAA,UAAOgI,SAAS,CAAC,YAAY,CAAAf,QAAA,EAAC,aACjB,CAACpD,UAAU,CAACyF,cAAc,CAAC1E,cAAc,CAAC,EAAIrD,iBAAiB,CAACqC,MAAM,CAAC5C,cAAc,CAAC,EAAI,CAAC,CAAC,CAAC,oBAC7F,CAACO,iBAAiB,CAACqC,MAAM,CAAC5C,cAAc,CAAC,EAAI,CAAC,CAAC,oBAAkB,CAACsI,cAAc,CAAC1E,cAAc,CAAC,SAC7G,EAAO,CACR,EACS,CAAC,CACV,CAAC,cACN9E,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACTjH,KAAA,CAAChB,GAAG,EAAAiI,QAAA,eACFnH,IAAA,CAACb,GAAG,EAAAgI,QAAA,cACFjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,IAAE,CAAQ,CAAC,CAAY,CAAC,cAC5CnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbe,GAAG,CAAC,GAAG,CACPtE,KAAK,CAAE1B,MAAM,CAAC1C,EAAG,CACjB4H,QAAQ,CAAGpD,CAAC,EAAKN,kBAAkB,CAAChB,KAAK,CAAE,IAAI,CAAEsB,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CACjE+D,QAAQ,CAAElH,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACNrC,IAAA,CAACb,GAAG,EAAAgI,QAAA,cACFjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,GAAC,CAAQ,CAAC,CAAY,CAAC,cAC3CnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbe,GAAG,CAAC,GAAG,CACPtE,KAAK,CAAE1B,MAAM,CAACzC,CAAE,CAChB2H,QAAQ,CAAGpD,CAAC,EAAKN,kBAAkB,CAAChB,KAAK,CAAE,GAAG,CAAEsB,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CAChE+D,QAAQ,CAAElH,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACNrC,IAAA,CAACb,GAAG,EAAAgI,QAAA,cACFjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,GAAC,CAAQ,CAAC,CAAY,CAAC,cAC3CnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbe,GAAG,CAAC,GAAG,CACPtE,KAAK,CAAE1B,MAAM,CAACxC,CAAE,CAChB0H,QAAQ,CAAGpD,CAAC,EAAKN,kBAAkB,CAAChB,KAAK,CAAE,GAAG,CAAEsB,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CAChE+D,QAAQ,CAAElH,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACNrC,IAAA,CAACb,GAAG,EAAAgI,QAAA,cACFjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,GAAC,CAAQ,CAAC,CAAY,CAAC,cAC3CnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbe,GAAG,CAAC,GAAG,CACPtE,KAAK,CAAE1B,MAAM,CAACvC,CAAE,CAChByH,QAAQ,CAAGpD,CAAC,EAAKN,kBAAkB,CAAChB,KAAK,CAAE,GAAG,CAAEsB,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CAChE+D,QAAQ,CAAElH,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,cACNrC,IAAA,CAACb,GAAG,EAAAgI,QAAA,cACFjH,KAAA,CAAClB,IAAI,CAAC4J,KAAK,EAACV,SAAS,CAAC,MAAM,CAAAf,QAAA,eAC1BnH,IAAA,CAAChB,IAAI,CAAC6J,KAAK,EAAA1B,QAAA,cAACnH,IAAA,WAAAmH,QAAA,CAAQ,IAAE,CAAQ,CAAC,CAAY,CAAC,cAC5CnH,IAAA,CAAChB,IAAI,CAAC8J,OAAO,EACXC,IAAI,CAAC,QAAQ,CACbe,GAAG,CAAC,GAAG,CACPtE,KAAK,CAAE1B,MAAM,CAACtC,EAAG,CACjBwH,QAAQ,CAAGpD,CAAC,EAAKN,kBAAkB,CAAChB,KAAK,CAAE,IAAI,CAAEsB,CAAC,CAACqD,MAAM,CAACzD,KAAK,CAAE,CACjE+D,QAAQ,CAAElH,YAAa,CACxB,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,CACH,CAAC,cACNrC,IAAA,CAACb,GAAG,EAACwJ,EAAE,CAAE,CAAE,CAACT,SAAS,CAAC,+CAA+C,CAAAf,QAAA,cACnEnH,IAAA,CAACf,MAAM,EACLuF,OAAO,CAAC,gBAAgB,CACxB6D,OAAO,CAAEA,CAAA,GAAMpD,eAAe,CAACX,KAAK,CAAE,CACtCiF,QAAQ,CAAEvI,OAAO,CAAC2C,MAAM,GAAK,CAAC,EAAItB,YAAa,CAC/C6F,SAAS,CAAC,MAAM,CAAAf,QAAA,cAEhBnH,IAAA,CAACL,OAAO,GAAE,CAAC,CACL,CAAC,CACN,CAAC,EACH,CAAC,CACG,CAAC,EAvIH2E,KAwIL,CAAC,CAEX,CAAC,CAAC,cAEFpE,KAAA,QAAKgI,SAAS,CAAC,qCAAqC,CAAAf,QAAA,eAClDjH,KAAA,CAACjB,MAAM,EACLuF,OAAO,CAAC,iBAAiB,CACzB6D,OAAO,CAAErD,YAAa,CACtBuE,QAAQ,CAAElH,YAAa,CAAA8E,QAAA,eAEvBnH,IAAA,CAACN,MAAM,EAACwI,SAAS,CAAC,MAAM,CAAE,CAAC,uBAC7B,EAAQ,CAAC,cACThI,KAAA,QAAAiH,QAAA,eACEjH,KAAA,CAACX,KAAK,EAACyK,EAAE,CAAC,MAAM,CAAC9B,SAAS,CAAC,UAAU,CAAAf,QAAA,EAAC,gBACtB,CAACQ,eAAe,CAACG,KAAK,EAC/B,CAAC,cACR5H,KAAA,CAACX,KAAK,EAACyK,EAAE,CAAC,SAAS,CAACC,IAAI,CAAC,MAAM,CAAC/B,SAAS,CAAC,KAAK,CAAAf,QAAA,EAAC,oBAC5B,CAACQ,eAAe,CAACxG,UAAU,CAAC4D,OAAO,CAAC,CAAC,CAAC,EACnD,CAAC,EACL,CAAC,EACH,CAAC,cAEN7E,KAAA,QAAKgI,SAAS,CAAC,4BAA4B,CAAAf,QAAA,eACzCnH,IAAA,CAACf,MAAM,EACLuF,OAAO,CAAC,WAAW,CACnB0D,SAAS,CAAC,MAAM,CAChBG,OAAO,CAAEA,CAAA,GAAM9H,QAAQ,CAAC,cAAc,CAAE,CACxCgJ,QAAQ,CAAElH,YAAa,CAAA8E,QAAA,CACxB,QAED,CAAQ,CAAC,cACTnH,IAAA,CAACf,MAAM,EACLuF,OAAO,CAAC,SAAS,CACjBuE,IAAI,CAAC,QAAQ,CACbQ,QAAQ,CAAElH,YAAa,CAAA8E,QAAA,CAEtB9E,YAAY,cACXnC,KAAA,CAAAE,SAAA,EAAA+G,QAAA,eACEnH,IAAA,CAACZ,OAAO,EAACiK,EAAE,CAAC,MAAM,CAAClB,SAAS,CAAC,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACF,IAAI,CAAC,QAAQ,CAAC,cAAY,MAAM,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,cAEtG,EAAE,CAAC,CAEH,uBACD,CACK,CAAC,EACN,CAAC,EACF,CAAC,CACE,CAAC,CACR,CAAC,EACJ,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAA7H,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}