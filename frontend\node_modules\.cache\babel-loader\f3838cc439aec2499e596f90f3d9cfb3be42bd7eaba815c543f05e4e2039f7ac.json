{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\AddFabric.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport Select from \"react-select\";\nimport { FaPlus, FaTrash } from \"react-icons/fa\";\nimport { Row, Col, Form, Button, Card, Alert, Spinner } from 'react-bootstrap';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\n\n// Common color presets with names\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst COLOR_PRESETS = [{\n  color: \"#000000\",\n  name: \"Black\"\n}, {\n  color: \"#FFFFFF\",\n  name: \"White\"\n}, {\n  color: \"#FF0000\",\n  name: \"Red\"\n}, {\n  color: \"#0000FF\",\n  name: \"<PERSON>\"\n}, {\n  color: \"#008000\",\n  name: \"Green\"\n}, {\n  color: \"#FFFF00\",\n  name: \"Yellow\"\n}, {\n  color: \"#FFA500\",\n  name: \"Orange\"\n}, {\n  color: \"#800080\",\n  name: \"Purple\"\n}, {\n  color: \"#FFC0CB\",\n  name: \"Pink\"\n}, {\n  color: \"#A52A2A\",\n  name: \"Brown\"\n}, {\n  color: \"#808080\",\n  name: \"Gray\"\n}];\nconst AddFabric = () => {\n  _s();\n  // State variables\n  const [fabricName, setFabricName] = useState(\"\");\n  const [selectedSupplier, setSelectedSupplier] = useState(null);\n  const [dateAdded, setDateAdded] = useState(\"\");\n  const [variants, setVariants] = useState([{\n    color: \"#000000\",\n    colorName: \"Black\",\n    totalYard: \"\",\n    pricePerYard: \"\"\n  }]);\n  const [suppliers, setSuppliers] = useState([]);\n  const [message, setMessage] = useState(\"\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [loading, setLoading] = useState(false);\n\n  // Effect to handle sidebar state based on window size\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Fetch suppliers\n  useEffect(() => {\n    setLoading(true);\n    axios.get(\"http://localhost:8000/api/suppliers/\").then(response => {\n      const supplierOptions = response.data.map(sup => ({\n        value: sup.supplier_id,\n        label: sup.name\n      }));\n      setSuppliers(supplierOptions);\n      setLoading(false);\n    }).catch(error => {\n      console.error(\"Error fetching suppliers:\", error);\n      setMessage(\"Failed to load suppliers\");\n      setLoading(false);\n    });\n  }, []);\n\n  // Handle adding a variant\n  const handleAddVariant = () => {\n    setVariants([...variants, {\n      color: \"#000000\",\n      colorName: \"Black\",\n      totalYard: \"\",\n      pricePerYard: \"\"\n    }]);\n  };\n\n  // Handle removing a variant\n  const handleRemoveVariant = index => {\n    const updated = variants.filter((_, i) => i !== index);\n    setVariants(updated);\n  };\n\n  // Handle variant input changes\n  const handleVariantChange = (index, field, value) => {\n    const updated = [...variants];\n    updated[index][field] = value;\n\n    // If color is changed, suggest a color name only if current name is empty or matches a preset\n    if (field === \"color\") {\n      const colorPreset = COLOR_PRESETS.find(preset => preset.color === value);\n      const currentColorName = updated[index].colorName;\n\n      // Only auto-update if the field is empty or contains a basic preset name\n      const isBasicPresetName = COLOR_PRESETS.some(preset => preset.name === currentColorName);\n      if (!currentColorName || isBasicPresetName) {\n        updated[index].colorName = colorPreset ? colorPreset.name : \"\";\n      }\n    }\n    setVariants(updated);\n  };\n\n  // Function to check if two colors are similar\n  const areColorsSimilar = (color1, color2) => {\n    // Convert hex to RGB\n    const hexToRgb = hex => {\n      const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n      return result ? {\n        r: parseInt(result[1], 16),\n        g: parseInt(result[2], 16),\n        b: parseInt(result[3], 16)\n      } : null;\n    };\n    const rgb1 = hexToRgb(color1);\n    const rgb2 = hexToRgb(color2);\n    if (!rgb1 || !rgb2) return false;\n\n    // Calculate color difference using Euclidean distance\n    const distance = Math.sqrt(Math.pow(rgb1.r - rgb2.r, 2) + Math.pow(rgb1.g - rgb2.g, 2) + Math.pow(rgb1.b - rgb2.b, 2));\n\n    // Colors are similar if distance is less than 50 (adjustable threshold)\n    return distance < 50;\n  };\n\n  // Submit handler\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setMessage(\"\");\n    setIsSubmitting(true);\n\n    // Basic validation\n    if (!fabricName.trim()) {\n      setMessage(\"Please enter a fabric name\");\n      setIsSubmitting(false);\n      return;\n    }\n    if (!selectedSupplier) {\n      setMessage(\"Please select a supplier\");\n      setIsSubmitting(false);\n      return;\n    }\n    if (!dateAdded) {\n      setMessage(\"Please select a date\");\n      setIsSubmitting(false);\n      return;\n    }\n\n    // Validate variants\n    for (let i = 0; i < variants.length; i++) {\n      const variant = variants[i];\n      if (variant.totalYard && parseFloat(variant.totalYard) < 0) {\n        setMessage(`Variant ${i + 1}: Total yard cannot be negative`);\n        setIsSubmitting(false);\n        return;\n      }\n      if (variant.pricePerYard && parseFloat(variant.pricePerYard) < 0) {\n        setMessage(`Variant ${i + 1}: Price per yard cannot be negative`);\n        setIsSubmitting(false);\n        return;\n      }\n    }\n\n    // Check for similar colors without descriptive names\n    for (let i = 0; i < variants.length; i++) {\n      for (let j = i + 1; j < variants.length; j++) {\n        const variant1 = variants[i];\n        const variant2 = variants[j];\n        if (areColorsSimilar(variant1.color, variant2.color)) {\n          const name1 = variant1.colorName || \"Unnamed\";\n          const name2 = variant2.colorName || \"Unnamed\";\n\n          // Check if names are too generic or similar\n          if (name1 === name2 || name1 === \"Unnamed\" || name2 === \"Unnamed\" || COLOR_PRESETS.some(preset => preset.name === name1 || preset.name === name2)) {\n            const proceed = window.confirm(`⚠️ Warning: Variants ${i + 1} and ${j + 1} have similar colors but generic names.\\n\\n` + `This might cause confusion during cutting and production.\\n\\n` + `Consider using more descriptive names like:\\n` + `• \"Black Line\" vs \"Black Circle\"\\n` + `• \"Navy Stripe\" vs \"Navy Solid\"\\n\\n` + `Do you want to continue anyway?`);\n            if (!proceed) {\n              setIsSubmitting(false);\n              return;\n            }\n          }\n        }\n      }\n    }\n    try {\n      const defResponse = await axios.post(\"http://localhost:8000/api/fabric-definitions/\", {\n        fabric_name: fabricName,\n        supplier: selectedSupplier.value,\n        date_added: dateAdded\n      });\n      if (defResponse.status === 201) {\n        const definitionId = defResponse.data.id;\n        for (let variant of variants) {\n          await axios.post(\"http://localhost:8000/api/fabric-variants/\", {\n            fabric_definition: definitionId,\n            color: variant.color,\n            color_name: variant.colorName,\n            total_yard: parseFloat(variant.totalYard) || 0,\n            price_per_yard: parseFloat(variant.pricePerYard) || 0\n          });\n        }\n        setMessage(\"✅ Fabric and variants created successfully!\");\n        setFabricName(\"\");\n        setSelectedSupplier(null);\n        setDateAdded(\"\");\n        setVariants([{\n          color: \"#000000\",\n          colorName: \"Black\",\n          totalYard: \"\",\n          pricePerYard: \"\"\n        }]);\n      }\n    } catch (error) {\n      console.error(\"Error creating fabric or variants:\", error);\n      setMessage(\"Error creating fabric or variants.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Function to select a preset color\n  const selectPresetColor = (index, preset) => {\n    const updated = [...variants];\n    updated[index].color = preset.color;\n    updated[index].colorName = preset.name;\n    setVariants(updated);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), \"Add Fabric\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: message.includes(\"✅\") ? \"success\" : \"danger\",\n        className: \"d-flex align-items-center\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-4 shadow-sm\",\n        style: {\n          backgroundColor: \"#D9EDFB\",\n          borderRadius: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Fabric Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: fabricName,\n                    onChange: e => setFabricName(e.target.value),\n                    required: true,\n                    placeholder: \"Enter fabric name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Supplier\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Loading suppliers...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(Select, {\n                    options: suppliers,\n                    value: selectedSupplier,\n                    onChange: setSelectedSupplier,\n                    placeholder: \"Select a supplier...\",\n                    isSearchable: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Date Added\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: dateAdded,\n                    onChange: e => setDateAdded(e.target.value),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mt-4 mb-3 border-bottom pb-2\",\n              children: \"Fabric Variants\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), variants.map((variant, index) => /*#__PURE__*/_jsxDEV(Card, {\n              className: \"mb-3 border\",\n              style: {\n                borderLeft: `5px solid ${variant.color}`,\n                borderRadius: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                className: \"d-flex justify-content-between align-items-center bg-light\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-0\",\n                  children: [\"Variant #\", index + 1]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), variants.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-danger\",\n                  size: \"sm\",\n                  onClick: () => handleRemoveVariant(index),\n                  children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this), \" Remove\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 4,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Color\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 346,\n                          columnNumber: 39\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"color\",\n                          value: variant.color,\n                          onChange: e => handleVariantChange(index, \"color\", e.target.value),\n                          className: \"me-2\",\n                          style: {\n                            width: '38px',\n                            height: '38px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 348,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"text\",\n                          placeholder: \"e.g., Black Line, Black Circle, Navy Stripe\",\n                          value: variant.colorName,\n                          onChange: e => handleVariantChange(index, \"colorName\", e.target.value)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted mb-2 d-block\",\n                        children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Tip:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 32\n                        }, this), \" Use descriptive names for similar colors (e.g., \\\"Black Line\\\", \\\"Black Circle\\\") to avoid confusion during cutting\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-presets d-flex flex-wrap gap-1 mt-1\",\n                        children: COLOR_PRESETS.map((preset, presetIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                          onClick: () => selectPresetColor(index, preset),\n                          style: {\n                            width: '20px',\n                            height: '20px',\n                            backgroundColor: preset.color,\n                            border: variant.color === preset.color ? '2px solid #000' : '1px solid #ccc',\n                            borderRadius: '4px',\n                            cursor: 'pointer'\n                          },\n                          title: preset.name\n                        }, presetIndex, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 367,\n                          columnNumber: 31\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 4,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Total Yard\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 387,\n                          columnNumber: 39\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"number\",\n                        min: \"0\",\n                        step: \"0.01\",\n                        value: variant.totalYard,\n                        onChange: e => {\n                          const value = e.target.value;\n                          // Only allow positive numbers\n                          if (value === '' || parseFloat(value) >= 0) {\n                            handleVariantChange(index, \"totalYard\", value);\n                          }\n                        },\n                        placeholder: \"Enter total yards\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 4,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Price per Yard\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 407,\n                          columnNumber: 39\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"number\",\n                        min: \"0\",\n                        step: \"0.01\",\n                        value: variant.pricePerYard,\n                        onChange: e => {\n                          const value = e.target.value;\n                          // Only allow positive numbers\n                          if (value === '' || parseFloat(value) >= 0) {\n                            handleVariantChange(index, \"pricePerYard\", value);\n                          }\n                        },\n                        placeholder: \"Enter price per yard\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 p-2 bg-light rounded d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '20px',\n                      height: '20px',\n                      backgroundColor: variant.color,\n                      border: '1px solid #ccc',\n                      borderRadius: '4px',\n                      marginRight: '8px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Preview:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: variant.colorName ? \"text-success fw-bold\" : \"text-warning\",\n                      children: variant.colorName || \"⚠️ Unnamed Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 25\n                    }, this), variant.totalYard ? ` - ${variant.totalYard} yards` : \" - No yards specified\", variant.pricePerYard ? ` - Rs. ${variant.pricePerYard}/yard` : \" - No price specified\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-primary\",\n                onClick: handleAddVariant,\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), \" Add Another Variant\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                disabled: isSubmitting,\n                className: \"px-5\",\n                children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 23\n                  }, this), \"Submitting...\"]\n                }, void 0, true) : 'Submit Fabric'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddFabric, \"CpyqXuUH10nqmizFVritpxNI7jU=\");\n_c = AddFabric;\nexport default AddFabric;\nvar _c;\n$RefreshReg$(_c, \"AddFabric\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Select", "FaPlus", "FaTrash", "Row", "Col", "Form", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "Spinner", "RoleBasedNavBar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "COLOR_PRESETS", "color", "name", "AddFabric", "_s", "fabricName", "setFabricName", "selectedSupplier", "setSelectedSupplier", "dateAdded", "setDateAdded", "variants", "setVariants", "colorName", "totalYard", "pricePerYard", "suppliers", "setSuppliers", "message", "setMessage", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "loading", "setLoading", "handleResize", "addEventListener", "removeEventListener", "get", "then", "response", "supplierOptions", "data", "map", "sup", "value", "supplier_id", "label", "catch", "error", "console", "handleAddVariant", "handleRemoveVariant", "index", "updated", "filter", "_", "i", "handleVariantChange", "field", "colorPreset", "find", "preset", "currentColorName", "isBasicPresetName", "some", "areColorsSimilar", "color1", "color2", "hexToRgb", "hex", "result", "exec", "r", "parseInt", "g", "b", "rgb1", "rgb2", "distance", "Math", "sqrt", "pow", "handleSubmit", "e", "preventDefault", "trim", "length", "variant", "parseFloat", "j", "variant1", "variant2", "name1", "name2", "proceed", "confirm", "defResponse", "post", "fabric_name", "supplier", "date_added", "status", "definitionId", "id", "fabric_definition", "color_name", "total_yard", "price_per_yard", "selectPresetColor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "width", "transition", "padding", "className", "includes", "backgroundColor", "borderRadius", "Body", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "required", "placeholder", "animation", "size", "options", "isSearchable", "borderLeft", "Header", "onClick", "height", "presetIndex", "border", "cursor", "title", "min", "step", "marginRight", "disabled", "as", "role", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/AddFabric.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport Select from \"react-select\";\r\nimport { FaPlus, FaTrash } from \"react-icons/fa\";\r\nimport { Row, Col, Form, <PERSON><PERSON>, Card, Alert, Spinner } from 'react-bootstrap';\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\n\r\n// Common color presets with names\r\nconst COLOR_PRESETS = [\r\n  { color: \"#000000\", name: \"Black\" },\r\n  { color: \"#FFFFFF\", name: \"White\" },\r\n  { color: \"#FF0000\", name: \"Red\" },\r\n  { color: \"#0000FF\", name: \"Blue\" },\r\n  { color: \"#008000\", name: \"Green\" },\r\n  { color: \"#FFFF00\", name: \"Yellow\" },\r\n  { color: \"#FFA500\", name: \"Orange\" },\r\n  { color: \"#800080\", name: \"Purple\" },\r\n  { color: \"#FFC0CB\", name: \"Pink\" },\r\n  { color: \"#A52A2A\", name: \"<PERSON>\" },\r\n  { color: \"#808080\", name: \"<PERSON>\" },\r\n];\r\n\r\nconst AddFabric = () => {\r\n  // State variables\r\n  const [fabricName, setFabricName] = useState(\"\");\r\n  const [selectedSupplier, setSelectedSupplier] = useState(null);\r\n  const [dateAdded, setDateAdded] = useState(\"\");\r\n  const [variants, setVariants] = useState([{ color: \"#000000\", colorName: \"Black\", totalYard: \"\", pricePerYard: \"\" }]);\r\n  const [suppliers, setSuppliers] = useState([]);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // Effect to handle sidebar state based on window size\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch suppliers\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios.get(\"http://localhost:8000/api/suppliers/\")\r\n      .then((response) => {\r\n        const supplierOptions = response.data.map((sup) => ({\r\n          value: sup.supplier_id,\r\n          label: sup.name,\r\n        }));\r\n        setSuppliers(supplierOptions);\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching suppliers:\", error);\r\n        setMessage(\"Failed to load suppliers\");\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  // Handle adding a variant\r\n  const handleAddVariant = () => {\r\n    setVariants([...variants, { color: \"#000000\", colorName: \"Black\", totalYard: \"\", pricePerYard: \"\" }]);\r\n  };\r\n\r\n  // Handle removing a variant\r\n  const handleRemoveVariant = (index) => {\r\n    const updated = variants.filter((_, i) => i !== index);\r\n    setVariants(updated);\r\n  };\r\n\r\n  // Handle variant input changes\r\n  const handleVariantChange = (index, field, value) => {\r\n    const updated = [...variants];\r\n    updated[index][field] = value;\r\n\r\n    // If color is changed, suggest a color name only if current name is empty or matches a preset\r\n    if (field === \"color\") {\r\n      const colorPreset = COLOR_PRESETS.find(preset => preset.color === value);\r\n      const currentColorName = updated[index].colorName;\r\n\r\n      // Only auto-update if the field is empty or contains a basic preset name\r\n      const isBasicPresetName = COLOR_PRESETS.some(preset => preset.name === currentColorName);\r\n\r\n      if (!currentColorName || isBasicPresetName) {\r\n        updated[index].colorName = colorPreset ? colorPreset.name : \"\";\r\n      }\r\n    }\r\n\r\n    setVariants(updated);\r\n  };\r\n\r\n  // Function to check if two colors are similar\r\n  const areColorsSimilar = (color1, color2) => {\r\n    // Convert hex to RGB\r\n    const hexToRgb = (hex) => {\r\n      const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\r\n      return result ? {\r\n        r: parseInt(result[1], 16),\r\n        g: parseInt(result[2], 16),\r\n        b: parseInt(result[3], 16)\r\n      } : null;\r\n    };\r\n\r\n    const rgb1 = hexToRgb(color1);\r\n    const rgb2 = hexToRgb(color2);\r\n\r\n    if (!rgb1 || !rgb2) return false;\r\n\r\n    // Calculate color difference using Euclidean distance\r\n    const distance = Math.sqrt(\r\n      Math.pow(rgb1.r - rgb2.r, 2) +\r\n      Math.pow(rgb1.g - rgb2.g, 2) +\r\n      Math.pow(rgb1.b - rgb2.b, 2)\r\n    );\r\n\r\n    // Colors are similar if distance is less than 50 (adjustable threshold)\r\n    return distance < 50;\r\n  };\r\n\r\n  // Submit handler\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setMessage(\"\");\r\n    setIsSubmitting(true);\r\n\r\n    // Basic validation\r\n    if (!fabricName.trim()) {\r\n      setMessage(\"Please enter a fabric name\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    if (!selectedSupplier) {\r\n      setMessage(\"Please select a supplier\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    if (!dateAdded) {\r\n      setMessage(\"Please select a date\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    // Validate variants\r\n    for (let i = 0; i < variants.length; i++) {\r\n      const variant = variants[i];\r\n\r\n      if (variant.totalYard && parseFloat(variant.totalYard) < 0) {\r\n        setMessage(`Variant ${i+1}: Total yard cannot be negative`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (variant.pricePerYard && parseFloat(variant.pricePerYard) < 0) {\r\n        setMessage(`Variant ${i+1}: Price per yard cannot be negative`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Check for similar colors without descriptive names\r\n    for (let i = 0; i < variants.length; i++) {\r\n      for (let j = i + 1; j < variants.length; j++) {\r\n        const variant1 = variants[i];\r\n        const variant2 = variants[j];\r\n\r\n        if (areColorsSimilar(variant1.color, variant2.color)) {\r\n          const name1 = variant1.colorName || \"Unnamed\";\r\n          const name2 = variant2.colorName || \"Unnamed\";\r\n\r\n          // Check if names are too generic or similar\r\n          if (name1 === name2 ||\r\n              name1 === \"Unnamed\" || name2 === \"Unnamed\" ||\r\n              COLOR_PRESETS.some(preset => preset.name === name1 || preset.name === name2)) {\r\n\r\n            const proceed = window.confirm(\r\n              `⚠️ Warning: Variants ${i+1} and ${j+1} have similar colors but generic names.\\n\\n` +\r\n              `This might cause confusion during cutting and production.\\n\\n` +\r\n              `Consider using more descriptive names like:\\n` +\r\n              `• \"Black Line\" vs \"Black Circle\"\\n` +\r\n              `• \"Navy Stripe\" vs \"Navy Solid\"\\n\\n` +\r\n              `Do you want to continue anyway?`\r\n            );\r\n\r\n            if (!proceed) {\r\n              setIsSubmitting(false);\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    try {\r\n      const defResponse = await axios.post(\"http://localhost:8000/api/fabric-definitions/\", {\r\n        fabric_name: fabricName,\r\n        supplier: selectedSupplier.value,\r\n        date_added: dateAdded,\r\n      });\r\n\r\n      if (defResponse.status === 201) {\r\n        const definitionId = defResponse.data.id;\r\n\r\n        for (let variant of variants) {\r\n          await axios.post(\"http://localhost:8000/api/fabric-variants/\", {\r\n            fabric_definition: definitionId,\r\n            color: variant.color,\r\n            color_name: variant.colorName,\r\n            total_yard: parseFloat(variant.totalYard) || 0,\r\n            price_per_yard: parseFloat(variant.pricePerYard) || 0,\r\n          });\r\n        }\r\n\r\n        setMessage(\"✅ Fabric and variants created successfully!\");\r\n        setFabricName(\"\");\r\n        setSelectedSupplier(null);\r\n        setDateAdded(\"\");\r\n        setVariants([{ color: \"#000000\", colorName: \"Black\", totalYard: \"\", pricePerYard: \"\" }]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating fabric or variants:\", error);\r\n      setMessage(\"Error creating fabric or variants.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to select a preset color\r\n  const selectPresetColor = (index, preset) => {\r\n    const updated = [...variants];\r\n    updated[index].color = preset.color;\r\n    updated[index].colorName = preset.name;\r\n    setVariants(updated);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <FaPlus className=\"me-2\" />\r\n          Add Fabric\r\n        </h2>\r\n\r\n        {message && (\r\n          <Alert\r\n            variant={message.includes(\"✅\") ? \"success\" : \"danger\"}\r\n            className=\"d-flex align-items-center\"\r\n          >\r\n            {message}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Fabric Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={fabricName}\r\n                      onChange={(e) => setFabricName(e.target.value)}\r\n                      required\r\n                      placeholder=\"Enter fabric name\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Supplier</strong></Form.Label>\r\n                    {loading ? (\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                        <span>Loading suppliers...</span>\r\n                      </div>\r\n                    ) : (\r\n                      <Select\r\n                        options={suppliers}\r\n                        value={selectedSupplier}\r\n                        onChange={setSelectedSupplier}\r\n                        placeholder=\"Select a supplier...\"\r\n                        isSearchable\r\n                      />\r\n                    )}\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Date Added</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={dateAdded}\r\n                      onChange={(e) => setDateAdded(e.target.value)}\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <h4 className=\"mt-4 mb-3 border-bottom pb-2\">Fabric Variants</h4>\r\n\r\n              {variants.map((variant, index) => (\r\n                <Card\r\n                  key={index}\r\n                  className=\"mb-3 border\"\r\n                  style={{\r\n                    borderLeft: `5px solid ${variant.color}`,\r\n                    borderRadius: \"8px\"\r\n                  }}\r\n                >\r\n                  <Card.Header className=\"d-flex justify-content-between align-items-center bg-light\">\r\n                    <h5 className=\"mb-0\">Variant #{index + 1}</h5>\r\n                    {variants.length > 1 && (\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={() => handleRemoveVariant(index)}\r\n                      >\r\n                        <FaTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    )}\r\n                  </Card.Header>\r\n                  <Card.Body>\r\n                    <Row>\r\n                      <Col md={4}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label><strong>Color</strong></Form.Label>\r\n                          <div className=\"d-flex align-items-center mb-2\">\r\n                            <Form.Control\r\n                              type=\"color\"\r\n                              value={variant.color}\r\n                              onChange={(e) => handleVariantChange(index, \"color\", e.target.value)}\r\n                              className=\"me-2\"\r\n                              style={{ width: '38px', height: '38px' }}\r\n                            />\r\n                            <Form.Control\r\n                              type=\"text\"\r\n                              placeholder=\"e.g., Black Line, Black Circle, Navy Stripe\"\r\n                              value={variant.colorName}\r\n                              onChange={(e) => handleVariantChange(index, \"colorName\", e.target.value)}\r\n                            />\r\n                          </div>\r\n                          <small className=\"text-muted mb-2 d-block\">\r\n                            💡 <strong>Tip:</strong> Use descriptive names for similar colors (e.g., \"Black Line\", \"Black Circle\") to avoid confusion during cutting\r\n                          </small>\r\n                          <div className=\"color-presets d-flex flex-wrap gap-1 mt-1\">\r\n                            {COLOR_PRESETS.map((preset, presetIndex) => (\r\n                              <div\r\n                                key={presetIndex}\r\n                                onClick={() => selectPresetColor(index, preset)}\r\n                                style={{\r\n                                  width: '20px',\r\n                                  height: '20px',\r\n                                  backgroundColor: preset.color,\r\n                                  border: variant.color === preset.color ? '2px solid #000' : '1px solid #ccc',\r\n                                  borderRadius: '4px',\r\n                                  cursor: 'pointer'\r\n                                }}\r\n                                title={preset.name}\r\n                              />\r\n                            ))}\r\n                          </div>\r\n                        </Form.Group>\r\n                      </Col>\r\n\r\n                      <Col md={4}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label><strong>Total Yard</strong></Form.Label>\r\n                          <Form.Control\r\n                            type=\"number\"\r\n                            min=\"0\"\r\n                            step=\"0.01\"\r\n                            value={variant.totalYard}\r\n                            onChange={(e) => {\r\n                              const value = e.target.value;\r\n                              // Only allow positive numbers\r\n                              if (value === '' || parseFloat(value) >= 0) {\r\n                                handleVariantChange(index, \"totalYard\", value);\r\n                              }\r\n                            }}\r\n                            placeholder=\"Enter total yards\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n\r\n                      <Col md={4}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label><strong>Price per Yard</strong></Form.Label>\r\n                          <Form.Control\r\n                            type=\"number\"\r\n                            min=\"0\"\r\n                            step=\"0.01\"\r\n                            value={variant.pricePerYard}\r\n                            onChange={(e) => {\r\n                              const value = e.target.value;\r\n                              // Only allow positive numbers\r\n                              if (value === '' || parseFloat(value) >= 0) {\r\n                                handleVariantChange(index, \"pricePerYard\", value);\r\n                              }\r\n                            }}\r\n                            placeholder=\"Enter price per yard\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    <div className=\"mt-2 p-2 bg-light rounded d-flex align-items-center\">\r\n                      <div\r\n                        style={{\r\n                          width: '20px',\r\n                          height: '20px',\r\n                          backgroundColor: variant.color,\r\n                          border: '1px solid #ccc',\r\n                          borderRadius: '4px',\r\n                          marginRight: '8px'\r\n                        }}\r\n                      ></div>\r\n                      <div>\r\n                        <strong>Preview:</strong>\r\n                        <span className={variant.colorName ? \"text-success fw-bold\" : \"text-warning\"}>\r\n                          {variant.colorName || \"⚠️ Unnamed Color\"}\r\n                        </span>\r\n                        {variant.totalYard ? ` - ${variant.totalYard} yards` : \" - No yards specified\"}\r\n                        {variant.pricePerYard ? ` - Rs. ${variant.pricePerYard}/yard` : \" - No price specified\"}\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              ))}\r\n\r\n              <div className=\"d-flex justify-content-center mb-4\">\r\n                <Button\r\n                  variant=\"outline-primary\"\r\n                  onClick={handleAddVariant}\r\n                  className=\"d-flex align-items-center\"\r\n                >\r\n                  <FaPlus className=\"me-2\" /> Add Another Variant\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Fabric'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n\r\n\r\nexport default AddFabric;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAChD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC9E,OAAO,sCAAsC;AAC7C,OAAOC,eAAe,MAAM,+BAA+B;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAAa,GAAG,CACpB;EAAEC,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAQ,CAAC,EACnC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAQ,CAAC,EACnC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAM,CAAC,EACjC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAO,CAAC,EAClC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAQ,CAAC,EACnC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAO,CAAC,EAClC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAQ,CAAC,EACnC;EAAED,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAO,CAAC,CACnC;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,CAAC;IAAEmB,KAAK,EAAE,SAAS;IAAEY,SAAS,EAAE,OAAO;IAAEC,SAAS,EAAE,EAAE;IAAEC,YAAY,EAAE;EAAG,CAAC,CAAC,CAAC;EACrH,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC0C,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6C,YAAY,GAAGA,CAAA,KAAM;MACzBL,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMJ,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd4C,UAAU,CAAC,IAAI,CAAC;IAChB3C,KAAK,CAAC+C,GAAG,CAAC,sCAAsC,CAAC,CAC9CC,IAAI,CAAEC,QAAQ,IAAK;MAClB,MAAMC,eAAe,GAAGD,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAEC,GAAG,KAAM;QAClDC,KAAK,EAAED,GAAG,CAACE,WAAW;QACtBC,KAAK,EAAEH,GAAG,CAACnC;MACb,CAAC,CAAC,CAAC;MACHe,YAAY,CAACiB,eAAe,CAAC;MAC7BP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDc,KAAK,CAAEC,KAAK,IAAK;MAChBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDvB,UAAU,CAAC,0BAA0B,CAAC;MACtCQ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhC,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE;MAAEV,KAAK,EAAE,SAAS;MAAEY,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE,EAAE;MAAEC,YAAY,EAAE;IAAG,CAAC,CAAC,CAAC;EACvG,CAAC;;EAED;EACA,MAAM8B,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,OAAO,GAAGpC,QAAQ,CAACqC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IACtDlC,WAAW,CAACmC,OAAO,CAAC;EACtB,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGA,CAACL,KAAK,EAAEM,KAAK,EAAEd,KAAK,KAAK;IACnD,MAAMS,OAAO,GAAG,CAAC,GAAGpC,QAAQ,CAAC;IAC7BoC,OAAO,CAACD,KAAK,CAAC,CAACM,KAAK,CAAC,GAAGd,KAAK;;IAE7B;IACA,IAAIc,KAAK,KAAK,OAAO,EAAE;MACrB,MAAMC,WAAW,GAAGrD,aAAa,CAACsD,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACtD,KAAK,KAAKqC,KAAK,CAAC;MACxE,MAAMkB,gBAAgB,GAAGT,OAAO,CAACD,KAAK,CAAC,CAACjC,SAAS;;MAEjD;MACA,MAAM4C,iBAAiB,GAAGzD,aAAa,CAAC0D,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACrD,IAAI,KAAKsD,gBAAgB,CAAC;MAExF,IAAI,CAACA,gBAAgB,IAAIC,iBAAiB,EAAE;QAC1CV,OAAO,CAACD,KAAK,CAAC,CAACjC,SAAS,GAAGwC,WAAW,GAAGA,WAAW,CAACnD,IAAI,GAAG,EAAE;MAChE;IACF;IAEAU,WAAW,CAACmC,OAAO,CAAC;EACtB,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC3C;IACA,MAAMC,QAAQ,GAAIC,GAAG,IAAK;MACxB,MAAMC,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACF,GAAG,CAAC;MACpE,OAAOC,MAAM,GAAG;QACdE,CAAC,EAAEC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1BI,CAAC,EAAED,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1BK,CAAC,EAAEF,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;MAC3B,CAAC,GAAG,IAAI;IACV,CAAC;IAED,MAAMM,IAAI,GAAGR,QAAQ,CAACF,MAAM,CAAC;IAC7B,MAAMW,IAAI,GAAGT,QAAQ,CAACD,MAAM,CAAC;IAE7B,IAAI,CAACS,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;;IAEhC;IACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CACxBD,IAAI,CAACE,GAAG,CAACL,IAAI,CAACJ,CAAC,GAAGK,IAAI,CAACL,CAAC,EAAE,CAAC,CAAC,GAC5BO,IAAI,CAACE,GAAG,CAACL,IAAI,CAACF,CAAC,GAAGG,IAAI,CAACH,CAAC,EAAE,CAAC,CAAC,GAC5BK,IAAI,CAACE,GAAG,CAACL,IAAI,CAACD,CAAC,GAAGE,IAAI,CAACF,CAAC,EAAE,CAAC,CAC7B,CAAC;;IAED;IACA,OAAOG,QAAQ,GAAG,EAAE;EACtB,CAAC;;EAED;EACA,MAAMI,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB3D,UAAU,CAAC,EAAE,CAAC;IACdE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAI,CAAChB,UAAU,CAAC0E,IAAI,CAAC,CAAC,EAAE;MACtB5D,UAAU,CAAC,4BAA4B,CAAC;MACxCE,eAAe,CAAC,KAAK,CAAC;MACtB;IACF;IAEA,IAAI,CAACd,gBAAgB,EAAE;MACrBY,UAAU,CAAC,0BAA0B,CAAC;MACtCE,eAAe,CAAC,KAAK,CAAC;MACtB;IACF;IAEA,IAAI,CAACZ,SAAS,EAAE;MACdU,UAAU,CAAC,sBAAsB,CAAC;MAClCE,eAAe,CAAC,KAAK,CAAC;MACtB;IACF;;IAEA;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,QAAQ,CAACqE,MAAM,EAAE9B,CAAC,EAAE,EAAE;MACxC,MAAM+B,OAAO,GAAGtE,QAAQ,CAACuC,CAAC,CAAC;MAE3B,IAAI+B,OAAO,CAACnE,SAAS,IAAIoE,UAAU,CAACD,OAAO,CAACnE,SAAS,CAAC,GAAG,CAAC,EAAE;QAC1DK,UAAU,CAAC,WAAW+B,CAAC,GAAC,CAAC,iCAAiC,CAAC;QAC3D7B,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,IAAI4D,OAAO,CAAClE,YAAY,IAAImE,UAAU,CAACD,OAAO,CAAClE,YAAY,CAAC,GAAG,CAAC,EAAE;QAChEI,UAAU,CAAC,WAAW+B,CAAC,GAAC,CAAC,qCAAqC,CAAC;QAC/D7B,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;IACF;;IAEA;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,QAAQ,CAACqE,MAAM,EAAE9B,CAAC,EAAE,EAAE;MACxC,KAAK,IAAIiC,CAAC,GAAGjC,CAAC,GAAG,CAAC,EAAEiC,CAAC,GAAGxE,QAAQ,CAACqE,MAAM,EAAEG,CAAC,EAAE,EAAE;QAC5C,MAAMC,QAAQ,GAAGzE,QAAQ,CAACuC,CAAC,CAAC;QAC5B,MAAMmC,QAAQ,GAAG1E,QAAQ,CAACwE,CAAC,CAAC;QAE5B,IAAIxB,gBAAgB,CAACyB,QAAQ,CAACnF,KAAK,EAAEoF,QAAQ,CAACpF,KAAK,CAAC,EAAE;UACpD,MAAMqF,KAAK,GAAGF,QAAQ,CAACvE,SAAS,IAAI,SAAS;UAC7C,MAAM0E,KAAK,GAAGF,QAAQ,CAACxE,SAAS,IAAI,SAAS;;UAE7C;UACA,IAAIyE,KAAK,KAAKC,KAAK,IACfD,KAAK,KAAK,SAAS,IAAIC,KAAK,KAAK,SAAS,IAC1CvF,aAAa,CAAC0D,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACrD,IAAI,KAAKoF,KAAK,IAAI/B,MAAM,CAACrD,IAAI,KAAKqF,KAAK,CAAC,EAAE;YAEhF,MAAMC,OAAO,GAAGhE,MAAM,CAACiE,OAAO,CAC5B,wBAAwBvC,CAAC,GAAC,CAAC,QAAQiC,CAAC,GAAC,CAAC,6CAA6C,GACnF,+DAA+D,GAC/D,+CAA+C,GAC/C,oCAAoC,GACpC,qCAAqC,GACrC,iCACF,CAAC;YAED,IAAI,CAACK,OAAO,EAAE;cACZnE,eAAe,CAAC,KAAK,CAAC;cACtB;YACF;UACF;QACF;MACF;IACF;IAEA,IAAI;MACF,MAAMqE,WAAW,GAAG,MAAM1G,KAAK,CAAC2G,IAAI,CAAC,+CAA+C,EAAE;QACpFC,WAAW,EAAEvF,UAAU;QACvBwF,QAAQ,EAAEtF,gBAAgB,CAAC+B,KAAK;QAChCwD,UAAU,EAAErF;MACd,CAAC,CAAC;MAEF,IAAIiF,WAAW,CAACK,MAAM,KAAK,GAAG,EAAE;QAC9B,MAAMC,YAAY,GAAGN,WAAW,CAACvD,IAAI,CAAC8D,EAAE;QAExC,KAAK,IAAIhB,OAAO,IAAItE,QAAQ,EAAE;UAC5B,MAAM3B,KAAK,CAAC2G,IAAI,CAAC,4CAA4C,EAAE;YAC7DO,iBAAiB,EAAEF,YAAY;YAC/B/F,KAAK,EAAEgF,OAAO,CAAChF,KAAK;YACpBkG,UAAU,EAAElB,OAAO,CAACpE,SAAS;YAC7BuF,UAAU,EAAElB,UAAU,CAACD,OAAO,CAACnE,SAAS,CAAC,IAAI,CAAC;YAC9CuF,cAAc,EAAEnB,UAAU,CAACD,OAAO,CAAClE,YAAY,CAAC,IAAI;UACtD,CAAC,CAAC;QACJ;QAEAI,UAAU,CAAC,6CAA6C,CAAC;QACzDb,aAAa,CAAC,EAAE,CAAC;QACjBE,mBAAmB,CAAC,IAAI,CAAC;QACzBE,YAAY,CAAC,EAAE,CAAC;QAChBE,WAAW,CAAC,CAAC;UAAEX,KAAK,EAAE,SAAS;UAAEY,SAAS,EAAE,OAAO;UAAEC,SAAS,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAC,CAAC,CAAC;MAC1F;IACF,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DvB,UAAU,CAAC,oCAAoC,CAAC;IAClD,CAAC,SAAS;MACRE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMiF,iBAAiB,GAAGA,CAACxD,KAAK,EAAES,MAAM,KAAK;IAC3C,MAAMR,OAAO,GAAG,CAAC,GAAGpC,QAAQ,CAAC;IAC7BoC,OAAO,CAACD,KAAK,CAAC,CAAC7C,KAAK,GAAGsD,MAAM,CAACtD,KAAK;IACnC8C,OAAO,CAACD,KAAK,CAAC,CAACjC,SAAS,GAAG0C,MAAM,CAACrD,IAAI;IACtCU,WAAW,CAACmC,OAAO,CAAC;EACtB,CAAC;EAED,oBACElD,OAAA,CAAAE,SAAA;IAAAwG,QAAA,gBACE1G,OAAA,CAACF,eAAe;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnB9G,OAAA;MACE+G,KAAK,EAAE;QACLC,UAAU,EAAEvF,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CwF,KAAK,EAAE,eAAexF,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzDyF,UAAU,EAAE,eAAe;QAC3BC,OAAO,EAAE;MACX,CAAE;MAAAT,QAAA,gBAEF1G,OAAA;QAAIoH,SAAS,EAAC,MAAM;QAAAV,QAAA,gBAClB1G,OAAA,CAACX,MAAM;UAAC+H,SAAS,EAAC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,cAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJzF,OAAO,iBACNrB,OAAA,CAACJ,KAAK;QACJwF,OAAO,EAAE/D,OAAO,CAACgG,QAAQ,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,QAAS;QACtDD,SAAS,EAAC,2BAA2B;QAAAV,QAAA,EAEpCrF;MAAO;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,eAED9G,OAAA,CAACL,IAAI;QAACyH,SAAS,EAAC,gBAAgB;QAACL,KAAK,EAAE;UAAEO,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAb,QAAA,eAC3F1G,OAAA,CAACL,IAAI,CAAC6H,IAAI;UAAAd,QAAA,eACR1G,OAAA,CAACP,IAAI;YAACgI,QAAQ,EAAE1C,YAAa;YAAA2B,QAAA,gBAC3B1G,OAAA,CAACT,GAAG;cAAAmH,QAAA,gBACF1G,OAAA,CAACR,GAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACT1G,OAAA,CAACP,IAAI,CAACkI,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAV,QAAA,gBAC1B1G,OAAA,CAACP,IAAI,CAACmI,KAAK;oBAAAlB,QAAA,eAAC1G,OAAA;sBAAA0G,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrD9G,OAAA,CAACP,IAAI,CAACoI,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXrF,KAAK,EAAEjC,UAAW;oBAClBuH,QAAQ,EAAG/C,CAAC,IAAKvE,aAAa,CAACuE,CAAC,CAACgD,MAAM,CAACvF,KAAK,CAAE;oBAC/CwF,QAAQ;oBACRC,WAAW,EAAC;kBAAmB;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN9G,OAAA,CAACR,GAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACT1G,OAAA,CAACP,IAAI,CAACkI,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAV,QAAA,gBAC1B1G,OAAA,CAACP,IAAI,CAACmI,KAAK;oBAAAlB,QAAA,eAAC1G,OAAA;sBAAA0G,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACjDjF,OAAO,gBACN7B,OAAA;oBAAKoH,SAAS,EAAC,2BAA2B;oBAAAV,QAAA,gBACxC1G,OAAA,CAACH,OAAO;sBAACsI,SAAS,EAAC,QAAQ;sBAACC,IAAI,EAAC,IAAI;sBAAChB,SAAS,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzD9G,OAAA;sBAAA0G,QAAA,EAAM;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,gBAEN9G,OAAA,CAACZ,MAAM;oBACLiJ,OAAO,EAAElH,SAAU;oBACnBsB,KAAK,EAAE/B,gBAAiB;oBACxBqH,QAAQ,EAAEpH,mBAAoB;oBAC9BuH,WAAW,EAAC,sBAAsB;oBAClCI,YAAY;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA,CAACT,GAAG;cAAAmH,QAAA,eACF1G,OAAA,CAACR,GAAG;gBAACkI,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACT1G,OAAA,CAACP,IAAI,CAACkI,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAV,QAAA,gBAC1B1G,OAAA,CAACP,IAAI,CAACmI,KAAK;oBAAAlB,QAAA,eAAC1G,OAAA;sBAAA0G,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpD9G,OAAA,CAACP,IAAI,CAACoI,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXrF,KAAK,EAAE7B,SAAU;oBACjBmH,QAAQ,EAAG/C,CAAC,IAAKnE,YAAY,CAACmE,CAAC,CAACgD,MAAM,CAACvF,KAAK,CAAE;oBAC9CwF,QAAQ;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAIoH,SAAS,EAAC,8BAA8B;cAAAV,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEhEhG,QAAQ,CAACyB,GAAG,CAAC,CAAC6C,OAAO,EAAEnC,KAAK,kBAC3BjD,OAAA,CAACL,IAAI;cAEHyH,SAAS,EAAC,aAAa;cACvBL,KAAK,EAAE;gBACLwB,UAAU,EAAE,aAAanD,OAAO,CAAChF,KAAK,EAAE;gBACxCmH,YAAY,EAAE;cAChB,CAAE;cAAAb,QAAA,gBAEF1G,OAAA,CAACL,IAAI,CAAC6I,MAAM;gBAACpB,SAAS,EAAC,4DAA4D;gBAAAV,QAAA,gBACjF1G,OAAA;kBAAIoH,SAAS,EAAC,MAAM;kBAAAV,QAAA,GAAC,WAAS,EAACzD,KAAK,GAAG,CAAC;gBAAA;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC7ChG,QAAQ,CAACqE,MAAM,GAAG,CAAC,iBAClBnF,OAAA,CAACN,MAAM;kBACL0F,OAAO,EAAC,gBAAgB;kBACxBgD,IAAI,EAAC,IAAI;kBACTK,OAAO,EAAEA,CAAA,KAAMzF,mBAAmB,CAACC,KAAK,CAAE;kBAAAyD,QAAA,gBAE1C1G,OAAA,CAACV,OAAO;oBAAC8H,SAAS,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAC9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eACd9G,OAAA,CAACL,IAAI,CAAC6H,IAAI;gBAAAd,QAAA,gBACR1G,OAAA,CAACT,GAAG;kBAAAmH,QAAA,gBACF1G,OAAA,CAACR,GAAG;oBAACkI,EAAE,EAAE,CAAE;oBAAAhB,QAAA,eACT1G,OAAA,CAACP,IAAI,CAACkI,KAAK;sBAACP,SAAS,EAAC,MAAM;sBAAAV,QAAA,gBAC1B1G,OAAA,CAACP,IAAI,CAACmI,KAAK;wBAAAlB,QAAA,eAAC1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC/C9G,OAAA;wBAAKoH,SAAS,EAAC,gCAAgC;wBAAAV,QAAA,gBAC7C1G,OAAA,CAACP,IAAI,CAACoI,OAAO;0BACXC,IAAI,EAAC,OAAO;0BACZrF,KAAK,EAAE2C,OAAO,CAAChF,KAAM;0BACrB2H,QAAQ,EAAG/C,CAAC,IAAK1B,mBAAmB,CAACL,KAAK,EAAE,OAAO,EAAE+B,CAAC,CAACgD,MAAM,CAACvF,KAAK,CAAE;0BACrE2E,SAAS,EAAC,MAAM;0BAChBL,KAAK,EAAE;4BAAEE,KAAK,EAAE,MAAM;4BAAEyB,MAAM,EAAE;0BAAO;wBAAE;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACF9G,OAAA,CAACP,IAAI,CAACoI,OAAO;0BACXC,IAAI,EAAC,MAAM;0BACXI,WAAW,EAAC,6CAA6C;0BACzDzF,KAAK,EAAE2C,OAAO,CAACpE,SAAU;0BACzB+G,QAAQ,EAAG/C,CAAC,IAAK1B,mBAAmB,CAACL,KAAK,EAAE,WAAW,EAAE+B,CAAC,CAACgD,MAAM,CAACvF,KAAK;wBAAE;0BAAAkE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACN9G,OAAA;wBAAOoH,SAAS,EAAC,yBAAyB;wBAAAV,QAAA,GAAC,eACtC,eAAA1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,wHAC1B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACR9G,OAAA;wBAAKoH,SAAS,EAAC,2CAA2C;wBAAAV,QAAA,EACvDvG,aAAa,CAACoC,GAAG,CAAC,CAACmB,MAAM,EAAEiF,WAAW,kBACrC3I,OAAA;0BAEEyI,OAAO,EAAEA,CAAA,KAAMhC,iBAAiB,CAACxD,KAAK,EAAES,MAAM,CAAE;0BAChDqD,KAAK,EAAE;4BACLE,KAAK,EAAE,MAAM;4BACbyB,MAAM,EAAE,MAAM;4BACdpB,eAAe,EAAE5D,MAAM,CAACtD,KAAK;4BAC7BwI,MAAM,EAAExD,OAAO,CAAChF,KAAK,KAAKsD,MAAM,CAACtD,KAAK,GAAG,gBAAgB,GAAG,gBAAgB;4BAC5EmH,YAAY,EAAE,KAAK;4BACnBsB,MAAM,EAAE;0BACV,CAAE;0BACFC,KAAK,EAAEpF,MAAM,CAACrD;wBAAK,GAVdsI,WAAW;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAWjB,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAEN9G,OAAA,CAACR,GAAG;oBAACkI,EAAE,EAAE,CAAE;oBAAAhB,QAAA,eACT1G,OAAA,CAACP,IAAI,CAACkI,KAAK;sBAACP,SAAS,EAAC,MAAM;sBAAAV,QAAA,gBAC1B1G,OAAA,CAACP,IAAI,CAACmI,KAAK;wBAAAlB,QAAA,eAAC1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACpD9G,OAAA,CAACP,IAAI,CAACoI,OAAO;wBACXC,IAAI,EAAC,QAAQ;wBACbiB,GAAG,EAAC,GAAG;wBACPC,IAAI,EAAC,MAAM;wBACXvG,KAAK,EAAE2C,OAAO,CAACnE,SAAU;wBACzB8G,QAAQ,EAAG/C,CAAC,IAAK;0BACf,MAAMvC,KAAK,GAAGuC,CAAC,CAACgD,MAAM,CAACvF,KAAK;0BAC5B;0BACA,IAAIA,KAAK,KAAK,EAAE,IAAI4C,UAAU,CAAC5C,KAAK,CAAC,IAAI,CAAC,EAAE;4BAC1Ca,mBAAmB,CAACL,KAAK,EAAE,WAAW,EAAER,KAAK,CAAC;0BAChD;wBACF,CAAE;wBACFyF,WAAW,EAAC;sBAAmB;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAEN9G,OAAA,CAACR,GAAG;oBAACkI,EAAE,EAAE,CAAE;oBAAAhB,QAAA,eACT1G,OAAA,CAACP,IAAI,CAACkI,KAAK;sBAACP,SAAS,EAAC,MAAM;sBAAAV,QAAA,gBAC1B1G,OAAA,CAACP,IAAI,CAACmI,KAAK;wBAAAlB,QAAA,eAAC1G,OAAA;0BAAA0G,QAAA,EAAQ;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACxD9G,OAAA,CAACP,IAAI,CAACoI,OAAO;wBACXC,IAAI,EAAC,QAAQ;wBACbiB,GAAG,EAAC,GAAG;wBACPC,IAAI,EAAC,MAAM;wBACXvG,KAAK,EAAE2C,OAAO,CAAClE,YAAa;wBAC5B6G,QAAQ,EAAG/C,CAAC,IAAK;0BACf,MAAMvC,KAAK,GAAGuC,CAAC,CAACgD,MAAM,CAACvF,KAAK;0BAC5B;0BACA,IAAIA,KAAK,KAAK,EAAE,IAAI4C,UAAU,CAAC5C,KAAK,CAAC,IAAI,CAAC,EAAE;4BAC1Ca,mBAAmB,CAACL,KAAK,EAAE,cAAc,EAAER,KAAK,CAAC;0BACnD;wBACF,CAAE;wBACFyF,WAAW,EAAC;sBAAsB;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9G,OAAA;kBAAKoH,SAAS,EAAC,qDAAqD;kBAAAV,QAAA,gBAClE1G,OAAA;oBACE+G,KAAK,EAAE;sBACLE,KAAK,EAAE,MAAM;sBACbyB,MAAM,EAAE,MAAM;sBACdpB,eAAe,EAAElC,OAAO,CAAChF,KAAK;sBAC9BwI,MAAM,EAAE,gBAAgB;sBACxBrB,YAAY,EAAE,KAAK;sBACnB0B,WAAW,EAAE;oBACf;kBAAE;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP9G,OAAA;oBAAA0G,QAAA,gBACE1G,OAAA;sBAAA0G,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzB9G,OAAA;sBAAMoH,SAAS,EAAEhC,OAAO,CAACpE,SAAS,GAAG,sBAAsB,GAAG,cAAe;sBAAA0F,QAAA,EAC1EtB,OAAO,CAACpE,SAAS,IAAI;oBAAkB;sBAAA2F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,EACN1B,OAAO,CAACnE,SAAS,GAAG,MAAMmE,OAAO,CAACnE,SAAS,QAAQ,GAAG,uBAAuB,EAC7EmE,OAAO,CAAClE,YAAY,GAAG,UAAUkE,OAAO,CAAClE,YAAY,OAAO,GAAG,uBAAuB;kBAAA;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GA3HP7D,KAAK;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4HN,CACP,CAAC,eAEF9G,OAAA;cAAKoH,SAAS,EAAC,oCAAoC;cAAAV,QAAA,eACjD1G,OAAA,CAACN,MAAM;gBACL0F,OAAO,EAAC,iBAAiB;gBACzBqD,OAAO,EAAE1F,gBAAiB;gBAC1BqE,SAAS,EAAC,2BAA2B;gBAAAV,QAAA,gBAErC1G,OAAA,CAACX,MAAM;kBAAC+H,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9G,OAAA;cAAKoH,SAAS,EAAC,oCAAoC;cAAAV,QAAA,eACjD1G,OAAA,CAACN,MAAM;gBACLoI,IAAI,EAAC,QAAQ;gBACb1C,OAAO,EAAC,SAAS;gBACjBgD,IAAI,EAAC,IAAI;gBACTc,QAAQ,EAAE3H,YAAa;gBACvB6F,SAAS,EAAC,MAAM;gBAAAV,QAAA,EAEfnF,YAAY,gBACXvB,OAAA,CAAAE,SAAA;kBAAAwG,QAAA,gBACE1G,OAAA,CAACH,OAAO;oBAACsJ,EAAE,EAAC,MAAM;oBAAChB,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAACgB,IAAI,EAAC,QAAQ;oBAAC,eAAY,MAAM;oBAAChC,SAAS,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAEtG;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACvG,EAAA,CA5cID,SAAS;AAAA+I,EAAA,GAAT/I,SAAS;AAgdf,eAAeA,SAAS;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}