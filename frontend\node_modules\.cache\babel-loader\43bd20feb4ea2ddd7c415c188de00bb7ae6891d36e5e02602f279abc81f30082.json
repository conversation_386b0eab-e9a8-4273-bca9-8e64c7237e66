{"ast": null, "code": "import React,{useState,useEffect}from'react';import axios from'axios';import{useParams,useNavigate}from'react-router-dom';import RoleBasedNavBar from'../components/RoleBasedNavBar';import{Container,Row,Col,Card,Table,Badge,Spinner,Alert,Button,ListGroup,Modal,Image}from'react-bootstrap';import{FaArrowLeft,FaCalendarAlt,FaCut,FaTshirt,FaInfoCircle,FaRulerHorizontal,FaClipboardList,FaMoneyBillWave,FaDownload,FaFilePdf,FaCheck,FaTimes,FaImage}from'react-icons/fa';import{jsPDF}from'jspdf';import autoTable from'jspdf-autotable';// We'll use a different approach for the logo\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CuttingRecordDetail=()=>{var _cuttingRecord$fabric,_cuttingRecord$detail3;const{recordId}=useParams();const navigate=useNavigate();const[cuttingRecord,setCuttingRecord]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[isSidebarOpen,setIsSidebarOpen]=useState(window.innerWidth>=768);const[returnPath,setReturnPath]=useState('');const[showPdfModal,setShowPdfModal]=useState(false);// Add resize event listener to update sidebar state\nuseEffect(()=>{const handleResize=()=>{setIsSidebarOpen(window.innerWidth>=768);};window.addEventListener(\"resize\",handleResize);return()=>window.removeEventListener(\"resize\",handleResize);},[]);// Get the return path from localStorage if available\nuseEffect(()=>{const savedReturnPath=localStorage.getItem('cuttingRecordReturnPath');if(savedReturnPath){setReturnPath(savedReturnPath);}else{// Default to the cutting records list\nsetReturnPath('/viewcutting');}// Clean up the localStorage when component unmounts\nreturn()=>{localStorage.removeItem('cuttingRecordReturnPath');};},[]);// Fetch cutting record data\nuseEffect(()=>{setLoading(true);axios.get(`http://localhost:8000/api/cutting/records/${recordId}/`).then(response=>{setCuttingRecord(response.data);setLoading(false);}).catch(error=>{console.error(\"Error fetching cutting record details:\",error);setError(\"Failed to load cutting record details.\");setLoading(false);});},[recordId]);// Format date for display\nconst formatDate=dateString=>{const options={year:'numeric',month:'short',day:'numeric'};return new Date(dateString).toLocaleDateString(undefined,options);};// Calculate total pieces for a detail\nconst calculateTotalPieces=detail=>{return detail.xs+detail.s+detail.m+detail.l+detail.xl;};// Calculate total pieces for all details\nconst calculateTotalAllPieces=()=>{if(!cuttingRecord||!cuttingRecord.details)return 0;return cuttingRecord.details.reduce((total,detail)=>{return total+calculateTotalPieces(detail);},0);};// Calculate total yard usage\nconst calculateTotalYardUsage=()=>{if(!cuttingRecord||!cuttingRecord.details)return 0;return cuttingRecord.details.reduce((total,detail)=>{return total+parseFloat(detail.yard_usage);},0).toFixed(2);};// Calculate fabric cutting value (cost)\nconst calculateCuttingValue=detail=>{if(!detail||!detail.fabric_variant_data)return 0;const yardUsage=parseFloat(detail.yard_usage);const pricePerYard=parseFloat(detail.fabric_variant_data.price_per_yard);return(yardUsage*pricePerYard).toFixed(2);};// Calculate total cutting value\nconst calculateTotalCuttingValue=()=>{if(!cuttingRecord||!cuttingRecord.details)return 0;return cuttingRecord.details.reduce((total,detail)=>{if(!detail.fabric_variant_data)return total;const value=parseFloat(detail.yard_usage)*parseFloat(detail.fabric_variant_data.price_per_yard);return total+value;},0).toFixed(2);};// Handle back button click\nconst handleBack=()=>{navigate(returnPath);};// Open PDF confirmation modal\nconst openPdfModal=()=>{setShowPdfModal(true);};// Close PDF confirmation modal\nconst closePdfModal=()=>{setShowPdfModal(false);};// Generate PDF report\nconst generatePDF=()=>{if(!cuttingRecord)return;try{var _cuttingRecord$detail,_cuttingRecord$detail2;const productName=cuttingRecord.product_name||\"N/A\";// Get fabric names from details\nconst fabricNames=new Set();(_cuttingRecord$detail=cuttingRecord.details)===null||_cuttingRecord$detail===void 0?void 0:_cuttingRecord$detail.forEach(detail=>{var _detail$fabric_varian,_detail$fabric_varian2;if((_detail$fabric_varian=detail.fabric_variant_data)!==null&&_detail$fabric_varian!==void 0&&(_detail$fabric_varian2=_detail$fabric_varian.fabric_definition_data)!==null&&_detail$fabric_varian2!==void 0&&_detail$fabric_varian2.fabric_name){fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);}});const fabricName=Array.from(fabricNames).join(', ')||\"N/A\";// Create PDF document with orientation and unit specifications\nconst doc=new jsPDF({orientation:'portrait',unit:'mm',format:'a4'});// Add the actual logo from the public directory\ntry{// Get the base URL for the current environment\nconst baseUrl=window.location.origin;// Add the logo to the PDF\ndoc.addImage(`${baseUrl}/logo.png`,'PNG',14,10,20,20);}catch(logoError){console.warn(\"Could not add logo to PDF:\",logoError);// Fallback to a simple placeholder if the logo can't be loaded\ndoc.setFillColor(41,128,185);// Primary blue color\ndoc.rect(14,10,20,20,'F');// Add \"PF\" text as a simple logo\ndoc.setFontSize(14);doc.setTextColor(255,255,255);doc.text(\"PF\",24,22,{align:'center'});}// Reset text color for the rest of the document\ndoc.setTextColor(0,0,0);// Add title and company info\ndoc.setFontSize(20);doc.setTextColor(0,0,0);doc.text(\"Cutting Record Report\",105,20,{align:'center'});doc.setFontSize(12);doc.text(\"Pri Fashion - Garment Management System\",105,28,{align:'center'});doc.text(`Generated on: ${new Date().toLocaleDateString()}`,105,34,{align:'center'});// Add cutting record details\ndoc.setFontSize(14);doc.text(\"Cutting Record Details\",14,45);doc.setFontSize(10);const details=[[\"Product Name:\",productName],[\"Fabric Name:\",fabricName],[\"Cutting Date:\",cuttingRecord.cutting_date],[\"Total Quantity:\",calculateTotalAllPieces().toString()],[\"Total Yard Usage:\",calculateTotalYardUsage().toString()],[\"Color Variants Used:\",((_cuttingRecord$detail2=cuttingRecord.details)===null||_cuttingRecord$detail2===void 0?void 0:_cuttingRecord$detail2.length.toString())||\"0\"],[\"Total Fabric Cost:\",`Rs. ${calculateTotalCuttingValue()}`]];// First table\nlet finalY=50;autoTable(doc,{startY:finalY,head:[[\"Property\",\"Value\"]],body:details,theme:'grid',headStyles:{fillColor:[41,128,185],textColor:255},styles:{fontSize:10}});// Get the final Y position after the first table\nfinalY=(doc.lastAutoTable||doc.previousAutoTable).finalY+10;// Add color usage details\ndoc.setFontSize(14);doc.text(\"Color Usage Details\",14,finalY);if(cuttingRecord.details&&cuttingRecord.details.length>0){const colorDetails=cuttingRecord.details.map(detail=>{var _detail$fabric_varian3,_detail$fabric_varian4,_detail$fabric_varian5;// Get the color code for display\nconst colorCode=((_detail$fabric_varian3=detail.fabric_variant_data)===null||_detail$fabric_varian3===void 0?void 0:_detail$fabric_varian3.color)||\"#CCCCCC\";const colorName=((_detail$fabric_varian4=detail.fabric_variant_data)===null||_detail$fabric_varian4===void 0?void 0:_detail$fabric_varian4.color_name)||\"N/A\";return[colorName,colorCode,// Add color code as a separate column\n`${parseFloat(detail.yard_usage).toFixed(2)} yards`,`Rs. ${(_detail$fabric_varian5=detail.fabric_variant_data)===null||_detail$fabric_varian5===void 0?void 0:_detail$fabric_varian5.price_per_yard.toFixed(2)}`,`Rs. ${calculateCuttingValue(detail)}`,detail.xs||0,detail.s||0,detail.m||0,detail.l||0,detail.xl||0,calculateTotalPieces(detail)];});// Second table with color swatches\nautoTable(doc,{startY:finalY+5,head:[[\"Color Name\",\"Color\",\"Yard Usage\",\"Price/Yard\",\"Fabric Cost\",\"XS\",\"S\",\"M\",\"L\",\"XL\",\"Total\"]],body:colorDetails,theme:'grid',headStyles:{fillColor:[41,128,185],textColor:255},styles:{fontSize:9},columnStyles:{0:{cellWidth:25},1:{cellWidth:15},// Color swatch column\n2:{cellWidth:20},3:{cellWidth:20},4:{cellWidth:20}},// Add color swatches to the color column\ndidDrawCell:data=>{if(data.section==='body'&&data.column.index===1){const colorHex=data.cell.raw;try{// Convert hex color to RGB\nconst r=parseInt(colorHex.substring(1,3),16)||0;const g=parseInt(colorHex.substring(3,5),16)||0;const b=parseInt(colorHex.substring(5,7),16)||0;// Set fill color using RGB values\ndoc.setFillColor(r,g,b);// Draw a color rectangle\ndoc.rect(data.cell.x+2,data.cell.y+2,data.cell.width-4,data.cell.height-4,'F');// Add a border around the color swatch\ndoc.setDrawColor(200,200,200);doc.rect(data.cell.x+2,data.cell.y+2,data.cell.width-4,data.cell.height-4,'S');}catch(error){console.warn(\"Error drawing color swatch:\",error);// Fallback to a gray color if there's an error\ndoc.setFillColor(200,200,200);doc.rect(data.cell.x+2,data.cell.y+2,data.cell.width-4,data.cell.height-4,'F');}}}});}else{doc.text(\"No color details available\",14,finalY+5);}// Get the final Y position after the second table\nfinalY=(doc.lastAutoTable||doc.previousAutoTable).finalY+15;// Add signature fields\ndoc.setFontSize(12);doc.text(\"Signatures:\",14,finalY);// Draw signature lines\nfinalY+=8;// Owner signature\ndoc.line(14,finalY+15,80,finalY+15);// Signature line\ndoc.setFontSize(10);doc.text(\"Owner Signature\",14,finalY+20);doc.text(\"Date: ________________\",14,finalY+25);// Cutter signature\ndoc.line(120,finalY+15,186,finalY+15);// Signature line\ndoc.text(\"Cutter Signature\",120,finalY+20);doc.text(\"Date: ________________\",120,finalY+25);// Add footer\nconst pageCount=doc.internal.getNumberOfPages();for(let i=1;i<=pageCount;i++){doc.setPage(i);doc.setFontSize(8);doc.text(`Page ${i} of ${pageCount} - Pri Fashion Garment Management System`,105,doc.internal.pageSize.height-10,{align:'center'});}// Save the PDF with a clean filename\nconst cleanProductName=productName.replace(/[^a-zA-Z0-9]/g,'_');doc.save(`Cutting_Record_${cuttingRecord.id}_${cleanProductName}.pdf`);// Close the modal\nclosePdfModal();}catch(error){console.error(\"Error generating PDF:\",error);alert(\"Failed to generate PDF. Please try again: \"+error.message);closePdfModal();}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(RoleBasedNavBar,{}),/*#__PURE__*/_jsxs(Container,{fluid:true,style:{marginLeft:isSidebarOpen?\"240px\":\"70px\",width:`calc(100% - ${isSidebarOpen?\"240px\":\"70px\"})`,transition:\"all 0.3s ease\",padding:\"20px\"},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center mb-3\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-secondary\",onClick:handleBack,children:[/*#__PURE__*/_jsx(FaArrowLeft,{className:\"me-2\"}),\" Back\"]}),!loading&&cuttingRecord&&/*#__PURE__*/_jsxs(Button,{variant:\"outline-success\",onClick:openPdfModal,children:[/*#__PURE__*/_jsx(FaDownload,{className:\"me-2\"}),\" Download PDF\"]})]}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"text-center\",children:error}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center my-5\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",variant:\"primary\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:\"Loading cutting record details...\"})]}):cuttingRecord&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Card,{className:\"mb-4 shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-primary text-white\",children:/*#__PURE__*/_jsxs(\"h4\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(FaCut,{className:\"me-2\"}),\"Cutting Record Details\"]})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-3\",children:cuttingRecord.product_name||\"Unnamed Cutting Record\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Fabric:\"}),' ',/*#__PURE__*/_jsx(\"span\",{className:\"text-primary\",children:(_cuttingRecord$fabric=cuttingRecord.fabric_definition_data)===null||_cuttingRecord$fabric===void 0?void 0:_cuttingRecord$fabric.fabric_name})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cutting Date:\"}),' ',/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(FaCalendarAlt,{className:\"me-1 text-secondary\"}),formatDate(cuttingRecord.cutting_date)]})]}),cuttingRecord.description&&/*#__PURE__*/_jsxs(\"p\",{className:\"mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Description:\"}),' ',/*#__PURE__*/_jsx(\"span\",{children:cuttingRecord.description})]})]}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(Card,{className:\"h-100 bg-light\",children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(\"h5\",{className:\"mb-3\",children:\"Summary\"}),/*#__PURE__*/_jsxs(ListGroup,{variant:\"flush\",children:[/*#__PURE__*/_jsxs(ListGroup.Item,{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(FaRulerHorizontal,{className:\"me-2 text-secondary\"}),\"Total Yard Usage\"]}),/*#__PURE__*/_jsxs(Badge,{bg:\"info\",pill:true,children:[calculateTotalYardUsage(),\" yards\"]})]}),/*#__PURE__*/_jsxs(ListGroup.Item,{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(FaTshirt,{className:\"me-2 text-secondary\"}),\"Total Pieces\"]}),/*#__PURE__*/_jsxs(Badge,{bg:\"success\",pill:true,children:[calculateTotalAllPieces(),\" pcs\"]})]}),/*#__PURE__*/_jsxs(ListGroup.Item,{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(FaClipboardList,{className:\"me-2 text-secondary\"}),\"Color Variants Used\"]}),/*#__PURE__*/_jsx(Badge,{bg:\"primary\",pill:true,children:((_cuttingRecord$detail3=cuttingRecord.details)===null||_cuttingRecord$detail3===void 0?void 0:_cuttingRecord$detail3.length)||0})]}),/*#__PURE__*/_jsxs(ListGroup.Item,{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(FaMoneyBillWave,{className:\"me-2 text-secondary\"}),\"Total Fabric Cost\"]}),/*#__PURE__*/_jsxs(Badge,{bg:\"warning\",text:\"dark\",pill:true,children:[\"Rs. \",calculateTotalCuttingValue()]})]})]})]})})})]})})]}),cuttingRecord.cutting_image&&/*#__PURE__*/_jsxs(Card,{className:\"mb-4 shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-secondary text-white\",children:/*#__PURE__*/_jsxs(\"h4\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(FaImage,{className:\"me-2\"}),\"Cutting Image\"]})}),/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center\",children:[/*#__PURE__*/_jsx(Image,{src:cuttingRecord.cutting_image,alt:\"Cutting process\",fluid:true,style:{maxHeight:'400px',border:'2px solid #dee2e6',borderRadius:'8px',boxShadow:'0 4px 8px rgba(0,0,0,0.1)'}}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[\"Cutting process image for \",cuttingRecord.product_name||\"this record\"]})})]})]}),/*#__PURE__*/_jsxs(Card,{className:\"shadow-sm\",children:[/*#__PURE__*/_jsx(Card.Header,{className:\"bg-info text-white\",children:/*#__PURE__*/_jsxs(\"h4\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(FaInfoCircle,{className:\"me-2\"}),\"Cutting Details\"]})}),/*#__PURE__*/_jsx(Card.Body,{className:\"p-0\",children:/*#__PURE__*/_jsxs(Table,{hover:true,responsive:true,className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-light\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Color\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Yard Usage\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Price per Yard\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Fabric Cost\"}),/*#__PURE__*/_jsx(\"th\",{children:\"XS\"}),/*#__PURE__*/_jsx(\"th\",{children:\"S\"}),/*#__PURE__*/_jsx(\"th\",{children:\"M\"}),/*#__PURE__*/_jsx(\"th\",{children:\"L\"}),/*#__PURE__*/_jsx(\"th\",{children:\"XL\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Total Pieces\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:cuttingRecord.details.map(detail=>{var _detail$fabric_varian6,_detail$fabric_varian7,_detail$fabric_varian8;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'20px',height:'20px',backgroundColor:((_detail$fabric_varian6=detail.fabric_variant_data)===null||_detail$fabric_varian6===void 0?void 0:_detail$fabric_varian6.color)||'#ccc',borderRadius:'4px',border:'1px solid #dee2e6',marginRight:'10px'}}),/*#__PURE__*/_jsx(\"span\",{children:((_detail$fabric_varian7=detail.fabric_variant_data)===null||_detail$fabric_varian7===void 0?void 0:_detail$fabric_varian7.color_name)||'Unknown'})]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"info\",pill:true,children:[parseFloat(detail.yard_usage).toFixed(2),\" yards\"]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"secondary\",pill:true,children:[\"Rs. \",(_detail$fabric_varian8=detail.fabric_variant_data)===null||_detail$fabric_varian8===void 0?void 0:_detail$fabric_varian8.price_per_yard.toFixed(2)]})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"warning\",text:\"dark\",pill:true,children:[\"Rs. \",calculateCuttingValue(detail)]})}),/*#__PURE__*/_jsx(\"td\",{children:detail.xs>0?detail.xs:'-'}),/*#__PURE__*/_jsx(\"td\",{children:detail.s>0?detail.s:'-'}),/*#__PURE__*/_jsx(\"td\",{children:detail.m>0?detail.m:'-'}),/*#__PURE__*/_jsx(\"td\",{children:detail.l>0?detail.l:'-'}),/*#__PURE__*/_jsx(\"td\",{children:detail.xl>0?detail.xl:'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(Badge,{bg:\"success\",pill:true,children:[calculateTotalPieces(detail),\" pcs\"]})})]},detail.id);})}),/*#__PURE__*/_jsx(\"tfoot\",{className:\"bg-light\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:\"Total\"}),/*#__PURE__*/_jsxs(\"td\",{className:\"fw-bold\",children:[calculateTotalYardUsage(),\" yards\"]}),/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:\"-\"}),/*#__PURE__*/_jsxs(\"td\",{className:\"fw-bold\",children:[\"Rs. \",calculateTotalCuttingValue()]}),/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:cuttingRecord.details.reduce((sum,detail)=>sum+detail.xs,0)}),/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:cuttingRecord.details.reduce((sum,detail)=>sum+detail.s,0)}),/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:cuttingRecord.details.reduce((sum,detail)=>sum+detail.m,0)}),/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:cuttingRecord.details.reduce((sum,detail)=>sum+detail.l,0)}),/*#__PURE__*/_jsx(\"td\",{className:\"fw-bold\",children:cuttingRecord.details.reduce((sum,detail)=>sum+detail.xl,0)}),/*#__PURE__*/_jsxs(\"td\",{className:\"fw-bold\",children:[calculateTotalAllPieces(),\" pcs\"]})]})})]})})]})]})]}),/*#__PURE__*/_jsxs(Modal,{show:showPdfModal,onHide:closePdfModal,centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,children:/*#__PURE__*/_jsxs(Modal.Title,{children:[/*#__PURE__*/_jsx(FaFilePdf,{className:\"text-danger me-2\"}),\"Generate PDF Report\"]})}),/*#__PURE__*/_jsxs(Modal.Body,{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Are you sure you want to generate a PDF report for this cutting record?\"}),cuttingRecord&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-light p-3 rounded\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Product:\"}),\" \",cuttingRecord.product_name||\"N/A\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Fabrics:\"}),\" \",(_cuttingRecord$detail4=>{const fabricNames=new Set();(_cuttingRecord$detail4=cuttingRecord.details)===null||_cuttingRecord$detail4===void 0?void 0:_cuttingRecord$detail4.forEach(detail=>{var _detail$fabric_varian9,_detail$fabric_varian10;if((_detail$fabric_varian9=detail.fabric_variant_data)!==null&&_detail$fabric_varian9!==void 0&&(_detail$fabric_varian10=_detail$fabric_varian9.fabric_definition_data)!==null&&_detail$fabric_varian10!==void 0&&_detail$fabric_varian10.fabric_name){fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);}});return Array.from(fabricNames).join(', ')||\"N/A\";})()]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-1\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Date:\"}),\" \",cuttingRecord.cutting_date]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mb-0\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Total Cost:\"}),\" Rs. \",calculateTotalCuttingValue()]})]})]}),/*#__PURE__*/_jsxs(Modal.Footer,{children:[/*#__PURE__*/_jsxs(Button,{variant:\"secondary\",onClick:closePdfModal,children:[/*#__PURE__*/_jsx(FaTimes,{className:\"me-1\"}),\" Cancel\"]}),/*#__PURE__*/_jsxs(Button,{variant:\"success\",onClick:generatePDF,children:[/*#__PURE__*/_jsx(FaCheck,{className:\"me-1\"}),\" Generate PDF\"]})]})]})]});};export default CuttingRecordDetail;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "RoleBasedNavBar", "Container", "Row", "Col", "Card", "Table", "Badge", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "ListGroup", "Modal", "Image", "FaArrowLeft", "FaCalendarAlt", "FaCut", "FaTshirt", "FaInfoCircle", "FaRulerHorizontal", "FaClipboardList", "FaMoneyBillWave", "FaDownload", "FaFilePdf", "FaCheck", "FaTimes", "FaImage", "jsPDF", "autoTable", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CuttingRecordDetail", "_cuttingRecord$fabric", "_cuttingRecord$detail3", "recordId", "navigate", "cutting<PERSON><PERSON>ord", "setCuttingRecord", "loading", "setLoading", "error", "setError", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "returnPath", "setReturnPath", "showPdfModal", "setShowPdfModal", "handleResize", "addEventListener", "removeEventListener", "savedReturnPath", "localStorage", "getItem", "removeItem", "get", "then", "response", "data", "catch", "console", "formatDate", "dateString", "options", "year", "month", "day", "Date", "toLocaleDateString", "undefined", "calculateTotalPieces", "detail", "xs", "s", "m", "l", "xl", "calculateTotalAllPieces", "details", "reduce", "total", "calculateTotalYardUsage", "parseFloat", "yard_usage", "toFixed", "calculateCuttingValue", "fabric_variant_data", "yardUsage", "pricePerYard", "price_per_yard", "calculateTotalCuttingValue", "value", "handleBack", "openPdfModal", "closePdfModal", "generatePDF", "_cuttingRecord$detail", "_cuttingRecord$detail2", "productName", "product_name", "fabricNames", "Set", "for<PERSON>ach", "_detail$fabric_varian", "_detail$fabric_varian2", "fabric_definition_data", "fabric_name", "add", "fabricName", "Array", "from", "join", "doc", "orientation", "unit", "format", "baseUrl", "location", "origin", "addImage", "logoError", "warn", "setFillColor", "rect", "setFontSize", "setTextColor", "text", "align", "cutting_date", "toString", "length", "finalY", "startY", "head", "body", "theme", "headStyles", "fillColor", "textColor", "styles", "fontSize", "lastAutoTable", "previousAutoTable", "colorDetails", "map", "_detail$fabric_varian3", "_detail$fabric_varian4", "_detail$fabric_varian5", "colorCode", "color", "colorName", "color_name", "columnStyles", "cellWidth", "didDrawCell", "section", "column", "index", "colorHex", "cell", "raw", "r", "parseInt", "substring", "g", "b", "x", "y", "width", "height", "setDrawColor", "line", "pageCount", "internal", "getNumberOfPages", "i", "setPage", "pageSize", "cleanProductName", "replace", "save", "id", "alert", "message", "children", "fluid", "style", "marginLeft", "transition", "padding", "className", "variant", "onClick", "animation", "role", "Header", "Body", "md", "description", "<PERSON><PERSON>", "bg", "pill", "cutting_image", "src", "alt", "maxHeight", "border", "borderRadius", "boxShadow", "hover", "responsive", "_detail$fabric_varian6", "_detail$fabric_varian7", "_detail$fabric_varian8", "backgroundColor", "marginRight", "sum", "show", "onHide", "centered", "closeButton", "Title", "_cuttingRecord$detail4", "_detail$fabric_varian9", "_detail$fabric_varian10", "Footer"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/CuttingRecordDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport {\r\n  Container, Row, Col, Card, Table, Badge, Spinner,\r\n  Alert, Button, ListGroup, Modal, Image\r\n} from 'react-bootstrap';\r\nimport {\r\n  FaArrowLeft, FaCalendarAlt, FaCut, FaTshirt,\r\n  FaInfoCircle, FaRulerHorizontal, FaClipboardList,\r\n  FaMoneyBillWave, FaDownload, FaFilePdf, FaCheck, FaTimes, FaImage\r\n} from 'react-icons/fa';\r\nimport { jsPDF } from 'jspdf';\r\nimport autoTable from 'jspdf-autotable';\r\n\r\n// We'll use a different approach for the logo\r\n\r\nconst CuttingRecordDetail = () => {\r\n  const { recordId } = useParams();\r\n  const navigate = useNavigate();\r\n  const [cuttingRecord, setCuttingRecord] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [returnPath, setReturnPath] = useState('');\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Get the return path from localStorage if available\r\n  useEffect(() => {\r\n    const savedReturnPath = localStorage.getItem('cuttingRecordReturnPath');\r\n    if (savedReturnPath) {\r\n      setReturnPath(savedReturnPath);\r\n    } else {\r\n      // Default to the cutting records list\r\n      setReturnPath('/viewcutting');\r\n    }\r\n\r\n    // Clean up the localStorage when component unmounts\r\n    return () => {\r\n      localStorage.removeItem('cuttingRecordReturnPath');\r\n    };\r\n  }, []);\r\n\r\n  // Fetch cutting record data\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios\r\n      .get(`http://localhost:8000/api/cutting/records/${recordId}/`)\r\n      .then((response) => {\r\n        setCuttingRecord(response.data);\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching cutting record details:\", error);\r\n        setError(\"Failed to load cutting record details.\");\r\n        setLoading(false);\r\n      });\r\n  }, [recordId]);\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    const options = { year: 'numeric', month: 'short', day: 'numeric' };\r\n    return new Date(dateString).toLocaleDateString(undefined, options);\r\n  };\r\n\r\n  // Calculate total pieces for a detail\r\n  const calculateTotalPieces = (detail) => {\r\n    return detail.xs + detail.s + detail.m + detail.l + detail.xl;\r\n  };\r\n\r\n  // Calculate total pieces for all details\r\n  const calculateTotalAllPieces = () => {\r\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\r\n    return cuttingRecord.details.reduce((total, detail) => {\r\n      return total + calculateTotalPieces(detail);\r\n    }, 0);\r\n  };\r\n\r\n  // Calculate total yard usage\r\n  const calculateTotalYardUsage = () => {\r\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\r\n    return cuttingRecord.details.reduce((total, detail) => {\r\n      return total + parseFloat(detail.yard_usage);\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Calculate fabric cutting value (cost)\r\n  const calculateCuttingValue = (detail) => {\r\n    if (!detail || !detail.fabric_variant_data) return 0;\r\n    const yardUsage = parseFloat(detail.yard_usage);\r\n    const pricePerYard = parseFloat(detail.fabric_variant_data.price_per_yard);\r\n    return (yardUsage * pricePerYard).toFixed(2);\r\n  };\r\n\r\n  // Calculate total cutting value\r\n  const calculateTotalCuttingValue = () => {\r\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\r\n    return cuttingRecord.details.reduce((total, detail) => {\r\n      if (!detail.fabric_variant_data) return total;\r\n      const value = parseFloat(detail.yard_usage) * parseFloat(detail.fabric_variant_data.price_per_yard);\r\n      return total + value;\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Handle back button click\r\n  const handleBack = () => {\r\n    navigate(returnPath);\r\n  };\r\n\r\n  // Open PDF confirmation modal\r\n  const openPdfModal = () => {\r\n    setShowPdfModal(true);\r\n  };\r\n\r\n  // Close PDF confirmation modal\r\n  const closePdfModal = () => {\r\n    setShowPdfModal(false);\r\n  };\r\n\r\n  // Generate PDF report\r\n  const generatePDF = () => {\r\n    if (!cuttingRecord) return;\r\n\r\n    try {\r\n      const productName = cuttingRecord.product_name || \"N/A\";\r\n      // Get fabric names from details\r\n      const fabricNames = new Set();\r\n      cuttingRecord.details?.forEach(detail => {\r\n        if (detail.fabric_variant_data?.fabric_definition_data?.fabric_name) {\r\n          fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\r\n        }\r\n      });\r\n      const fabricName = Array.from(fabricNames).join(', ') || \"N/A\";\r\n\r\n      // Create PDF document with orientation and unit specifications\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Add the actual logo from the public directory\r\n      try {\r\n        // Get the base URL for the current environment\r\n        const baseUrl = window.location.origin;\r\n\r\n        // Add the logo to the PDF\r\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\r\n      } catch (logoError) {\r\n        console.warn(\"Could not add logo to PDF:\", logoError);\r\n\r\n        // Fallback to a simple placeholder if the logo can't be loaded\r\n        doc.setFillColor(41, 128, 185); // Primary blue color\r\n        doc.rect(14, 10, 20, 20, 'F');\r\n\r\n        // Add \"PF\" text as a simple logo\r\n        doc.setFontSize(14);\r\n        doc.setTextColor(255, 255, 255);\r\n        doc.text(\"PF\", 24, 22, { align: 'center' });\r\n      }\r\n\r\n      // Reset text color for the rest of the document\r\n      doc.setTextColor(0, 0, 0);\r\n\r\n      // Add title and company info\r\n      doc.setFontSize(20);\r\n      doc.setTextColor(0, 0, 0);\r\n      doc.text(\"Cutting Record Report\", 105, 20, { align: 'center' });\r\n\r\n      doc.setFontSize(12);\r\n      doc.text(\"Pri Fashion - Garment Management System\", 105, 28, { align: 'center' });\r\n      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 34, { align: 'center' });\r\n\r\n      // Add cutting record details\r\n      doc.setFontSize(14);\r\n      doc.text(\"Cutting Record Details\", 14, 45);\r\n\r\n      doc.setFontSize(10);\r\n      const details = [\r\n        [\"Product Name:\", productName],\r\n        [\"Fabric Name:\", fabricName],\r\n        [\"Cutting Date:\", cuttingRecord.cutting_date],\r\n        [\"Total Quantity:\", calculateTotalAllPieces().toString()],\r\n        [\"Total Yard Usage:\", calculateTotalYardUsage().toString()],\r\n        [\"Color Variants Used:\", cuttingRecord.details?.length.toString() || \"0\"],\r\n        [\"Total Fabric Cost:\", `Rs. ${calculateTotalCuttingValue()}`]\r\n      ];\r\n\r\n      // First table\r\n      let finalY = 50;\r\n      autoTable(doc, {\r\n        startY: finalY,\r\n        head: [[\"Property\", \"Value\"]],\r\n        body: details,\r\n        theme: 'grid',\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n        styles: { fontSize: 10 }\r\n      });\r\n\r\n      // Get the final Y position after the first table\r\n      finalY = (doc.lastAutoTable || doc.previousAutoTable).finalY + 10;\r\n\r\n      // Add color usage details\r\n      doc.setFontSize(14);\r\n      doc.text(\"Color Usage Details\", 14, finalY);\r\n\r\n      if (cuttingRecord.details && cuttingRecord.details.length > 0) {\r\n        const colorDetails = cuttingRecord.details.map(detail => {\r\n          // Get the color code for display\r\n          const colorCode = detail.fabric_variant_data?.color || \"#CCCCCC\";\r\n          const colorName = detail.fabric_variant_data?.color_name || \"N/A\";\r\n\r\n          return [\r\n            colorName,\r\n            colorCode, // Add color code as a separate column\r\n            `${parseFloat(detail.yard_usage).toFixed(2)} yards`,\r\n            `Rs. ${detail.fabric_variant_data?.price_per_yard.toFixed(2)}`,\r\n            `Rs. ${calculateCuttingValue(detail)}`,\r\n            detail.xs || 0,\r\n            detail.s || 0,\r\n            detail.m || 0,\r\n            detail.l || 0,\r\n            detail.xl || 0,\r\n            calculateTotalPieces(detail)\r\n          ];\r\n        });\r\n\r\n        // Second table with color swatches\r\n        autoTable(doc, {\r\n          startY: finalY + 5,\r\n          head: [[\"Color Name\", \"Color\", \"Yard Usage\", \"Price/Yard\", \"Fabric Cost\", \"XS\", \"S\", \"M\", \"L\", \"XL\", \"Total\"]],\r\n          body: colorDetails,\r\n          theme: 'grid',\r\n          headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n          styles: { fontSize: 9 },\r\n          columnStyles: {\r\n            0: { cellWidth: 25 },\r\n            1: { cellWidth: 15 }, // Color swatch column\r\n            2: { cellWidth: 20 },\r\n            3: { cellWidth: 20 },\r\n            4: { cellWidth: 20 },\r\n          },\r\n          // Add color swatches to the color column\r\n          didDrawCell: (data) => {\r\n            if (data.section === 'body' && data.column.index === 1) {\r\n              const colorHex = data.cell.raw;\r\n\r\n              try {\r\n                // Convert hex color to RGB\r\n                const r = parseInt(colorHex.substring(1, 3), 16) || 0;\r\n                const g = parseInt(colorHex.substring(3, 5), 16) || 0;\r\n                const b = parseInt(colorHex.substring(5, 7), 16) || 0;\r\n\r\n                // Set fill color using RGB values\r\n                doc.setFillColor(r, g, b);\r\n\r\n                // Draw a color rectangle\r\n                doc.rect(\r\n                  data.cell.x + 2,\r\n                  data.cell.y + 2,\r\n                  data.cell.width - 4,\r\n                  data.cell.height - 4,\r\n                  'F'\r\n                );\r\n\r\n                // Add a border around the color swatch\r\n                doc.setDrawColor(200, 200, 200);\r\n                doc.rect(\r\n                  data.cell.x + 2,\r\n                  data.cell.y + 2,\r\n                  data.cell.width - 4,\r\n                  data.cell.height - 4,\r\n                  'S'\r\n                );\r\n              } catch (error) {\r\n                console.warn(\"Error drawing color swatch:\", error);\r\n                // Fallback to a gray color if there's an error\r\n                doc.setFillColor(200, 200, 200);\r\n                doc.rect(\r\n                  data.cell.x + 2,\r\n                  data.cell.y + 2,\r\n                  data.cell.width - 4,\r\n                  data.cell.height - 4,\r\n                  'F'\r\n                );\r\n              }\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        doc.text(\"No color details available\", 14, finalY + 5);\r\n      }\r\n\r\n      // Get the final Y position after the second table\r\n      finalY = (doc.lastAutoTable || doc.previousAutoTable).finalY + 15;\r\n\r\n      // Add signature fields\r\n      doc.setFontSize(12);\r\n      doc.text(\"Signatures:\", 14, finalY);\r\n\r\n      // Draw signature lines\r\n      finalY += 8;\r\n\r\n      // Owner signature\r\n      doc.line(14, finalY + 15, 80, finalY + 15); // Signature line\r\n      doc.setFontSize(10);\r\n      doc.text(\"Owner Signature\", 14, finalY + 20);\r\n      doc.text(\"Date: ________________\", 14, finalY + 25);\r\n\r\n      // Cutter signature\r\n      doc.line(120, finalY + 15, 186, finalY + 15); // Signature line\r\n      doc.text(\"Cutter Signature\", 120, finalY + 20);\r\n      doc.text(\"Date: ________________\", 120, finalY + 25);\r\n\r\n      // Add footer\r\n      const pageCount = doc.internal.getNumberOfPages();\r\n      for (let i = 1; i <= pageCount; i++) {\r\n        doc.setPage(i);\r\n        doc.setFontSize(8);\r\n        doc.text(\r\n          `Page ${i} of ${pageCount} - Pri Fashion Garment Management System`,\r\n          105,\r\n          doc.internal.pageSize.height - 10,\r\n          { align: 'center' }\r\n        );\r\n      }\r\n\r\n      // Save the PDF with a clean filename\r\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Cutting_Record_${cuttingRecord.id}_${cleanProductName}.pdf`);\r\n\r\n      // Close the modal\r\n      closePdfModal();\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      alert(\"Failed to generate PDF. Please try again: \" + error.message);\r\n      closePdfModal();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <Container fluid\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        {/* Back button and PDF button */}\r\n        <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n          <Button\r\n            variant=\"outline-secondary\"\r\n            onClick={handleBack}\r\n          >\r\n            <FaArrowLeft className=\"me-2\" /> Back\r\n          </Button>\r\n          {!loading && cuttingRecord && (\r\n            <Button\r\n              variant=\"outline-success\"\r\n              onClick={openPdfModal}\r\n            >\r\n              <FaDownload className=\"me-2\" /> Download PDF\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        {error && <Alert variant=\"danger\" className=\"text-center\">{error}</Alert>}\r\n\r\n        {loading ? (\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading cutting record details...</p>\r\n          </div>\r\n        ) : cuttingRecord && (\r\n          <>\r\n            {/* Cutting Record Header */}\r\n            <Card className=\"mb-4 shadow-sm\">\r\n              <Card.Header className=\"bg-primary text-white\">\r\n                <h4 className=\"mb-0\">\r\n                  <FaCut className=\"me-2\" />\r\n                  Cutting Record Details\r\n                </h4>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <Row>\r\n                  <Col md={6}>\r\n                    <h5 className=\"mb-3\">\r\n                      {cuttingRecord.product_name || \"Unnamed Cutting Record\"}\r\n                    </h5>\r\n                    <p className=\"mb-2\">\r\n                      <strong>Fabric:</strong>{' '}\r\n                      <span className=\"text-primary\">\r\n                        {cuttingRecord.fabric_definition_data?.fabric_name}\r\n                      </span>\r\n                    </p>\r\n                    <p className=\"mb-2\">\r\n                      <strong>Cutting Date:</strong>{' '}\r\n                      <span>\r\n                        <FaCalendarAlt className=\"me-1 text-secondary\" />\r\n                        {formatDate(cuttingRecord.cutting_date)}\r\n                      </span>\r\n                    </p>\r\n                    {cuttingRecord.description && (\r\n                      <p className=\"mb-2\">\r\n                        <strong>Description:</strong>{' '}\r\n                        <span>{cuttingRecord.description}</span>\r\n                      </p>\r\n                    )}\r\n                  </Col>\r\n                  <Col md={6}>\r\n                    <Card className=\"h-100 bg-light\">\r\n                      <Card.Body>\r\n                        <h5 className=\"mb-3\">Summary</h5>\r\n                        <ListGroup variant=\"flush\">\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaRulerHorizontal className=\"me-2 text-secondary\" />\r\n                              Total Yard Usage\r\n                            </div>\r\n                            <Badge bg=\"info\" pill>\r\n                              {calculateTotalYardUsage()} yards\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaTshirt className=\"me-2 text-secondary\" />\r\n                              Total Pieces\r\n                            </div>\r\n                            <Badge bg=\"success\" pill>\r\n                              {calculateTotalAllPieces()} pcs\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaClipboardList className=\"me-2 text-secondary\" />\r\n                              Color Variants Used\r\n                            </div>\r\n                            <Badge bg=\"primary\" pill>\r\n                              {cuttingRecord.details?.length || 0}\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaMoneyBillWave className=\"me-2 text-secondary\" />\r\n                              Total Fabric Cost\r\n                            </div>\r\n                            <Badge bg=\"warning\" text=\"dark\" pill>\r\n                              Rs. {calculateTotalCuttingValue()}\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                        </ListGroup>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  </Col>\r\n                </Row>\r\n              </Card.Body>\r\n            </Card>\r\n\r\n            {/* Cutting Image Section */}\r\n            {cuttingRecord.cutting_image && (\r\n              <Card className=\"mb-4 shadow-sm\">\r\n                <Card.Header className=\"bg-secondary text-white\">\r\n                  <h4 className=\"mb-0\">\r\n                    <FaImage className=\"me-2\" />\r\n                    Cutting Image\r\n                  </h4>\r\n                </Card.Header>\r\n                <Card.Body className=\"text-center\">\r\n                  <Image\r\n                    src={cuttingRecord.cutting_image}\r\n                    alt=\"Cutting process\"\r\n                    fluid\r\n                    style={{\r\n                      maxHeight: '400px',\r\n                      border: '2px solid #dee2e6',\r\n                      borderRadius: '8px',\r\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\r\n                    }}\r\n                  />\r\n                  <div className=\"mt-2\">\r\n                    <small className=\"text-muted\">\r\n                      Cutting process image for {cuttingRecord.product_name || \"this record\"}\r\n                    </small>\r\n                  </div>\r\n                </Card.Body>\r\n              </Card>\r\n            )}\r\n\r\n            {/* Cutting Details Table */}\r\n            <Card className=\"shadow-sm\">\r\n              <Card.Header className=\"bg-info text-white\">\r\n                <h4 className=\"mb-0\">\r\n                  <FaInfoCircle className=\"me-2\" />\r\n                  Cutting Details\r\n                </h4>\r\n              </Card.Header>\r\n              <Card.Body className=\"p-0\">\r\n                <Table hover responsive className=\"mb-0\">\r\n                  <thead className=\"bg-light\">\r\n                    <tr>\r\n                      <th>Color</th>\r\n                      <th>Yard Usage</th>\r\n                      <th>Price per Yard</th>\r\n                      <th>Fabric Cost</th>\r\n                      <th>XS</th>\r\n                      <th>S</th>\r\n                      <th>M</th>\r\n                      <th>L</th>\r\n                      <th>XL</th>\r\n                      <th>Total Pieces</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {cuttingRecord.details.map((detail) => (\r\n                      <tr key={detail.id}>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div\r\n                              style={{\r\n                                width: '20px',\r\n                                height: '20px',\r\n                                backgroundColor: detail.fabric_variant_data?.color || '#ccc',\r\n                                borderRadius: '4px',\r\n                                border: '1px solid #dee2e6',\r\n                                marginRight: '10px'\r\n                              }}\r\n                            />\r\n                            <span>{detail.fabric_variant_data?.color_name || 'Unknown'}</span>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"info\" pill>\r\n                            {parseFloat(detail.yard_usage).toFixed(2)} yards\r\n                          </Badge>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"secondary\" pill>\r\n                            Rs. {detail.fabric_variant_data?.price_per_yard.toFixed(2)}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"warning\" text=\"dark\" pill>\r\n                            Rs. {calculateCuttingValue(detail)}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>{detail.xs > 0 ? detail.xs : '-'}</td>\r\n                        <td>{detail.s > 0 ? detail.s : '-'}</td>\r\n                        <td>{detail.m > 0 ? detail.m : '-'}</td>\r\n                        <td>{detail.l > 0 ? detail.l : '-'}</td>\r\n                        <td>{detail.xl > 0 ? detail.xl : '-'}</td>\r\n                        <td>\r\n                          <Badge bg=\"success\" pill>\r\n                            {calculateTotalPieces(detail)} pcs\r\n                          </Badge>\r\n                        </td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                  <tfoot className=\"bg-light\">\r\n                    <tr>\r\n                      <td className=\"fw-bold\">Total</td>\r\n                      <td className=\"fw-bold\">{calculateTotalYardUsage()} yards</td>\r\n                      <td className=\"fw-bold\">-</td>\r\n                      <td className=\"fw-bold\">Rs. {calculateTotalCuttingValue()}</td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.xs, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.s, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.m, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.l, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.xl, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">{calculateTotalAllPieces()} pcs</td>\r\n                    </tr>\r\n                  </tfoot>\r\n                </Table>\r\n              </Card.Body>\r\n            </Card>\r\n          </>\r\n        )}\r\n      </Container>\r\n\r\n      {/* PDF Confirmation Modal */}\r\n      <Modal show={showPdfModal} onHide={closePdfModal} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"text-danger me-2\" />\r\n            Generate PDF Report\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Are you sure you want to generate a PDF report for this cutting record?</p>\r\n          {cuttingRecord && (\r\n            <div className=\"bg-light p-3 rounded\">\r\n              <p className=\"mb-1\"><strong>Product:</strong> {cuttingRecord.product_name || \"N/A\"}</p>\r\n              <p className=\"mb-1\"><strong>Fabrics:</strong> {(() => {\r\n                const fabricNames = new Set();\r\n                cuttingRecord.details?.forEach(detail => {\r\n                  if (detail.fabric_variant_data?.fabric_definition_data?.fabric_name) {\r\n                    fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\r\n                  }\r\n                });\r\n                return Array.from(fabricNames).join(', ') || \"N/A\";\r\n              })()}</p>\r\n              <p className=\"mb-1\"><strong>Date:</strong> {cuttingRecord.cutting_date}</p>\r\n              <p className=\"mb-0\"><strong>Total Cost:</strong> Rs. {calculateTotalCuttingValue()}</p>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={closePdfModal}>\r\n            <FaTimes className=\"me-1\" /> Cancel\r\n          </Button>\r\n          <Button variant=\"success\" onClick={generatePDF}>\r\n            <FaCheck className=\"me-1\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default CuttingRecordDetail;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,OACEC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,OAAO,CAChDC,KAAK,CAAEC,MAAM,CAAEC,SAAS,CAAEC,KAAK,CAAEC,KAAK,KACjC,iBAAiB,CACxB,OACEC,WAAW,CAAEC,aAAa,CAAEC,KAAK,CAAEC,QAAQ,CAC3CC,YAAY,CAAEC,iBAAiB,CAAEC,eAAe,CAChDC,eAAe,CAAEC,UAAU,CAAEC,SAAS,CAAEC,OAAO,CAAEC,OAAO,CAAEC,OAAO,KAC5D,gBAAgB,CACvB,OAASC,KAAK,KAAQ,OAAO,CAC7B,MAAO,CAAAC,SAAS,KAAM,iBAAiB,CAEvC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAChC,KAAM,CAAEC,QAAS,CAAC,CAAGvC,SAAS,CAAC,CAAC,CAChC,KAAM,CAAAwC,QAAQ,CAAGvC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkD,aAAa,CAAEC,gBAAgB,CAAC,CAAGnD,QAAQ,CAACoD,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5E,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACwD,YAAY,CAAEC,eAAe,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyD,YAAY,CAAGA,CAAA,GAAM,CACzBP,gBAAgB,CAACC,MAAM,CAACC,UAAU,EAAI,GAAG,CAAC,CAC5C,CAAC,CAEDD,MAAM,CAACO,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMN,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4D,eAAe,CAAGC,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC,CACvE,GAAIF,eAAe,CAAE,CACnBN,aAAa,CAACM,eAAe,CAAC,CAChC,CAAC,IAAM,CACL;AACAN,aAAa,CAAC,cAAc,CAAC,CAC/B,CAEA;AACA,MAAO,IAAM,CACXO,YAAY,CAACE,UAAU,CAAC,yBAAyB,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA/D,SAAS,CAAC,IAAM,CACd8C,UAAU,CAAC,IAAI,CAAC,CAChB7C,KAAK,CACF+D,GAAG,CAAC,6CAA6CvB,QAAQ,GAAG,CAAC,CAC7DwB,IAAI,CAAEC,QAAQ,EAAK,CAClBtB,gBAAgB,CAACsB,QAAQ,CAACC,IAAI,CAAC,CAC/BrB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACDsB,KAAK,CAAErB,KAAK,EAAK,CAChBsB,OAAO,CAACtB,KAAK,CAAC,wCAAwC,CAAEA,KAAK,CAAC,CAC9DC,QAAQ,CAAC,wCAAwC,CAAC,CAClDF,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAAE,CAACL,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAA6B,UAAU,CAAIC,UAAU,EAAK,CACjC,KAAM,CAAAC,OAAO,CAAG,CAAEC,IAAI,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAO,CAAEC,GAAG,CAAE,SAAU,CAAC,CACnE,MAAO,IAAI,CAAAC,IAAI,CAACL,UAAU,CAAC,CAACM,kBAAkB,CAACC,SAAS,CAAEN,OAAO,CAAC,CACpE,CAAC,CAED;AACA,KAAM,CAAAO,oBAAoB,CAAIC,MAAM,EAAK,CACvC,MAAO,CAAAA,MAAM,CAACC,EAAE,CAAGD,MAAM,CAACE,CAAC,CAAGF,MAAM,CAACG,CAAC,CAAGH,MAAM,CAACI,CAAC,CAAGJ,MAAM,CAACK,EAAE,CAC/D,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAM,CACpC,GAAI,CAAC3C,aAAa,EAAI,CAACA,aAAa,CAAC4C,OAAO,CAAE,MAAO,EAAC,CACtD,MAAO,CAAA5C,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,CAAET,MAAM,GAAK,CACrD,MAAO,CAAAS,KAAK,CAAGV,oBAAoB,CAACC,MAAM,CAAC,CAC7C,CAAC,CAAE,CAAC,CAAC,CACP,CAAC,CAED;AACA,KAAM,CAAAU,uBAAuB,CAAGA,CAAA,GAAM,CACpC,GAAI,CAAC/C,aAAa,EAAI,CAACA,aAAa,CAAC4C,OAAO,CAAE,MAAO,EAAC,CACtD,MAAO,CAAA5C,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,CAAET,MAAM,GAAK,CACrD,MAAO,CAAAS,KAAK,CAAGE,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAC9C,CAAC,CAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAC,qBAAqB,CAAId,MAAM,EAAK,CACxC,GAAI,CAACA,MAAM,EAAI,CAACA,MAAM,CAACe,mBAAmB,CAAE,MAAO,EAAC,CACpD,KAAM,CAAAC,SAAS,CAAGL,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAC/C,KAAM,CAAAK,YAAY,CAAGN,UAAU,CAACX,MAAM,CAACe,mBAAmB,CAACG,cAAc,CAAC,CAC1E,MAAO,CAACF,SAAS,CAAGC,YAAY,EAAEJ,OAAO,CAAC,CAAC,CAAC,CAC9C,CAAC,CAED;AACA,KAAM,CAAAM,0BAA0B,CAAGA,CAAA,GAAM,CACvC,GAAI,CAACxD,aAAa,EAAI,CAACA,aAAa,CAAC4C,OAAO,CAAE,MAAO,EAAC,CACtD,MAAO,CAAA5C,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,CAAET,MAAM,GAAK,CACrD,GAAI,CAACA,MAAM,CAACe,mBAAmB,CAAE,MAAO,CAAAN,KAAK,CAC7C,KAAM,CAAAW,KAAK,CAAGT,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAAGD,UAAU,CAACX,MAAM,CAACe,mBAAmB,CAACG,cAAc,CAAC,CACnG,MAAO,CAAAT,KAAK,CAAGW,KAAK,CACtB,CAAC,CAAE,CAAC,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAQ,UAAU,CAAGA,CAAA,GAAM,CACvB3D,QAAQ,CAACW,UAAU,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAiD,YAAY,CAAGA,CAAA,GAAM,CACzB9C,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAA+C,aAAa,CAAGA,CAAA,GAAM,CAC1B/C,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAgD,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI,CAAC7D,aAAa,CAAE,OAEpB,GAAI,KAAA8D,qBAAA,CAAAC,sBAAA,CACF,KAAM,CAAAC,WAAW,CAAGhE,aAAa,CAACiE,YAAY,EAAI,KAAK,CACvD;AACA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC7B,CAAAL,qBAAA,CAAA9D,aAAa,CAAC4C,OAAO,UAAAkB,qBAAA,iBAArBA,qBAAA,CAAuBM,OAAO,CAAC/B,MAAM,EAAI,KAAAgC,qBAAA,CAAAC,sBAAA,CACvC,IAAAD,qBAAA,CAAIhC,MAAM,CAACe,mBAAmB,UAAAiB,qBAAA,YAAAC,sBAAA,CAA1BD,qBAAA,CAA4BE,sBAAsB,UAAAD,sBAAA,WAAlDA,sBAAA,CAAoDE,WAAW,CAAE,CACnEN,WAAW,CAACO,GAAG,CAACpC,MAAM,CAACe,mBAAmB,CAACmB,sBAAsB,CAACC,WAAW,CAAC,CAChF,CACF,CAAC,CAAC,CACF,KAAM,CAAAE,UAAU,CAAGC,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,EAAI,KAAK,CAE9D;AACA,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAA3F,KAAK,CAAC,CACpB4F,WAAW,CAAE,UAAU,CACvBC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IACV,CAAC,CAAC,CAEF;AACA,GAAI,CACF;AACA,KAAM,CAAAC,OAAO,CAAG1E,MAAM,CAAC2E,QAAQ,CAACC,MAAM,CAEtC;AACAN,GAAG,CAACO,QAAQ,CAAC,GAAGH,OAAO,WAAW,CAAE,KAAK,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC5D,CAAE,MAAOI,SAAS,CAAE,CAClB5D,OAAO,CAAC6D,IAAI,CAAC,4BAA4B,CAAED,SAAS,CAAC,CAErD;AACAR,GAAG,CAACU,YAAY,CAAC,EAAE,CAAE,GAAG,CAAE,GAAG,CAAC,CAAE;AAChCV,GAAG,CAACW,IAAI,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAC,CAE7B;AACAX,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACa,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/Bb,GAAG,CAACc,IAAI,CAAC,IAAI,CAAE,EAAE,CAAE,EAAE,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAC7C,CAEA;AACAf,GAAG,CAACa,YAAY,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEzB;AACAb,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACa,YAAY,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACzBb,GAAG,CAACc,IAAI,CAAC,uBAAuB,CAAE,GAAG,CAAE,EAAE,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CAE/Df,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACc,IAAI,CAAC,yCAAyC,CAAE,GAAG,CAAE,EAAE,CAAE,CAAEC,KAAK,CAAE,QAAS,CAAC,CAAC,CACjFf,GAAG,CAACc,IAAI,CAAC,iBAAiB,GAAI,CAAA3D,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,CAAE,GAAG,CAAE,EAAE,CAAE,CAAE2D,KAAK,CAAE,QAAS,CAAC,CAAC,CAE1F;AACAf,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACc,IAAI,CAAC,wBAAwB,CAAE,EAAE,CAAE,EAAE,CAAC,CAE1Cd,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnB,KAAM,CAAA9C,OAAO,CAAG,CACd,CAAC,eAAe,CAAEoB,WAAW,CAAC,CAC9B,CAAC,cAAc,CAAEU,UAAU,CAAC,CAC5B,CAAC,eAAe,CAAE1E,aAAa,CAAC8F,YAAY,CAAC,CAC7C,CAAC,iBAAiB,CAAEnD,uBAAuB,CAAC,CAAC,CAACoD,QAAQ,CAAC,CAAC,CAAC,CACzD,CAAC,mBAAmB,CAAEhD,uBAAuB,CAAC,CAAC,CAACgD,QAAQ,CAAC,CAAC,CAAC,CAC3D,CAAC,sBAAsB,CAAE,EAAAhC,sBAAA,CAAA/D,aAAa,CAAC4C,OAAO,UAAAmB,sBAAA,iBAArBA,sBAAA,CAAuBiC,MAAM,CAACD,QAAQ,CAAC,CAAC,GAAI,GAAG,CAAC,CACzE,CAAC,oBAAoB,CAAE,OAAOvC,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAC9D,CAED;AACA,GAAI,CAAAyC,MAAM,CAAG,EAAE,CACf7G,SAAS,CAAC0F,GAAG,CAAE,CACboB,MAAM,CAAED,MAAM,CACdE,IAAI,CAAE,CAAC,CAAC,UAAU,CAAE,OAAO,CAAC,CAAC,CAC7BC,IAAI,CAAExD,OAAO,CACbyD,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAC,EAAE,CAAE,GAAG,CAAE,GAAG,CAAC,CAAEC,SAAS,CAAE,GAAI,CAAC,CACzDC,MAAM,CAAE,CAAEC,QAAQ,CAAE,EAAG,CACzB,CAAC,CAAC,CAEF;AACAT,MAAM,CAAG,CAACnB,GAAG,CAAC6B,aAAa,EAAI7B,GAAG,CAAC8B,iBAAiB,EAAEX,MAAM,CAAG,EAAE,CAEjE;AACAnB,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACc,IAAI,CAAC,qBAAqB,CAAE,EAAE,CAAEK,MAAM,CAAC,CAE3C,GAAIjG,aAAa,CAAC4C,OAAO,EAAI5C,aAAa,CAAC4C,OAAO,CAACoD,MAAM,CAAG,CAAC,CAAE,CAC7D,KAAM,CAAAa,YAAY,CAAG7G,aAAa,CAAC4C,OAAO,CAACkE,GAAG,CAACzE,MAAM,EAAI,KAAA0E,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACvD;AACA,KAAM,CAAAC,SAAS,CAAG,EAAAH,sBAAA,CAAA1E,MAAM,CAACe,mBAAmB,UAAA2D,sBAAA,iBAA1BA,sBAAA,CAA4BI,KAAK,GAAI,SAAS,CAChE,KAAM,CAAAC,SAAS,CAAG,EAAAJ,sBAAA,CAAA3E,MAAM,CAACe,mBAAmB,UAAA4D,sBAAA,iBAA1BA,sBAAA,CAA4BK,UAAU,GAAI,KAAK,CAEjE,MAAO,CACLD,SAAS,CACTF,SAAS,CAAE;AACX,GAAGlE,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,QAAQ,CACnD,QAAA+D,sBAAA,CAAO5E,MAAM,CAACe,mBAAmB,UAAA6D,sBAAA,iBAA1BA,sBAAA,CAA4B1D,cAAc,CAACL,OAAO,CAAC,CAAC,CAAC,EAAE,CAC9D,OAAOC,qBAAqB,CAACd,MAAM,CAAC,EAAE,CACtCA,MAAM,CAACC,EAAE,EAAI,CAAC,CACdD,MAAM,CAACE,CAAC,EAAI,CAAC,CACbF,MAAM,CAACG,CAAC,EAAI,CAAC,CACbH,MAAM,CAACI,CAAC,EAAI,CAAC,CACbJ,MAAM,CAACK,EAAE,EAAI,CAAC,CACdN,oBAAoB,CAACC,MAAM,CAAC,CAC7B,CACH,CAAC,CAAC,CAEF;AACAjD,SAAS,CAAC0F,GAAG,CAAE,CACboB,MAAM,CAAED,MAAM,CAAG,CAAC,CAClBE,IAAI,CAAE,CAAC,CAAC,YAAY,CAAE,OAAO,CAAE,YAAY,CAAE,YAAY,CAAE,aAAa,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,OAAO,CAAC,CAAC,CAC9GC,IAAI,CAAES,YAAY,CAClBR,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,CAAEC,SAAS,CAAE,CAAC,EAAE,CAAE,GAAG,CAAE,GAAG,CAAC,CAAEC,SAAS,CAAE,GAAI,CAAC,CACzDC,MAAM,CAAE,CAAEC,QAAQ,CAAE,CAAE,CAAC,CACvBY,YAAY,CAAE,CACZ,CAAC,CAAE,CAAEC,SAAS,CAAE,EAAG,CAAC,CACpB,CAAC,CAAE,CAAEA,SAAS,CAAE,EAAG,CAAC,CAAE;AACtB,CAAC,CAAE,CAAEA,SAAS,CAAE,EAAG,CAAC,CACpB,CAAC,CAAE,CAAEA,SAAS,CAAE,EAAG,CAAC,CACpB,CAAC,CAAE,CAAEA,SAAS,CAAE,EAAG,CACrB,CAAC,CACD;AACAC,WAAW,CAAGhG,IAAI,EAAK,CACrB,GAAIA,IAAI,CAACiG,OAAO,GAAK,MAAM,EAAIjG,IAAI,CAACkG,MAAM,CAACC,KAAK,GAAK,CAAC,CAAE,CACtD,KAAM,CAAAC,QAAQ,CAAGpG,IAAI,CAACqG,IAAI,CAACC,GAAG,CAE9B,GAAI,CACF;AACA,KAAM,CAAAC,CAAC,CAAGC,QAAQ,CAACJ,QAAQ,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,EAAI,CAAC,CACrD,KAAM,CAAAC,CAAC,CAAGF,QAAQ,CAACJ,QAAQ,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,EAAI,CAAC,CACrD,KAAM,CAAAE,CAAC,CAAGH,QAAQ,CAACJ,QAAQ,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAC,EAAI,CAAC,CAErD;AACAnD,GAAG,CAACU,YAAY,CAACuC,CAAC,CAAEG,CAAC,CAAEC,CAAC,CAAC,CAEzB;AACArD,GAAG,CAACW,IAAI,CACNjE,IAAI,CAACqG,IAAI,CAACO,CAAC,CAAG,CAAC,CACf5G,IAAI,CAACqG,IAAI,CAACQ,CAAC,CAAG,CAAC,CACf7G,IAAI,CAACqG,IAAI,CAACS,KAAK,CAAG,CAAC,CACnB9G,IAAI,CAACqG,IAAI,CAACU,MAAM,CAAG,CAAC,CACpB,GACF,CAAC,CAED;AACAzD,GAAG,CAAC0D,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/B1D,GAAG,CAACW,IAAI,CACNjE,IAAI,CAACqG,IAAI,CAACO,CAAC,CAAG,CAAC,CACf5G,IAAI,CAACqG,IAAI,CAACQ,CAAC,CAAG,CAAC,CACf7G,IAAI,CAACqG,IAAI,CAACS,KAAK,CAAG,CAAC,CACnB9G,IAAI,CAACqG,IAAI,CAACU,MAAM,CAAG,CAAC,CACpB,GACF,CAAC,CACH,CAAE,MAAOnI,KAAK,CAAE,CACdsB,OAAO,CAAC6D,IAAI,CAAC,6BAA6B,CAAEnF,KAAK,CAAC,CAClD;AACA0E,GAAG,CAACU,YAAY,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/BV,GAAG,CAACW,IAAI,CACNjE,IAAI,CAACqG,IAAI,CAACO,CAAC,CAAG,CAAC,CACf5G,IAAI,CAACqG,IAAI,CAACQ,CAAC,CAAG,CAAC,CACf7G,IAAI,CAACqG,IAAI,CAACS,KAAK,CAAG,CAAC,CACnB9G,IAAI,CAACqG,IAAI,CAACU,MAAM,CAAG,CAAC,CACpB,GACF,CAAC,CACH,CACF,CACF,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzD,GAAG,CAACc,IAAI,CAAC,4BAA4B,CAAE,EAAE,CAAEK,MAAM,CAAG,CAAC,CAAC,CACxD,CAEA;AACAA,MAAM,CAAG,CAACnB,GAAG,CAAC6B,aAAa,EAAI7B,GAAG,CAAC8B,iBAAiB,EAAEX,MAAM,CAAG,EAAE,CAEjE;AACAnB,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACc,IAAI,CAAC,aAAa,CAAE,EAAE,CAAEK,MAAM,CAAC,CAEnC;AACAA,MAAM,EAAI,CAAC,CAEX;AACAnB,GAAG,CAAC2D,IAAI,CAAC,EAAE,CAAExC,MAAM,CAAG,EAAE,CAAE,EAAE,CAAEA,MAAM,CAAG,EAAE,CAAC,CAAE;AAC5CnB,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC,CACnBZ,GAAG,CAACc,IAAI,CAAC,iBAAiB,CAAE,EAAE,CAAEK,MAAM,CAAG,EAAE,CAAC,CAC5CnB,GAAG,CAACc,IAAI,CAAC,wBAAwB,CAAE,EAAE,CAAEK,MAAM,CAAG,EAAE,CAAC,CAEnD;AACAnB,GAAG,CAAC2D,IAAI,CAAC,GAAG,CAAExC,MAAM,CAAG,EAAE,CAAE,GAAG,CAAEA,MAAM,CAAG,EAAE,CAAC,CAAE;AAC9CnB,GAAG,CAACc,IAAI,CAAC,kBAAkB,CAAE,GAAG,CAAEK,MAAM,CAAG,EAAE,CAAC,CAC9CnB,GAAG,CAACc,IAAI,CAAC,wBAAwB,CAAE,GAAG,CAAEK,MAAM,CAAG,EAAE,CAAC,CAEpD;AACA,KAAM,CAAAyC,SAAS,CAAG5D,GAAG,CAAC6D,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CACjD,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAIH,SAAS,CAAEG,CAAC,EAAE,CAAE,CACnC/D,GAAG,CAACgE,OAAO,CAACD,CAAC,CAAC,CACd/D,GAAG,CAACY,WAAW,CAAC,CAAC,CAAC,CAClBZ,GAAG,CAACc,IAAI,CACN,QAAQiD,CAAC,OAAOH,SAAS,0CAA0C,CACnE,GAAG,CACH5D,GAAG,CAAC6D,QAAQ,CAACI,QAAQ,CAACR,MAAM,CAAG,EAAE,CACjC,CAAE1C,KAAK,CAAE,QAAS,CACpB,CAAC,CACH,CAEA;AACA,KAAM,CAAAmD,gBAAgB,CAAGhF,WAAW,CAACiF,OAAO,CAAC,eAAe,CAAE,GAAG,CAAC,CAClEnE,GAAG,CAACoE,IAAI,CAAC,kBAAkBlJ,aAAa,CAACmJ,EAAE,IAAIH,gBAAgB,MAAM,CAAC,CAEtE;AACApF,aAAa,CAAC,CAAC,CACjB,CAAE,MAAOxD,KAAK,CAAE,CACdsB,OAAO,CAACtB,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CgJ,KAAK,CAAC,4CAA4C,CAAGhJ,KAAK,CAACiJ,OAAO,CAAC,CACnEzF,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAED,mBACEpE,KAAA,CAAAE,SAAA,EAAA4J,QAAA,eACEhK,IAAA,CAAC7B,eAAe,GAAE,CAAC,cACnB+B,KAAA,CAAC9B,SAAS,EAAC6L,KAAK,MACdC,KAAK,CAAE,CACLC,UAAU,CAAEnJ,aAAa,CAAG,OAAO,CAAG,MAAM,CAC5CgI,KAAK,CAAE,eAAehI,aAAa,CAAG,OAAO,CAAG,MAAM,GAAG,CACzDoJ,UAAU,CAAE,eAAe,CAC3BC,OAAO,CAAE,MACX,CAAE,CAAAL,QAAA,eAGF9J,KAAA,QAAKoK,SAAS,CAAC,wDAAwD,CAAAN,QAAA,eACrE9J,KAAA,CAACtB,MAAM,EACL2L,OAAO,CAAC,mBAAmB,CAC3BC,OAAO,CAAEpG,UAAW,CAAA4F,QAAA,eAEpBhK,IAAA,CAAChB,WAAW,EAACsL,SAAS,CAAC,MAAM,CAAE,CAAC,QAClC,EAAQ,CAAC,CACR,CAAC1J,OAAO,EAAIF,aAAa,eACxBR,KAAA,CAACtB,MAAM,EACL2L,OAAO,CAAC,iBAAiB,CACzBC,OAAO,CAAEnG,YAAa,CAAA2F,QAAA,eAEtBhK,IAAA,CAACR,UAAU,EAAC8K,SAAS,CAAC,MAAM,CAAE,CAAC,gBACjC,EAAQ,CACT,EACE,CAAC,CAELxJ,KAAK,eAAId,IAAA,CAACrB,KAAK,EAAC4L,OAAO,CAAC,QAAQ,CAACD,SAAS,CAAC,aAAa,CAAAN,QAAA,CAAElJ,KAAK,CAAQ,CAAC,CAExEF,OAAO,cACNV,KAAA,QAAKoK,SAAS,CAAC,kBAAkB,CAAAN,QAAA,eAC/BhK,IAAA,CAACtB,OAAO,EAAC+L,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAACH,OAAO,CAAC,SAAS,CAAAP,QAAA,cACzDhK,IAAA,SAAMsK,SAAS,CAAC,iBAAiB,CAAAN,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,cACVhK,IAAA,MAAGsK,SAAS,CAAC,MAAM,CAAAN,QAAA,CAAC,mCAAiC,CAAG,CAAC,EACtD,CAAC,CACJtJ,aAAa,eACfR,KAAA,CAAAE,SAAA,EAAA4J,QAAA,eAEE9J,KAAA,CAAC3B,IAAI,EAAC+L,SAAS,CAAC,gBAAgB,CAAAN,QAAA,eAC9BhK,IAAA,CAACzB,IAAI,CAACoM,MAAM,EAACL,SAAS,CAAC,uBAAuB,CAAAN,QAAA,cAC5C9J,KAAA,OAAIoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAClBhK,IAAA,CAACd,KAAK,EAACoL,SAAS,CAAC,MAAM,CAAE,CAAC,yBAE5B,EAAI,CAAC,CACM,CAAC,cACdtK,IAAA,CAACzB,IAAI,CAACqM,IAAI,EAAAZ,QAAA,cACR9J,KAAA,CAAC7B,GAAG,EAAA2L,QAAA,eACF9J,KAAA,CAAC5B,GAAG,EAACuM,EAAE,CAAE,CAAE,CAAAb,QAAA,eACThK,IAAA,OAAIsK,SAAS,CAAC,MAAM,CAAAN,QAAA,CACjBtJ,aAAa,CAACiE,YAAY,EAAI,wBAAwB,CACrD,CAAC,cACLzE,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eACjBhK,IAAA,WAAAgK,QAAA,CAAQ,SAAO,CAAQ,CAAC,CAAC,GAAG,cAC5BhK,IAAA,SAAMsK,SAAS,CAAC,cAAc,CAAAN,QAAA,EAAA1J,qBAAA,CAC3BI,aAAa,CAACuE,sBAAsB,UAAA3E,qBAAA,iBAApCA,qBAAA,CAAsC4E,WAAW,CAC9C,CAAC,EACN,CAAC,cACJhF,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eACjBhK,IAAA,WAAAgK,QAAA,CAAQ,eAAa,CAAQ,CAAC,CAAC,GAAG,cAClC9J,KAAA,SAAA8J,QAAA,eACEhK,IAAA,CAACf,aAAa,EAACqL,SAAS,CAAC,qBAAqB,CAAE,CAAC,CAChDjI,UAAU,CAAC3B,aAAa,CAAC8F,YAAY,CAAC,EACnC,CAAC,EACN,CAAC,CACH9F,aAAa,CAACoK,WAAW,eACxB5K,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eACjBhK,IAAA,WAAAgK,QAAA,CAAQ,cAAY,CAAQ,CAAC,CAAC,GAAG,cACjChK,IAAA,SAAAgK,QAAA,CAAOtJ,aAAa,CAACoK,WAAW,CAAO,CAAC,EACvC,CACJ,EACE,CAAC,cACN9K,IAAA,CAAC1B,GAAG,EAACuM,EAAE,CAAE,CAAE,CAAAb,QAAA,cACThK,IAAA,CAACzB,IAAI,EAAC+L,SAAS,CAAC,gBAAgB,CAAAN,QAAA,cAC9B9J,KAAA,CAAC3B,IAAI,CAACqM,IAAI,EAAAZ,QAAA,eACRhK,IAAA,OAAIsK,SAAS,CAAC,MAAM,CAAAN,QAAA,CAAC,SAAO,CAAI,CAAC,cACjC9J,KAAA,CAACrB,SAAS,EAAC0L,OAAO,CAAC,OAAO,CAAAP,QAAA,eACxB9J,KAAA,CAACrB,SAAS,CAACkM,IAAI,EAACT,SAAS,CAAC,mDAAmD,CAAAN,QAAA,eAC3E9J,KAAA,QAAA8J,QAAA,eACEhK,IAAA,CAACX,iBAAiB,EAACiL,SAAS,CAAC,qBAAqB,CAAE,CAAC,mBAEvD,EAAK,CAAC,cACNpK,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,MAAM,CAACC,IAAI,MAAAjB,QAAA,EAClBvG,uBAAuB,CAAC,CAAC,CAAC,QAC7B,EAAO,CAAC,EACM,CAAC,cACjBvD,KAAA,CAACrB,SAAS,CAACkM,IAAI,EAACT,SAAS,CAAC,mDAAmD,CAAAN,QAAA,eAC3E9J,KAAA,QAAA8J,QAAA,eACEhK,IAAA,CAACb,QAAQ,EAACmL,SAAS,CAAC,qBAAqB,CAAE,CAAC,eAE9C,EAAK,CAAC,cACNpK,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,SAAS,CAACC,IAAI,MAAAjB,QAAA,EACrB3G,uBAAuB,CAAC,CAAC,CAAC,MAC7B,EAAO,CAAC,EACM,CAAC,cACjBnD,KAAA,CAACrB,SAAS,CAACkM,IAAI,EAACT,SAAS,CAAC,mDAAmD,CAAAN,QAAA,eAC3E9J,KAAA,QAAA8J,QAAA,eACEhK,IAAA,CAACV,eAAe,EAACgL,SAAS,CAAC,qBAAqB,CAAE,CAAC,sBAErD,EAAK,CAAC,cACNtK,IAAA,CAACvB,KAAK,EAACuM,EAAE,CAAC,SAAS,CAACC,IAAI,MAAAjB,QAAA,CACrB,EAAAzJ,sBAAA,CAAAG,aAAa,CAAC4C,OAAO,UAAA/C,sBAAA,iBAArBA,sBAAA,CAAuBmG,MAAM,GAAI,CAAC,CAC9B,CAAC,EACM,CAAC,cACjBxG,KAAA,CAACrB,SAAS,CAACkM,IAAI,EAACT,SAAS,CAAC,mDAAmD,CAAAN,QAAA,eAC3E9J,KAAA,QAAA8J,QAAA,eACEhK,IAAA,CAACT,eAAe,EAAC+K,SAAS,CAAC,qBAAqB,CAAE,CAAC,oBAErD,EAAK,CAAC,cACNpK,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,SAAS,CAAC1E,IAAI,CAAC,MAAM,CAAC2E,IAAI,MAAAjB,QAAA,EAAC,MAC/B,CAAC9F,0BAA0B,CAAC,CAAC,EAC5B,CAAC,EACM,CAAC,EACR,CAAC,EACH,CAAC,CACR,CAAC,CACJ,CAAC,EACH,CAAC,CACG,CAAC,EACR,CAAC,CAGNxD,aAAa,CAACwK,aAAa,eAC1BhL,KAAA,CAAC3B,IAAI,EAAC+L,SAAS,CAAC,gBAAgB,CAAAN,QAAA,eAC9BhK,IAAA,CAACzB,IAAI,CAACoM,MAAM,EAACL,SAAS,CAAC,yBAAyB,CAAAN,QAAA,cAC9C9J,KAAA,OAAIoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAClBhK,IAAA,CAACJ,OAAO,EAAC0K,SAAS,CAAC,MAAM,CAAE,CAAC,gBAE9B,EAAI,CAAC,CACM,CAAC,cACdpK,KAAA,CAAC3B,IAAI,CAACqM,IAAI,EAACN,SAAS,CAAC,aAAa,CAAAN,QAAA,eAChChK,IAAA,CAACjB,KAAK,EACJoM,GAAG,CAAEzK,aAAa,CAACwK,aAAc,CACjCE,GAAG,CAAC,iBAAiB,CACrBnB,KAAK,MACLC,KAAK,CAAE,CACLmB,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,SAAS,CAAE,2BACb,CAAE,CACH,CAAC,cACFxL,IAAA,QAAKsK,SAAS,CAAC,MAAM,CAAAN,QAAA,cACnB9J,KAAA,UAAOoK,SAAS,CAAC,YAAY,CAAAN,QAAA,EAAC,4BACF,CAACtJ,aAAa,CAACiE,YAAY,EAAI,aAAa,EACjE,CAAC,CACL,CAAC,EACG,CAAC,EACR,CACP,cAGDzE,KAAA,CAAC3B,IAAI,EAAC+L,SAAS,CAAC,WAAW,CAAAN,QAAA,eACzBhK,IAAA,CAACzB,IAAI,CAACoM,MAAM,EAACL,SAAS,CAAC,oBAAoB,CAAAN,QAAA,cACzC9J,KAAA,OAAIoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAClBhK,IAAA,CAACZ,YAAY,EAACkL,SAAS,CAAC,MAAM,CAAE,CAAC,kBAEnC,EAAI,CAAC,CACM,CAAC,cACdtK,IAAA,CAACzB,IAAI,CAACqM,IAAI,EAACN,SAAS,CAAC,KAAK,CAAAN,QAAA,cACxB9J,KAAA,CAAC1B,KAAK,EAACiN,KAAK,MAACC,UAAU,MAACpB,SAAS,CAAC,MAAM,CAAAN,QAAA,eACtChK,IAAA,UAAOsK,SAAS,CAAC,UAAU,CAAAN,QAAA,cACzB9J,KAAA,OAAA8J,QAAA,eACEhK,IAAA,OAAAgK,QAAA,CAAI,OAAK,CAAI,CAAC,cACdhK,IAAA,OAAAgK,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBhK,IAAA,OAAAgK,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBhK,IAAA,OAAAgK,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBhK,IAAA,OAAAgK,QAAA,CAAI,IAAE,CAAI,CAAC,cACXhK,IAAA,OAAAgK,QAAA,CAAI,GAAC,CAAI,CAAC,cACVhK,IAAA,OAAAgK,QAAA,CAAI,GAAC,CAAI,CAAC,cACVhK,IAAA,OAAAgK,QAAA,CAAI,GAAC,CAAI,CAAC,cACVhK,IAAA,OAAAgK,QAAA,CAAI,IAAE,CAAI,CAAC,cACXhK,IAAA,OAAAgK,QAAA,CAAI,cAAY,CAAI,CAAC,EACnB,CAAC,CACA,CAAC,cACRhK,IAAA,UAAAgK,QAAA,CACGtJ,aAAa,CAAC4C,OAAO,CAACkE,GAAG,CAAEzE,MAAM,OAAA4I,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAChC3L,KAAA,OAAA8J,QAAA,eACEhK,IAAA,OAAAgK,QAAA,cACE9J,KAAA,QAAKoK,SAAS,CAAC,2BAA2B,CAAAN,QAAA,eACxChK,IAAA,QACEkK,KAAK,CAAE,CACLlB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACd6C,eAAe,CAAE,EAAAH,sBAAA,CAAA5I,MAAM,CAACe,mBAAmB,UAAA6H,sBAAA,iBAA1BA,sBAAA,CAA4B9D,KAAK,GAAI,MAAM,CAC5D0D,YAAY,CAAE,KAAK,CACnBD,MAAM,CAAE,mBAAmB,CAC3BS,WAAW,CAAE,MACf,CAAE,CACH,CAAC,cACF/L,IAAA,SAAAgK,QAAA,CAAO,EAAA4B,sBAAA,CAAA7I,MAAM,CAACe,mBAAmB,UAAA8H,sBAAA,iBAA1BA,sBAAA,CAA4B7D,UAAU,GAAI,SAAS,CAAO,CAAC,EAC/D,CAAC,CACJ,CAAC,cACL/H,IAAA,OAAAgK,QAAA,cACE9J,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,MAAM,CAACC,IAAI,MAAAjB,QAAA,EAClBtG,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,QAC5C,EAAO,CAAC,CACN,CAAC,cACL5D,IAAA,OAAAgK,QAAA,cACE9J,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,WAAW,CAACC,IAAI,MAAAjB,QAAA,EAAC,MACrB,EAAA6B,sBAAA,CAAC9I,MAAM,CAACe,mBAAmB,UAAA+H,sBAAA,iBAA1BA,sBAAA,CAA4B5H,cAAc,CAACL,OAAO,CAAC,CAAC,CAAC,EACrD,CAAC,CACN,CAAC,cACL5D,IAAA,OAAAgK,QAAA,cACE9J,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,SAAS,CAAC1E,IAAI,CAAC,MAAM,CAAC2E,IAAI,MAAAjB,QAAA,EAAC,MAC/B,CAACnG,qBAAqB,CAACd,MAAM,CAAC,EAC7B,CAAC,CACN,CAAC,cACL/C,IAAA,OAAAgK,QAAA,CAAKjH,MAAM,CAACC,EAAE,CAAG,CAAC,CAAGD,MAAM,CAACC,EAAE,CAAG,GAAG,CAAK,CAAC,cAC1ChD,IAAA,OAAAgK,QAAA,CAAKjH,MAAM,CAACE,CAAC,CAAG,CAAC,CAAGF,MAAM,CAACE,CAAC,CAAG,GAAG,CAAK,CAAC,cACxCjD,IAAA,OAAAgK,QAAA,CAAKjH,MAAM,CAACG,CAAC,CAAG,CAAC,CAAGH,MAAM,CAACG,CAAC,CAAG,GAAG,CAAK,CAAC,cACxClD,IAAA,OAAAgK,QAAA,CAAKjH,MAAM,CAACI,CAAC,CAAG,CAAC,CAAGJ,MAAM,CAACI,CAAC,CAAG,GAAG,CAAK,CAAC,cACxCnD,IAAA,OAAAgK,QAAA,CAAKjH,MAAM,CAACK,EAAE,CAAG,CAAC,CAAGL,MAAM,CAACK,EAAE,CAAG,GAAG,CAAK,CAAC,cAC1CpD,IAAA,OAAAgK,QAAA,cACE9J,KAAA,CAACzB,KAAK,EAACuM,EAAE,CAAC,SAAS,CAACC,IAAI,MAAAjB,QAAA,EACrBlH,oBAAoB,CAACC,MAAM,CAAC,CAAC,MAChC,EAAO,CAAC,CACN,CAAC,GAxCEA,MAAM,CAAC8G,EAyCZ,CAAC,EACN,CAAC,CACG,CAAC,cACR7J,IAAA,UAAOsK,SAAS,CAAC,UAAU,CAAAN,QAAA,cACzB9J,KAAA,OAAA8J,QAAA,eACEhK,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CAAC,OAAK,CAAI,CAAC,cAClC9J,KAAA,OAAIoK,SAAS,CAAC,SAAS,CAAAN,QAAA,EAAEvG,uBAAuB,CAAC,CAAC,CAAC,QAAM,EAAI,CAAC,cAC9DzD,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CAAC,GAAC,CAAI,CAAC,cAC9B9J,KAAA,OAAIoK,SAAS,CAAC,SAAS,CAAAN,QAAA,EAAC,MAAI,CAAC9F,0BAA0B,CAAC,CAAC,EAAK,CAAC,cAC/DlE,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACyI,GAAG,CAAEjJ,MAAM,GAAKiJ,GAAG,CAAGjJ,MAAM,CAACC,EAAE,CAAE,CAAC,CAAC,CAChE,CAAC,cACLhD,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACyI,GAAG,CAAEjJ,MAAM,GAAKiJ,GAAG,CAAGjJ,MAAM,CAACE,CAAC,CAAE,CAAC,CAAC,CAC/D,CAAC,cACLjD,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACyI,GAAG,CAAEjJ,MAAM,GAAKiJ,GAAG,CAAGjJ,MAAM,CAACG,CAAC,CAAE,CAAC,CAAC,CAC/D,CAAC,cACLlD,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACyI,GAAG,CAAEjJ,MAAM,GAAKiJ,GAAG,CAAGjJ,MAAM,CAACI,CAAC,CAAE,CAAC,CAAC,CAC/D,CAAC,cACLnD,IAAA,OAAIsK,SAAS,CAAC,SAAS,CAAAN,QAAA,CACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACyI,GAAG,CAAEjJ,MAAM,GAAKiJ,GAAG,CAAGjJ,MAAM,CAACK,EAAE,CAAE,CAAC,CAAC,CAChE,CAAC,cACLlD,KAAA,OAAIoK,SAAS,CAAC,SAAS,CAAAN,QAAA,EAAE3G,uBAAuB,CAAC,CAAC,CAAC,MAAI,EAAI,CAAC,EAC1D,CAAC,CACA,CAAC,EACH,CAAC,CACC,CAAC,EACR,CAAC,EACP,CACH,EACQ,CAAC,cAGZnD,KAAA,CAACpB,KAAK,EAACmN,IAAI,CAAE3K,YAAa,CAAC4K,MAAM,CAAE5H,aAAc,CAAC6H,QAAQ,MAAAnC,QAAA,eACxDhK,IAAA,CAAClB,KAAK,CAAC6L,MAAM,EAACyB,WAAW,MAAApC,QAAA,cACvB9J,KAAA,CAACpB,KAAK,CAACuN,KAAK,EAAArC,QAAA,eACVhK,IAAA,CAACP,SAAS,EAAC6K,SAAS,CAAC,kBAAkB,CAAE,CAAC,sBAE5C,EAAa,CAAC,CACF,CAAC,cACfpK,KAAA,CAACpB,KAAK,CAAC8L,IAAI,EAAAZ,QAAA,eACThK,IAAA,MAAAgK,QAAA,CAAG,yEAAuE,CAAG,CAAC,CAC7EtJ,aAAa,eACZR,KAAA,QAAKoK,SAAS,CAAC,sBAAsB,CAAAN,QAAA,eACnC9J,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAAChK,IAAA,WAAAgK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACtJ,aAAa,CAACiE,YAAY,EAAI,KAAK,EAAI,CAAC,cACvFzE,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAAChK,IAAA,WAAAgK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAC,CAACsC,sBAAA,EAAM,CACpD,KAAM,CAAA1H,WAAW,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC7B,CAAAyH,sBAAA,CAAA5L,aAAa,CAAC4C,OAAO,UAAAgJ,sBAAA,iBAArBA,sBAAA,CAAuBxH,OAAO,CAAC/B,MAAM,EAAI,KAAAwJ,sBAAA,CAAAC,uBAAA,CACvC,IAAAD,sBAAA,CAAIxJ,MAAM,CAACe,mBAAmB,UAAAyI,sBAAA,YAAAC,uBAAA,CAA1BD,sBAAA,CAA4BtH,sBAAsB,UAAAuH,uBAAA,WAAlDA,uBAAA,CAAoDtH,WAAW,CAAE,CACnEN,WAAW,CAACO,GAAG,CAACpC,MAAM,CAACe,mBAAmB,CAACmB,sBAAsB,CAACC,WAAW,CAAC,CAChF,CACF,CAAC,CAAC,CACF,MAAO,CAAAG,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,EAAI,KAAK,CACpD,CAAC,EAAE,CAAC,EAAI,CAAC,cACTrF,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAAChK,IAAA,WAAAgK,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACtJ,aAAa,CAAC8F,YAAY,EAAI,CAAC,cAC3EtG,KAAA,MAAGoK,SAAS,CAAC,MAAM,CAAAN,QAAA,eAAChK,IAAA,WAAAgK,QAAA,CAAQ,aAAW,CAAQ,CAAC,QAAK,CAAC9F,0BAA0B,CAAC,CAAC,EAAI,CAAC,EACpF,CACN,EACS,CAAC,cACbhE,KAAA,CAACpB,KAAK,CAAC2N,MAAM,EAAAzC,QAAA,eACX9J,KAAA,CAACtB,MAAM,EAAC2L,OAAO,CAAC,WAAW,CAACC,OAAO,CAAElG,aAAc,CAAA0F,QAAA,eACjDhK,IAAA,CAACL,OAAO,EAAC2K,SAAS,CAAC,MAAM,CAAE,CAAC,UAC9B,EAAQ,CAAC,cACTpK,KAAA,CAACtB,MAAM,EAAC2L,OAAO,CAAC,SAAS,CAACC,OAAO,CAAEjG,WAAY,CAAAyF,QAAA,eAC7ChK,IAAA,CAACN,OAAO,EAAC4K,SAAS,CAAC,MAAM,CAAE,CAAC,gBAC9B,EAAQ,CAAC,EACG,CAAC,EACV,CAAC,EACR,CAAC,CAEP,CAAC,CAED,cAAe,CAAAjK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}